# APNs Certificates

This directory is for storing your Apple Push Notification service (APNs) certificates.

## Setting Up APNs for Your iOS App

To enable push notifications for your iOS app, you'll need to:

1. **Create an App ID** in the Apple Developer Portal with Push Notifications capability enabled.

2. **Generate an APNs Authentication Key**:
   - Go to the Apple Developer Portal → Certificates, Identifiers & Profiles → Keys
   - Create a new key with APNs enabled
   - Download the `.p8` key file (you can only download it once!)
   - Note the Key ID and your Team ID

3. **Place the Key File in This Directory**:
   - Rename your downloaded key to `AuthKey_XXXXXXXXXX.p8` (replace XXXXXXXXXX with your Key ID)
   - Place it in this directory

4. **Update Environment Variables**:
   - Set the following variables in your `.env` file:
     ```
     APNS_KEY_PATH=./certs/AuthKey_XXXXXXXXXX.p8
     APNS_KEY_ID=XXXXXXXXXX
     APNS_TEAM_ID=XXXXXXXXXX
     APNS_BUNDLE_ID=com.yourapp.bundle
     ```

## Development Testing

For development without a real APNs certificate:

1. Set `MOCK_NOTIFICATIONS=true` in your `.env` file
2. The system will log notification attempts instead of sending real notifications

## Security Note

- **NEVER commit your `.p8` key file to version control**
- This directory is included in `.gitignore` to prevent accidental commits
- Keep your key secure as it grants access to send notifications to your app's users
