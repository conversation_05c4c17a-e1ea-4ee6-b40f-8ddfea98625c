{"name": "kickoffscorev2", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc || true", "start": "node dist/server.js", "dev": "nodemon src/server.ts", "test": "echo \"Error: no test specified\" && exit 1", "fetch-complete-season": "npx ts-node src/scripts/fetchCompleteSeasonData.ts", "fetch-season-fixtures": "npx ts-node src/scripts/fetchSeasonFixtures.ts", "fetch-league-fixtures": "npx ts-node src/scripts/fetchLeagueFixtures.ts", "test-transfers-team43": "npx ts-node src/scripts/testTransfersForTeam43.ts", "quick-transfer-test": "npx ts-node src/scripts/quickTransferTest.ts", "test-transfer-scheduler": "npx ts-node src/scripts/testTransferScheduler.ts", "sync-elo": "npx ts-node -e \"import('./src/services/eloService').then(s => s.syncEloRatings())\"", "sync-elo-options": "npx ts-node src/scripts/syncEloOptions.ts", "sync-elo-full": "npx ts-node -e \"import('./src/services/eloService').then(s => s.syncEloRatingsWithFullRemapping())\"", "manage-elo-mappings": "npx ts-node src/scripts/manageEloMappings.ts", "manual-elo-mappings": "npx ts-node src/scripts/manualEloMappings.ts", "final-elo-mappings": "npx ts-node src/scripts/finalEloMappings.ts", "ultimate-elo-mappings": "npx ts-node src/scripts/ultimateEloMappings.ts", "final-remaining-mappings": "npx ts-node src/scripts/finalRemainingMappings.ts", "final-21-mappings": "npx ts-node src/scripts/final21Mappings.ts", "final-exact-mappings": "npx ts-node src/scripts/finalExactMappings.ts", "final-last-mappings": "npx ts-node src/scripts/finalLastMappings.ts", "test:accuracy": "npx ts-node src/scripts/runAccuracyTest.ts", "enhanced-jobs": "npx ts-node src/scripts/runEnhancedPredictionJobs.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.9", "@types/mongodb": "^4.0.6", "@types/mongoose": "^5.11.96", "@types/node": "^22.14.1", "@types/node-cron": "^3.0.11", "@types/socket.io": "^3.0.1", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@playwright/test": "^1.54.1", "@types/cors": "^2.8.17", "@types/cron": "^2.0.1", "@types/multer": "^1.4.11", "axios": "^1.9.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "cron": "^4.3.0", "csv-parser": "^3.0.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "fastest-levenshtein": "^1.0.16", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.15.0", "mongoose": "^8.16.4", "multer": "^1.4.5-lts.1", "node-apn": "^3.0.0", "node-cron": "^3.0.3", "sharp": "^0.34.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "ws": "^8.18.2"}, "description": ""}