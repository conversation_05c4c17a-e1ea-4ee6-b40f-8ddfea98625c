// Comprehensive TypeScript interfaces for fixture-related data

export interface Player {
  id: number;
  name: string;
  number?: number;
  pos?: string;
  photo?: string;
  grid?: string; // Grid position like "1:1", "2:4", etc.
}

export interface Coach {
  id: number;
  name: string;
  photo?: string;
}

export interface TeamInfo {
  id: number;
  name: string;
  logo: string | null;
  colors?: {
    player?: {
      primary?: string;
      number?: string;
      border?: string;
    };
    goalkeeper?: {
      primary?: string;
      number?: string;
      border?: string;
    };
  };
}

// Statistics interfaces
export interface StatItem {
  type: string;
  value: string | number | null;
}

export interface TeamStatistics {
  team: TeamInfo;
  statistics: StatItem[];
}

export type MatchStatistics = TeamStatistics[];

// Events interfaces
export interface EventTime {
  elapsed: number;
  extra?: number;
}

export interface EventPlayer {
  id: number;
  name: string;
}

export interface EventTeam {
  id: number;
  name: string;
  logo: string | null;
}

export interface MatchEvent {
  time: EventTime;
  team: EventTeam;
  player?: EventPlayer;
  assist?: EventPlayer;
  type: string;
  detail: string;
  comments?: string;
}

export type MatchEvents = MatchEvent[];

// Lineups interfaces
export interface LineupPlayer {
  player: Player;
}

export interface TeamLineup {
  team: TeamInfo;
  coach?: Coach;
  formation?: string;
  startXI?: LineupPlayer[];
  substitutes?: LineupPlayer[];
}

export type MatchLineups = TeamLineup[];

// Head-to-Head interfaces
export interface HeadToHeadFixture {
  id: number;
  date: string;
}

export interface HeadToHeadGoals {
  home: number | null;
  away: number | null;
}

export interface HeadToHeadTeams {
  home: TeamInfo;
  away: TeamInfo;
}

export interface HeadToHeadMatch {
  fixture: HeadToHeadFixture;
  teams: HeadToHeadTeams;
  goals: HeadToHeadGoals;
  league?: {
    name: string;
  };
}

export type HeadToHeadData = HeadToHeadMatch[];

// Predictions interfaces
export interface PredictionWinner {
  id: number | null;
  name: string;
  comment: string;
}

export interface PredictionGoals {
  home: string;
  away: string;
}

export interface PredictionPercent {
  home: string;
  draw: string;
  away: string;
}

export interface PredictionData {
  winner: PredictionWinner;
  win_or_draw: boolean;
  under_over: string | null;
  goals: PredictionGoals;
  advice: string;
  percent: PredictionPercent;
}

export interface FixturePredictions {
  fixture: {
    id: number;
  };
  league: {
    id: number;
    name: string;
  };
  teams: {
    home: TeamInfo;
    away: TeamInfo;
  };
  predictions: PredictionData;
}

// Standings interfaces
export interface StandingTeam {
  id: number;
  name: string;
  logo: string | null;
}

export interface StandingStats {
  played: number;
  win: number;
  draw: number;
  lose: number;
  goals: {
    for: number;
    against: number;
  };
}

export interface StandingEntry {
  rank: number;
  team: StandingTeam;
  points: number;
  goalsDiff: number;
  group?: string;
  form?: string;
  status?: string;
  description?: string;
  all: StandingStats;
  home: StandingStats;
  away: StandingStats;
  update: string;
}

export interface LeagueStanding {
  league: {
    id: number;
    name: string;
    country: string;
    logo: string;
    flag: string;
    season: number;
    standings: StandingEntry[][];
  };
}

export type StandingsData = LeagueStanding[];

// Form data interfaces
export interface FormFixture {
  id: number;
  date: string;
}

export interface FormTeams {
  home: TeamInfo;
  away: TeamInfo;
}

export interface FormGoals {
  home: number | null;
  away: number | null;
}

export interface FormMatch {
  fixture: FormFixture;
  teams: FormTeams;
  goals: FormGoals;
  league: {
    name: string;
  };
}

export type TeamFormData = FormMatch[];

// Combined fixture page data interface
export interface FixturePageData {
  fixture: import('@/lib/api').Fixture;
  statistics?: MatchStatistics;
  events?: MatchEvents;
  lineups?: MatchLineups;
  headToHead?: HeadToHeadData;
  predictions?: FixturePredictions;
}
