/**
 * Utility functions for handling country flags
 * Provides fallback mechanisms for missing flag images
 */

// Comprehensive country name to ISO code mapping
const COUNTRY_CODE_MAPPINGS: { [key: string]: string | null } = {
  // English-speaking regions
  'england': 'gb',
  'scotland': 'gb',
  'wales': 'gb',
  'northern ireland': 'gb',
  'united kingdom': 'gb',
  'usa': 'us',
  'united states': 'us',
  'united states of america': 'us',
  
  // Asian countries
  'south korea': 'kr',
  'north korea': 'kp',
  'japan': 'jp',
  'china': 'cn',
  'india': 'in',
  'thailand': 'th',
  'vietnam': 'vn',
  'malaysia': 'my',
  'singapore': 'sg',
  'indonesia': 'id',
  'philippines': 'ph',
  
  // European countries
  'czech republic': 'cz',
  'bosnia and herzegovina': 'ba',
  'poland': 'pl',
  'sweden': 'se',
  'switzerland': 'ch',
  'bulgaria': 'bg',
  'faroe islands': 'fo',
  'kazakhstan': 'kz',
  'russia': 'ru',
  'ukraine': 'ua',
  'belarus': 'by',
  'lithuania': 'lt',
  'latvia': 'lv',
  'estonia': 'ee',
  'finland': 'fi',
  'norway': 'no',
  'denmark': 'dk',
  'iceland': 'is',
  'ireland': 'ie',
  'netherlands': 'nl',
  'belgium': 'be',
  'luxembourg': 'lu',
  'france': 'fr',
  'spain': 'es',
  'portugal': 'pt',
  'italy': 'it',
  'germany': 'de',
  'austria': 'at',
  'slovenia': 'si',
  'croatia': 'hr',
  'serbia': 'rs',
  'montenegro': 'me',
  'albania': 'al',
  'north macedonia': 'mk',
  'greece': 'gr',
  'turkey': 'tr',
  'cyprus': 'cy',
  'malta': 'mt',
  'romania': 'ro',
  'moldova': 'md',
  'hungary': 'hu',
  'slovakia': 'sk',
  
  // American countries
  'canada': 'ca',
  'mexico': 'mx',
  'guatemala': 'gt',
  'belize': 'bz',
  'honduras': 'hn',
  'el salvador': 'sv',
  'nicaragua': 'ni',
  'costa rica': 'cr',
  'panama': 'pa',
  'cuba': 'cu',
  'jamaica': 'jm',
  'haiti': 'ht',
  'dominican republic': 'do',
  'puerto rico': 'pr',
  'trinidad and tobago': 'tt',
  'barbados': 'bb',
  'brazil': 'br',
  'argentina': 'ar',
  'uruguay': 'uy',
  'paraguay': 'py',
  'chile': 'cl',
  'bolivia': 'bo',
  'peru': 'pe',
  'ecuador': 'ec',
  'colombia': 'co',
  'venezuela': 've',
  'guyana': 'gy',
  'suriname': 'sr',
  
  // Middle Eastern countries
  'saudi arabia': 'sa',
  'united arab emirates': 'ae',
  'qatar': 'qa',
  'bahrain': 'bh',
  'kuwait': 'kw',
  'oman': 'om',
  'yemen': 'ye',
  'iraq': 'iq',
  'iran': 'ir',
  'israel': 'il',
  'palestine': 'ps',
  'lebanon': 'lb',
  'syria': 'sy',
  'jordan': 'jo',
  
  // African countries
  'south africa': 'za',
  'egypt': 'eg',
  'morocco': 'ma',
  'algeria': 'dz',
  'tunisia': 'tn',
  'libya': 'ly',
  'sudan': 'sd',
  'ethiopia': 'et',
  'kenya': 'ke',
  'uganda': 'ug',
  'tanzania': 'tz',
  'rwanda': 'rw',
  'burundi': 'bi',
  'democratic republic of the congo': 'cd',
  'republic of the congo': 'cg',
  'cameroon': 'cm',
  'nigeria': 'ng',
  'ghana': 'gh',
  'ivory coast': 'ci',
  'senegal': 'sn',
  'mali': 'ml',
  'burkina faso': 'bf',
  'niger': 'ne',
  'chad': 'td',
  'central african republic': 'cf',
  'gabon': 'ga',
  'equatorial guinea': 'gq',
  'sao tome and principe': 'st',
  'cape verde': 'cv',
  'guinea-bissau': 'gw',
  'guinea': 'gn',
  'sierra leone': 'sl',
  'liberia': 'lr',
  'madagascar': 'mg',
  'mauritius': 'mu',
  'seychelles': 'sc',
  'comoros': 'km',
  'djibouti': 'dj',
  'eritrea': 'er',
  'somalia': 'so',
  'mozambique': 'mz',
  'malawi': 'mw',
  'zambia': 'zm',
  'zimbabwe': 'zw',
  'botswana': 'bw',
  'namibia': 'na',
  'lesotho': 'ls',
  'eswatini': 'sz',
  'swaziland': 'sz', // Alternative name
  
  // Oceania
  'australia': 'au',
  'new zealand': 'nz',
  'fiji': 'fj',
  'papua new guinea': 'pg',
  'solomon islands': 'sb',
  'vanuatu': 'vu',
  'samoa': 'ws',
  'tonga': 'to',
  'palau': 'pw',
  'micronesia': 'fm',
  'marshall islands': 'mh',
  'kiribati': 'ki',
  'nauru': 'nr',
  'tuvalu': 'tv',
  
  // Special cases
  'world': null, // No flag available for world competitions
  'international': null,
  'global': null,
};

/**
 * Get the correct country code for a given country name
 */
export function getCountryCode(countryName: string): string | null {
  const normalizedName = countryName.toLowerCase().trim();
  const mappedCode = COUNTRY_CODE_MAPPINGS[normalizedName];
  
  if (mappedCode !== undefined) {
    return mappedCode;
  }
  
  // Fallback: use first 2 letters of country name (remove spaces and special chars)
  const fallbackCode = normalizedName.replace(/[^a-z]/g, '').substring(0, 2);
  return fallbackCode.length >= 2 ? fallbackCode : null;
}

/**
 * Get country flag URL from API-Sports media CDN
 */
export function getCountryFlagUrl(countryName: string): string | null {
  const countryCode = getCountryCode(countryName);
  
  if (!countryCode) {
    return null;
  }
  
  return `https://media.api-sports.io/flags/${countryCode}.svg`;
}

/**
 * Get alternative flag URL from flagcdn.com
 */
export function getAlternativeFlagUrl(countryCode: string): string {
  return `https://flagcdn.com/w20/${countryCode}.png`;
}

/**
 * Handle flag image error with fallback mechanism
 */
export function handleFlagError(
  event: React.SyntheticEvent<HTMLImageElement>,
  fallbackSrc?: string
): void {
  const img = event.currentTarget;
  const currentSrc = img.src;
  
  // If it's the original API-Sports flag URL that failed, try flagcdn.com as backup
  if (currentSrc.includes('media.api-sports.io/flags/')) {
    const countryCode = currentSrc.split('/flags/')[1]?.replace('.svg', '');
    if (countryCode) {
      img.src = getAlternativeFlagUrl(countryCode);
      return;
    }
  }
  
  // If flagcdn also failed or no country code found, use fallback
  if (fallbackSrc) {
    img.src = fallbackSrc;
  } else {
    // Hide the image element if no fallback is provided
    img.style.display = 'none';
    
    // Try to show a globe icon in the parent container
    const parent = img.parentElement;
    if (parent && !parent.querySelector('.flag-fallback-icon')) {
      const icon = document.createElement('div');
      icon.className = 'flag-fallback-icon w-[18px] h-[18px] flex items-center justify-center text-muted-foreground';
      icon.innerHTML = '🌍';
      parent.appendChild(icon);
    }
  }
}
