import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Consistent form colors throughout the application
export const getFormColors = (result: string) => {
  switch (result.toUpperCase()) {
    case 'W':
      return {
        bg: 'bg-[#45C325]',
        text: 'text-white',
        hex: '#45C325'
      };
    case 'D':
      return {
        bg: 'bg-[#5A5A5A]',
        text: 'text-white',
        hex: '#5A5A5A'
      };
    case 'L':
      return {
        bg: 'bg-[#DD0000]',
        text: 'text-white',
        hex: '#DD0000'
      };
    default:
      return {
        bg: 'bg-gray-400',
        text: 'text-white',
        hex: '#9CA3AF'
      };
  }
};
