import axios from 'axios';
import { StandingEntry } from '@/types/fixture';

// Backend API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
const API_KEY = process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756';

// Create axios instance with default config
export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': API_KEY,
  },
});

// API response types
export interface League {
  _id: number;
  id?: number; // Optional for backward compatibility
  league: {
    id: number;
    name: string;
    type: string;
    logo: string;
  };
  country: {
    name: string;
    code: string | null;
    flag: string | null;
  };
  seasons: Array<{
    year: number;
    start: string;
    end: string;
    current: boolean;
  }>;
}

export interface Team {
  id: number;
  name: string;
  logo: string;
  country: string;
}

export interface Fixture {
  _id: number;
  id?: number; // Optional for backward compatibility
  fixture: {
    id: number;
    referee: string | null;
    timezone: string;
    date: string;
    timestamp: number;
    periods: {
      first: number | null;
      second: number | null;
    };
    venue: {
      id: number | null;
      name: string | null;
      city: string | null;
    };
    status: {
      long: string;
      short: string;
      elapsed: number | null;
    };
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo: string;
    flag: string;
    season: number;
    round: string;
  };
  teams: {
    home: Team;
    away: Team;
  };
  goals: {
    home: number | null;
    away: number | null;
  };
  score: {
    halftime: {
      home: number | null;
      away: number | null;
    };
    fulltime: {
      home: number | null;
      away: number | null;
    };
    extratime: {
      home: number | null;
      away: number | null;
    };
    penalty: {
      home: number | null;
      away: number | null;
    };
  };
  // Optional detailed data that comes from WebSocket
  events?: Array<{
    time: {
      elapsed: number;
      extra: number | null;
    };
    team: { id: number; name: string; logo: string | null };
    player: { id: number | null; name: string | null };
    assist: { id: number | null; name: string | null };
    type: string; // "Goal", "Card", "Subst", "Var"
    detail: string; // e.g., "Normal Goal", "Yellow Card", "Substitution 1"
    comments: string | null;
  }>;
  statistics?: Array<{
    team: { id: number; name: string; logo: string | null };
    statistics: Array<{
      type: string; // e.g., "Shots on Goal", "Ball Possession"
      value: number | string | null; // Value can be number or string (like "55%")
    }>;
  }>;
  lineups?: Array<{
    team: {
      id: number;
      name: string;
      logo: string | null;
      colors: {
        player?: { primary: string; number: string; border: string };
        goalkeeper?: { primary: string; number: string; border: string };
      } | null;
    };
    coach: { id: number | null; name: string | null; photo: string | null };
    formation: string | null;
    startXI: Array<{
      player: { id: number; name: string; number: number | null; pos: string | null; grid: string | null };
    }>;
    substitutes: Array<{
      player: { id: number; name: string; number: number | null; pos: string | null; grid: string | null };
    }>;
  }>;
  players?: Array<{
    team: { id: number; name: string; logo: string | null };
    players: Array<{
      player: { id: number; name: string; photo: string | null };
      statistics: Array<{
        games: {
          minutes: number | null;
          number: number | null;
          position: string | null;
          rating: string | null;
          captain: boolean | null;
          substitute: boolean | null;
        };
        offsides: number | null;
        shots: {
          total: number | null;
          on: number | null;
        };
        goals: {
          total: number | null;
          conceded: number | null;
          assists: number | null;
          saves: number | null;
        };
        passes: {
          total: number | null;
          key: number | null;
          accuracy: string | null;
        };
        tackles: {
          total: number | null;
          blocks: number | null;
          interceptions: number | null;
        };
        duels: {
          total: number | null;
          won: number | null;
        };
        dribbles: {
          attempts: number | null;
          success: number | null;
          past: number | null;
        };
        fouls: {
          drawn: number | null;
          committed: number | null;
        };
        cards: {
          yellow: number | null;
          red: number | null;
        };
        penalty: {
          won: number | null;
          committed: number | null;
          scored: number | null;
          missed: number | null;
          saved: number | null;
        };
      }>;
    }>;
  }>;
}

export interface Prediction {
  fixture: {
    id: number;
  };
  league: {
    id: number;
    name: string;
  };
  teams: {
    home: Team;
    away: Team;
  };
  predictions: {
    winner: {
      id: number | null;
      name: string;
      comment: string;
    };
    win_or_draw: boolean;
    under_over: string | null;
    goals: {
      home: string;
      away: string;
    };
    advice: string;
    percent: {
      home: string;
      draw: string;
      away: string;
    };
  };
}

// API functions
export const apiService = {
  // Get leagues
  async getLeagues(): Promise<League[]> {
    try {
      const response = await api.get('/api/leagues');
      return response.data;
    } catch (error) {
      console.error('Error fetching leagues:', error);
      throw error;
    }
  },

  // Get fixtures by date
  async getFixtures(date?: string): Promise<Fixture[]> {
    try {
      const params = date ? { date } : {};
      const response = await api.get('/api/fixtures', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching fixtures:', error);
      throw error;
    }
  },

  // Get fixtures by league
  async getFixturesByLeague(leagueId: number, season?: number): Promise<Fixture[]> {
    try {
      const params = season ? { league: leagueId, season } : { league: leagueId };
      const response = await api.get('/api/fixtures', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching fixtures by league:', error);
      throw error;
    }
  },

  // Get live fixtures
  async getLiveFixtures(): Promise<Fixture[]> {
    try {
      // Add timestamp to prevent caching for live data
      const response = await api.get('/api/fixtures/live', {
        params: { _t: Date.now() }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching live fixtures:', error);
      throw error;
    }
  },

  // Get predictions
  async getPredictions(fixtureId: number): Promise<Prediction> {
    try {
      const response = await api.get(`/api/predictions`, {
        params: { fixture: fixtureId }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching predictions:', error);
      throw error;
    }
  },

  // Get single fixture by ID
  async getFixtureById(fixtureId: number): Promise<Fixture> {
    try {
      const response = await api.get('/api/fixtures', {
        params: { id: fixtureId }
      });
      const fixtures = response.data;
      return Array.isArray(fixtures) ? fixtures[0] : fixtures;
    } catch (error) {
      console.error('Error fetching fixture by ID:', error);
      throw error;
    }
  },

  // Search fixtures
  async searchFixtures(query: string): Promise<Fixture[]> {
    try {
      const response = await api.get('/api/fixtures/search', {
        params: { q: query }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching fixtures:', error);
      throw error;
    }
  },

  // Get team information
  async getTeam(teamId: number): Promise<Team> {
    try {
      const response = await api.get(`/api/teams/${teamId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching team:', error);
      throw error;
    }
  },

  // Get league standings
  async getStandings(leagueId: number, season: number): Promise<StandingEntry[][]> {
    try {
      const params = { leagueId, season };
      const response = await api.get('/api/standings', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching standings:', error);
      throw error;
    }
  }
};

// Error handling utility
export const handleApiError = (error: unknown) => {
  // Type guard for axios errors
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as { response: { data: { message?: string } } };
    console.error('API Error:', axiosError.response.data);
    return axiosError.response.data.message || 'An error occurred';
  } else if (error && typeof error === 'object' && 'request' in error) {
    // Request was made but no response received
    console.error('Network Error:', (error as { request: unknown }).request);
    return 'Network error. Please check your connection.';
  } else {
    // Something else happened
    const message = error && typeof error === 'object' && 'message' in error
      ? (error as { message: string }).message
      : 'An unexpected error occurred';
    console.error('Error:', message);
    return message;
  }
};
