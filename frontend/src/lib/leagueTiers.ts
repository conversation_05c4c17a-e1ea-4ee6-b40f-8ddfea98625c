/**
 * Configuration file for league tiers
 * This file contains the mapping of league IDs to their popularity tiers
 * Tier 1 is the most popular, Tier 6 is the least popular
 */

import { Fixture } from './api';

// League tier mapping
export const leagueTierMap: Record<number, number> = {
  // Tier 1: Pinnacle Global & Continental Competitions
  1: 1, // World Cup (FIFA)
  8: 1, // World Cup (FIFA)
  4: 1, // European Championship (UEFA Euro) / European Championship Qualification
  5: 1, // UEFA Nations League
  2: 1, // Champions League (UEFA)
  3: 1, // Europa League (UEFA)

  // Tier 2: The "Big 5" European Leagues
  39: 2, // Premier League (England)
  140: 2, // La Liga (Spain)
  78: 2, // Bundesliga (Germany)
  135: 2, // Serie A (Italy)
  61: 2, // Ligue 1 (France)
  9: 2, // Copa America (CONMEBOL)
  13: 2, // Copa Libertadores (CONMEBOL)
  6: 2, // Africa Cup of Nations (CAF)
  7: 2, // AFC Asian Cup (Asia)

  // Tier 3: Major Domestic Cups, Other Strong Continental & Key Leagues
  45: 3, // FA Cup (England)
  143: 3, // Copa Del Rey (Spain)
  81: 3, // DFB Pokal (Germany)
  137: 3, // Coppa Italia (Italy)
  848: 3, // Europa Conference League (UEFA)
  66: 3, // Coupe de France (France)
  48: 3, // League Cup (Carabao Cup) (England)
  71: 3, // Serie A (Brazil)
  128: 3, // Liga Profesional de Fútbol (Argentina)
  253: 3, // Major League Soccer (USA)
  307: 3, // Pro League (Saudi Arabia)
  88: 3, // Eredivisie (Netherlands - includes Eredivisie Playoffs)
  94: 3, // Liga Portugal (Primeira Liga) (Portugal)
  203: 3, // Super Lig (Süper Lig) (Turkey)
  15: 3, // FIFA Club World Cup
  32: 3, // WC Qualification Europe (UEFA)
  34: 3, // WC Qualification South America (CONMEBOL)
  30: 3, // WC Qualification Asia (AFC)
  29: 3, // WC Qualification Africa (CAF)
  31: 3, // WC Qualification Concacaf
  36: 3, // Africa Cup of Nations Qualifications (CAF)
  38: 3, // UEFA U21 Championship
  850: 3, // UEFA U21 Championship - Qualification

  // Tier 4: Strong Second Tiers, Other Notable Leagues & Cups
  40: 4, // Championship (England - includes Championship Play-Offs)
  79: 4, // 2. Bundesliga (Germany)
  141: 4, // La Liga 2 (Segunda División) (Spain)
  136: 4, // Serie B (Italy)
  62: 4, // Ligue 2 (France)
  667: 4, // Friendlies Clubs (World)
  10: 4, // Friendlies International (World)
  188: 4, // A-League Men (Australia)
  262: 4, // Liga MX (Mexico)
  17: 4, // AFC Champions League Elite (Asia)
  12: 4, // CAF Champions League (Africa)
  856: 4, // CONCACAF Champions Cup (North/Central America & Caribbean)
  11: 4, // Copa Sudamericana (CONMEBOL)
  73: 4, // Copa do Brasil (Brazil)
  130: 4, // Copa Argentina (Argentina)
  144: 4, // Pro League (Jupiler Pro League) (Belgium)
  179: 4, // Premiership Play-Offs (Scotland) - Represents the top flight implicitly
  408: 4, // Premiership (Northern Ireland)
  181: 4, // Scottish Cup (FA Cup) (Scotland)
  90: 4, // KNVB Beker (Netherlands)
  96: 4, // Taça De Portugal (Portugal)
  206: 4, // Turkish Cup (Turkey)
  218: 4, // Admiral Bundesliga (Austria)
  207: 4, // Super League (Switzerland)
  197: 4, // Super League (Super League 1) (Greece)
  106: 4, // Ekstraklasa (Poland)
  119: 4, // Superliga (Denmark)
  235: 4, // Premier League (Russia)
  286: 4, // Super Liga (Serbia)
  210: 4, // 1. HNL (HNL) (Croatia)
  333: 4, // Premier League (Ukraine)
  283: 4, // Liga 1 (Liga I) (Romania)
  271: 4, // OTP Bank Liga (NB I) (Hungary)
  345: 4, // Chance Národní Liga (Czech Liga) (Czech Republic)
  332: 4, // Niké Liga (Super Liga) (Slovakia)
  315: 4, // Premier Liga (Premijer Liga) (Bosnia & Herzegovina)
  239: 4, // Liga BetPlay (Primera A) (Colombia)
  265: 4, // Primera Division (Chile)
  281: 4, // Primera Division (Peru)
  250: 4, // Division 1 (Primera División) - Apertura (Paraguay)
  252: 4, // Division 1 (Primera División) - Clausura (Paraguay)
  268: 4, // Primera Division - Apertura (Uruguay)
  270: 4, // Primera Division - Clausura (Uruguay)
  242: 4, // Liga Pro (Ecuador)
  292: 4, // K League 1 (South Korea)
  98: 4, // J-League (J1 League) (Japan)
  169: 4, // Super League (China)
  305: 4, // Stars League (Qatar)
  301: 4, // Uae League (Pro League) (UAE)
  200: 4, // Botola Pro (Morocco)
  202: 4, // Ligue 1 (Tunisia)
  186: 4, // Ligue 1 (Algeria)
  233: 4, // Premier League (Egypt)
  288: 4, // Premier League (Premier Soccer League) (South Africa)
  525: 4, // UEFA Champions League Women
  254: 4, // NWSL (USA Women)
  44: 4, // FA WSL (England Women)
  480: 4, // Olympic Games (Men)
  524: 4, // Olympic Games (Women)
  22: 4, // CONCACAF Gold Cup
};

/**
 * Priority map for leagues within each tier
 * Lower number means higher priority within the tier
 * This ensures leagues are displayed in a specific order within each tier
 */
export const leaguePriorityMap: Record<number, number> = {
  // Tier 1 leagues in order of priority
  1: 1,   // World Cup (FIFA) 
  8: 2,   // World Cup Women (FIFA)
  4: 3,   // European Championship (UEFA Euro)
  2: 4,   // Champions League (UEFA)
  3: 5,   // Europa League (UEFA)
  
  // Tier 2 leagues in order of priority (Big 5 European Leagues)
  39: 1,  // Premier League (England)
  140: 2, // La Liga (Spain)
  78: 3,  // Bundesliga (Germany)
  135: 4, // Serie A (Italy)
  61: 5,  // Ligue 1 (France)
  9: 6,   // Copa America (CONMEBOL)
  13: 7,  // Copa Libertadores (CONMEBOL)
  6: 8,   // Africa Cup of Nations (CAF)
  7: 9,   // AFC Asian Cup (Asia)
  
  // Tier 3 leagues in order of priority
  45: 1,  // FA Cup (England)
  143: 2, // Copa Del Rey (Spain)
  81: 3,  // DFB Pokal (Germany)
  137: 4, // Coppa Italia (Italy)
  848: 5, // Europa Conference League (UEFA)
  66: 6,  // Coupe de France (France)
  48: 7,  // League Cup (Carabao Cup) (England)
  71: 8,  // Serie A (Brazil)
  128: 9, // Liga Profesional de Fútbol (Argentina)
  253: 10, // Major League Soccer (USA)
  307: 11, // Pro League (Saudi Arabia)
  88: 12,  // Eredivisie (Netherlands - includes Eredivisie Playoffs)
  94: 13,  // Liga Portugal (Primeira Liga) (Portugal)
  203: 14, // Super Lig (Süper Lig) (Turkey)
  15: 15,  // FIFA Club World Cup
  32: 16,  // WC Qualification Europe (UEFA)
  34: 17,  // WC Qualification South America (CONMEBOL)
  30: 18,  // WC Qualification Asia (AFC)
  29: 19,  // WC Qualification Africa (CAF)
  31: 20,  // WC Qualification Concacaf
  36: 21,  // Africa Cup of Nations Qualifications (CAF)
  
  // Tier 4 leagues in order of priority
  40: 1,   // Championship (England - includes Championship Play-Offs)
  79: 2,   // 2. Bundesliga (Germany)
  141: 3,  // La Liga 2 (Segunda División) (Spain)
  136: 4,  // Serie B (Italy)
  62: 5,   // Ligue 2 (France)
  188: 6,  // A-League Men (Australia)
  262: 7,  // Liga MX (Mexico)
  17: 8,   // AFC Champions League Elite (Asia)
  12: 9,   // CAF Champions League (Africa)
  856: 10, // CONCACAF Champions Cup (North/Central America & Caribbean)
  11: 11,  // Copa Sudamericana (CONMEBOL)
  73: 12,  // Copa do Brasil (Brazil)
  130: 13, // Copa Argentina (Argentina)
  144: 14, // Pro League (Jupiler Pro League) (Belgium)
  179: 15, // Premiership Play-Offs (Scotland) - Represents the top flight implicitly
  408: 16, // Premiership (Northern Ireland)
  181: 17, // Scottish Cup (FA Cup) (Scotland)
  90: 18,  // KNVB Beker (Netherlands)
  96: 19,  // Taça De Portugal (Portugal)
  206: 20, // Turkish Cup (Turkey)
  218: 21, // Admiral Bundesliga (Austria)
  207: 22, // Super League (Switzerland)
  197: 23, // Super League (Super League 1) (Greece)
  106: 24, // Ekstraklasa (Poland)
  119: 25, // Superliga (Denmark)
  235: 26, // Premier League (Russia)
  286: 27, // Super Liga (Serbia)
  210: 28, // 1. HNL (HNL) (Croatia)
  333: 29, // Premier League (Ukraine)
  283: 30, // Liga 1 (Liga I) (Romania)
  271: 31, // OTP Bank Liga (NB I) (Hungary)
  345: 32, // Chance Národní Liga (Czech Liga) (Czech Republic)
  332: 33, // Niké Liga (Super Liga) (Slovakia)
  315: 34, // Premier Liga (Premijer Liga) (Bosnia & Herzegovina)
  239: 35, // Liga BetPlay (Primera A) (Colombia)
  265: 36, // Primera Division (Chile)
  281: 37, // Primera Division (Peru)
  250: 38, // Division 1 (Primera División) - Apertura (Paraguay)
  252: 39, // Division 1 (Primera División) - Clausura (Paraguay)
  268: 40, // Primera Division - Apertura (Uruguay)
  270: 41, // Primera Division - Clausura (Uruguay)
  242: 42, // Liga Pro (Ecuador)
  292: 43, // K League 1 (South Korea)
  98: 44,  // J-League (J1 League) (Japan)
  169: 45, // Super League (China)
  305: 46, // Stars League (Qatar)
  301: 47, // Uae League (Pro League) (UAE)
  200: 48, // Botola Pro (Morocco)
  202: 49, // Ligue 1 (Tunisia)
  186: 50, // Ligue 1 (Algeria)
  233: 51, // Premier League (Egypt)
  288: 52, // Premier League (South Africa)
  525: 53, // UEFA Champions League Women
  254: 54, // NWSL (USA Women)
  44: 55,  // FA WSL (England Women)
  480: 56, // Olympic Games (Men)
  524: 57, // Olympic Games (Women)
  22: 58,  // CONCACAF Gold Cup
};

/**
 * Function to get the tier of a league
 * @param leagueId - The league ID to check
 * @returns number - The tier of the league (1-6), or 7 if not found (lowest priority)
 */
export function getLeagueTier(leagueId: number): number {
  return leagueId in leagueTierMap ? leagueTierMap[leagueId] : 7;
}

/**
 * Function to get the priority of a league within its tier
 * @param leagueId - The league ID to check
 * @returns number - The priority within the tier (lower is higher priority), or 999 if not explicitly prioritized
 */
export function getLeaguePriority(leagueId: number): number {
  return leagueId in leaguePriorityMap ? leaguePriorityMap[leagueId] : 999;
}

/**
 * Interface for grouped fixtures by league
 */
export interface LeagueGroup {
  leagueId: number;
  leagueName: string;
  leagueLogo: string;
  leagueCountry: string;
  tier: number;
  priority: number;
  fixtures: Fixture[];
}

/**
 * Function to group fixtures by league and sort by tier/priority
 * @param fixtures - Array of fixtures to group
 * @returns LeagueGroup[] - Grouped and sorted fixtures
 */
export function groupFixturesByLeague(fixtures: Fixture[]): LeagueGroup[] {
  // Group fixtures by league ID
  const leagueGroups = new Map<number, LeagueGroup>();

  fixtures.forEach(fixture => {
    const leagueId = fixture.league.id;
    
    if (!leagueGroups.has(leagueId)) {
      leagueGroups.set(leagueId, {
        leagueId,
        leagueName: fixture.league.name,
        leagueLogo: fixture.league.logo,
        leagueCountry: fixture.league.country,
        tier: getLeagueTier(leagueId),
        priority: getLeaguePriority(leagueId),
        fixtures: []
      });
    }
    
    leagueGroups.get(leagueId)!.fixtures.push(fixture);
  });
  
  // Convert to array and sort by tier, then by priority within tier
  return Array.from(leagueGroups.values()).sort((a, b) => {
    // First sort by tier (lower tier number = higher priority)
    if (a.tier !== b.tier) {
      return a.tier - b.tier;
    }
    // Then sort by priority within tier (lower priority number = higher priority)
    return a.priority - b.priority;
  });
}
