/**
 * Reusable button styling utilities for consistent button appearance across components
 * Used in Standings, HeadToHead, and other components with toggle buttons
 */

export const getButtonStyle = (isActive: boolean, theme?: string) => {
  if (theme === 'light') {
    return isActive ? {
      backgroundColor: '#1A3050',
      borderColor: '#1A3050',
      color: '#FFFFFF',
      borderWidth: '1px',
      borderStyle: 'solid'
    } : {
      backgroundColor: '#FFFFFF',
      borderColor: '#e5e7eb',
      color: '#000000',
      borderWidth: '1px',
      borderStyle: 'solid'
    };
  } else if (theme === 'dark') {
    return isActive ? {
      backgroundColor: '#FFFFFF',
      borderColor: 'transparent',
      color: '#000000',
      borderWidth: '1px',
      borderStyle: 'solid'
    } : {
      backgroundColor: '#333333',
      borderColor: 'transparent',
      color: '#FFFFFF',
      borderWidth: '1px',
      borderStyle: 'solid'
    };
  }
  return {
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: 'transparent'
  }; // Fallback for undefined theme
};

export const handleButtonHover = (
  e: React.MouseEvent<HTMLButtonElement>, 
  isActive: boolean, 
  isEntering: boolean, 
  theme?: string
) => {
  if (theme === 'light' && !isActive) {
    e.currentTarget.style.backgroundColor = isEntering ? '#E1E7EC' : '#FFFFFF';
  } else if (theme === 'dark' && !isActive) {
    e.currentTarget.style.backgroundColor = isEntering ? '#3C3C3C' : '#333333';
  }
};
