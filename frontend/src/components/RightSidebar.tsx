'use client';

import { useState } from 'react';
import Image from 'next/image';
import { TrendingUp, Users, Trophy, Calendar, ArrowRight } from 'lucide-react';

interface RightSidebarProps {
  className?: string;
}

interface PredictionCard {
  id: string;
  title: string;
  description: string;
  confidence: number;
  type: 'prediction' | 'tip';
}

interface TransferNews {
  id: string;
  playerName: string;
  fromTeam: string;
  toTeam: string;
  value: string;
  status: 'rumor' | 'confirmed' | 'completed';
}

interface TopLeague {
  id: number;
  name: string;
  country: string;
  logo: string;
  matchesToday: number;
}

export default function RightSidebar({ className = '' }: RightSidebarProps) {
  const [predictions] = useState<PredictionCard[]>([
    {
      id: '1',
      title: 'KickoffScore Predict',
      description: 'Make your predictions',
      confidence: 85,
      type: 'prediction'
    },
    {
      id: '2', 
      title: 'Top Scorer Tips',
      description: 'Weekly betting insights',
      confidence: 78,
      type: 'tip'
    }
  ]);

  const [transfers] = useState<TransferNews[]>([
    {
      id: '1',
      playerName: '<PERSON>',
      fromTeam: 'Sporting CP',
      toTeam: 'Arsenal',
      value: '€63.5M',
      status: 'rumor'
    },
    {
      id: '2',
      playerName: '<PERSON>cko', 
      fromTeam: 'Feyenoord',
      toTeam: 'Juventus',
      value: '€30M',
      status: 'confirmed'
    },
    {
      id: '3',
      playerName: 'Pervis Estupiñán',
      fromTeam: 'Brighton',
      toTeam: 'Chelsea',
      value: '€17M',
      status: 'completed'
    }
  ]);

  const [topLeagues] = useState<TopLeague[]>([
    {
      id: 39,
      name: 'Premier League',
      country: 'England',
      logo: 'https://media.api-sports.io/football/leagues/39.png',
      matchesToday: 3
    },
    {
      id: 140,
      name: 'La Liga',
      country: 'Spain', 
      logo: 'https://media.api-sports.io/football/leagues/140.png',
      matchesToday: 2
    },
    {
      id: 78,
      name: 'Bundesliga',
      country: 'Germany',
      logo: 'https://media.api-sports.io/football/leagues/78.png',
      matchesToday: 4
    }
  ]);

  const getStatusColor = (status: TransferNews['status']) => {
    switch (status) {
      case 'completed': return 'text-green-500';
      case 'confirmed': return 'text-blue-500';
      case 'rumor': return 'text-yellow-500';
      default: return 'text-muted-foreground';
    }
  };

  const getStatusText = (status: TransferNews['status']) => {
    switch (status) {
      case 'completed': return 'Done';
      case 'confirmed': return 'Confirmed';
      case 'rumor': return 'Rumor';
      default: return status;
    }
  };

  return (
    <div
      className={`pr-4 pl-1 py-4 space-y-4 h-full overflow-y-auto ${className}`}
      style={{ minWidth: '300px' }}
      suppressHydrationWarning
    >
      {/* Predictions Section */}
      <div className="bg-card rounded-lg border container-border overflow-hidden">
        <div className="p-4">
          <div className="flex items-center space-x-2 mb-4">
            <Trophy className="w-4 h-4 text-primary" />
            <h2 className="text-lg font-semibold text-foreground">Predictions</h2>
          </div>

          <div className="space-y-2">
            {predictions.map((prediction) => (
              <div
                key={prediction.id}
                className="bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-lg p-3 border border-border/50 hover:border-primary/30 transition-colors cursor-pointer"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="font-medium text-sm">{prediction.title}</div>
                  <ArrowRight className="w-3 h-3 text-muted-foreground" />
                </div>
                <p className="text-xs text-muted-foreground mb-2">{prediction.description}</p>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-muted rounded-full h-1.5">
                    <div
                      className="bg-primary h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${prediction.confidence}%` }}
                    />
                  </div>
                  <span className="text-xs font-medium">{prediction.confidence}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Transfers Section */}
      <div className="bg-card rounded-lg border container-border overflow-hidden">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-primary" />
              <h2 className="text-lg font-semibold text-foreground">Top Transfers</h2>
            </div>
            <span className="text-xs text-muted-foreground">By date</span>
          </div>

          <div className="space-y-2">
            {transfers.map((transfer) => (
              <div
                key={transfer.id}
                className="bg-muted/30 rounded-lg p-3 border border-border/50 hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="font-medium text-sm">{transfer.playerName}</div>
                  <span className="text-sm font-bold">{transfer.value}</span>
                </div>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <span>{transfer.fromTeam}</span>
                  <ArrowRight className="w-3 h-3" />
                  <span>{transfer.toTeam}</span>
                </div>
                <div className="mt-2">
                  <span className={`text-xs font-medium ${getStatusColor(transfer.status)}`}>
                    {getStatusText(transfer.status)}
                  </span>
                </div>
              </div>
            ))}
          </div>

          <button className="w-full text-center text-xs text-primary hover:text-primary/80 transition-colors py-2 mt-3">
            Transfer Centre
          </button>
        </div>
      </div>

      {/* Top Leagues Today */}
      <div className="bg-card rounded-lg border container-border overflow-hidden">
        <div className="p-4">
          <div className="flex items-center space-x-2 mb-4">
            <TrendingUp className="w-4 h-4 text-primary" />
            <h2 className="text-lg font-semibold text-foreground">Top Leagues Today</h2>
          </div>

          <div className="space-y-1">
            {topLeagues.map((league) => (
              <div
                key={league.id}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted transition-colors cursor-pointer"
              >
                <div className="w-6 h-6 flex-shrink-0">
                  <Image
                    src={league.logo}
                    alt={league.name}
                    width={24}
                    height={24}
                    className="w-6 h-6 object-contain"
                    loading="lazy"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{league.name}</p>
                  <p className="text-xs text-muted-foreground">{league.country}</p>
                </div>
                <div className="text-xs text-muted-foreground">
                  {league.matchesToday} matches
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Build Your Own XI Section */}
      <div className="bg-card rounded-lg border container-border overflow-hidden">
        <div className="p-4">
          <div className="bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-lg p-4 border border-border/50">
            <div className="flex items-center space-x-2 mb-3">
              <Calendar className="w-4 h-4 text-primary" />
              <h2 className="font-semibold text-sm">Build your own XI</h2>
            </div>
            <p className="text-xs text-muted-foreground mb-3">
              Try your hand at team selection
            </p>
            <button className="w-full bg-primary text-primary-foreground text-xs py-2 px-3 rounded-md hover:bg-primary/90 transition-colors">
              Start Building
            </button>
          </div>
        </div>
      </div>

    </div>
  );
}
