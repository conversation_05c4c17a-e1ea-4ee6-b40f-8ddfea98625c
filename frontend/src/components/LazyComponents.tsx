import dynamic from 'next/dynamic';

// Loading component for better UX
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

// Lazy load heavy components with proper loading states
export const LazyCalendar = dynamic(
  () => import('@/components/ui/calendar').then(mod => ({ default: mod.Calendar })),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

export const LazyFramerMotion = dynamic(
  () => import('framer-motion').then(mod => ({ default: mod.motion.div })),
  {
    loading: () => <div />,
    ssr: false,
  }
);

// Lazy load fixture-related components (heavy with data)
export const LazyFixtureContent = dynamic(
  () => import('@/components/fixture/FixtureContent'),
  {
    loading: () => (
      <div className="animate-pulse space-y-4 p-6">
        <div className="h-8 bg-muted rounded w-3/4"></div>
        <div className="h-4 bg-muted rounded w-1/2"></div>
        <div className="space-y-2">
          <div className="h-4 bg-muted rounded"></div>
          <div className="h-4 bg-muted rounded w-5/6"></div>
        </div>
      </div>
    ),
    ssr: false,
  }
);

export const LazyMatchStatistics = dynamic(
  () => import('@/components/fixture/MatchStatistics'),
  {
    loading: () => (
      <div className="animate-pulse space-y-4 p-6">
        <div className="h-6 bg-muted rounded w-1/3"></div>
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="flex justify-between items-center">
            <div className="h-4 bg-muted rounded w-1/4"></div>
            <div className="h-2 bg-muted rounded flex-1 mx-4"></div>
            <div className="h-4 bg-muted rounded w-1/4"></div>
          </div>
        ))}
      </div>
    ),
    ssr: false,
  }
);

export const LazyMatchLineups = dynamic(
  () => import('@/components/fixture/MatchLineups'),
  {
    loading: () => (
      <div className="animate-pulse p-6">
        <div className="h-6 bg-muted rounded w-1/3 mb-4"></div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            {Array.from({ length: 11 }).map((_, i) => (
              <div key={i} className="h-4 bg-muted rounded"></div>
            ))}
          </div>
          <div className="space-y-2">
            {Array.from({ length: 11 }).map((_, i) => (
              <div key={i} className="h-4 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    ),
    ssr: false,
  }
);

export const LazyFootballPitch = dynamic(
  () => import('@/components/fixture/FootballPitch'),
  {
    loading: () => (
      <div className="animate-pulse p-6">
        <div className="aspect-[4/3] bg-muted rounded-lg"></div>
      </div>
    ),
    ssr: false,
  }
);

// Lazy load chart components if you add them later
// export const LazyChart = dynamic(
//   () => import('recharts').then(mod => ({ default: mod.LineChart })),
//   {
//     loading: () => (
//       <div className="animate-pulse">
//         <div className="h-64 bg-muted rounded"></div>
//       </div>
//     ),
//     ssr: false,
//   }
// );

// Mobile-specific components
export const LazyMobileBottomNav = dynamic(
  () => import('@/components/MobileBottomNav'),
  {
    loading: () => null,
    ssr: false,
  }
);

export const LazyMobileLeaguesView = dynamic(
  () => import('@/components/MobileLeaguesView'),
  {
    loading: () => (
      <div className="animate-pulse p-4 space-y-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-3">
            <div className="w-6 h-6 bg-muted rounded-full"></div>
            <div className="h-4 bg-muted rounded flex-1"></div>
          </div>
        ))}
      </div>
    ),
    ssr: false,
  }
);

export const LazyMobileNewsView = dynamic(
  () => import('@/components/MobileNewsView'),
  {
    loading: () => (
      <div className="animate-pulse p-4 space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
          </div>
        ))}
      </div>
    ),
    ssr: false,
  }
);
