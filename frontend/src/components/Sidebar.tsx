'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { ChevronDown, ChevronRight, Filter } from 'lucide-react';
import { apiService, League, handleApiError } from '@/lib/api';
import { getCountryFlagUrl, handleFlagError } from '@/utils/countryFlags';

interface SidebarProps {
  onLeagueSelect?: (leagueId: number) => void;
  selectedLeagueId?: number | null;
}

interface CountryGroup {
  name: string;
  flag: string | null;
  leagues: League[];
}

export default function Sidebar({}: SidebarProps) {
  const [allLeaguesOpen, setAllLeaguesOpen] = useState(false);
  const [topLeagues, setTopLeagues] = useState<League[]>([]);
  const [allLeagues, setAllLeagues] = useState<League[]>([]);
  const [isLoadingTopLeagues, setIsLoadingTopLeagues] = useState(true);
  const [isLoadingAllLeagues, setIsLoadingAllLeagues] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedCountries, setExpandedCountries] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');

  // Function to format league names by removing UEFA prefix
  const formatLeagueName = (name: string): string => {
    if (name === 'UEFA Champions League') return 'Champions League';
    if (name === 'UEFA Europa League') return 'Europa League';
    if (name === 'UEFA Europa Conference League') return 'Conference League';
    return name;
  };

  useEffect(() => {
    const fetchLeagues = async () => {
      setError(null);
      setIsLoadingTopLeagues(true);
      try {
        const leaguesData = await apiService.getLeagues();
        const topLeagueIds = [2, 3, 39, 140, 78, 135, 61, 848, 40, 45];
        const topLeaguesFiltered = leaguesData.filter(l => topLeagueIds.includes(l._id));
        // Sort leagues according to the specified order
        const leagueOrder = [2, 3, 39, 140, 78, 135, 61, 848, 40, 45];
        const sortedTopLeagues = topLeaguesFiltered.sort((a, b) => {
          return leagueOrder.indexOf(a._id) - leagueOrder.indexOf(b._id);
        });
        setTopLeagues(sortedTopLeagues);
        setAllLeagues(leaguesData.filter(l => !topLeagueIds.includes(l._id)));
        setIsLoadingTopLeagues(false);
      } catch (err) {
        setError(handleApiError(err));
        setIsLoadingTopLeagues(false);
        console.error('Failed to fetch leagues:', err);
      }
    };
    fetchLeagues();
  }, []);

  const groupLeaguesByCountry = (leaguesList: League[]): CountryGroup[] => {
    const map = new Map<string, CountryGroup>();
    leaguesList.forEach(league => {
      const name = league.country.name;
      if (!map.has(name)) {
        map.set(name, {
          name,
          flag: league.country.flag,
          leagues: [],
        });
      }
      map.get(name)!.leagues.push(league);
    });
    return Array.from(map.values())
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(group => ({
        ...group,
        leagues: group.leagues.sort((a, b) => a.league.name.localeCompare(b.league.name)),
      }));
  };

  const filteredAllLeagues = allLeagues.filter(l =>
    l.league.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    l.country.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const countryGroups = groupLeaguesByCountry(filteredAllLeagues);

  const toggleCountry = (name: string) => {
    const updated = new Set(expandedCountries);
    if (updated.has(name)) {
      updated.delete(name);
    } else {
      updated.add(name);
    }
    setExpandedCountries(updated);
  };

const LeagueItemSkeleton = () => (
  <div className="flex items-center space-x-3 p-2 w-full rounded-lg animate-pulse">
    <div className="w-[18px] h-[18px] bg-muted rounded flex-shrink-0" />
    <div className="flex-1 min-w-0">
      <div className="h-4 bg-muted rounded w-3/4" />
    </div>
  </div>
);



  const CountryItemSkeleton = () => (
    <div className="rounded-lg overflow-hidden w-full">
      <div className="w-full flex items-center justify-between p-3 hover:bg-muted transition-colors">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div className="w-6 h-4 bg-muted rounded-sm animate-pulse" />
          <div className="h-4 bg-muted rounded animate-pulse flex-1 max-w-32" />
        </div>
        <div className="w-4 h-4 bg-muted rounded animate-pulse ml-3" />
      </div>
    </div>
  );

  const LeagueItem = ({ league }: { league: League }) => (
    <div className="flex items-center space-x-3 p-2 w-full hover:bg-muted rounded-lg transition-colors">
      <div className="w-[18px] h-[18px] bg-white rounded flex items-center justify-center overflow-hidden flex-shrink-0">
        <Image
          src={league.league.logo}
          alt={formatLeagueName(league.league.name)}
          width={18}
          height={18}
          className="w-[18px] h-[18px] object-contain"
          loading="lazy"
          onError={(e) => {
            // Replace with league initial instead of hiding to maintain layout
            const parent = e.currentTarget.parentElement;
            if (parent) {
              const formattedName = formatLeagueName(league.league.name);
              parent.innerHTML =
                '<span class="text-xs font-bold text-muted-foreground">' +
                formattedName.charAt(0) +
                '</span>';
            }
          }}
        />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate text-foreground">
          {formatLeagueName(league.league.name)}
        </p>
      </div>
    </div>
  );

  return (
    <div
      className="pl-4 pr-1 py-4 space-y-4 h-full overflow-y-auto"
      style={{ minWidth: '280px' }}
      suppressHydrationWarning
    >
      {/* Top Leagues */}
      <div className="bg-card rounded-lg border container-border overflow-hidden">
        <div className="p-4 min-h-[468px] flex flex-col">
          <h2 className="font-semibold text-foreground mb-4" style={{ fontSize: '16px' }}>
            Top Leagues
          </h2>

          <div className="space-y-1 flex-1">
            {isLoadingTopLeagues ? (
              // Show exactly 10 skeletons to match expected content height
              Array.from({ length: 10 }).map((_, i) => <LeagueItemSkeleton key={i} />)
            ) : topLeagues.length > 0 ? (
              topLeagues.map(league => <LeagueItem key={league._id} league={league} />)
            ) : error ? (
              <div className="text-destructive text-sm py-2">Failed to load leagues</div>
            ) : (
              <div className="text-muted-foreground text-sm py-2">No leagues available</div>
            )}
          </div>
        </div>
      </div>


      {/* All Leagues */}
      <div className="bg-card rounded-lg border container-border overflow-hidden">
        <button
          onClick={() => {
            if (!allLeaguesOpen && allLeagues.length === 0 && !isLoadingAllLeagues) {
              setIsLoadingAllLeagues(true);
              setTimeout(() => setIsLoadingAllLeagues(false), 800);
            }
            setAllLeaguesOpen(!allLeaguesOpen);
          }}
          className="w-full flex items-center justify-between p-4 hover:bg-muted transition-colors"
        >
          <h2 className="font-semibold text-foreground" style={{ fontSize: '16px' }}>All leagues</h2>
          {allLeaguesOpen
            ? <ChevronDown className="w-5 h-5 text-muted-foreground" />
            : <ChevronRight className="w-5 h-5 text-muted-foreground" />}
        </button>

        {allLeaguesOpen && (
          <div className="px-4 pb-4">
            <div className="mb-4">
              <div className="flex items-center space-x-2 p-3 border border-border rounded-lg bg-muted">
                <Filter className="w-4 h-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Filter"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 bg-transparent text-sm text-foreground placeholder-muted-foreground outline-none"
                />
              </div>
            </div>

            <div className="space-y-2">
              {isLoadingAllLeagues
                ? Array.from({ length: 3 }).map((_, i) => <CountryItemSkeleton key={i} />)
                : countryGroups.length > 0
                ? countryGroups.map(country => (
                    <div key={country.name} className="rounded-lg overflow-hidden w-full">
                      <button
                        onClick={() => toggleCountry(country.name)}
                        className="w-full flex items-center justify-between p-3 hover:bg-muted transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-[18px] h-[18px] flex items-center justify-center flex-shrink-0">
                            {(() => {
                              const flagUrl = getCountryFlagUrl(country.name);
                              return flagUrl ? (
                                <Image
                                  src={flagUrl}
                                  alt={country.name}
                                  width={18}
                                  height={18}
                                  className="w-[18px] h-[18px] object-cover rounded-full"
                                  loading="lazy"
                                  onError={(e) => {
                                    handleFlagError(e);
                                  }}
                                />
                              ) : (
                                <div className="w-[18px] h-[18px] bg-muted rounded-full flex items-center justify-center">
                                  <span className="text-xs font-bold text-muted-foreground">{country.name.charAt(0)}</span>
                                </div>
                              );
                            })()}
                          </div>
                          <span className="text-sm font-medium text-foreground">{country.name}</span>
                        </div>
                        <ChevronDown
                          className={`w-4 h-4 text-muted-foreground transition-transform ${
                            expandedCountries.has(country.name) ? 'rotate-180' : ''
                          }`}
                        />
                      </button>
                      {expandedCountries.has(country.name) && (
                        <div className="px-3 pb-3 space-y-1">
                          {country.leagues.map(league => (
                            <LeagueItem key={league._id} league={league} />
                          ))}
                        </div>
                      )}
                    </div>
                  ))
                : <div className="text-muted-foreground text-sm py-2">
                    {searchQuery ? `No leagues found matching "${searchQuery}"` : 'No additional leagues available'}
                  </div>}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
