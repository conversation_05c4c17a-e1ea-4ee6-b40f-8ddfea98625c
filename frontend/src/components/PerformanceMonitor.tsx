'use client';

import { useEffect } from 'react';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

export default function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return;

    const metrics: PerformanceMetrics = {};

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0] as PerformanceEntry;
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime;
      }

      // Time to First Byte
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            if (lastEntry) {
              metrics.lcp = lastEntry.startTime;
            }
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
              const fidEntry = entry as PerformanceEntry & { processingStart: number };
              if ('processingStart' in fidEntry) {
                metrics.fid = fidEntry.processingStart - fidEntry.startTime;
              }
            });
          });
          fidObserver.observe({ entryTypes: ['first-input'] });

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            const entries = list.getEntries();
            entries.forEach((entry) => {
              const clsEntry = entry as PerformanceEntry & { hadRecentInput: boolean; value: number };
              if ('hadRecentInput' in clsEntry && 'value' in clsEntry && !clsEntry.hadRecentInput) {
                clsValue += clsEntry.value;
              }
            });
            metrics.cls = clsValue;
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

          // Send metrics after page load
          setTimeout(() => {
            sendMetrics(metrics);
          }, 5000);
        } catch (error) {
          console.warn('Performance monitoring not supported:', error);
        }
      }
    };

    // Send metrics to analytics (replace with your analytics service)
    const sendMetrics = (metrics: PerformanceMetrics) => {
      // Example: Send to Google Analytics 4
      if (typeof window !== 'undefined' && 'gtag' in window) {
        const gtag = (window as { gtag: (event: string, name: string, params: Record<string, unknown>) => void }).gtag;
        Object.entries(metrics).forEach(([key, value]) => {
          if (value !== undefined) {
            gtag('event', 'web_vital', {
              name: key,
              value: Math.round(value),
              event_category: 'performance',
            });
          }
        });
      }

      // Example: Send to custom analytics endpoint
      if (process.env.NODE_ENV === 'production') {
        fetch('/api/analytics/performance', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            url: window.location.href,
            userAgent: navigator.userAgent,
            metrics,
            timestamp: Date.now(),
          }),
        }).catch(() => {
          // Silently fail - don't impact user experience
        });
      }

      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.table(metrics);
      }
    };

    // Measure performance after page load
    if (document.readyState === 'complete') {
      measureWebVitals();
    } else {
      window.addEventListener('load', measureWebVitals);
    }

    // Cleanup
    return () => {
      window.removeEventListener('load', measureWebVitals);
    };
  }, []);

  // This component doesn't render anything
  return null;
}

// Hook for component-level performance monitoring
export function usePerformanceMonitor(componentName: string) {
  useEffect(() => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      if (process.env.NODE_ENV === 'development' && renderTime > 16) {
        console.warn(`${componentName} took ${renderTime.toFixed(2)}ms to render (>16ms)`);
      }
    };
  });
}

// Performance budget checker
export function checkPerformanceBudget() {
  if (typeof window === 'undefined') return;

  setTimeout(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const loadTime = navigation.loadEventEnd - navigation.fetchStart;

    if (loadTime > 3000) {
      console.warn(`Page load time: ${loadTime}ms exceeds 3s budget`);
    }
  }, 1000);
}
