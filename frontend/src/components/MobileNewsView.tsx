'use client';

import Image from 'next/image';
import { Clock, ExternalLink } from 'lucide-react';

interface NewsItem {
  id: string;
  title: string;
  summary: string;
  publishedAt: string;
  source: string;
  imageUrl?: string;
}

// Mock news data - replace with real API call later
const mockNews: NewsItem[] = [
  {
    id: '1',
    title: 'Premier League Transfer Window: Latest Updates',
    summary: 'The latest transfer news and rumors from the Premier League as clubs prepare for the upcoming season.',
    publishedAt: '2025-01-29T10:30:00Z',
    source: 'Football News',
    imageUrl: 'https://media.api-sports.io/football/leagues/39.png'
  },
  {
    id: '2',
    title: 'Champions League Draw Results',
    summary: 'The Champions League knockout stage draw has been completed with some exciting matchups ahead.',
    publishedAt: '2025-01-29T08:15:00Z',
    source: 'UEFA News',
    imageUrl: 'https://media.api-sports.io/football/leagues/2.png'
  },
  {
    id: '3',
    title: 'La Liga Title Race Heats Up',
    summary: 'With several teams in contention, the La Liga title race is becoming increasingly competitive.',
    publishedAt: '2025-01-28T16:45:00Z',
    source: 'Spanish Football',
    imageUrl: 'https://media.api-sports.io/football/leagues/140.png'
  },
  {
    id: '4',
    title: 'World Cup Qualifiers Schedule Released',
    summary: 'FIFA has announced the schedule for the upcoming World Cup qualification matches.',
    publishedAt: '2025-01-28T14:20:00Z',
    source: 'FIFA News'
  },
  {
    id: '5',
    title: 'Bundesliga Winter Break Ends',
    summary: 'German clubs are back in action as the Bundesliga winter break comes to an end.',
    publishedAt: '2025-01-28T12:00:00Z',
    source: 'German Football',
    imageUrl: 'https://media.api-sports.io/football/leagues/78.png'
  }
];

export default function MobileNewsView() {
  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const publishedDate = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - publishedDate.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  return (
    <div className="h-full overflow-y-auto pb-20 bg-background">
      <div className="p-4">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-foreground mb-2">Football News</h1>
          <p className="text-muted-foreground">Stay updated with the latest football news and updates</p>
        </div>

        <div className="space-y-4">
          {mockNews.map((article) => (
            <article
              key={article.id}
              className="bg-card rounded-lg border border-border overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
            >
              <div className="p-4">
                <div className="flex items-start space-x-3">
                  {article.imageUrl && (
                    <div className="w-16 h-16 flex-shrink-0 bg-muted rounded-lg overflow-hidden">
                      <Image
                        src={article.imageUrl}
                        alt=""
                        width={64}
                        height={64}
                        className="w-full h-full object-contain"
                        loading="lazy"
                      />
                    </div>
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <h2 className="text-lg font-semibold text-foreground mb-2 line-clamp-2">
                      {article.title}
                    </h2>
                    
                    <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                      {article.summary}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center space-x-4">
                        <span className="font-medium">{article.source}</span>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{formatTimeAgo(article.publishedAt)}</span>
                        </div>
                      </div>
                      
                      <ExternalLink className="w-3 h-3" />
                    </div>
                  </div>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Load More Button */}
        <div className="mt-8 text-center">
          <button className="px-6 py-2 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors">
            Load More News
          </button>
        </div>

        {/* Coming Soon Notice */}
        <div className="mt-8 p-4 bg-muted rounded-lg text-center">
          <p className="text-muted-foreground text-sm">
            📰 Real-time news integration coming soon! Stay tuned for live football updates.
          </p>
        </div>
      </div>
    </div>
  );
}
