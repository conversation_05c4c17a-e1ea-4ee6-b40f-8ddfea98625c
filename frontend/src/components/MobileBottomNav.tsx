'use client';

import React, { useState, useRef, useEffect, useMemo } from 'react';
import { motion, useScroll, useMotionValueEvent, AnimatePresence } from 'framer-motion';
import { Calendar, Trophy, Newspaper } from 'lucide-react';

type IconComponentType = React.ElementType<{ className?: string }>;

export interface MobileNavItem {
  label: string;
  icon: IconComponentType;
  id: string;
}

export interface MobileBottomNavProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
  accentColor?: string;
}

const navItems: MobileNavItem[] = [
  { label: 'Matches', icon: Calendar, id: 'matches' },
  { label: 'Leagues', icon: Trophy, id: 'leagues' },
  { label: 'News', icon: Newspaper, id: 'news' },
];

const MobileBottomNav: React.FC<MobileBottomNavProps> = ({
  activeTab,
  onTabChange,
  accentColor = '#1A3050'
}) => {
  const textRefs = useRef<(HTMLElement | null)[]>([]);
  const itemRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const [visible, setVisible] = useState(true);

  const { scrollYProgress } = useScroll();
  const activeIndex = navItems.findIndex(item => item.id === activeTab);

  // Auto-hide on scroll logic
  useMotionValueEvent(scrollYProgress, "change", (current) => {
    if (typeof current === "number") {
      const previous = scrollYProgress.getPrevious();
      if (previous !== undefined) {
        const direction = current - previous;

        if (scrollYProgress.get() < 0.02) {
          setVisible(true);
        } else {
          if (direction < -0.001) {
            setVisible(true); // Scrolling up
          } else if (direction > 0.001) {
            setVisible(false); // Scrolling down
          }
        }
      }
    }
  });

  useEffect(() => {
    const setLineWidth = () => {
      const activeItemElement = itemRefs.current[activeIndex];
      const activeTextElement = textRefs.current[activeIndex];

      if (activeItemElement && activeTextElement) {
        const textWidth = activeTextElement.offsetWidth;
        activeItemElement.style.setProperty('--lineWidth', `${textWidth}px`);
      }
    };

    if (activeIndex >= 0) {
      setLineWidth();
    }

    window.addEventListener('resize', setLineWidth);
    return () => {
      window.removeEventListener('resize', setLineWidth);
    };
  }, [activeIndex]);

  const handleItemClick = (tabId: string) => {
    onTabChange(tabId);
  };

  const navStyle = useMemo(() => {
    return { '--accent-color': accentColor } as React.CSSProperties;
  }, [accentColor]);

  return (
    <AnimatePresence>
      <motion.nav
        initial={{
          opacity: 1,
          y: 0,
        }}
        animate={{
          y: visible ? 0 : 100,
          opacity: visible ? 1 : 0,
        }}
        transition={{
          duration: 0.3,
          ease: "easeInOut",
        }}
        className="mobile-bottom-nav-floating"
        role="navigation"
        style={navStyle}
      >
      {navItems.map((item, index) => {
        const isActive = item.id === activeTab;
        const IconComponent = item.icon;

        return (
          <motion.button
            key={item.id}
            className={`mobile-nav-item ${isActive ? 'active' : ''}`}
            onClick={() => handleItemClick(item.id)}
            ref={(el) => { itemRefs.current[index] = el; }}
            style={{ '--lineWidth': '0px' } as React.CSSProperties}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="mobile-nav-icon">
              <IconComponent className="icon" />
            </div>
            <strong
              className={`mobile-nav-text ${isActive ? 'active' : ''}`}
              ref={(el) => { textRefs.current[index] = el; }}
            >
              {item.label}
            </strong>
          </motion.button>
        );
      })}
      </motion.nav>
    </AnimatePresence>
  );
};

export default MobileBottomNav;
