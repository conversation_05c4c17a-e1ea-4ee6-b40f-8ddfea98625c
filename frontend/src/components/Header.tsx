'use client';

import { useState, useRef, useEffect } from 'react';
import { Search, Settings, User, PanelRight } from 'lucide-react';
import { ThemeToggle } from './ThemeToggle';
import Logo from './Logo';

interface HeaderProps {
  onToggleRightSidebar?: () => void;
}

export default function Header({ onToggleRightSidebar }: HeaderProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const settingsRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setIsSettingsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="bg-card border-b border-border px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Left side - Logo and Navigation */}
        <div className="flex items-center space-x-8">
          {/* Logo */}
          <Logo />

          {/* Navigation Menu */}
          <nav className="hidden md:flex items-center space-x-6">
            <a href="#" className="text-muted-foreground hover:text-primary font-medium">
              News
            </a>
            <a href="#" className="text-muted-foreground hover:text-primary font-medium">
              Transfers
            </a>
            <a href="#" className="text-muted-foreground hover:text-primary font-medium">
              About us
            </a>
          </nav>
        </div>

        {/* Right side - Search and Settings */}
        <div className="flex items-center space-x-4">
          {/* Search Bar */}
          <div className="hidden md:flex items-center bg-muted rounded-lg px-3 py-2 w-64">
            <Search className="w-4 h-4 text-muted-foreground mr-2" />
            <input
              type="text"
              placeholder="Search teams, leagues, countries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-transparent outline-none text-sm flex-1 text-foreground placeholder-muted-foreground"
            />
          </div>

          {/* Right Sidebar Toggle (Desktop only) */}
          {onToggleRightSidebar && (
            <button
              onClick={onToggleRightSidebar}
              className="hidden xl:block p-2 text-muted-foreground hover:text-primary hover:bg-muted rounded-lg transition-colors"
              title="Toggle right sidebar"
            >
              <PanelRight className="w-5 h-5" />
            </button>
          )}

          {/* Settings */}
          <div className="relative" ref={settingsRef}>
            <button
              onClick={() => setIsSettingsOpen(!isSettingsOpen)}
              className="p-2 text-muted-foreground hover:text-primary hover:bg-muted rounded-lg transition-colors"
            >
              <Settings className="w-5 h-5" />
            </button>

            {/* Settings Dropdown */}
            {isSettingsOpen && (
              <div className="absolute right-0 mt-2 w-64 bg-card rounded-lg shadow-lg border border-border py-2 z-50">
                {/* Sign In/Sign Up */}
                <div className="px-4 py-2 border-b border-border">
                  <div className="flex items-center space-x-3">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <div className="flex-1">
                      <button className="text-sm text-primary hover:text-primary/80 font-medium">
                        Sign In
                      </button>
                      <span className="text-sm text-muted-foreground mx-2">|</span>
                      <button className="text-sm text-primary hover:text-primary/80 font-medium">
                        Sign Up
                      </button>
                    </div>
                  </div>
                </div>

                {/* Theme Toggle */}
                <div className="px-4 py-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-foreground">Theme</span>
                    <ThemeToggle />
                  </div>
                </div>
              </div>
            )}
          </div>


        </div>
      </div>

      {/* Mobile Search Bar */}
      <div className="md:hidden mt-3">
        <div className="flex items-center bg-muted rounded-lg px-3 py-2">
          <Search className="w-4 h-4 text-muted-foreground mr-2" />
          <input
            type="text"
            placeholder="Search teams, leagues, countries..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-transparent outline-none text-sm flex-1 text-foreground placeholder-muted-foreground"
          />
        </div>
      </div>
    </header>
  );
}
