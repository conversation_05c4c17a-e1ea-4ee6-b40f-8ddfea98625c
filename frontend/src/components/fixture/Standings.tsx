'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { apiService, handleApiError } from '@/lib/api';
import { StandingEntry } from '@/types/fixture';
import { Fixture } from '@/lib/api';
import { useTheme } from 'next-themes';
import { getFormColors } from '@/lib/utils';
import { getButtonStyle, handleButtonHover } from '@/lib/buttonUtils';

interface StandingsProps {
  fixture: Fixture;
}

interface FormIndicatorProps {
  result: string;
}

const FormIndicator = ({ result }: FormIndicatorProps) => {
  const colors = getFormColors(result);
  return (
    <div className={`w-2 h-2 rounded-full ${colors.bg}`} title={result} />
  );
};

export default function Standings({ fixture }: StandingsProps) {
  const { resolvedTheme } = useTheme();
  const [standings, setStandings] = useState<StandingEntry[][]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewType, setViewType] = useState<'all' | 'home' | 'away'>('all');



  useEffect(() => {
    const fetchStandings = async () => {
      if (!fixture.league.id || !fixture.league.season) {
        setError('League or season information not available');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const standingsData = await apiService.getStandings(
          fixture.league.id,
          fixture.league.season
        );
        
        setStandings(standingsData);
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        console.error('Error fetching standings:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchStandings();
  }, [fixture.league.id, fixture.league.season]);

  if (loading) {
    return (
      <div className="bg-card rounded-lg border container-border p-4 md:p-6 mx-2 md:mx-0">
        <div className="space-y-4">
          <div className="h-6 bg-muted animate-pulse rounded w-48"></div>
          <div className="space-y-2">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 py-2">
                <div className="w-6 h-4 bg-muted animate-pulse rounded"></div>
                <div className="w-6 h-6 bg-muted animate-pulse rounded-full"></div>
                <div className="h-4 bg-muted animate-pulse rounded flex-1"></div>
                <div className="w-8 h-4 bg-muted animate-pulse rounded"></div>
                <div className="w-8 h-4 bg-muted animate-pulse rounded"></div>
                <div className="w-8 h-4 bg-muted animate-pulse rounded"></div>
                <div className="w-8 h-4 bg-muted animate-pulse rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-card rounded-lg border border-border p-4 md:p-6 mx-2 md:mx-0">
        <div className="text-center py-8">
          <p className="text-muted-foreground mb-2">Failed to load standings</p>
          <p className="text-sm text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  if (!standings || standings.length === 0) {
    return (
      <div className="bg-card rounded-lg border border-border p-4 md:p-6 mx-2 md:mx-0">
        <div className="text-center py-8">
          <p className="text-muted-foreground">No standings available for this league</p>
        </div>
      </div>
    );
  }

  const homeTeamId = fixture.teams.home.id;
  const awayTeamId = fixture.teams.away.id;

  const isTeamInFixture = (teamId: number) => {
    return teamId === homeTeamId || teamId === awayTeamId;
  };

  const getStandingsData = (team: StandingEntry) => {
    switch (viewType) {
      case 'home':
        return {
          played: team.home.played,
          win: team.home.win,
          draw: team.home.draw,
          lose: team.home.lose,
          goalsFor: team.home.goals.for,
          goalsAgainst: team.home.goals.against,
          points: (team.home.win || 0) * 3 + (team.home.draw || 0),
          goalDifference: (team.home.goals.for || 0) - (team.home.goals.against || 0),
        };
      case 'away':
        return {
          played: team.away.played,
          win: team.away.win,
          draw: team.away.draw,
          lose: team.away.lose,
          goalsFor: team.away.goals.for,
          goalsAgainst: team.away.goals.against,
          points: (team.away.win || 0) * 3 + (team.away.draw || 0),
          goalDifference: (team.away.goals.for || 0) - (team.away.goals.against || 0),
        };
      default:
        return {
          played: team.all.played,
          win: team.all.win,
          draw: team.all.draw,
          lose: team.all.lose,
          goalsFor: team.all.goals.for,
          goalsAgainst: team.all.goals.against,
          points: team.points,
          goalDifference: team.goalsDiff,
        };
    }
  };

  // Sort standings based on view type
  const getSortedStandings = (groups: StandingEntry[][]) => {
    if (viewType === 'all') {
      return groups; // Use original order for overall standings
    }

    return groups.map(group => {
      return [...group].sort((a, b) => {
        const aData = getStandingsData(a);
        const bData = getStandingsData(b);

        // Sort by points first
        if (aData.points !== bData.points) {
          return bData.points - aData.points;
        }

        // Then by goal difference
        if (aData.goalDifference !== bData.goalDifference) {
          return bData.goalDifference - aData.goalDifference;
        }

        // Then by goals scored
        return (bData.goalsFor || 0) - (aData.goalsFor || 0);
      }).map((team, index) => ({
        ...team,
        rank: index + 1 // Update rank based on new position
      }));
    });
  };

  const sortedStandings = getSortedStandings(standings);

  return (
    <div className="bg-card rounded-lg border container-border overflow-hidden mx-2 md:mx-0">
      {sortedStandings.map((group, groupIndex) => (
        <div key={groupIndex} className={groupIndex > 0 ? 'border-t border-border' : ''}>
          {/* Group header if there are multiple groups */}
          {standings.length > 1 && group.length > 0 && group[0].group && (
            <div className="bg-muted px-4 py-2 border-b border-border">
              <h3 className="font-medium text-sm">{group[0].group}</h3>
            </div>
          )}

          {/* View Type Toggle */}
          <div className="bg-muted/30 px-4 py-3 border-b border-border">
            <div className="flex space-x-2">
              <button
                onClick={() => setViewType('all')}
                className="px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer"
                style={getButtonStyle(viewType === 'all', resolvedTheme)}
                onMouseEnter={(e) => handleButtonHover(e, viewType === 'all', true, resolvedTheme)}
                onMouseLeave={(e) => handleButtonHover(e, viewType === 'all', false, resolvedTheme)}
              >
                All
              </button>
              <button
                onClick={() => setViewType('home')}
                className="px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer"
                style={getButtonStyle(viewType === 'home', resolvedTheme)}
                onMouseEnter={(e) => handleButtonHover(e, viewType === 'home', true, resolvedTheme)}
                onMouseLeave={(e) => handleButtonHover(e, viewType === 'home', false, resolvedTheme)}
              >
                Home
              </button>
              <button
                onClick={() => setViewType('away')}
                className="px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer"
                style={getButtonStyle(viewType === 'away', resolvedTheme)}
                onMouseEnter={(e) => handleButtonHover(e, viewType === 'away', true, resolvedTheme)}
                onMouseLeave={(e) => handleButtonHover(e, viewType === 'away', false, resolvedTheme)}
              >
                Away
              </button>
            </div>
          </div>

          {/* Table header */}
          <div className="bg-muted/50 px-4 py-2 border-b border-border">
            <div className="grid grid-cols-12 gap-2 text-xs font-medium text-muted-foreground">
              <div className="col-span-1">#</div>
              <div className="col-span-5 md:col-span-3">Team</div>
              <div className="col-span-2 md:col-span-1 text-center">PL</div>
              <div className="col-span-1 text-center hidden md:block">W</div>
              <div className="col-span-1 text-center hidden md:block">D</div>
              <div className="col-span-1 text-center hidden md:block">L</div>
              <div className="col-span-1 text-center hidden md:block">G</div>
              <div className="col-span-2 md:col-span-1 text-center">+/-</div>
              <div className="col-span-2 md:col-span-1 text-center">PTS</div>
              <div className="col-span-1 text-center hidden md:block">Form</div>
            </div>
          </div>

          {/* Table rows */}
          <div className="divide-y divide-border">
            {group.map((team) => {
              const teamData = getStandingsData(team);
              return (
                <div
                  key={team.team.id}
                  className={`px-4 py-3 hover:bg-muted/30 transition-colors ${
                    isTeamInFixture(team.team.id) ? 'bg-primary/5 border-l-2 border-l-primary' : ''
                  }`}
                >
                  <div className="grid grid-cols-12 gap-2 items-center text-sm">
                    {/* Position */}
                    <div className="col-span-1 font-medium">
                      {team.rank}
                    </div>

                    {/* Team */}
                    <div className="col-span-5 md:col-span-3 flex items-center space-x-2 min-w-0">
                      <div className="w-5 h-5 relative flex-shrink-0">
                        <Image
                          src={team.team.logo || '/placeholder-team.png'}
                          alt={team.team.name}
                          fill
                          className="object-contain"
                          sizes="20px"
                        />
                      </div>
                      <span className="truncate font-medium">{team.team.name}</span>
                    </div>

                    {/* Played */}
                    <div className="col-span-2 md:col-span-1 text-center">
                      {teamData.played}
                    </div>

                    {/* Won - Hidden on mobile */}
                    <div className="col-span-1 text-center hidden md:block">
                      {teamData.win}
                    </div>

                    {/* Drawn - Hidden on mobile */}
                    <div className="col-span-1 text-center hidden md:block">
                      {teamData.draw}
                    </div>

                    {/* Lost - Hidden on mobile */}
                    <div className="col-span-1 text-center hidden md:block">
                      {teamData.lose}
                    </div>

                    {/* Goals - Hidden on mobile */}
                    <div className="col-span-1 text-center hidden md:block">
                      <span>
                        {teamData.goalsFor}:{teamData.goalsAgainst}
                      </span>
                    </div>

                    {/* Goal Difference */}
                    <div className="col-span-2 md:col-span-1 text-center">
                      <span>
                        {teamData.goalDifference > 0 ? '+' : ''}{teamData.goalDifference}
                      </span>
                    </div>

                    {/* Points */}
                    <div className="col-span-2 md:col-span-1 text-center font-semibold">
                      {teamData.points}
                    </div>

                    {/* Form - Hidden on mobile */}
                    <div className="col-span-1 flex justify-center space-x-1 hidden md:flex">
                      {team.form ? (
                        team.form.slice(-5).split('').map((result, index) => (
                          <FormIndicator key={index} result={result} />
                        ))
                      ) : (
                        <span className="text-xs text-muted-foreground">-</span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}

      {/* Legend */}
      <div className="bg-muted/30 px-4 py-2 border-t border-border">
        <div className="flex items-center space-x-4 text-xs text-muted-foreground">
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <span>Win</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
            <span>Draw</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 rounded-full bg-red-500"></div>
            <span>Loss</span>
          </div>
        </div>
      </div>
    </div>
  );
}
