'use client';

import Image from 'next/image';
import { Fixture } from '@/lib/api';
import type { MatchEvents } from '@/types/fixture';

interface MatchEventsProps {
  events?: MatchEvents;
  fixture: Fixture;
}

export default function MatchEvents({ events, fixture }: MatchEventsProps) {
  if (!events || events.length === 0) {
    return (
      <div className="bg-card rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold mb-4">Commentary</h3>
        <p className="text-muted-foreground">No events available for this match.</p>
      </div>
    );
  }

  // Helper function to get event icon
  const getEventIcon = (type: string, detail: string) => {
    switch (type.toLowerCase()) {
      case 'goal':
        return '⚽';
      case 'card':
        return detail.includes('Yellow') ? '🟨' : '🟥';
      case 'subst':
        return '🔄';
      case 'var':
        return '📺';
      default:
        return '•';
    }
  };

  // Helper function to get event color
  const getEventColor = (type: string, detail: string) => {
    switch (type.toLowerCase()) {
      case 'goal':
        return 'text-green-600';
      case 'card':
        return detail.includes('Yellow') ? 'text-yellow-600' : 'text-red-600';
      case 'subst':
        return 'text-blue-600';
      case 'var':
        return 'text-purple-600';
      default:
        return 'text-muted-foreground';
    }
  };

  // Sort events by time (most recent first for live commentary feel)
  const sortedEvents = [...events].sort((a, b) => {
    const timeA = a.time.elapsed + (a.time.extra || 0);
    const timeB = b.time.elapsed + (b.time.extra || 0);
    return timeB - timeA;
  });

  return (
    <div className="bg-card rounded-lg border border-border p-6">
      <h3 className="text-lg font-semibold mb-6">Commentary</h3>
      
      <div className="space-y-4">
        {sortedEvents.map((event, index) => {
          const isHomeTeam = event.team.id === fixture.teams.home.id;
          
          return (
            <div key={index} className="flex items-start space-x-4">
              {/* Time */}
              <div className="flex-shrink-0 w-12 text-right">
                <span className="text-sm font-mono text-muted-foreground">
                  {event.time.elapsed}&apos;
                  {event.time.extra && <span className="text-xs">+{event.time.extra}</span>}
                </span>
              </div>
              
              {/* Event Icon */}
              <div className="flex-shrink-0 w-8 text-center">
                <span className="text-lg">
                  {getEventIcon(event.type, event.detail)}
                </span>
              </div>
              
              {/* Event Details */}
              <div className="flex-1">
                <div className={`font-medium ${getEventColor(event.type, event.detail)}`}>
                  {event.type} - {event.detail}
                </div>
                
                <div className="text-sm text-foreground mt-1">
                  {event.player?.name && (
                    <span className="font-medium">{event.player.name}</span>
                  )}
                  {event.assist?.name && (
                    <span className="text-muted-foreground"> (Assist: {event.assist.name})</span>
                  )}
                </div>
                
                <div className="text-xs text-muted-foreground mt-1">
                  {event.team.name}
                  {event.comments && (
                    <span className="ml-2">• {event.comments}</span>
                  )}
                </div>
              </div>
              
              {/* Team Logo */}
              <div className="flex-shrink-0 w-8">
                <Image
                  src={event.team.logo || (isHomeTeam ? fixture.teams.home.logo : fixture.teams.away.logo)}
                  alt={event.team.name}
                  width={24}
                  height={24}
                  className="w-6 h-6 rounded object-contain"
                />
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Match Status */}
      <div className="mt-6 pt-4 border-t border-border text-center">
        <span className="text-sm text-muted-foreground">
          {fixture.fixture.status.long}
          {fixture.fixture.status.elapsed && ` - ${fixture.fixture.status.elapsed}'`}
        </span>
      </div>
    </div>
  );
}
