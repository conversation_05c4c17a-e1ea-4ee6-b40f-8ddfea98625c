'use client';

import { Fixture } from '@/lib/api';
import Image from 'next/image';
import { format, differenceInHours, differenceInSeconds, isToday, isTomorrow } from 'date-fns';
import { useState, useEffect } from 'react';
import { getFormColors } from '@/lib/utils';

interface FixtureHeaderProps {
  fixture: Fixture;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function FixtureHeader({ fixture, activeTab = 'facts', onTabChange }: FixtureHeaderProps) {
  const [homeForm, setHomeForm] = useState<string[]>(['W', 'W', 'L', 'W', 'D']);
  const [awayForm, setAwayForm] = useState<string[]>(['L', 'W', 'D', 'W', 'L']);
  const [formLoading, setFormLoading] = useState(true);

  const isLive = ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE'].includes(fixture.fixture.status.short);
  const isFinished = ['FT', 'AET', 'PEN'].includes(fixture.fixture.status.short);
  const isUpcoming = fixture.fixture.status.short === 'NS';

  useEffect(() => {
    const fetchTeamForm = async () => {
      try {
        setFormLoading(true);

        // Fetch form data for both teams
        const [homeResponse, awayResponse] = await Promise.all([
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/teams/form?teamId=${fixture.teams.home.id}`, {
            headers: {
              'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
            }
          }),
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/teams/form?teamId=${fixture.teams.away.id}`, {
            headers: {
              'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
            }
          })
        ]);

        if (homeResponse.ok) {
          const homeData = await homeResponse.json();
          console.log('Home team form data:', homeData);
          if (homeData.form && typeof homeData.form === 'string' && homeData.form.length > 0) {
            // Form is a string like "LDLWW" - convert to array
            setHomeForm(homeData.form.split('').slice(-5));
          }
        }

        if (awayResponse.ok) {
          const awayData = await awayResponse.json();
          console.log('Away team form data:', awayData);
          if (awayData.form && typeof awayData.form === 'string' && awayData.form.length > 0) {
            // Form is a string like "DWLWL" - convert to array
            setAwayForm(awayData.form.split('').slice(-5));
          }
        }
      } catch (error) {
        console.error('Error fetching team form:', error);
      } finally {
        setFormLoading(false);
      }
    };

    fetchTeamForm();
  }, [fixture.teams.home.id, fixture.teams.away.id]);

  // No timer needed - we'll show static relative time text instead of live countdown

  const formatMatchTime = () => {
    if (isLive) {
      const elapsed = fixture.fixture.status.elapsed || 0;
      return (
        <span>
          {elapsed}<span className="live-timer-apostrophe">&apos;</span>
        </span>
      );
    }
    if (isFinished) {
      return 'FT';
    }
    if (isUpcoming) {
      return format(new Date(fixture.fixture.date), 'HH:mm');
    }
    return fixture.fixture.status.short;
  };



  const getTimeUntilMatch = () => {
    const matchDate = new Date(fixture.fixture.date);
    const now = new Date(); // Use current time directly, no need for state

    if (matchDate <= now) {
      return null; // Match has started or finished
    }

    const hoursUntil = differenceInHours(matchDate, now);
    const minutesUntil = Math.floor((differenceInSeconds(matchDate, now) % 3600) / 60);

    // Show relative time text instead of live countdown
    if (hoursUntil < 1) {
      if (minutesUntil <= 1) {
        return 'Starting soon';
      } else {
        return `in ${minutesUntil} minutes`;
      }
    } else if (hoursUntil < 24) {
      if (hoursUntil === 1 && minutesUntil > 30) {
        return 'in 1.5 hours';
      } else if (hoursUntil === 1) {
        return 'in 1 hour';
      } else {
        return `in ${hoursUntil} hours`;
      }
    } else {
      // Show relative date for matches more than 24 hours away
      if (isToday(matchDate)) {
        return 'Today';
      } else if (isTomorrow(matchDate)) {
        return 'Tomorrow';
      } else {
        const daysDiff = Math.ceil(hoursUntil / 24);
        if (daysDiff === 2) {
          return 'Day after tomorrow';
        } else if (daysDiff <= 7) {
          return `in ${daysDiff} days`;
        } else {
          return format(matchDate, 'MMM d');
        }
      }
    }
  };

  // Get team form data
  const getTeamForm = (teamId: number) => {
    if (formLoading) {
      return ['?', '?', '?', '?', '?'];
    }
    return teamId === fixture.teams.home.id ? homeForm : awayForm;
  };

  // Define tabs based on match status
  const getTabs = () => {
    const baseTabs = [
      { id: 'facts', label: isUpcoming ? 'Preview' : 'Facts' },
      { id: 'h2h', label: 'Head-to-Head' },
      { id: 'standings', label: 'Table' }
    ];

    // Add tabs for live/finished matches (removed commentary)
    if (isLive || isFinished) {
      baseTabs.splice(1, 0,
        { id: 'stats', label: 'Stats' },
        { id: 'lineup', label: 'Lineup' }
      );
    }

    return baseTabs;
  };

  const renderFormIndicator = (form: string[]) => {
    return (
      <div className="flex space-x-1">
        {form.map((result, index) => {
          const colors = getFormColors(result);
          return (
            <div
              key={index}
              className={`w-[14px] h-[14px] md:w-5 md:h-5 rounded-full ${
                result === '?' ? 'bg-gray-400 animate-pulse' : colors.bg
              }`}
              title={result === '?' ? 'Loading...' : result} // Show result on hover
            />
          );
        })}
      </div>
    );
  };

  return (
    <div className="bg-card rounded-none md:rounded-lg border-0 md:border container-border p-4 pb-0">
      {/* Top Section: Competition Info */}
      <div className="flex items-center justify-center mb-4 relative">
        <div className="flex items-center space-x-3">
          {fixture.league.logo && (
            <Image
              src={fixture.league.logo}
              alt={fixture.league.name}
              width={28}
              height={28}
              className="w-7 h-7 md:w-6 md:h-6 rounded"
            />
          )}
          <div className="text-center">
            <h2 className="font-semibold text-sm md:text-base text-foreground">{fixture.league.name}</h2>
            <p className="text-xs md:text-sm text-muted-foreground">{fixture.league.round}</p>
          </div>
        </div>

        {/* Follow Button - positioned absolutely to the right */}
        <button className="absolute right-0 px-4 py-2 bg-muted text-foreground text-sm rounded-lg hover:bg-muted/80 transition-colors">
          Follow
        </button>
      </div>

      {/* Match Details - Hidden on mobile */}
      <div className="hidden md:flex items-center justify-center space-x-6 text-sm text-muted-foreground mb-6 py-4 border-t border-b border-border">
        <div className="flex items-center space-x-1">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <span>{format(new Date(fixture.fixture.date), 'EEE dd MMM, HH:mm')}</span>
        </div>

        {fixture.fixture.venue.name && (
          <div className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>{fixture.fixture.venue.name}</span>
          </div>
        )}

        {fixture.fixture.referee && (
          <div className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span>{fixture.fixture.referee}</span>
          </div>
        )}
      </div>

      {/* Main Match Section */}
      <div className="text-center mb-6">
        {/* Teams Row */}
        <div className="flex items-center justify-between mb-4">
          {/* Home Team */}
          <div className="flex-1 flex flex-col items-center text-center">
            <Image
              src={fixture.teams.home.logo || '/default-team-logo.png'}
              alt={fixture.teams.home.name}
              width={40}
              height={40}
              className="w-[34px] h-[34px] md:w-10 md:h-10 rounded flex-shrink-0 mb-2"
            />
            <h3 className="font-bold text-lg md:text-2xl text-foreground mb-2 px-1">{fixture.teams.home.name}</h3>
            <div>
              {renderFormIndicator(getTeamForm(fixture.teams.home.id))}
            </div>
          </div>

          {/* Score/Time */}
          <div className="flex flex-col items-center mx-2 md:mx-4 flex-shrink-0">
            {isUpcoming ? (
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-foreground">
                  {format(new Date(fixture.fixture.date), 'HH:mm')}
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  {getTimeUntilMatch()}
                </div>
              </div>
            ) : (
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-foreground">
                  {fixture.goals.home ?? 0} - {fixture.goals.away ?? 0}
                </div>
                <div className="flex items-center justify-center space-x-2 mt-1">
                  <span className={`text-sm ${isLive ? 'text-red-500' : 'text-muted-foreground'}`}>
                    {formatMatchTime()}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Away Team */}
          <div className="flex-1 flex flex-col items-center text-center">
            <Image
              src={fixture.teams.away.logo || '/default-team-logo.png'}
              alt={fixture.teams.away.name}
              width={40}
              height={40}
              className="w-[34px] h-[34px] md:w-10 md:h-10 rounded flex-shrink-0 mb-2"
            />
            <h3 className="font-bold text-lg md:text-2xl text-foreground mb-2 px-1">{fixture.teams.away.name}</h3>
            <div>
              {renderFormIndicator(getTeamForm(fixture.teams.away.id))}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="mt-6">
        <div className="flex overflow-x-auto scrollbar-hide">
          {getTabs().map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange?.(tab.id)}
              className={`
                flex-shrink-0 px-4 md:px-6 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap hover:no-underline
                ${activeTab === tab.id
                  ? 'border-primary text-primary bg-primary/5'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground/50'
                }
              `}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>




    </div>
  );
}
