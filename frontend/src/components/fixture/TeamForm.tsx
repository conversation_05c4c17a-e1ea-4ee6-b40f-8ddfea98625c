'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Fixture } from '@/lib/api';
import { getFormColors } from '@/lib/utils';

interface TeamFormProps {
  fixture: Fixture;
}

interface FormMatch {
  fixture: {
    id: number;
    date: string;
  };
  teams: {
    home: { id: number; name: string; logo: string };
    away: { id: number; name: string; logo: string };
  };
  goals: {
    home: number | null;
    away: number | null;
  };
  league: {
    name: string;
  };
}

export default function TeamForm({ fixture }: TeamFormProps) {
  const [homeForm, setHomeForm] = useState<FormMatch[]>([]);
  const [awayForm, setAwayForm] = useState<FormMatch[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeamForm = async () => {
      try {
        setLoading(true);
        
        // Fetch last 5 matches for both teams
        const [homeResponse, awayResponse] = await Promise.all([
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures?team=${fixture.teams.home.id}&last=5`, {
            headers: {
              'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
            }
          }),
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures?team=${fixture.teams.away.id}&last=5`, {
            headers: {
              'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
            }
          })
        ]);

        if (homeResponse.ok && awayResponse.ok) {
          const homeData = await homeResponse.json();
          const awayData = await awayResponse.json();
          
          setHomeForm(Array.isArray(homeData) ? homeData.slice(0, 5) : []);
          setAwayForm(Array.isArray(awayData) ? awayData.slice(0, 5) : []);
        }
      } catch (error) {
        console.error('Error fetching team form:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTeamForm();
  }, [fixture.teams.home.id, fixture.teams.away.id]);

  // Helper function to determine match result
  const getMatchResult = (match: FormMatch, teamId: number): 'W' | 'D' | 'L' => {
    if (match.goals.home === null || match.goals.away === null) return 'D';
    
    const isHome = match.teams.home.id === teamId;
    const homeGoals = match.goals.home;
    const awayGoals = match.goals.away;
    
    if (homeGoals === awayGoals) return 'D';
    
    if (isHome) {
      return homeGoals > awayGoals ? 'W' : 'L';
    } else {
      return awayGoals > homeGoals ? 'W' : 'L';
    }
  };

  // Helper function to get result color
  const getResultColor = (result: 'W' | 'D' | 'L'): string => {
    const colors = getFormColors(result);
    return `${colors.bg} ${colors.text}`;
  };

  // Helper function to format score
  const formatScore = (match: FormMatch, teamId: number): string => {
    if (match.goals.home === null || match.goals.away === null) return '-';
    
    const isHome = match.teams.home.id === teamId;
    if (isHome) {
      return `${match.goals.home}-${match.goals.away}`;
    } else {
      return `${match.goals.away}-${match.goals.home}`;
    }
  };

  // Helper function to get opponent
  const getOpponent = (match: FormMatch, teamId: number) => {
    return match.teams.home.id === teamId ? match.teams.away : match.teams.home;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
          <div className="flex space-x-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
          <div className="flex space-x-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Home Team Form */}
      <div className="bg-muted/30 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Image
              src={fixture.teams.home.logo}
              alt={fixture.teams.home.name}
              width={24}
              height={24}
              className="w-6 h-6 md:w-6 md:h-6 object-contain"
            />
            <span className="font-medium">{fixture.teams.home.name}</span>
          </div>
          <div className="flex space-x-1">
            {homeForm.map((match) => {
              const result = getMatchResult(match, fixture.teams.home.id);
              return (
                <div
                  key={match.fixture.id}
                  className={`w-9 h-9 md:w-8 md:h-8 rounded flex items-center justify-center text-xs font-bold ${getResultColor(result)}`}
                  title={`vs ${getOpponent(match, fixture.teams.home.id).name} (${formatScore(match, fixture.teams.home.id)})`}
                >
                  {result}
                </div>
              );
            })}
          </div>
        </div>
        
        {/* Recent matches details */}
        <div className="space-y-2">
          {homeForm.slice(0, 3).map((match) => {
            const opponent = getOpponent(match, fixture.teams.home.id);
            const result = getMatchResult(match, fixture.teams.home.id);
            const score = formatScore(match, fixture.teams.home.id);
            
            return (
              <div key={match.fixture.id} className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-2">
                  <div className={`w-5 h-5 md:w-4 md:h-4 rounded text-xs flex items-center justify-center ${getResultColor(result)}`}>
                    {result}
                  </div>
                  <span className="text-muted-foreground">vs</span>
                  <span>{opponent.name}</span>
                </div>
                <span className="font-medium">{score}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Away Team Form */}
      <div className="bg-muted/30 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Image
              src={fixture.teams.away.logo}
              alt={fixture.teams.away.name}
              width={24}
              height={24}
              className="w-6 h-6 md:w-6 md:h-6 object-contain"
            />
            <span className="font-medium">{fixture.teams.away.name}</span>
          </div>
          <div className="flex space-x-1">
            {awayForm.map((match) => {
              const result = getMatchResult(match, fixture.teams.away.id);
              return (
                <div
                  key={match.fixture.id}
                  className={`w-9 h-9 md:w-8 md:h-8 rounded flex items-center justify-center text-xs font-bold ${getResultColor(result)}`}
                  title={`vs ${getOpponent(match, fixture.teams.away.id).name} (${formatScore(match, fixture.teams.away.id)})`}
                >
                  {result}
                </div>
              );
            })}
          </div>
        </div>
        
        {/* Recent matches details */}
        <div className="space-y-2">
          {awayForm.slice(0, 3).map((match) => {
            const opponent = getOpponent(match, fixture.teams.away.id);
            const result = getMatchResult(match, fixture.teams.away.id);
            const score = formatScore(match, fixture.teams.away.id);
            
            return (
              <div key={match.fixture.id} className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-2">
                  <div className={`w-5 h-5 md:w-4 md:h-4 rounded text-xs flex items-center justify-center ${getResultColor(result)}`}>
                    {result}
                  </div>
                  <span className="text-muted-foreground">vs</span>
                  <span>{opponent.name}</span>
                </div>
                <span className="font-medium">{score}</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
