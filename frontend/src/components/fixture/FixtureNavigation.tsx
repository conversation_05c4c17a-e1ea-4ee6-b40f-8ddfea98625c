'use client';

interface FixtureNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  matchStatus: string;
}

export default function FixtureNavigation({ activeTab, onTabChange, matchStatus }: FixtureNavigationProps) {
  const isUpcoming = matchStatus === 'NS';
  const isLive = ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE'].includes(matchStatus);
  const isFinished = ['FT', 'AET', 'PEN'].includes(matchStatus);

  // Define tabs based on match status
  const getTabs = () => {
    const baseTabs = [
      { id: 'facts', label: isUpcoming ? 'Preview' : 'Facts' },
      { id: 'h2h', label: 'Head-to-Head' },
      { id: 'standings', label: 'Table' }
    ];

    // Add tabs for live/finished matches (removed commentary)
    if (isLive || isFinished) {
      baseTabs.splice(1, 0,
        { id: 'stats', label: 'Stats' },
        { id: 'lineup', label: 'Lineup' }
      );
    }

    return baseTabs;
  };

  const tabs = getTabs();

  return (
    <div className="bg-card rounded-lg border container-border mx-2 md:mx-0">
      <div className="flex overflow-x-auto scrollbar-hide">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`
              flex-shrink-0 px-3 md:px-6 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap min-h-[48px] flex items-center
              ${activeTab === tab.id
                ? 'border-primary text-primary bg-primary/5'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground/50'
              }
            `}
          >
            {tab.label}
          </button>
        ))}
      </div>
    </div>
  );
}
