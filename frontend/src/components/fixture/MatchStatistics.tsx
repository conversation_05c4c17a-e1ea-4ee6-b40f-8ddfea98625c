'use client';

import { Fixture } from '@/lib/api';
import type { MatchStatistics, TeamStatistics, StatItem, MatchLineups } from '@/types/fixture';
import { useTheme } from 'next-themes';

interface MatchStatisticsProps {
  statistics?: MatchStatistics;
  fixture: Fixture;
  lineups?: MatchLineups;
}

interface StatConfig {
  key: string;
  label: string;
  isPercentage: boolean;
}

export default function MatchStatistics({ statistics, fixture, lineups }: MatchStatisticsProps) {
  const { resolvedTheme } = useTheme();

  if (!statistics || statistics.length === 0) {
    return (
      <div className="bg-card rounded-lg border container-border p-6 mx-2 md:mx-0">
        <h3 className="text-lg font-semibold mb-4">Match Statistics</h3>
        <p className="text-muted-foreground">Statistics not available for this match.</p>
      </div>
    );
  }

  // Parse statistics data
  const homeStats = statistics.find((stat: TeamStatistics) => stat.team.id === fixture.teams.home.id);
  const awayStats = statistics.find((stat: TeamStatistics) => stat.team.id === fixture.teams.away.id);

  if (!homeStats || !awayStats) {
    return (
      <div className="bg-card rounded-lg border container-border p-6 mx-2 md:mx-0">
        <h3 className="text-lg font-semibold mb-4">Match Statistics</h3>
        <p className="text-muted-foreground">Statistics data incomplete.</p>
      </div>
    );
  }

  // Helper function to get stat value
  const getStatValue = (stats: StatItem[], type: string): number => {
    const stat = stats.find(s => s.type === type);
    if (!stat || stat.value === null) return 0;
    
    // Handle percentage values
    if (typeof stat.value === 'string' && stat.value.includes('%')) {
      return parseInt(stat.value.replace('%', ''));
    }
    
    return typeof stat.value === 'number' ? stat.value : parseInt(stat.value.toString()) || 0;
  };

  // Helper function to check if a color is too similar to background
  const isColorTooSimilar = (color: string, isDark: boolean) => {
    const normalizedColor = color.toLowerCase().replace('#', '');
    // Common problematic colors
    const lightProblematic = ['ffffff', 'fefefe', 'fdfdfd', 'fcfcfc', 'fbfbfb', 'fafafa', 'f9f9f9', 'f8f8f8'];
    const darkProblematic = ['000000', '010101', '020202', '030303', '1a1a1a', '1b1b1b', '1c1c1c', '1d1d1d', '1e1e1e', '1f1f1f'];

    if (isDark) {
      return darkProblematic.includes(normalizedColor);
    } else {
      return lightProblematic.includes(normalizedColor);
    }
  };

  // Helper function to check if two teams have the same colors
  const getTeamColorsWithConflictResolution = () => {
    if (!lineups || lineups.length < 2) {
      return {
        home: { background: '#22c55e', text: '#ffffff' },
        away: { background: '#3b82f6', text: '#ffffff' }
      };
    }

    const homeLineup = lineups.find(lineup => lineup.team.id === fixture.teams.home.id);
    const awayLineup = lineups.find(lineup => lineup.team.id === fixture.teams.away.id);

    if (!homeLineup?.team.colors || !awayLineup?.team.colors) {
      return {
        home: { background: '#22c55e', text: '#ffffff' },
        away: { background: '#3b82f6', text: '#ffffff' }
      };
    }

    const homePlayerColors = homeLineup.team.colors.player;
    const homeGoalkeeperColors = homeLineup.team.colors.goalkeeper;
    const awayPlayerColors = awayLineup.team.colors.player;
    const awayGoalkeeperColors = awayLineup.team.colors.goalkeeper;

    // Check if we're in dark mode using next-themes
    const isDarkMode = resolvedTheme === 'dark';

    // Default colors
    let homeColors = { background: '#22c55e', text: '#ffffff' };
    let awayColors = { background: '#3b82f6', text: '#ffffff' };

    // Get home team colors (always use player colors first, fallback to goalkeeper if theme conflict)
    if (homePlayerColors?.primary) {
      if (isColorTooSimilar(homePlayerColors.primary, isDarkMode)) {
        // Use goalkeeper colors for home team due to theme conflict
        if (homeGoalkeeperColors?.primary) {
          homeColors = {
            background: `#${homeGoalkeeperColors.primary}`,
            text: homeGoalkeeperColors.number ? `#${homeGoalkeeperColors.number}` : '#ffffff'
          };
        }
      } else {
        homeColors = {
          background: `#${homePlayerColors.primary}`,
          text: homePlayerColors.number ? `#${homePlayerColors.number}` : '#ffffff'
        };
      }
    }

    // Get away team colors with additional check for color conflicts with home team
    if (awayPlayerColors?.primary) {
      const awayPlayerBg = `#${awayPlayerColors.primary}`;
      const shouldUseGoalkeeperForTheme = isColorTooSimilar(awayPlayerColors.primary, isDarkMode);
      const shouldUseGoalkeeperForConflict = homeColors.background.toLowerCase() === awayPlayerBg.toLowerCase();

      if (shouldUseGoalkeeperForTheme || shouldUseGoalkeeperForConflict) {
        // Use goalkeeper colors for away team due to theme conflict or team color conflict
        if (awayGoalkeeperColors?.primary) {
          awayColors = {
            background: `#${awayGoalkeeperColors.primary}`,
            text: awayGoalkeeperColors.number ? `#${awayGoalkeeperColors.number}` : '#ffffff'
          };
        }
      } else {
        awayColors = {
          background: awayPlayerBg,
          text: awayPlayerColors.number ? `#${awayPlayerColors.number}` : '#ffffff'
        };
      }
    }

    return { home: homeColors, away: awayColors };
  };

  // Helper function to get team colors with smart fallback
  const getTeamColors = (teamId: number) => {
    const colors = getTeamColorsWithConflictResolution();
    return teamId === fixture.teams.home.id ? colors.home : colors.away;
  };

  // Helper function to render stat bar (FotMob style)
  const renderStatBar = (homeValue: number, awayValue: number, label: string, isPercentage = false) => {
    const total = homeValue + awayValue;
    const homePercentage = total > 0 ? (homeValue / total) * 100 : 50;
    const awayPercentage = total > 0 ? (awayValue / total) * 100 : 50;

    const homeColors = getTeamColors(fixture.teams.home.id);
    const awayColors = getTeamColors(fixture.teams.away.id);

    // Make ball possession bar thicker
    const barHeight = label === 'Ball possession' ? 'h-4' : 'h-2';

    // Determine which value is higher for highlighting
    const homeIsHigher = homeValue > awayValue;
    const awayIsHigher = awayValue > homeValue;

    // Format display value - show decimals for xG
    const formatValue = (value: number) => {
      if (label.toLowerCase().includes('xg') || label.toLowerCase().includes('expected goals')) {
        return value.toFixed(2);
      }
      return isPercentage ? `${value}%` : value.toString();
    };

    return (
      <div className="mb-6">
        {/* Inline layout with stat values and bar */}
        <div className="flex items-center gap-3">
          {/* Home team stat */}
          <div
            className="px-2 py-1 rounded-full text-sm font-bold min-w-[44px] md:min-w-[40px] text-center"
            style={homeIsHigher ? {
              backgroundColor: homeColors.background,
              color: homeColors.text
            } : {}}
          >
            <span className={homeIsHigher ? '' : 'text-foreground dark:text-white'}>
              {formatValue(homeValue)}
            </span>
          </div>

          {/* Stat bar with label */}
          <div className="flex-1 flex flex-col gap-1">
            <span className="text-sm font-medium text-muted-foreground text-center">{label}</span>
            <div className={`flex ${barHeight} rounded-full overflow-hidden`}>
              <div
                className="transition-all duration-300"
                style={{ width: `${homePercentage}%`, backgroundColor: homeColors.background }}
              />
              <div
                className="transition-all duration-300"
                style={{ width: `${awayPercentage}%`, backgroundColor: awayColors.background }}
              />
            </div>
          </div>

          {/* Away team stat */}
          <div
            className="px-2 py-1 rounded-full text-sm font-bold min-w-[44px] md:min-w-[40px] text-center"
            style={awayIsHigher ? {
              backgroundColor: awayColors.background,
              color: awayColors.text
            } : {}}
          >
            <span className={awayIsHigher ? '' : 'text-foreground dark:text-white'}>
              {formatValue(awayValue)}
            </span>
          </div>
        </div>
      </div>
    );
  };

  // Helper function to render complex stat (like shots breakdown)
  const renderComplexStat = (homeTotal: number, awayTotal: number, homeSuccess: number, awaySuccess: number, label: string, successLabel = 'on target', failLabel = 'off target') => {
    const homeFail = homeTotal - homeSuccess;
    const awayFail = awayTotal - awaySuccess;

    const homeColors = getTeamColors(fixture.teams.home.id);
    const awayColors = getTeamColors(fixture.teams.away.id);

    // Determine which value is higher for highlighting
    const homeIsHigher = homeTotal > awayTotal;
    const awayIsHigher = awayTotal > homeTotal;

    return (
      <div className="mb-6">
        {/* Inline layout with stat values and complex bar */}
        <div className="flex items-center gap-3">
          {/* Home team stat */}
          <div
            className="px-2 py-1 rounded-full text-sm font-bold min-w-[44px] md:min-w-[40px] text-center"
            style={homeIsHigher ? {
              backgroundColor: homeColors.background,
              color: homeColors.text
            } : {}}
          >
            <span className={homeIsHigher ? '' : 'text-foreground dark:text-white'}>
              {homeTotal}
            </span>
          </div>

          {/* Complex stat bar with label */}
          <div className="flex-1 flex flex-col gap-1">
            <span className="text-sm font-medium text-muted-foreground text-center">{label}</span>

            {/* Complex bar showing success vs fail */}
            <div className="flex space-x-1 h-8">
              {/* Home side */}
              <div className="flex-1 flex">
                <div
                  className="flex items-center justify-center text-white text-xs font-bold rounded-l"
                  style={{
                    width: `${(homeFail / (homeTotal || 1)) * 100}%`,
                    backgroundColor: homeColors.background,
                    opacity: 0.7
                  }}
                >
                  {homeFail > 0 && homeFail}
                </div>
                <div
                  className="flex items-center justify-center text-white text-xs font-bold border-l-2 border-white"
                  style={{
                    width: `${(homeSuccess / (homeTotal || 1)) * 100}%`,
                    backgroundColor: homeColors.background
                  }}
                >
                  {homeSuccess > 0 && homeSuccess}
                </div>
              </div>

              {/* Away side */}
              <div className="flex-1 flex">
                <div
                  className="flex items-center justify-center text-white text-xs font-bold border-r-2 border-white"
                  style={{
                    width: `${(awaySuccess / (awayTotal || 1)) * 100}%`,
                    backgroundColor: awayColors.background
                  }}
                >
                  {awaySuccess > 0 && awaySuccess}
                </div>
                <div
                  className="flex items-center justify-center text-white text-xs font-bold rounded-r"
                  style={{
                    width: `${(awayFail / (awayTotal || 1)) * 100}%`,
                    backgroundColor: awayColors.background,
                    opacity: 0.7
                  }}
                >
                  {awayFail > 0 && awayFail}
                </div>
              </div>
            </div>

            {/* Labels */}
            <div className="flex justify-center mt-2 space-x-4 text-xs text-muted-foreground">
              <span>{failLabel}</span>
              <span className="font-medium">{successLabel}</span>
            </div>
          </div>

          {/* Away team stat */}
          <div
            className="px-2 py-1 rounded-full text-sm font-bold min-w-[44px] md:min-w-[40px] text-center"
            style={awayIsHigher ? {
              backgroundColor: awayColors.background,
              color: awayColors.text
            } : {}}
          >
            <span className={awayIsHigher ? '' : 'text-foreground dark:text-white'}>
              {awayTotal}
            </span>
          </div>
        </div>
      </div>
    );
  };



  // Define categorized stats
  const statCategories = {
    general: [
      { key: 'Ball Possession', label: 'Ball possession', isPercentage: true },
      { key: 'Total Shots', label: 'Total shots', isPercentage: false },
      { key: 'expected_goals', label: 'Expected goals (xG)', isPercentage: false },
      { key: 'Free Kicks', label: 'Free kicks', isPercentage: false },
      { key: 'Throw-ins', label: 'Throw-ins', isPercentage: false },
    ],
    attack: [
      { key: 'Total Shots', label: 'Total shots', isPercentage: false },
      { key: 'Shots on Goal', label: 'Shots on target', isPercentage: false },
      { key: 'Shots off Goal', label: 'Shots off target', isPercentage: false },
      { key: 'Corner Kicks', label: 'Corner kicks', isPercentage: false },
      { key: 'Shots insidebox', label: 'Shots inside box', isPercentage: false },
      { key: 'Shots outsidebox', label: 'Shots outside box', isPercentage: false },
      { key: 'Big Chances', label: 'Big chances', isPercentage: false },
      { key: 'Big Chances Missed', label: 'Big chances missed', isPercentage: false },
      { key: 'expected_goals', label: 'Expected goals (xG)', isPercentage: false },
      { key: 'Dangerous Attacks', label: 'Dangerous attacks', isPercentage: false },
      { key: 'Attacks', label: 'Total attacks', isPercentage: false },
    ],
    passing: [
      { key: 'Total passes', label: 'Total passes', isPercentage: false },
      { key: 'Passes accurate', label: 'Accurate passes', isPercentage: false },
      { key: 'Passes %', label: 'Pass accuracy', isPercentage: true },
      { key: 'Key Passes', label: 'Key passes', isPercentage: false },
      { key: 'Crosses', label: 'Crosses', isPercentage: false },
      { key: 'Crosses accurate', label: 'Accurate crosses', isPercentage: false },
      { key: 'Long balls', label: 'Long balls', isPercentage: false },
      { key: 'Long balls accurate', label: 'Accurate long balls', isPercentage: false },
    ],
    defence: [
      { key: 'Offsides', label: 'Offsides', isPercentage: false },
      { key: 'Goalkeeper Saves', label: 'Goalkeeper saves', isPercentage: false },
      { key: 'Blocked Shots', label: 'Blocked shots', isPercentage: false },
      { key: 'Interceptions', label: 'Interceptions', isPercentage: false },
      { key: 'Tackles', label: 'Tackles', isPercentage: false },
      { key: 'Clearances', label: 'Clearances', isPercentage: false },
      { key: 'Duels', label: 'Duels', isPercentage: false },
      { key: 'Duels won', label: 'Duels won', isPercentage: false },
      { key: 'Aerial Duels', label: 'Aerial duels', isPercentage: false },
      { key: 'Aerial Duels won', label: 'Aerial duels won', isPercentage: false },
    ],
    discipline: [
      { key: 'Fouls', label: 'Fouls', isPercentage: false },
      { key: 'Yellow Cards', label: 'Yellow cards', isPercentage: false },
      { key: 'Red Cards', label: 'Red cards', isPercentage: false },
      { key: 'Penalties conceded', label: 'Penalties conceded', isPercentage: false },
    ]
  };

  const renderStatCategory = (categoryName: string, stats: StatConfig[]) => {
    const hasStats = stats.some(statConfig => {
      const homeValue = getStatValue(homeStats.statistics, statConfig.key);
      const awayValue = getStatValue(awayStats.statistics, statConfig.key);
      return homeValue > 0 || awayValue > 0;
    });

    if (!hasStats) return null;

    return (
      <div className="mb-8">
        <div className="text-center text-sm font-bold text-foreground dark:text-white uppercase tracking-wide mb-6">
          {categoryName}
        </div>
        <div className="space-y-4">
          {stats.map((statConfig) => {
            let homeValue = getStatValue(homeStats.statistics, statConfig.key);
            let awayValue = getStatValue(awayStats.statistics, statConfig.key);

            // Special handling for "Shots off Goal" - calculate from Total Shots - Shots on Goal
            if (statConfig.key === 'Shots off Goal') {
              const homeTotalShots = getStatValue(homeStats.statistics, 'Total Shots');
              const homeOnTarget = getStatValue(homeStats.statistics, 'Shots on Goal');
              const awayTotalShots = getStatValue(awayStats.statistics, 'Total Shots');
              const awayOnTarget = getStatValue(awayStats.statistics, 'Shots on Goal');

              homeValue = homeTotalShots - homeOnTarget;
              awayValue = awayTotalShots - awayOnTarget;
            }

            // Skip if both values are 0 or null
            if (homeValue === 0 && awayValue === 0) return null;



            // Special handling for duels (show won vs total)
            if (statConfig.key === 'Duels') {
              const homeDuelsWon = getStatValue(homeStats.statistics, 'Duels won');
              const awayDuelsWon = getStatValue(awayStats.statistics, 'Duels won');
              return (
                <div key={statConfig.key}>
                  {renderComplexStat(homeValue, awayValue, homeDuelsWon, awayDuelsWon, statConfig.label, 'won', 'lost')}
                </div>
              );
            }

            // Special handling for aerial duels
            if (statConfig.key === 'Aerial Duels') {
              const homeAerialWon = getStatValue(homeStats.statistics, 'Aerial Duels won');
              const awayAerialWon = getStatValue(awayStats.statistics, 'Aerial Duels won');
              return (
                <div key={statConfig.key}>
                  {renderComplexStat(homeValue, awayValue, homeAerialWon, awayAerialWon, statConfig.label, 'won', 'lost')}
                </div>
              );
            }

            return (
              <div key={statConfig.key}>
                {renderStatBar(homeValue, awayValue, statConfig.label, statConfig.isPercentage)}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-card rounded-lg border container-border p-6 mx-2 md:mx-0">
      <div className="space-y-6">
        {renderStatCategory('General Stats', statCategories.general)}
        {renderStatCategory('Attack', statCategories.attack)}
        {renderStatCategory('Passing', statCategories.passing)}
        {renderStatCategory('Defence', statCategories.defence)}
        {renderStatCategory('Discipline', statCategories.discipline)}
      </div>
    </div>
  );
}
