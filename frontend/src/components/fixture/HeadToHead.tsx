'use client';

import { Fixture } from '@/lib/api';
import { HeadToHeadData, HeadToHeadMatch, MatchLineups } from '@/types/fixture';
import Image from 'next/image';
import { format } from 'date-fns';
import { useState } from 'react';
import { useTheme } from 'next-themes';
import { useRouter } from 'next/navigation';
import { getButtonStyle, handleButtonHover } from '@/lib/buttonUtils';
import { getFormColors } from '@/lib/utils';

interface HeadToHeadProps {
  headToHead?: HeadToHeadData;
  fixture: Fixture;
  lineups?: MatchLineups;
}

export default function HeadToHead({ headToHead, fixture, lineups }: HeadToHeadProps) {
  const { resolvedTheme } = useTheme();
  const router = useRouter();
  const [homeFilter, setHomeFilter] = useState(false);
  const [tournamentFilter, setTournamentFilter] = useState(false);
  const [showAll, setShowAll] = useState(false);

  // Helper function to generate fixture URL
  const generateFixtureUrl = (match: HeadToHeadMatch) => {
    const homeTeam = match.teams.home.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    const awayTeam = match.teams.away.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    return `/match/${homeTeam}-vs-${awayTeam}/${match.fixture.id}`;
  };

  // Helper function to get bar color based on percentage
  const getBarColor = (percentage: number) => {
    if (percentage >= 70) {
      return '#7BBE45'; // Green for high percentages
    } else if (percentage <= 50) {
      return '#ECA803'; // Orange for medium percentages
    } else if (percentage < 30) {
      return '#F44C36'; // Red for low percentages
    } else {
      return '#ECA803'; // Default to orange for 51-69%
    }
  };

  // Helper function to check if a color is too similar to background
  const isColorTooSimilar = (color: string, isDark: boolean) => {
    const normalizedColor = color.toLowerCase().replace('#', '');
    // Common problematic colors
    const lightProblematic = ['ffffff', 'fefefe', 'fdfdfd', 'fcfcfc', 'fbfbfb', 'fafafa', 'f9f9f9', 'f8f8f8'];
    const darkProblematic = ['000000', '010101', '020202', '030303', '1a1a1a', '1b1b1b', '1c1c1c', '1d1d1d', '1e1e1e', '1f1f1f'];

    if (isDark) {
      return darkProblematic.includes(normalizedColor);
    } else {
      return lightProblematic.includes(normalizedColor);
    }
  };

  // Helper function to get team colors with conflict resolution (same as MatchStatistics)
  const getTeamColorsWithConflictResolution = () => {
    if (!lineups || lineups.length < 2) {
      return {
        home: { background: '#22c55e', text: '#ffffff' },
        away: { background: '#3b82f6', text: '#ffffff' }
      };
    }

    const homeLineup = lineups.find(lineup => lineup.team.id === fixture.teams.home.id);
    const awayLineup = lineups.find(lineup => lineup.team.id === fixture.teams.away.id);

    if (!homeLineup?.team.colors || !awayLineup?.team.colors) {
      return {
        home: { background: '#22c55e', text: '#ffffff' },
        away: { background: '#3b82f6', text: '#ffffff' }
      };
    }

    const homePlayerColors = homeLineup.team.colors.player;
    const homeGoalkeeperColors = homeLineup.team.colors.goalkeeper;
    const awayPlayerColors = awayLineup.team.colors.player;
    const awayGoalkeeperColors = awayLineup.team.colors.goalkeeper;

    // Check if we're in dark mode using next-themes
    const isDarkMode = resolvedTheme === 'dark';

    // Default colors
    let homeColors = { background: '#22c55e', text: '#ffffff' };
    let awayColors = { background: '#3b82f6', text: '#ffffff' };

    // Get home team colors (always use player colors first, fallback to goalkeeper if theme conflict)
    if (homePlayerColors?.primary) {
      if (isColorTooSimilar(homePlayerColors.primary, isDarkMode)) {
        // Use goalkeeper colors for home team due to theme conflict
        if (homeGoalkeeperColors?.primary) {
          homeColors = {
            background: `#${homeGoalkeeperColors.primary}`,
            text: homeGoalkeeperColors.number ? `#${homeGoalkeeperColors.number}` : '#ffffff'
          };
        }
      } else {
        homeColors = {
          background: `#${homePlayerColors.primary}`,
          text: homePlayerColors.number ? `#${homePlayerColors.number}` : '#ffffff'
        };
      }
    }

    // Get away team colors with additional check for color conflicts with home team
    if (awayPlayerColors?.primary) {
      const awayPlayerBg = `#${awayPlayerColors.primary}`;
      const shouldUseGoalkeeperForTheme = isColorTooSimilar(awayPlayerColors.primary, isDarkMode);
      const shouldUseGoalkeeperForConflict = homeColors.background.toLowerCase() === awayPlayerBg.toLowerCase();

      if (shouldUseGoalkeeperForTheme || shouldUseGoalkeeperForConflict) {
        // Use goalkeeper colors for away team due to theme conflict or team color conflict
        if (awayGoalkeeperColors?.primary) {
          awayColors = {
            background: `#${awayGoalkeeperColors.primary}`,
            text: awayGoalkeeperColors.number ? `#${awayGoalkeeperColors.number}` : '#ffffff'
          };
        }
      } else {
        awayColors = {
          background: awayPlayerBg,
          text: awayPlayerColors.number ? `#${awayPlayerColors.number}` : '#ffffff'
        };
      }
    }

    return { home: homeColors, away: awayColors };
  };

  // Helper function to get team colors for win/draw/loss circles
  const getTeamWinColors = () => {
    const teamColors = getTeamColorsWithConflictResolution();
    const drawColors = getFormColors('D'); // Gray for draws

    return {
      homeWin: teamColors.home.background,
      draw: drawColors.hex,
      awayWin: teamColors.away.background
    };
  };



  if (!headToHead || headToHead.length === 0) {
    return (
      <div className="bg-card rounded-lg border container-border p-6 mx-2 md:mx-0">
        <h3 className="text-lg font-semibold mb-4">Head-to-Head</h3>
        <p className="text-muted-foreground">No head-to-head data available.</p>
      </div>
    );
  }

  // Separate finished and upcoming matches
  const finishedMatches = headToHead
    .filter(match => match.goals.home !== null && match.goals.away !== null)
    .sort((a, b) => new Date(b.fixture.date).getTime() - new Date(a.fixture.date).getTime());

  const upcomingMatches = headToHead
    .filter(match => match.goals.home === null && match.goals.away === null)
    .sort((a, b) => new Date(a.fixture.date).getTime() - new Date(b.fixture.date).getTime());

  // Apply filters to all matches (finished + upcoming)
  const getFilteredMatches = () => {
    let filteredFinished = finishedMatches;
    let filteredUpcoming = upcomingMatches;

    // Apply home filter
    if (homeFilter) {
      filteredFinished = filteredFinished.filter(match =>
        match.teams.home.id === fixture.teams.home.id
      );
      filteredUpcoming = filteredUpcoming.filter(match =>
        match.teams.home.id === fixture.teams.home.id
      );
    }

    // Apply tournament filter
    if (tournamentFilter) {
      filteredFinished = filteredFinished.filter(match =>
        match.league?.name === fixture.league.name
      );
      filteredUpcoming = filteredUpcoming.filter(match =>
        match.league?.name === fixture.league.name
      );
    }

    // Combine upcoming matches first, then finished matches
    return [...filteredUpcoming, ...filteredFinished];
  };

  // Get filtered matches for statistics (only finished matches)
  const getFilteredFinishedMatches = () => {
    let filtered = finishedMatches;

    if (homeFilter) {
      filtered = filtered.filter(match =>
        match.teams.home.id === fixture.teams.home.id
      );
    }

    if (tournamentFilter) {
      filtered = filtered.filter(match =>
        match.league?.name === fixture.league.name
      );
    }

    return filtered;
  };

  const filteredMatches = getFilteredMatches();
  const filteredFinishedMatches = getFilteredFinishedMatches();
  const displayedMatches = showAll ? filteredMatches : filteredMatches.slice(0, 5);

  // Calculate statistics based on filtered finished matches only
  const calculateStats = (matches: HeadToHeadMatch[]) => {
    let homeWins = 0;
    let awayWins = 0;
    let draws = 0;
    let homeGoalsTotal = 0;
    let awayGoalsTotal = 0;
    let over15 = 0;
    let over25 = 0;
    let over35 = 0;
    let btts = 0;

    matches.forEach((match: HeadToHeadMatch) => {
      const homeGoals = match.goals.home;
      const awayGoals = match.goals.away;

      if (homeGoals === null || awayGoals === null) return;

      // Determine which team is home in this historical match
      const isHomeTeamHome = match.teams.home.id === fixture.teams.home.id;

      if (isHomeTeamHome) {
        homeGoalsTotal += homeGoals;
        awayGoalsTotal += awayGoals;
      } else {
        homeGoalsTotal += awayGoals;
        awayGoalsTotal += homeGoals;
      }

      const totalGoals = homeGoals + awayGoals;

      if (homeGoals > awayGoals) {
        if (isHomeTeamHome) {
          homeWins++;
        } else {
          awayWins++;
        }
      } else if (awayGoals > homeGoals) {
        if (isHomeTeamHome) {
          awayWins++;
        } else {
          homeWins++;
        }
      } else {
        draws++;
      }

      // Calculate over/under stats
      if (totalGoals > 1.5) over15++;
      if (totalGoals > 2.5) over25++;
      if (totalGoals > 3.5) over35++;

      // Both teams to score
      if (homeGoals > 0 && awayGoals > 0) btts++;
    });

    return {
      totalMatches: matches.length,
      homeWins,
      awayWins,
      draws,
      homeGoalsTotal,
      awayGoalsTotal,
      over15,
      over25,
      over35,
      btts
    };
  };

  const stats = calculateStats(filteredFinishedMatches);





  return (
    <div className="bg-card rounded-lg border container-border p-6 mx-2 md:mx-0">
      {/* Header with team logos and simple win stats */}
      <div className="flex items-center justify-center mb-6 px-4 md:px-8">
        <div className="flex items-center justify-between w-full max-w-md">
          {/* Home team logo */}
          <div className="w-10 h-10 md:w-12 md:h-12 rounded-lg border border-border flex items-center justify-center bg-muted/30">
            <Image
              src={fixture.teams.home.logo}
              alt={fixture.teams.home.name}
              width={28}
              height={28}
              className="w-7 h-7 md:w-8 md:h-8 object-contain"
            />
          </div>

          {/* Win/Draw/Loss Stats */}
          <div className="flex items-center gap-3 md:gap-4">
            <div className="text-center">
              <div
                className="w-10 h-10 md:w-12 md:h-12 rounded-full text-white flex items-center justify-center font-bold text-sm md:text-lg"
                style={{ backgroundColor: getTeamWinColors().homeWin }}
              >
                {stats.homeWins}
              </div>
              <div className="text-xs text-muted-foreground mt-1">Wins</div>
            </div>
            <div className="text-center">
              <div
                className="w-10 h-10 md:w-12 md:h-12 rounded-full text-white flex items-center justify-center font-bold text-sm md:text-lg"
                style={{ backgroundColor: getTeamWinColors().draw }}
              >
                {stats.draws}
              </div>
              <div className="text-xs text-muted-foreground mt-1">Draws</div>
            </div>
            <div className="text-center">
              <div
                className="w-10 h-10 md:w-12 md:h-12 rounded-full text-white flex items-center justify-center font-bold text-sm md:text-lg"
                style={{ backgroundColor: getTeamWinColors().awayWin }}
              >
                {stats.awayWins}
              </div>
              <div className="text-xs text-muted-foreground mt-1">Wins</div>
            </div>
          </div>

          {/* Away team logo */}
          <div className="w-10 h-10 md:w-12 md:h-12 rounded-lg border border-border flex items-center justify-center bg-muted/30">
            <Image
              src={fixture.teams.away.logo}
              alt={fixture.teams.away.name}
              width={28}
              height={28}
              className="w-7 h-7 md:w-8 md:h-8 object-contain"
            />
          </div>
        </div>
      </div>

      {/* Summary text */}
      <div className="text-sm text-muted-foreground mb-6 text-center">
        {fixture.teams.home.name} vs {fixture.teams.away.name}&apos;s head to head record shows that in the previous {stats.totalMatches} meetings, {fixture.teams.home.name} has won {stats.homeWins} times, {fixture.teams.away.name} has won {stats.awayWins} times, and {stats.draws} ended in a draw. {fixture.teams.home.name} scored {stats.homeGoalsTotal} goals and {fixture.teams.away.name} scored {stats.awayGoalsTotal} goals in these matches.
      </div>

      {/* Statistics cards */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">{stats.totalMatches > 0 ? Math.round((stats.over15 / stats.totalMatches) * 100) : 0}%</div>
          <div className="text-xs text-muted-foreground">Over 1.5</div>
          <div className="text-xs text-muted-foreground">{stats.over15} / {stats.totalMatches} matches</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: `${stats.totalMatches > 0 ? (stats.over15 / stats.totalMatches) * 100 : 0}%`,
                backgroundColor: getBarColor(stats.totalMatches > 0 ? (stats.over15 / stats.totalMatches) * 100 : 0)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">{stats.totalMatches > 0 ? Math.round((stats.over25 / stats.totalMatches) * 100) : 0}%</div>
          <div className="text-xs text-muted-foreground">Over 2.5</div>
          <div className="text-xs text-muted-foreground">{stats.over25} / {stats.totalMatches} matches</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: `${stats.totalMatches > 0 ? (stats.over25 / stats.totalMatches) * 100 : 0}%`,
                backgroundColor: getBarColor(stats.totalMatches > 0 ? (stats.over25 / stats.totalMatches) * 100 : 0)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">{stats.totalMatches > 0 ? Math.round((stats.over35 / stats.totalMatches) * 100) : 0}%</div>
          <div className="text-xs text-muted-foreground">Over 3.5</div>
          <div className="text-xs text-muted-foreground">{stats.over35} / {stats.totalMatches} matches</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: `${stats.totalMatches > 0 ? (stats.over35 / stats.totalMatches) * 100 : 0}%`,
                backgroundColor: getBarColor(stats.totalMatches > 0 ? (stats.over35 / stats.totalMatches) * 100 : 0)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">{stats.totalMatches > 0 ? Math.round((stats.btts / stats.totalMatches) * 100) : 0}%</div>
          <div className="text-xs text-muted-foreground">BTTS</div>
          <div className="text-xs text-muted-foreground">{stats.btts} / {stats.totalMatches} matches</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: `${stats.totalMatches > 0 ? (stats.btts / stats.totalMatches) * 100 : 0}%`,
                backgroundColor: getBarColor(stats.totalMatches > 0 ? (stats.btts / stats.totalMatches) * 100 : 0)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">26%</div>
          <div className="text-xs text-muted-foreground">Clean Sheets</div>
          <div className="text-xs text-muted-foreground">{fixture.teams.home.name}</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: '26%',
                backgroundColor: getBarColor(26)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">26%</div>
          <div className="text-xs text-muted-foreground">Clean Sheets</div>
          <div className="text-xs text-muted-foreground">{fixture.teams.away.name}</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: '26%',
                backgroundColor: getBarColor(26)
              }}
            />
          </div>
        </div>
      </div>

      {/* Filter Buttons */}
      <div className="flex gap-2 mb-4">
        <button
          onClick={() => setHomeFilter(!homeFilter)}
          className="px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer"
          style={getButtonStyle(homeFilter, resolvedTheme)}
          onMouseEnter={(e) => handleButtonHover(e, homeFilter, true, resolvedTheme)}
          onMouseLeave={(e) => handleButtonHover(e, homeFilter, false, resolvedTheme)}
        >
          Home
        </button>
        <button
          onClick={() => setTournamentFilter(!tournamentFilter)}
          className="px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer"
          style={getButtonStyle(tournamentFilter, resolvedTheme)}
          onMouseEnter={(e) => handleButtonHover(e, tournamentFilter, true, resolvedTheme)}
          onMouseLeave={(e) => handleButtonHover(e, tournamentFilter, false, resolvedTheme)}
        >
          This tournament
        </button>
      </div>

      {/* All Matches */}
      <div className="space-y-4">
        {displayedMatches.map((match: HeadToHeadMatch, index: number) => {
            return (
              <div
                key={match.fixture.id || index}
                className="border-b border-border last:border-b-0 pb-4 last:pb-0 cursor-pointer hover:bg-muted/30 rounded-lg p-2 -m-2 transition-colors"
                onClick={() => router.push(generateFixtureUrl(match))}
              >
                {/* First row: Date and League */}
                <div className="flex items-center justify-between mb-2">
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(match.fixture.date), 'd MMM yyyy')}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {match.league?.name || 'Unknown League'}
                  </div>
                </div>

                {/* Second row: Teams and Score */}
                <div className="flex items-center justify-center">
                  <div className="flex items-center justify-between w-full max-w-md">
                    <div className="flex items-center space-x-2 flex-1 justify-end">
                      <span className="font-medium text-sm">
                        {match.teams.home.name}
                      </span>
                      {match.teams.home.logo && (
                        <Image
                          src={match.teams.home.logo}
                          alt={match.teams.home.name}
                          width={16}
                          height={16}
                          className="w-4 h-4 object-contain"
                        />
                      )}
                    </div>

                    <div className="text-sm font-bold mx-4 min-w-[50px] text-center">
                      {match.goals.home !== null && match.goals.away !== null
                        ? `${match.goals.home} - ${match.goals.away}`
                        : format(new Date(match.fixture.date), 'HH:mm')
                      }
                    </div>

                    <div className="flex items-center space-x-2 flex-1 justify-start">
                      {match.teams.away.logo && (
                        <Image
                          src={match.teams.away.logo}
                          alt={match.teams.away.name}
                          width={16}
                          height={16}
                          className="w-4 h-4 object-contain"
                        />
                      )}
                      <span className="font-medium text-sm">
                        {match.teams.away.name}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
      </div>

      {/* Show More Button */}
      {filteredMatches.length > 5 && (
        <div className="flex justify-center mt-4">
          <button
            onClick={() => setShowAll(!showAll)}
            className="px-4 py-2 text-sm font-medium text-primary hover:text-primary/80 transition-colors"
          >
            {showAll ? 'Show Less' : `Show More (${filteredMatches.length - 5} more)`}
          </button>
        </div>
      )}
    </div>
  );
}
