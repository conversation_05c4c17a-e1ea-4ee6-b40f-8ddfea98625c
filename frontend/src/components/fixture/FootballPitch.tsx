'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { TeamLineup, MatchEvents, Player } from '@/types/fixture';
import { Fixture } from '@/lib/api';

interface FootballPitchProps {
  homeLineup: TeamLineup;
  awayLineup: TeamLineup;
  fixture: Fixture;
  events?: MatchEvents;
}

export default function FootballPitch({
  homeLineup,
  awayLineup,
  fixture,
  events
}: FootballPitchProps) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Get player goals from events
  const getPlayerGoals = (playerId: number, teamId: number): number => {
    if (!events) return 0;
    
    return events.filter(event => 
      event.player?.id === playerId && 
      event.team.id === teamId && 
      event.type === 'Goal' &&
      !event.detail.toLowerCase().includes('own')
    ).length;
  };

  // Get player cards from events
  const getPlayerCards = (playerId: number, teamId: number): { yellow: number; red: number } => {
    if (!events) return { yellow: 0, red: 0 };
    
    const cardEvents = events.filter(event => 
      event.player?.id === playerId && 
      event.team.id === teamId && 
      event.type === 'Card'
    );
    
    const yellow = cardEvents.filter(event => 
      event.detail.toLowerCase().includes('yellow')
    ).length;
    
    const red = cardEvents.filter(event => 
      event.detail.toLowerCase().includes('red')
    ).length;
    
    return { yellow, red };
  };

  const convertGridToPosition = (
    apiGrid: string,
    isHome: boolean,
    lineup: { player: Player }[],
    isMobile: boolean = false
  ) => {
    const [apiColumn, apiRow] = apiGrid.split(':').map(Number);

    const totalPlayersInLine = lineup.filter(p =>
      p.player.grid?.startsWith(`${apiColumn}:`)
    ).length;

    if (isMobile) {
      // Vertical layout for mobile
      const baseXPositionPercentage = (apiRow - 0.5) / totalPlayersInLine * 100;
      const xPositionPercentage = isHome ? baseXPositionPercentage : (100 - baseXPositionPercentage);
      
      const homeColumnPositions = { 1: 97, 2: 85, 3: 73, 4: 62, 5: 55 };
      const awayColumnPositions = { 1: 3,  2: 15, 3: 27, 4: 38, 5: 45 };
      const columnPositions = isHome ? homeColumnPositions : awayColumnPositions;
      const yPositionPercentage = columnPositions[apiColumn as keyof typeof columnPositions] || 50;

      return { x: xPositionPercentage, y: yPositionPercentage };
    } else {
      // Horizontal layout for desktop
      const yPositionPercentage = (apiRow - 0.5) / totalPlayersInLine * 100;

      const homeColumnPositions = { 1: 5, 2: 18, 3: 29, 4: 39, 5: 45 };
      const awayColumnPositions = { 1: 95, 2: 82, 3: 71, 4: 61, 5: 55 };
      const columnPositions = isHome ? homeColumnPositions : awayColumnPositions;
      const xPositionPercentage = columnPositions[apiColumn as keyof typeof columnPositions] || 50;

      return { x: xPositionPercentage, y: yPositionPercentage };
    }
  };

  // Render a player
  const renderPlayer = (player: Player, isHome: boolean) => {
    const goals = getPlayerGoals(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);
    const cards = getPlayerCards(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);

    const homeColors = homeLineup.team.colors?.player;
    const awayColors = awayLineup.team.colors?.player;
    const homeGkColors = homeLineup.team.colors?.goalkeeper;
    const awayGkColors = awayLineup.team.colors?.goalkeeper;

    let playerColor: string;
    let textColor: string;
    
    if (player.pos === 'G') {
      playerColor = isHome ? `#${homeGkColors?.primary || '059669'}` : `#${awayGkColors?.primary || 'dc2626'}`;
      textColor = isHome ? `#${homeGkColors?.number || 'ffffff'}` : `#${awayGkColors?.number || 'ffffff'}`;
    } else {
      playerColor = isHome ? `#${homeColors?.primary || '3b82f6'}` : `#${awayColors?.primary || 'ef4444'}`;
      textColor = isHome ? `#${homeColors?.number || 'ffffff'}` : `#${awayColors?.number || 'ffffff'}`;
    }

    return (
      <div key={player.id} className="flex flex-col items-center">
        <div className="relative w-10 h-10">
          <svg viewBox="0 0 36 36" className="w-full h-full drop-shadow-lg">
            <path
              fill={playerColor}
              stroke="#FFFFFF"
              strokeWidth="1.5"
              d="M 6 5 L 12 5 C 14 3, 22 3, 24 5 L 30 5 L 34 10 L 30 14 L 30 31 L 6 31 L 6 14 L 2 10 L 6 5 Z"
            />
          </svg>
          <div
            className="absolute inset-0 flex items-center justify-center font-bold text-sm"
            style={{ color: textColor }}
          >
            <span>{player.number || '?'}</span>
          </div>
          {goals > 0 && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center text-xs text-white font-bold">
              {goals}
            </div>
          )}
          {cards.red > 0 ? (
            <div className="absolute -top-1.5 -left-1.5 w-3 h-4 bg-red-500 rounded-sm"></div>
          ) : cards.yellow > 0 && (
            <div className="absolute -top-1.5 -left-1.5 w-3 h-4 bg-yellow-400 rounded-sm"></div>
          )}
        </div>
        <div 
          className="mt-1 text-xs text-center text-white font-medium max-w-16 truncate"
          style={{ textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)' }}
        >
          {player.name.split(' ').pop()}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-card rounded-lg border container-border p-4 md:p-6 mx-2 md:mx-0">
      {/* Team Headers */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-3">
          {homeLineup.team.logo ? (
            <Image
              src={homeLineup.team.logo}
              alt={homeLineup.team.name}
              width={32}
              height={32}
              className="w-8 h-8"
            />
          ) : (
            <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
              <span className="text-xs font-bold">{homeLineup.team.name.charAt(0)}</span>
            </div>
          )}
          <div>
            <h4 className="font-semibold">{homeLineup.team.name}</h4>
            {homeLineup.formation && (
              <p className="text-sm text-muted-foreground">{homeLineup.formation}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <div>
            <h4 className="font-semibold text-right">{awayLineup.team.name}</h4>
            {awayLineup.formation && (
              <p className="text-sm text-muted-foreground text-right">{awayLineup.formation}</p>
            )}
          </div>
          {awayLineup.team.logo ? (
            <Image
              src={awayLineup.team.logo}
              alt={awayLineup.team.name}
              width={32}
              height={32}
              className="w-8 h-8"
            />
          ) : (
            <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
              <span className="text-xs font-bold">{awayLineup.team.name.charAt(0)}</span>
            </div>
          )}
        </div>
      </div>

      {/* Football Pitch */}
      <div className="w-full max-w-6xl mx-auto">
        <div
          className="relative w-full shadow-lg overflow-hidden rounded-md"
          style={{
            paddingBottom: isMobile ? '240%' : '64.7%',
            backgroundColor: 'var(--pitch-color, #333333)'
          }}
        >
          {/* Pitch Markings SVG */}
          <svg
            className="absolute inset-0 w-full h-full"
            fill="none"
            style={{ stroke: 'var(--pitch-line-color)' }}
            strokeWidth="2"
          >
            {/* Horizontal layout for desktop */}
            <g className="hidden md:block">
              <svg viewBox="0 0 1050 680" strokeWidth="2">
                {/* Outer Boundary */}
                <rect x="1" y="1" width="1048" height="678" />
                {/* Center Line & Circle */}
                <line x1="525" y1="1" x2="525" y2="679" />
                <circle cx="525" cy="340" r="91.5" />
                <circle cx="525" cy="340" r="3" fill="var(--pitch-line-color)" />
                {/* Left Side */}
                <rect x="1" y="138.5" width="165" height="403" />
                <rect x="1" y="248.5" width="55" height="183" />
                <circle cx="110" cy="340" r="3" fill="rgba(255, 255, 255, 0.5)" />
                <path d="M 166,268 A 91.5,91.5 0 0 1 166,412" />
                {/* Right Side */}
                <rect x="884" y="138.5" width="165" height="403" />
                <rect x="994" y="248.5" width="55" height="183" />
                <circle cx="940" cy="340" r="3" fill="rgba(255, 255, 255, 0.5)" />
                <path d="M 884,268 A 91.5,91.5 0 0 0 884,412" />
              </svg>
            </g>

            {/* UPDATED Vertical layout for mobile */}
            <g className="block md:hidden">
              <svg viewBox="0 0 680 1632" strokeWidth="2">
                {/* Outer Boundary */}
                <rect x="1" y="1" width="678" height="1630" />
                {/* Center Line & Circle */}
                <line x1="1" y1="816" x2="679" y2="816" />
                <circle cx="340" cy="816" r="91.5" />
                <circle cx="340" cy="816" r="3" fill="rgba(255, 255, 255, 0.5)" />
                {/* Top Side (Away) */}
                <rect x="138.5" y="1" width="403" height="180" />
                <rect x="248.5" y="1" width="183" height="86" />
                <circle cx="340" cy="172" r="3" fill="rgba(255, 255, 255, 0.5)" />
                <path d="M 268,181 A 91.5,91.5 0 0 0 412,181" />
                {/* Bottom Side (Home) */}
                <rect x="138.5" y="1451" width="403" height="180" />
                <rect x="248.5" y="1545" width="183" height="86" />
                <circle cx="340" cy="1460" r="3" fill="rgba(255, 255, 255, 0.5)" />
                <path d="M 268,1451 A 91.5,91.5 0 0 1 412,1451" />
              </svg>
            </g>
          </svg>

          {/* Players Positioned Using Grid System */}
          <div className="absolute inset-0">
            {homeLineup.startXI?.map((playerData) => {
              const player = playerData.player;
              if (!player.grid || !homeLineup.startXI) return null;
              const position = convertGridToPosition(player.grid, true, homeLineup.startXI, isMobile);
              return (
                <div
                  key={player.id}
                  className="absolute"
                  style={{
                    left: `${position.x}%`,
                    top: `${position.y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  {renderPlayer(player, true)}
                </div>
              );
            })}

            {awayLineup.startXI?.map((playerData) => {
              const player = playerData.player;
              if (!player.grid || !awayLineup.startXI) return null;
              const position = convertGridToPosition(player.grid, false, awayLineup.startXI, isMobile);
              return (
                <div
                  key={player.id}
                  className="absolute"
                  style={{
                    left: `${position.x}%`,
                    top: `${position.y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  {renderPlayer(player, false)}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}