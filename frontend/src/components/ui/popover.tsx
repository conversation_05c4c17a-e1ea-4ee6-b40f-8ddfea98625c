"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface PopoverProps {
  children: React.ReactNode;
  content: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  align?: "start" | "center" | "end";
  side?: "top" | "right" | "bottom" | "left";
}

export function Popover({ 
  children, 
  content, 
  open, 
  onOpenChange, 
  align = "center",
  side = "bottom" 
}: PopoverProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const popoverRef = React.useRef<HTMLDivElement>(null);
  const triggerRef = React.useRef<HTMLDivElement>(null);

  const isControlled = open !== undefined;
  const openState = isControlled ? open : isOpen;
  const setOpenState = isControlled ? onOpenChange : setIsOpen;

  // Close popover when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // Don't close if clicking on calendar navigation buttons or calendar content
      const isCalendarNavigation = target.closest('[role="button"][aria-label*="Month"]') ||
                                   target.closest('button[aria-label*="Previous Month"]') ||
                                   target.closest('button[aria-label*="Next Month"]') ||
                                   target.closest('button[aria-label*="Go to the Previous Month"]') ||
                                   target.closest('button[aria-label*="Go to the Next Month"]') ||
                                   target.closest('.rdp-nav') ||
                                   target.closest('[class*="button_previous"]') ||
                                   target.closest('[class*="button_next"]');

      if (
        popoverRef.current &&
        triggerRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node) &&
        !isCalendarNavigation
      ) {
        setOpenState?.(false);
      }
    };

    if (openState) {
      document.addEventListener("click", handleClickOutside);
      return () => document.removeEventListener("click", handleClickOutside);
    }
  }, [openState, setOpenState]);

  // Close popover on escape key
  React.useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && openState) {
        setOpenState?.(false);
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [openState, setOpenState]);

  const getPositionClasses = () => {
    const positions = {
      top: "bottom-full mb-2",
      bottom: "top-full mt-2",
      left: "right-full mr-2",
      right: "left-full ml-2",
    };

    const alignments = {
      start: side === "top" || side === "bottom" ? "left-0" : "top-0",
      center: side === "top" || side === "bottom" ? "left-1/2 -translate-x-1/2" : "top-1/2 -translate-y-1/2",
      end: side === "top" || side === "bottom" ? "right-0" : "bottom-0",
    };

    return `${positions[side]} ${alignments[align]}`;
  };

  return (
    <div className="relative">
      <div
        ref={triggerRef}
        onClick={() => setOpenState?.(!openState)}
        className="cursor-pointer"
      >
        {children}
      </div>
      
      {openState && (
        <div
          ref={popoverRef}
          className={cn(
            "absolute z-50 bg-card border border-border rounded-lg shadow-lg p-4",
            getPositionClasses()
          )}
        >
          {content}
        </div>
      )}
    </div>
  );
}
