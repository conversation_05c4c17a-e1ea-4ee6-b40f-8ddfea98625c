import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/ThemeProvider";

// Optimize font loading with fallback and reduced weight
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true,
  weight: ['400', '500', '600'], // Only load needed weights
  fallback: ['system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
});

export const metadata: Metadata = {
  title: "KickoffScore - Live Football Scores & Predictions",
  description: "Get live football scores, fixtures, and expert predictions for all major leagues and tournaments.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={inter.variable}>
      <head>
        {/* Critical resource hints */}
        <link rel="dns-prefetch" href="https://media.api-sports.io" />
        <link rel="preconnect" href="https://media.api-sports.io" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://api.kickoffpredictions.com" />
        <link rel="preconnect" href="https://api.kickoffpredictions.com" crossOrigin="anonymous" />

        {/* Preload critical resources */}
        <link rel="preload" href="/favicon.ico" as="image" />

        {/* Critical CSS - inline for immediate rendering */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Critical font fallback to prevent FOIT */
            body {
              font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 0;
              line-height: 1.5;
            }

            /* Critical layout to prevent CLS */
            .mobile-nav-scroll {
              scrollbar-width: none;
              -ms-overflow-style: none;
              min-height: 32px;
            }
            .mobile-nav-scroll::-webkit-scrollbar { display: none; }

            /* Critical button dimensions */
            .mobile-nav-button {
              min-width: 32px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            /* Prevent layout shift */
            * {
              box-sizing: border-box;
            }

            /* Critical loading states */
            .loading-skeleton {
              background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
              background-size: 200% 100%;
              animation: loading 1.5s infinite;
            }

            @keyframes loading {
              0% { background-position: 200% 0; }
              100% { background-position: -200% 0; }
            }

            /* Critical viewport */
            html {
              scroll-behavior: smooth;
            }
          `
        }} />
      </head>
      <body
        className={`${inter.className} font-sans antialiased`}
        suppressHydrationWarning={true}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange={false}
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
