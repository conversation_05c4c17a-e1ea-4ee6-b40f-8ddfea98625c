@import "tailwindcss";

:root {
--background: #FAFAFA;
--foreground: #171717;
--card: #FFFFFF;
--card-foreground: #171717;
--primary: #00c758;
--primary-foreground: #FFFFFF;
--secondary: #f3f4f6;
--secondary-foreground: #374151;
--muted: rgb(246 245 245);
--muted-foreground: #6b7280;
--accent: #f3f4f6;
--accent-foreground: #374151;
--destructive: #ef4444;
--destructive-foreground: #FFFFFF;
--border: rgba(240, 240, 240, 1.0);
--input: #e5e7eb;
--ring: #00c758;
--container-2x: 45rem;
--radius-lg: 1rem;
--pitch-color: #227237;
--pitch-line-color: rgba(255, 255, 255, 0.5);
}

.dark {
--background: #0a0a0a;
--foreground: #ededed;
--card: #1d1d1d;
--card-foreground: #ededed;
--primary: #00c758;
--primary-foreground: #FFFFFF;
--secondary: #262626;
--secondary-foreground: #d4d4d8;
--muted: #262626;
--muted-foreground: #a1a1aa;
--accent: #262626;
--accent-foreground: #d4d4d8;
--destructive: #ef4444;
--destructive-foreground: #FFFFFF;
--border: rgba(64, 64, 64, 0.5);
--input: #404040;
--ring: #00c758;
--container-2x: 45rem;
--radius-lg: 1rem;
--pitch-color: #2C2C2C;
--pitch-line-color: #343434;
}

@theme inline {
--color-background: var(--background);
--color-foreground: var(--foreground);
--color-card: var(--card);
--color-card-foreground: var(--card-foreground);
--color-primary: var(--primary);
--color-primary-foreground: var(--primary-foreground);
--color-secondary: var(--secondary);
--color-secondary-foreground: var(--secondary-foreground);
--color-muted: var(--muted);
--color-muted-foreground: var(--muted-foreground);
--color-accent: var(--accent);
--color-accent-foreground: var(--accent-foreground);
--color-destructive: var(--destructive);
--color-destructive-foreground: var(--destructive-foreground);
--color-border: var(--border);
--color-input: var(--input);
--color-ring: var(--ring);
--font-sans: var(--font-inter), 'Inter', system-ui, -apple-system, sans-serif;
--container-2x: var(--container-2x);
--radius-lg: var(--radius-lg);
}

body {
background: var(--background);
color: var(--foreground);
font-family: var(--font-inter), 'Inter', system-ui, -apple-system, 'Segoe UI', 'Roboto', sans-serif;
font-feature-settings: 'liga' 1, 'calt' 1; /* Enable ligatures and contextual alternates */
font-display: swap; /* Improve font loading performance */
}

/* Critical font loading optimization */
@font-face {
  font-family: 'Inter-fallback';
  src: local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont');
  font-display: swap;
  ascent-override: 90%;
  descent-override: 22%;
  line-gap-override: 0%;
}

/* Optimize font rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Hide scrollbars for mobile navigation to prevent CLS */
.mobile-nav-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.mobile-nav-scroll::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Visually hidden but accessible to screen readers and SEO */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* CLS Prevention - Ensure images maintain layout */
img {
/* Prevent layout shifts during image loading */
display: block;
max-width: 100%;
height: 100%;
}

/* Aspect ratio containers for consistent image sizing */
.aspect-square {
aspect-ratio: 1 / 1;
}

.aspect-flag {
aspect-ratio: 3 / 2;
}

/* Prevent layout shifts from font loading */
.font-display-swap {
font-display: swap;
}

/* Consistent form colors throughout the application */
:root {
  --form-win-color: #45C325;
  --form-draw-color: #5A5A5A;
  --form-loss-color: #DD0000;
}

/* Form indicator utility classes */
.form-win {
  background-color: var(--form-win-color);
  color: white;
}

.form-draw {
  background-color: var(--form-draw-color);
  color: white;
}

.form-loss {
  background-color: var(--form-loss-color);
  color: white;
}


/* Override Tailwind's transition classes to remove unwanted properties */
.transition-all {
transition-property: none !important;
transition-timing-function: none !important;
transition-duration: 0s !important;
}

.transition-colors {
transition-property: none !important;
transition-timing-function: none !important;
transition-duration: 0s !important;
}

/* Skeleton loading improvements */
.skeleton {
background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
background-size: 200% 100%;
animation: loading 1.5s infinite;
}

@keyframes loading {
0% {
background-position: -200% 0;
}
100% {
background-position: 200% 0;
}
}

/* Mobile Bottom Navigation Styles - Floating Design */
.mobile-bottom-nav-floating {
  position: fixed;
  bottom: 16px;
  left: 16px;
  right: 16px;
  z-index: 50;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 20px;
  padding: 8px 16px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    0 8px 20px -4px rgba(0, 0, 0, 0.1),
    0 4px 8px -4px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  width: auto;
  height: 56px;
}

/* Dark theme enhancements */
.dark .mobile-bottom-nav-floating {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 8px 10px -6px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Container borders - transparent in dark theme */
.container-border {
  border-color: var(--border);
}

.dark .container-border {
  border-color: transparent;
}

@media (min-width: 768px) {
  .mobile-bottom-nav-floating {
    display: none;
  }
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  position: relative;
  flex: 1;
  border-radius: 12px;
  height: 40px;
}

/* Only show underline for active items */
.mobile-nav-item::before {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 0px;
  height: 2px;
  background: var(--accent-color, #1A3050);
  border-radius: 1px;
}

.mobile-nav-item.active::before {
  width: var(--lineWidth, 0px);
}

/* Light theme active state */
.mobile-nav-item.active {
  background: rgba(26, 48, 80, 0.08);
}

/* Dark theme styles */
.dark .mobile-nav-item::before {
  background: #00c758;
}

.dark .mobile-nav-item.active {
  background: rgba(0, 199, 88, 0.1);
}

.mobile-nav-icon {
  margin-bottom: 2px;
}

.mobile-nav-item.active .mobile-nav-icon {
  animation: iconBounce 0.6s ease;
}

.mobile-nav-icon .icon {
  width: 18px;
  height: 18px;
  color: var(--muted-foreground);
}

/* Light theme active icon */
.mobile-nav-item.active .mobile-nav-icon .icon {
  color: var(--accent-color, #1A3050);
}

/* Dark theme icon colors */
.dark .mobile-nav-icon .icon {
  color: #ffffff;
}

.dark .mobile-nav-item.active .mobile-nav-icon .icon {
  color: #00c758;
}

.mobile-nav-text {
  font-size: 10px;
  font-weight: 500;
  color: var(--muted-foreground);
  white-space: nowrap;
  margin-top: 1px;
}

/* Light theme active text */
.mobile-nav-text.active {
  color: var(--accent-color, #1A3050);
  font-weight: 600;
}

/* Dark theme text colors */
.dark .mobile-nav-text {
  color: #ffffff;
}

.dark .mobile-nav-text.active {
  color: #00c758;
  font-weight: 600;
}

@keyframes iconBounce {
  0%, 100% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(-3px);
  }
  40% {
    transform: translateY(0);
  }
  60% {
    transform: translateY(-1px);
  }
  80% {
    transform: translateY(0);
  }
}

/* Blinking animation for live fixture timer apostrophe */
@keyframes blinkApostrophe {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.live-timer-apostrophe {
  animation: blinkApostrophe 1s infinite;
}

/* Hide scrollbars */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Add bottom padding on mobile to account for floating bottom navigation */
@media (max-width: 767px) {
  body {
    padding-bottom: 80px;
  }
}