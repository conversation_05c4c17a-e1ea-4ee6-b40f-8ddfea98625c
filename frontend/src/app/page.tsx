'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import MainContent from '@/components/MainContent';
import RightSidebar from '@/components/RightSidebar';
import ServerFooter from '@/components/ServerFooter';
import { LazyMobileBottomNav, LazyMobileLeaguesView, LazyMobileNewsView } from '@/components/LazyComponents';
import PerformanceMonitor from '@/components/PerformanceMonitor';

export default function Home() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false); // Start with false to prevent hydration mismatch
  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(false); // Right sidebar state
  const [selectedLeagueId, setSelectedLeagueId] = useState<number | null>(null);
  const [mobileActiveTab, setMobileActiveTab] = useState('matches');

  // Handle responsive sidebars
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
        setIsRightSidebarOpen(false);
      } else if (window.innerWidth < 1200) {
        setIsSidebarOpen(true);
        setIsRightSidebarOpen(false);
      } else {
        setIsSidebarOpen(true);
        setIsRightSidebarOpen(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleRightSidebar = () => {
    setIsRightSidebarOpen(!isRightSidebarOpen);
  };

  const handleLeagueSelect = (leagueId: number) => {
    setSelectedLeagueId(selectedLeagueId === leagueId ? null : leagueId);
    // On mobile, switch back to matches view after league selection
    if (typeof window !== 'undefined' && window.innerWidth < 768) {
      setMobileActiveTab('matches');
    }
  };

  const handleClearLeagueFilter = () => {
    setSelectedLeagueId(null);
  };

  const handleMobileTabChange = (tabId: string) => {
    setMobileActiveTab(tabId);
  };

  // Note: Removed the page-level skeleton loading to prevent double skeleton loading
  // Individual components (MainContent, Sidebar) handle their own loading states

  return (
    <div className="min-h-screen bg-background">
      <Header onToggleRightSidebar={toggleRightSidebar} />

      {/* SEO H1 Title - Hidden visually but accessible to search engines */}
      <h1 className="sr-only">
        Kickoffscore - livescore and schedule for Premier League, Champions League
      </h1>

      <div className="flex justify-center px-0 md:px-4">
        <div className="flex max-w-7xl w-full">
          {/* Left Sidebar */}
          <div className={`${isSidebarOpen ? 'block' : 'hidden'} md:block`}>
            <Sidebar
              onLeagueSelect={handleLeagueSelect}
              selectedLeagueId={selectedLeagueId}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Desktop: Always show MainContent */}
            <div className="hidden md:block">
              <MainContent
                selectedLeagueId={selectedLeagueId}
                onClearLeagueFilter={handleClearLeagueFilter}
              />
            </div>

            {/* Mobile: Show different views based on active tab */}
            <div className="md:hidden">
              {mobileActiveTab === 'matches' && (
                <MainContent
                  selectedLeagueId={selectedLeagueId}
                  onClearLeagueFilter={handleClearLeagueFilter}
                />
              )}
              {mobileActiveTab === 'leagues' && (
                <LazyMobileLeaguesView
                  onLeagueSelect={handleLeagueSelect}
                  selectedLeagueId={selectedLeagueId}
                />
              )}
              {mobileActiveTab === 'news' && <LazyMobileNewsView />}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className={`${isRightSidebarOpen ? 'block' : 'hidden'} xl:block`}>
            <RightSidebar />
          </div>
        </div>
      </div>

      <ServerFooter />

      {/* Mobile Bottom Navigation */}
      <LazyMobileBottomNav
        activeTab={mobileActiveTab}
        onTabChange={handleMobileTabChange}
        accentColor="#1A3050"
      />

      {/* Performance Monitoring */}
      <PerformanceMonitor />
    </div>
  );
}
