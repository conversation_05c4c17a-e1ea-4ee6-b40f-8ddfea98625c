# KickoffScore Frontend

A modern, responsive React frontend for the KickoffScore football prediction platform built with Next.js 15, TypeScript, and Tailwind CSS.

## Features

### 🏟️ Core Features
- **Live Fixtures**: Real-time football match data with live score updates
- **League Navigation**: Browse fixtures by top leagues or all available leagues
- **Date Navigation**: Navigate through different dates to view past and upcoming fixtures
- **Search Functionality**: Search for teams, leagues, and countries
- **Responsive Design**: Mobile-first design that works on all devices
- **Real-time Updates**: WebSocket integration for live match updates

### 🎨 Design
- **Clean UI**: White containers (#FFFFFF) on light gray background (#FAFAFA)
- **Mobile Responsive**: Collapsible sidebar on mobile devices
- **Modern Components**: Built with Tailwind CSS for consistent styling
- **Loading States**: Skeleton loading and error handling
- **Interactive Elements**: Hover effects and smooth transitions

### 🔧 Technical Features
- **Next.js 15**: Latest version with App Router and Turbopack
- **TypeScript**: Full type safety throughout the application
- **Socket.IO**: Real-time WebSocket connections for live updates
- **API Integration**: RESTful API integration with the KickoffScore backend
- **Error Handling**: Comprehensive error handling and fallbacks
- **Performance**: Optimized with React hooks and efficient state management

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- KickoffScore backend running on port 3000

### Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   The `.env.local` file is already configured for local development:
   ```
   NEXT_PUBLIC_API_URL=http://localhost:3000
   NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
   NEXT_PUBLIC_API_KEY=1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**:
   Navigate to [http://localhost:3001](http://localhost:3001)

### Production Build

```bash
# Build for production
npm run build

# Start production server
npm start
```

## API Integration

The frontend integrates with the KickoffScore backend API:

### Endpoints Used
- `GET /api/leagues` - Fetch available leagues
- `GET /api/fixtures` - Fetch fixtures by date
- `GET /api/fixtures/live` - Fetch live fixtures
- `GET /api/fixtures/search` - Search fixtures

### WebSocket Events
- `fixture-update` - Live fixture updates
- `fixture-goal` - Goal events
- `fixture-card` - Card events
- `fixture-status` - Status changes

## Components

### Header
- Logo and branding
- Search functionality
- Mobile menu toggle
- Responsive navigation

### Sidebar
- Top leagues section (collapsible)
- All leagues section (collapsible)
- League filtering
- Loading states and error handling

### MainContent
- Date navigation
- Top tips section
- Search bar
- Fixtures list with live updates
- League filtering
- Loading and error states

### Footer
- Company information
- Quick links
- Social media links
- Live data indicator

## Development

### Available Scripts
- `npm run dev` - Start development server on port 3001
- `npm run build` - Build for production
- `npm run start` - Start production server on port 3001
- `npm run lint` - Run ESLint

### Code Style
- TypeScript for type safety
- ESLint for code quality
- Consistent component structure
- Custom hooks for reusable logic

## Deployment

The frontend can be deployed to any platform that supports Next.js. Make sure to update environment variables for production deployment.

## License

This project is part of the KickoffScore platform.
