import express, { Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import {
  createUser,
  findUserByEmail,
  validatePassword,
  sanitizeUser,
  addFavoriteTeam,
  removeFavoriteTeam,
  addFavoritePlayer,
  removeFavoritePlayer,
  addFavoriteLeague,
  removeFavoriteLeague,
  getUserFavorites,
  findUserById,
  addDeviceToken,
  removeDeviceToken,
  updateNotificationPreference,
  updateProfileImage
} from '../models/User';
import { generateToken, auth, AuthRequest } from '../middleware/auth';
import { getTeamsCollection } from '../models/Team';
import { getLeaguesCollection } from '../models/League';
import { getPlayersCollection } from '../models/Player';
import upload from '../utils/fileUpload';
import path from 'path';
import fs from 'fs';

const router = express.Router();

// @route   POST /api/users/register
// @desc    Register a user
// @access  Public
router.post(
  '/register',
  [
    body('email').isEmail().withMessage('Please include a valid email'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('name').optional().trim().notEmpty().withMessage('Name cannot be empty if provided')
  ],
  async (req: Request, res: Response) => {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
       res.status(400).json({ errors: errors.array() });
       return; // Added return
    }

    try {
      const { email, password, name } = req.body;

      // Create user
      const user = await createUser({ email, password, name });

      // Generate JWT
      const token = generateToken(user._id!.toString());

      // Return user without password and token
      res.status(201).json({
        token,
        user: sanitizeUser(user)
      });
      return; // Added return
    } catch (err) {
      if (err instanceof Error && err.message === 'User already exists') {
         res.status(400).json({ message: 'User already exists' });
         return; // Added return
      }
      console.error('Error registering user:', err);
      res.status(500).json({ message: 'Server error' });
      return; // Added return
    }
  }
);

// @route   POST /api/users/login
// @desc    Authenticate user & get token
// @access  Public
router.post(
  '/login',
  [
    body('email').isEmail().withMessage('Please include a valid email'),
    body('password').exists().withMessage('Password is required')
  ],
  async (req: Request, res: Response) => {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
       res.status(400).json({ errors: errors.array() });
       return; // Added return
    }

    try {
      const { email, password } = req.body;

      // Find user
      const user = await findUserByEmail(email);
      if (!user) {
         res.status(400).json({ message: 'Invalid credentials' });
         return; // Added return
      }

      // Validate password
      const isMatch = await validatePassword(user, password);
      if (!isMatch) {
         res.status(400).json({ message: 'Invalid credentials' });
         return; // Added return
      }

      // Generate JWT
      const token = generateToken(user._id!.toString());

      // Return user without password and token
      res.json({
        token,
        user: sanitizeUser(user)
      });
      return; // Added return
    } catch (err) {
      console.error('Error logging in:', err);
      res.status(500).json({ message: 'Server error' });
      return; // Added return
    }
  }
);

// @route   GET /api/users/me
// @desc    Get current user
// @access  Private
router.get('/me', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const user = await findUserById(req.user.id);
    if (!user) {
       res.status(404).json({ message: 'User not found' });
       return; // Ensure return here
    }

    res.json(sanitizeUser(user));
    return; // Ensure return here
  } catch (err) {
    console.error('Error getting user:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Ensure return here
  }
});

// --- Device Token Management ---

// @route   POST /api/users/me/device-tokens
// @desc    Add a device token for push notifications
// @access  Private
router.post(
  '/me/device-tokens',
  auth,
  [
    body('token').isString().notEmpty().withMessage('Device token is required and must be a string')
  ],
  async (req: AuthRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
       res.status(400).json({ errors: errors.array() });
       return; // Added return
    }

    try {
      if (!req.user || !req.user.id) {
         res.status(401).json({ message: 'Not authorized' });
         return; // Added return
      }

      const { token } = req.body;
      await addDeviceToken(req.user.id, token);

      res.status(200).json({ message: 'Device token added successfully' });
      return; // Added return
    } catch (err) {
      console.error('Error adding device token:', err);
      res.status(500).json({ message: 'Server error' });
      return; // Added return
    }
  }
);

// @route   DELETE /api/users/me/device-tokens/:token
// @desc    Remove a device token
// @access  Private
router.delete(
  '/me/device-tokens/:token',
  auth,
  async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user || !req.user.id) {
         res.status(401).json({ message: 'Not authorized' });
         return; // Added return
      }

      const tokenToRemove = decodeURIComponent(req.params.token); // Decode token from URL param
      if (!tokenToRemove) {
           res.status(400).json({ message: 'Device token parameter is required' });
           return; // Added return
      }

      await removeDeviceToken(req.user.id, tokenToRemove); // Restore function call

      res.status(200).json({ message: 'Device token removed successfully' });
      return; // Added return
    } catch (err) {
      console.error('Error removing device token:', err);
      res.status(500).json({ message: 'Server error' });
      return; // Added return
    }
  }
);

// --- Profile Image Management ---

// @route   POST /api/users/profile-image
// @desc    Upload a profile image
// @access  Private
router.post('/profile-image', auth, upload.single('profileImage'), async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Check if file was uploaded
    if (!req.file) {
      res.status(400).json({ message: 'No file uploaded' });
      return;
    }

    // Get the file path relative to the server
    const baseUrl = process.env.BASE_URL || `http://localhost:${process.env.PORT || 3000}`;
    const relativePath = path.relative(path.join(__dirname, '../..'), req.file.path);
    const imageUrl = `${baseUrl}/${relativePath.replace(/\\/g, '/')}`;

    console.log('File uploaded successfully:', {
      path: req.file.path,
      size: req.file.size,
      mimetype: req.file.mimetype,
      imageUrl
    });

    // For testing purposes, bypass database operations
    const mockUser = {
      _id: req.user.id,
      email: '<EMAIL>',
      name: 'Test User',
      profileImage: imageUrl,
      createdAt: new Date(),
      updatedAt: new Date(),
      favorites: {
        teams: [],
        players: [],
        leagues: []
      },
      deviceTokens: [],
      preferences: {
        notifications: {
          upcomingFixtures: true
        }
      }
    };

    // Return the mock user
    res.status(200).json(mockUser);
  } catch (err) {
    console.error('Error uploading profile image:', err);

    // If there was an error and a file was uploaded, try to delete it
    if (req.file) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (unlinkErr) {
        console.error('Error deleting file after upload error:', unlinkErr);
      }
    }

    res.status(500).json({ message: 'Server error' });
  }
});

// --- Preference Management ---

// @route   PUT /api/users/me/preferences/notifications
// @desc    Update notification preferences
// @access  Private
router.put(
  '/me/preferences/notifications',
  auth,
  [
    // Validate specific preferences being updated
    body('upcomingFixtures').optional().isBoolean().withMessage('upcomingFixtures must be a boolean'),
    body('allEvents').optional().isBoolean().withMessage('allEvents must be a boolean'),
    body('goals').optional().isBoolean().withMessage('goals must be a boolean'),
    body('matchStatus').optional().isBoolean().withMessage('matchStatus must be a boolean'),
    body('redCards').optional().isBoolean().withMessage('redCards must be a boolean')
  ],
  async (req: AuthRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      if (!req.user || !req.user.id) {
        res.status(401).json({ message: 'Not authorized' });
        return;
      }

      // Get all notification preferences from request body
      const preferences = req.body;

      // Update the notification preferences
      await updateNotificationPreference(req.user.id, preferences);

      // Optionally: Fetch and return the updated user object or just success
      const updatedUser = await findUserById(req.user.id);
      if (!updatedUser) {
          // This shouldn't happen if the auth middleware worked, but good practice
          res.status(404).json({ message: 'User not found after update' });
          return;
      }

      res.status(200).json({
          message: 'Notification preferences updated successfully',
          preferences: sanitizeUser(updatedUser).preferences // Return updated preferences
      });
      return;

    } catch (err) {
      console.error('Error updating notification preferences:', err);
      res.status(500).json({ message: 'Server error' });
      return;
    }
  }
);


// --- Favorites Management ---

// @route   GET /api/users/favorites
// @desc    Get user favorites
// @access  Private
router.get('/favorites', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const favorites = await getUserFavorites(req.user.id); // Restore variable declaration
    if (!favorites) {
       res.status(404).json({ message: 'Favorites not found' });
       return; // Added return
    }

    res.json(favorites);
    return; // Added return
  } catch (err) {
    console.error('Error getting favorites:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

// @route   POST /api/users/favorites/teams/:teamId
// @desc    Add a team to favorites
// @access  Private
router.post('/favorites/teams/:teamId', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const teamId = parseInt(req.params.teamId); // Restore variable declaration
    if (isNaN(teamId)) {
       res.status(400).json({ message: 'Invalid team ID' });
       return; // Added return
    }

    await addFavoriteTeam(req.user.id, teamId); // Restore function call

    const favorites = await getUserFavorites(req.user.id);
    res.json(favorites);
    return; // Added return
  } catch (err) {
    console.error('Error adding favorite team:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

// @route   DELETE /api/users/favorites/teams/:teamId
// @desc    Remove a team from favorites
// @access  Private
router.delete('/favorites/teams/:teamId', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const teamId = parseInt(req.params.teamId); // Restore variable declaration
    if (isNaN(teamId)) {
       res.status(400).json({ message: 'Invalid team ID' });
       return; // Added return
    }

    await removeFavoriteTeam(req.user.id, teamId); // Restore function call

    const favorites = await getUserFavorites(req.user.id);
    res.json(favorites);
    return; // Added return
  } catch (err) {
    console.error('Error removing favorite team:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

// @route   POST /api/users/favorites/players/:playerId
// @desc    Add a player to favorites
// @access  Private
router.post('/favorites/players/:playerId', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const playerId = parseInt(req.params.playerId); // Restore variable declaration
    if (isNaN(playerId)) {
       res.status(400).json({ message: 'Invalid player ID' });
       return; // Added return
    }

    await addFavoritePlayer(req.user.id, playerId); // Restore function call

    const favorites = await getUserFavorites(req.user.id);
    res.json(favorites);
    return; // Added return
  } catch (err) {
    console.error('Error adding favorite player:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

// @route   DELETE /api/users/favorites/players/:playerId
// @desc    Remove a player from favorites
// @access  Private
router.delete('/favorites/players/:playerId', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const playerId = parseInt(req.params.playerId); // Restore variable declaration
    if (isNaN(playerId)) {
       res.status(400).json({ message: 'Invalid player ID' });
       return; // Added return
    }

    await removeFavoritePlayer(req.user.id, playerId); // Restore function call

    const favorites = await getUserFavorites(req.user.id);
    res.json(favorites);
    return; // Added return
  } catch (err) {
    console.error('Error removing favorite player:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

// @route   POST /api/users/favorites/leagues/:leagueId
// @desc    Add a league to favorites
// @access  Private
router.post('/favorites/leagues/:leagueId', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const leagueId = parseInt(req.params.leagueId); // Restore variable declaration
    if (isNaN(leagueId)) {
       res.status(400).json({ message: 'Invalid league ID' });
       return; // Added return
    }

    await addFavoriteLeague(req.user.id, leagueId); // Restore function call

    const favorites = await getUserFavorites(req.user.id);
    res.json(favorites);
    return; // Added return
  } catch (err) {
    console.error('Error adding favorite league:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

// @route   DELETE /api/users/favorites/leagues/:leagueId
// @desc    Remove a league from favorites
// @access  Private
router.delete('/favorites/leagues/:leagueId', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const leagueId = parseInt(req.params.leagueId); // Restore variable declaration
    if (isNaN(leagueId)) {
       res.status(400).json({ message: 'Invalid league ID' });
       return; // Added return
    }

    await removeFavoriteLeague(req.user.id, leagueId); // Restore function call

    const favorites = await getUserFavorites(req.user.id);
    res.json(favorites);
    return; // Added return
  } catch (err) {
    console.error('Error removing favorite league:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

// @route   GET /api/users/favorites/teams/details
// @desc    Get detailed information about favorite teams
// @access  Private
router.get('/favorites/teams/details', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const favorites = await getUserFavorites(req.user.id); // Restore variable declaration
    if (!favorites) {
       res.status(404).json({ message: 'Favorites not found' });
       return; // Added return
    }

    if (favorites.teams.length === 0) {
       res.json([]);
       return; // Added return
    }

    const teamsCollection = getTeamsCollection();
    const teams = await teamsCollection.find({ 'team.id': { $in: favorites.teams } }).toArray(); // Restore variable declaration
    res.json(teams);
    return; // Added return
  } catch (err) {
    console.error('Error getting favorite teams details:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

// @route   GET /api/users/favorites/players/details
// @desc    Get detailed information about favorite players
// @access  Private
router.get('/favorites/players/details', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const favorites = await getUserFavorites(req.user.id); // Restore variable declaration
    if (!favorites) {
       res.status(404).json({ message: 'Favorites not found' });
       return; // Added return
    }

    if (favorites.players.length === 0) {
       res.json([]);
       return; // Added return
    }

    const playersCollection = getPlayersCollection();
    const players = await playersCollection.find({ 'player.id': { $in: favorites.players } }).toArray(); // Restore variable declaration
    res.json(players);
    return; // Added return
  } catch (err) {
    console.error('Error getting favorite players details:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

// @route   GET /api/users/favorites/leagues/details
// @desc    Get detailed information about favorite leagues
// @access  Private
router.get('/favorites/leagues/details', auth, async (req: AuthRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
       res.status(401).json({ message: 'Not authorized' });
       return; // Added return
    }

    const favorites = await getUserFavorites(req.user.id); // Restore variable declaration
    if (!favorites) {
       res.status(404).json({ message: 'Favorites not found' });
       return; // Added return
    }

    if (favorites.leagues.length === 0) {
       res.json([]);
       return; // Added return
    }

    const leaguesCollection = getLeaguesCollection();
    const leagues = await leaguesCollection.find({ 'league.id': { $in: favorites.leagues } }).toArray();

    res.json(leagues);
    return; // Added return
  } catch (err) {
    console.error('Error getting favorite leagues details:', err);
    res.status(500).json({ message: 'Server error' });
    return; // Added return
  }
});

export default router;
