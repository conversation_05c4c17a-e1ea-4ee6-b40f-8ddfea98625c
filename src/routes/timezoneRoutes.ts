import express, { Router, <PERSON>questH<PERSON><PERSON> } from 'express';
import { fetchTimezones } from '../services/apiFootball';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();
const TIMEZONES_CACHE_KEY = 'timezones:all';
const CACHE_TTL_SECONDS = 60 * 60 * 24 * 7; // Cache for 1 week (timezones rarely change)

// Define the handler function with RequestHandler type
const getTimezonesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();

    try {
        // 1. Try to get from cache first
        const cachedTimezones = await redisClient.get(TIMEZONES_CACHE_KEY);
        if (cachedTimezones) {
            console.log('Returning cached timezones');
            res.status(200).json(JSON.parse(cachedTimezones));
            return;
        }

        // 2. Fetch from API if not in cache
        console.log('Fetching timezones from API...');
        const timezones = await fetchTimezones();

        // 3. Cache the result
        await redisClient.setex(TIMEZONES_CACHE_KEY, CACHE_TTL_SECONDS, JSON.stringify(timezones));

        // 4. Return the timezones
        res.status(200).json(timezones);

    } catch (error) {
        console.error('Error fetching timezones:', error);
        res.status(500).json({ message: 'Failed to fetch timezones' });
    }
};

// GET /api/timezones - Fetches all available timezones (with caching)
router.get('/', getTimezonesHandler);

export default router;
