import express, { Router, RequestHandler } from 'express';
import { getTransfersCollection } from '../models/Transfer';
import { getRedisClient } from '../config/redis';
import { Filter } from 'mongodb';

const router: Router = express.Router();
const TRANSFERS_CACHE_KEY = 'transfers:';

// Cache durations
const CACHE_TTL_HOUR = 60 * 60;
const CACHE_TTL_DAY = 60 * 60 * 24;
const CACHE_TTL_WEEK = CACHE_TTL_DAY * 7;

// GET /api/transfers/teams?team=X - Get team transfers
const getTeamTransfersHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { team } = req.query;

    // Parse and validate parameters
    const teamId = team ? parseInt(team as string) : undefined;

    // Validate parameter is provided
    if (!teamId) {
        res.status(400).json({ message: 'Team ID is required.' });
        return;
    }

    // Validate parameter type
    if (isNaN(teamId)) {
        res.status(400).json({ message: 'Invalid team ID.' });
        return;
    }

    // Build cache key
    const cacheKey = `${TRANSFERS_CACHE_KEY}team:${teamId}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving team transfers from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Build MongoDB filter
        const filter: Filter<any> = {
            '$or': [
                { 'transfers.teams.in.id': teamId },
                { 'transfers.teams.out.id': teamId }
            ]
        };

        // Fetch from database
        const collection = getTransfersCollection();
        const transfers = await collection.find(filter).toArray();

        // Transform to match API response format
        const response = transfers.map(transfer => ({
            player: transfer.player,
            update: transfer.update,
            transfers: transfer.transfers
        }));

        // Cache the result
        const cacheTTL = CACHE_TTL_WEEK; // Transfers don't change often
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(response));

        if (transfers.length === 0) {
            res.status(404).json({ message: 'No transfers found for this team' });
            return;
        }

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching team transfers:', error);
        res.status(500).json({ message: 'Failed to fetch team transfers' });
    }
};

// GET /api/transfers/players?player=X - Get player transfers
const getPlayerTransfersHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { player } = req.query;

    // Parse and validate parameters
    const playerId = player ? parseInt(player as string) : undefined;

    // Validate parameter is provided
    if (!playerId) {
        res.status(400).json({ message: 'Player ID is required.' });
        return;
    }

    // Validate parameter type
    if (isNaN(playerId)) {
        res.status(400).json({ message: 'Invalid player ID.' });
        return;
    }

    // Build cache key
    const cacheKey = `${TRANSFERS_CACHE_KEY}player:${playerId}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving player transfers from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Build MongoDB filter
        const filter: Filter<any> = { 'player.id': playerId };

        // Fetch from database
        const collection = getTransfersCollection();
        const transfers = await collection.find(filter).toArray();

        // Transform to match API response format
        const response = transfers.map(transfer => ({
            player: transfer.player,
            update: transfer.update,
            transfers: transfer.transfers
        }));

        // Cache the result
        const cacheTTL = CACHE_TTL_WEEK; // Transfers don't change often
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(response));

        if (transfers.length === 0) {
            res.status(404).json({ message: 'No transfers found for this player' });
            return;
        }

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching player transfers:', error);
        res.status(500).json({ message: 'Failed to fetch player transfers' });
    }
};

// Register routes
router.get('/teams', getTeamTransfersHandler);
router.get('/players', getPlayerTransfersHandler);

export default router;
