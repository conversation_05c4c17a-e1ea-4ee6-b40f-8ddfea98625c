import express, { Router, RequestHandler } from 'express';
import { auth, AuthRequest } from '../middleware/auth';
import { getFixtureMessages, isChatOpen } from '../models/Message';
import { getFixturesCollection } from '../models/Fixture';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();

// Cache TTL in seconds
const MESSAGES_CACHE_TTL = 60; // 1 minute

// @route   GET /api/fixtures/:fixtureId/messages
// @desc    Get messages for a fixture
// @access  Private
const getMessagesHandler: RequestHandler = async (req, res) => {
  try {
    const fixtureId = parseInt(req.params.fixtureId);
    if (isNaN(fixtureId)) {
      res.status(400).json({ message: 'Invalid fixture ID' });
      return;
    }

    // Check if fixture exists
    const fixtureCollection = getFixturesCollection();
    const fixture = await fixtureCollection.findOne({ _id: fixtureId });
    if (!fixture) {
      res.status(404).json({ message: 'Fixture not found' });
      return;
    }

    // Get pagination parameters
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;
    const before = req.query.before ? new Date(req.query.before as string) : undefined;

    // Try to get from cache if not paginating
    const redisClient = getRedisClient();
    const cacheKey = `fixture:${fixtureId}:messages`;
    
    let messages;
    if (!before && limit === 50) {
      const cachedMessages = await redisClient.get(cacheKey);
      if (cachedMessages) {
        res.status(200).json(JSON.parse(cachedMessages));
        return;
      }
    }

    // Get messages from database
    messages = await getFixtureMessages(fixtureId, limit, before);
    
    // Cache the result if not paginating
    if (!before && limit === 50) {
      await redisClient.setex(cacheKey, MESSAGES_CACHE_TTL, JSON.stringify(messages));
    }
    
    // Check if chat is open
    const chatOpen = await isChatOpen(fixtureId);
    
    res.status(200).json({
      messages,
      chatOpen,
      pagination: {
        limit,
        hasMore: messages.length === limit,
        nextBefore: messages.length > 0 ? messages[messages.length - 1].createdAt : null
      }
    });
  } catch (err) {
    console.error('Error getting messages:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Register routes
router.get('/:fixtureId/messages', auth, getMessagesHandler);

export default router;
