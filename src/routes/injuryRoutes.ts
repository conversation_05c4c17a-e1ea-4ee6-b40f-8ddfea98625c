import express, { Router, RequestHandler } from 'express';
import { getInjuriesCollection, Injury } from '../models/Injury';
import { getRedisClient } from '../config/redis';
import { Filter } from 'mongodb';
import dayjs from 'dayjs';
import { apiIdToInternalId, initializeKnownPlayerMappings } from '../services/playerMappingService';

const router: Router = express.Router();
const INJURIES_BASE_CACHE_KEY = 'injuries:';
const CACHE_TTL_SECONDS = 60 * 15; // Cache for 15 minutes (job runs every 4 hours)

// Define the handler function with RequestHandler type
const getInjuriesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const queryParams = req.query;

    // --- Parameter Validation ---
    const { league, season, fixture, team, player, date, ids } = queryParams;
    const leagueId = league ? parseInt(league as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;
    const fixtureId = fixture ? parseInt(fixture as string) : undefined;
    const teamId = team ? parseInt(team as string) : undefined;
    const playerId = player ? parseInt(player as string) : undefined;
    const fixtureIds = ids ? (ids as string).split('-').map(idStr => parseInt(idStr)).filter(idNum => !isNaN(idNum)) : undefined;

    // Basic validation: At least one major filter must be present
    if (!leagueId && !fixtureId && !teamId && !playerId && !date && !fixtureIds) {
        res.status(400).json({ message: 'Missing required filter parameter (league, fixture, team, player, date, or ids).' });
        return; // Exit handler
    }
    // Season required with league, team, player
    if ((leagueId || teamId || playerId) && !seasonYear) {
        res.status(400).json({ message: 'Parameter "season" is required when using "league", "team", or "player".' });
        return; // Exit handler
    }
    // Validate date format if present
    if (date && (typeof date !== 'string' || !dayjs(date, 'YYYY-MM-DD', true).isValid())) {
         res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD.' });
         return; // Exit handler
    }
     // Validate ids format if present
    if (ids && (!fixtureIds || fixtureIds.length === 0)) {
        res.status(400).json({ message: 'Invalid format for ids parameter.' });
        return; // Exit handler
    }

    // --- Build MongoDB Filter ---
    const filter: Filter<Injury> = {};
    if (leagueId && seasonYear) {
        filter['league.id'] = leagueId;
        filter['league.season'] = seasonYear;
    }
    if (fixtureId) filter['fixture.id'] = fixtureId;
    if (teamId) filter['team.id'] = teamId;

    // Handle player ID mapping
    if (playerId) {
        // Initialize known mappings
        await initializeKnownPlayerMappings();

        // Get the internal ID if it exists
        const internalId = await apiIdToInternalId(playerId);

        if (internalId && internalId !== playerId) {
            // If we have both IDs, search for either
            filter['player.id'] = { $in: [playerId, internalId] };
            console.log(`Searching for player with both API ID ${playerId} and internal ID ${internalId}`);
        } else {
            // Otherwise just use the provided ID
            filter['player.id'] = playerId;
        }
    }
    if (fixtureIds) filter['fixture.id'] = { $in: fixtureIds };
    if (date) {
        // Find injuries active on a specific date. This requires comparing the date
        // with the fixture date. Since we only store fixture date string, a regex might work,
        // or store fixture timestamp in Injury model for better range queries.
        // Simple approach: filter by fixture date string matching the date.
        filter['fixture.date'] = { $regex: `^${date}` };
        // Note: This doesn't account for multi-day injuries spanning the date.
        // A more complex model/query would be needed for that.
    }
    // Timezone filtering is complex on stored UTC data, usually handled client-side or at API fetch time.

    // --- Caching Logic ---
    // Cache based on common specific queries (e.g., by fixture, by team+league+season)
    let cacheKey: string | null = null;
    if (fixtureId && Object.keys(queryParams).length === 1) {
        cacheKey = `${INJURIES_BASE_CACHE_KEY}fixture:${fixtureId}`;
    } else if (fixtureIds && Object.keys(queryParams).length === 1) {
         // Cache batch fixture IDs requests with a reasonable number of IDs
         if (fixtureIds.length <= 20) {
             cacheKey = `${INJURIES_BASE_CACHE_KEY}fixtures:${ids}`;
         }
    } else if (teamId && leagueId && seasonYear && Object.keys(queryParams).length === 3) {
        cacheKey = `${INJURIES_BASE_CACHE_KEY}team:${teamId}_league:${leagueId}_season:${seasonYear}`;
    }
    // Add more cache keys as needed

    try {
        // 1. Check cache
        if (cacheKey) {
            const cachedInjuries = await redisClient.get(cacheKey);
            if (cachedInjuries) {
                console.log(`Serving injuries from cache (Key: ${cacheKey})`);
                res.status(200).json(JSON.parse(cachedInjuries)); // Removed return
                return; // Exit handler
            }
        }

        // 2. Fetch from DB
        console.log('Fetching injuries from DB with filter:', JSON.stringify(filter));
        const collection = getInjuriesCollection();
        const projection = { lastUpdated: 0 }; // Exclude internal field

        const injuries = await collection.find(filter, { projection }).toArray();

        // 3. Store in cache (only for cacheable queries)
        if (cacheKey && injuries.length > 0) {
             await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(injuries));
             console.log(`Injuries stored in cache (Key: ${cacheKey})`);
        }

        res.status(200).json(injuries); // Removed implicit return

    } catch (error) {
        console.error('Error fetching injuries:', error);
        res.status(500).json({ message: 'Failed to fetch injuries' }); // Removed implicit return
    }
};

// GET /api/injuries?fixture=X or ?league=X&season=Y etc.
router.get('/', getInjuriesHandler);

export default router;
