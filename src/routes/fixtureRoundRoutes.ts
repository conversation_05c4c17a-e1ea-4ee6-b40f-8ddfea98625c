import express, { Router, RequestHandler } from 'express';
import { getFixtureRoundsCollection, FixtureRoundList, createFixtureRoundId } from '../models/FixtureRound';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();
const ROUNDS_BASE_CACHE_KEY = 'fixtures:rounds:';
const CACHE_TTL_SECONDS = 60 * 60; // Cache for 1 hour (job runs daily)

// Define the handler function with RequestHandler type
const getFixtureRoundsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { leagueId, season } = req.query; // Required params

    // Validate required parameters
    if (!leagueId || !season) {
        res.status(400).json({ message: 'Missing required query parameters: leagueId, season' });
        return;
    }

    const leagueNum = parseInt(leagueId as string);
    const seasonNum = parseInt(season as string);

    if (isNaN(leagueNum) || isNaN(seasonNum)) {
        res.status(400).json({ message: 'Invalid numeric value for leagueId or season' });
        return;
    }

    const roundId = createFixtureRoundId(leagueNum, seasonNum);
    const cacheKey = `${ROUNDS_BASE_CACHE_KEY}${roundId}`;

    try {
        // 1. Check cache
        const cachedRounds = await redisClient.get(cacheKey);
        if (cachedRounds) {
            console.log(`Serving fixture rounds from cache (Key: ${cacheKey})`);
            // Return the rounds array directly
            res.status(200).json(JSON.parse(cachedRounds));
            return;
        }

        // 2. Fetch from DB
        console.log(`Fetching fixture rounds from DB (ID: ${roundId})`);
        const collection = getFixtureRoundsCollection();
        const projection = { rounds: 1, _id: 0 }; // Only get the rounds array

        const roundDoc = await collection.findOne({ _id: roundId }, { projection });

        if (!roundDoc || !roundDoc.rounds) {
            // Optionally trigger on-demand fetch? For now, return 404.
            res.status(404).json({ message: 'Fixture rounds not found for the specified league and season.' });
            return;
        }

        // 3. Store in cache (store only the rounds array)
        await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(roundDoc.rounds));
        console.log(`Fixture rounds stored in cache (Key: ${cacheKey})`);

        res.status(200).json(roundDoc.rounds); // Return the rounds array

    } catch (error) {
        console.error(`Error fetching fixture rounds for ID ${roundId}:`, error);
        res.status(500).json({ message: 'Failed to fetch fixture rounds' });
    }
};

// GET /api/fixtures/rounds?leagueId=X&season=Y
router.get('/', getFixtureRoundsHandler);

export default router;
