import express, { Router, RequestHandler } from 'express';
import { getSidelinedCollection } from '../models/Sidelined';
import { getRedisClient } from '../config/redis';
import { Filter } from 'mongodb';
import { apiIdToInternalId, initializeKnownPlayerMappings } from '../services/playerMappingService';

const router: Router = express.Router();
const SIDELINED_CACHE_KEY = 'sidelined:';

// Cache durations
const CACHE_TTL_HOUR = 60 * 60;
const CACHE_TTL_DAY = 60 * 60 * 24;

// GET /api/sidelined - Get sidelined players
const getSidelinedHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    // Accept plural 'players' and 'coaches' for multiple IDs
    const { player, players, coach, coaches } = req.query;

    let playerIds: number[] = [];
    let coachIds: number[] = [];
    let mappedPlayerIds: number[] = [];

    // Helper function to parse comma-separated IDs
    const parseIds = (param: any): number[] => {
        if (typeof param === 'string') {
            return param.split(',')
                        .map(id => parseInt(id.trim()))
                        .filter(id => !isNaN(id));
        }
        return [];
    };

    // Prioritize plural parameters if provided
    if (players) {
        playerIds = parseIds(players);
    } else if (player) {
        const singlePlayerId = parseInt(player as string);
        if (!isNaN(singlePlayerId)) {
            playerIds = [singlePlayerId];
        }
    }

    if (coaches) {
        coachIds = parseIds(coaches);
    } else if (coach) {
        const singleCoachId = parseInt(coach as string);
        if (!isNaN(singleCoachId)) {
            coachIds = [singleCoachId];
        }
    }

    // Validate at least one valid ID array is present
    if (playerIds.length === 0 && coachIds.length === 0) {
        res.status(400).json({ message: 'Valid player ID(s) or coach ID(s) are required using player/players or coach/coaches parameters.' });
        return;
    }

    // Map API player IDs to internal IDs
    if (playerIds.length > 0) {
        // Initialize known mappings first
        await initializeKnownPlayerMappings();

        // Map each player ID
        mappedPlayerIds = [...playerIds]; // Start with original IDs

        for (const apiId of playerIds) {
            const internalId = await apiIdToInternalId(apiId);
            if (internalId && !mappedPlayerIds.includes(internalId)) {
                mappedPlayerIds.push(internalId);
            }
        }

        console.log(`Original player IDs: ${playerIds.join(', ')}`);
        console.log(`Mapped player IDs: ${mappedPlayerIds.join(', ')}`);
    }

    // Build cache key - use sorted IDs for consistency
    let cacheKey = `${SIDELINED_CACHE_KEY}`;
    if (playerIds.length > 0) {
        // Use the original player IDs for the cache key to maintain backward compatibility
        const cacheKeyIds = playerIds.sort((a, b) => a - b).join(',');
        cacheKey += `players:${cacheKeyIds}`;
    }
    if (coachIds.length > 0) {
        cacheKey += `${playerIds.length > 0 ? '_' : ''}coaches:${coachIds.sort((a, b) => a - b).join(',')}`;
    }

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            const parsedData = JSON.parse(cachedData);
            // If the cache has data or if it's explicitly an empty array that's not due to mapping
            if (parsedData.length > 0 || playerIds.length === 0 || mappedPlayerIds.length === playerIds.length) {
                console.log(`Serving sidelined players from cache (Key: ${cacheKey})`);
                res.status(200).json(parsedData);
                return;
            }
            // If we have mapped IDs and the cache is empty, we should still check the database
            // This handles the case where the original ID has no data but the mapped ID might
            console.log(`Cache hit but empty with mapped IDs. Checking database...`);
        }

        // Build MongoDB filter
        const filter: Filter<any> = { $or: [] }; // Use $or to combine player and coach filters if both are provided

        if (mappedPlayerIds.length > 0) {
            filter.$or.push({ 'player.id': { $in: mappedPlayerIds } });
        }

        if (coachIds.length > 0) {
            // Note: Coach sidelined filtering might require schema adjustments or a different query approach
            // Assuming a hypothetical 'coach.id' field for demonstration
            // filter.$or.push({ 'coach.id': { $in: coachIds } });
            console.warn("Filtering sidelined by coach ID is requested but may not be fully supported by the current schema/query.");
            // If you have a way to link sidelined players to coaches (e.g., via team history), implement that logic here.
            // For now, we'll proceed without adding the coach filter to avoid errors if the field doesn't exist.
            // If only coach IDs are provided, the filter.$or might be empty, leading to fetching all sidelined players.
            // Consider returning an error or specific message if only coach filtering is requested and not implemented.
             if (playerIds.length === 0) {
                 // If only coach IDs were provided and filtering isn't implemented, return an appropriate response
                 res.status(501).json({ message: 'Filtering sidelined players by coach ID is not currently implemented.' });
                 return;
             }
        }

        // If $or array has only one condition, simplify the filter
        if (filter.$or.length === 1) {
            Object.assign(filter, filter.$or[0]);
            delete filter.$or;
        } else if (filter.$or.length === 0) {
             // This case should ideally be handled by the initial validation, but as a safeguard:
             res.status(400).json({ message: 'No valid filter criteria provided.' });
             return;
        }


        // Fetch from database
        const collection = getSidelinedCollection();
        const sidelined = await collection.find(filter).toArray();

        // Transform to match API response format
        const response = sidelined.map(item => ({
            player: item.player,
            team: item.team,
            type: item.type,
            reason: item.reason,
            start: item.start,
            end: item.end
        }));

        // Return empty array if no sidelined players found
        if (sidelined.length === 0) {
            // Only cache empty results if we're not using mapped IDs or if the mapping didn't add any IDs
            // This prevents caching empty results when the original ID has no data but the mapped ID might
            if (playerIds.length === 0 || mappedPlayerIds.length === playerIds.length) {
                const cacheTTL = CACHE_TTL_HOUR; // Sidelined status can change frequently
                await redisClient.setex(cacheKey, cacheTTL, JSON.stringify([]));
            }
            res.status(200).json([]);
            return;
        }

        // Cache the result
        const cacheTTL = CACHE_TTL_HOUR; // Sidelined status can change frequently
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(response));

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching sidelined players:', error);
        res.status(500).json({ message: 'Failed to fetch sidelined players' });
    }
};

// GET /api/sidelined/teams?team=X&season=Y - Get team sidelined players
const getTeamSidelinedHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { team, season } = req.query;

    // Parse and validate parameters
    const teamId = team ? parseInt(team as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;

    // Validate parameters are provided
    if (!teamId) {
        res.status(400).json({ message: 'Team ID is required.' });
        return;
    }

    // Validate parameter types
    if (isNaN(teamId)) {
        res.status(400).json({ message: 'Invalid team ID.' });
        return;
    }

    if (seasonYear && isNaN(seasonYear)) {
        res.status(400).json({ message: 'Invalid season year.' });
        return;
    }

    // Build cache key
    let cacheKey = `${SIDELINED_CACHE_KEY}team:${teamId}`;
    if (seasonYear) cacheKey += `:season:${seasonYear}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving team sidelined players from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Build MongoDB filter
        const filter: Filter<any> = { 'team.id': teamId };

        // Fetch from database
        const collection = getSidelinedCollection();
        const sidelined = await collection.find(filter).toArray();

        // Transform to match API response format
        const response = sidelined.map(item => ({
            player: item.player,
            team: item.team,
            type: item.type,
            reason: item.reason,
            start: item.start,
            end: item.end
        }));

        // Cache the result
        const cacheTTL = CACHE_TTL_HOUR; // Sidelined status can change frequently
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(response));

        // Return empty array if no sidelined players found
        if (sidelined.length === 0) {
            // Cache empty array
            await redisClient.setex(cacheKey, cacheTTL, JSON.stringify([]));
            res.status(200).json([]);
            return;
        }

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching team sidelined players:', error);
        res.status(500).json({ message: 'Failed to fetch team sidelined players' });
    }
};

// GET /api/sidelined/leagues?league=X&season=Y - Get league sidelined players
const getLeagueSidelinedHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { league, season } = req.query;

    // Parse and validate parameters
    const leagueId = league ? parseInt(league as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;

    // Validate parameters are provided
    if (!leagueId) {
        res.status(400).json({ message: 'League ID is required.' });
        return;
    }

    if (!seasonYear) {
        res.status(400).json({ message: 'Season year is required.' });
        return;
    }

    // Validate parameter types
    if (isNaN(leagueId)) {
        res.status(400).json({ message: 'Invalid league ID.' });
        return;
    }

    if (isNaN(seasonYear)) {
        res.status(400).json({ message: 'Invalid season year.' });
        return;
    }

    // Build cache key
    const cacheKey = `${SIDELINED_CACHE_KEY}league:${leagueId}:season:${seasonYear}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving league sidelined players from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // For league sidelined, we need to get teams in the league first
        // Then get sidelined players for those teams
        const { getTeamsCollection } = await import('../models/Team');
        const teamsCollection = getTeamsCollection();

        // Find teams in the specified league and season
        const teams = await teamsCollection.find({
            'league.id': leagueId,
            'league.season': seasonYear
        }).toArray();

        if (!teams || teams.length === 0) {
            // Cache empty array
            await redisClient.setex(cacheKey, CACHE_TTL_HOUR, JSON.stringify([]));
            res.status(200).json([]);
            return;
        }

        const teamIds = teams.map(team => team._id);

        // Build MongoDB filter
        const filter: Filter<any> = { 'team.id': { $in: teamIds } };

        // Fetch from database
        const collection = getSidelinedCollection();
        const sidelined = await collection.find(filter).toArray();

        // Transform to match API response format
        const response = sidelined.map(item => ({
            player: item.player,
            team: item.team,
            type: item.type,
            reason: item.reason,
            start: item.start,
            end: item.end
        }));

        // Cache the result
        const cacheTTL = CACHE_TTL_HOUR; // Sidelined status can change frequently
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(response));

        // Return empty array if no sidelined players found
        if (sidelined.length === 0) {
            // Cache empty array
            await redisClient.setex(cacheKey, cacheTTL, JSON.stringify([]));
            res.status(200).json([]);
            return;
        }

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching league sidelined players:', error);
        res.status(500).json({ message: 'Failed to fetch league sidelined players' });
    }
};

// Register routes
router.get('/', getSidelinedHandler);
router.get('/teams', getTeamSidelinedHandler);
router.get('/leagues', getLeagueSidelinedHandler);

export default router;
