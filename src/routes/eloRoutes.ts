/**
 * ELO Rating Routes
 * 
 * API endpoints for ELO rating data:
 * - GET /api/elo/ratings - Get all ELO ratings
 * - GET /api/elo/ratings/:club - Get specific club ELO rating
 * - GET /api/elo/team/:teamId - Get ELO data for API-Football team
 * - GET /api/elo/mappings - Get team name mappings
 * - POST /api/elo/sync - Manual sync trigger
 * - GET /api/elo/top/:count - Get top N teams by ELO
 */

import express, { Router, Request, Response, RequestHandler } from 'express';
import { 
    getEloRatingsCollection,
    getTeamNameMappingsCollection,
    getEloEnhancedStrengthCollection
} from '../models/EloRating';
import { 
    getCachedEloRatings,
    getEloStrength,
    triggerManualEloSync
} from '../services/eloService';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();
const CACHE_TTL = 60 * 60 * 4; // 4 hours

/**
 * GET /api/elo/ratings
 * Get all ELO ratings with optional filtering
 */
router.get('/ratings', async (req: Request, res: Response) => {
    try {
        const { country, minElo, maxElo, ranked } = req.query;
        
        const collection = getEloRatingsCollection();
        const filter: any = {};
        
        if (country) filter.country = new RegExp(country as string, 'i');
        if (minElo) filter.elo = { ...filter.elo, $gte: parseFloat(minElo as string) };
        if (maxElo) filter.elo = { ...filter.elo, $lte: parseFloat(maxElo as string) };
        if (ranked === 'true') filter.rank = { $ne: null };
        
        const ratings = await collection
            .find(filter)
            .sort({ elo: -1 })
            .limit(100)
            .toArray();
        
        res.json({
            success: true,
            count: ratings.length,
            data: ratings
        });
        
    } catch (error) {
        console.error('Error fetching ELO ratings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch ELO ratings'
        });
    }
});

/**
 * GET /api/elo/ratings/:club
 * Get ELO rating for specific club
 */
router.get('/ratings/:club', (async (req: Request, res: Response) => {
    try {
        const { club } = req.params;
        const collection = getEloRatingsCollection();
        
        const rating = await collection.findOne({ 
            club: new RegExp(`^${club}$`, 'i') 
        });
        
        if (!rating) {
            res.status(404).json({
                success: false,
                error: 'Club not found'
            });
            return;
        }

        res.json({
            success: true,
            data: rating
        });
        
    } catch (error) {
        console.error('Error fetching club ELO rating:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch club ELO rating'
        });
    }
}) as RequestHandler);

/**
 * GET /api/elo/team/:teamId
 * Get ELO data for API-Football team
 */
router.get('/team/:teamId', (async (req: Request, res: Response) => {
    try {
        const teamId = parseInt(req.params.teamId);
        
        if (isNaN(teamId)) {
            res.status(400).json({
                success: false,
                error: 'Invalid team ID'
            });
            return;
        }

        const eloStrength = await getEloStrength(teamId);

        if (!eloStrength) {
            res.status(404).json({
                success: false,
                error: 'ELO data not found for this team'
            });
            return;
        }

        res.json({
            success: true,
            data: eloStrength
        });
        
    } catch (error) {
        console.error('Error fetching team ELO data:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch team ELO data'
        });
    }
}) as RequestHandler);

/**
 * GET /api/elo/mappings
 * Get team name mappings
 */
router.get('/mappings', async (req: Request, res: Response) => {
    try {
        const { minConfidence } = req.query;
        const collection = getTeamNameMappingsCollection();
        
        const filter: any = {};
        if (minConfidence) {
            filter.confidence = { $gte: parseFloat(minConfidence as string) };
        }
        
        const mappings = await collection
            .find(filter)
            .sort({ confidence: -1 })
            .toArray();
        
        res.json({
            success: true,
            count: mappings.length,
            data: mappings
        });
        
    } catch (error) {
        console.error('Error fetching team mappings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch team mappings'
        });
    }
});

/**
 * POST /api/elo/sync
 * Manual ELO sync trigger
 */
router.post('/sync', async (req: Request, res: Response) => {
    try {
        await triggerManualEloSync();
        
        res.json({
            success: true,
            message: 'ELO ratings sync completed successfully'
        });
        
    } catch (error) {
        console.error('Error in manual ELO sync:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to sync ELO ratings'
        });
    }
});

/**
 * GET /api/elo/top/:count
 * Get top N teams by ELO rating
 */
router.get('/top/:count', (async (req: Request, res: Response) => {
    try {
        const count = parseInt(req.params.count);
        
        if (isNaN(count) || count < 1 || count > 100) {
            res.status(400).json({
                success: false,
                error: 'Count must be between 1 and 100'
            });
            return;
        }
        
        const redisClient = getRedisClient();
        const cacheKey = `elo_top_${count}`;
        
        // Check cache first
        const cached = await redisClient.get(cacheKey);
        if (cached) {
            res.json({
                success: true,
                count,
                data: JSON.parse(cached),
                cached: true
            });
            return;
        }
        
        const collection = getEloRatingsCollection();
        const topTeams = await collection
            .find({})
            .sort({ elo: -1 })
            .limit(count)
            .toArray();
        
        // Cache result
        await redisClient.setex(cacheKey, CACHE_TTL, JSON.stringify(topTeams));
        
        res.json({
            success: true,
            count: topTeams.length,
            data: topTeams,
            cached: false
        });
        
    } catch (error) {
        console.error('Error fetching top ELO teams:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch top ELO teams'
        });
    }
}) as RequestHandler);

/**
 * GET /api/elo/stats
 * Get ELO statistics and overview
 */
router.get('/stats', async (req: Request, res: Response) => {
    try {
        const collection = getEloRatingsCollection();
        const mappingsCollection = getTeamNameMappingsCollection();
        
        const [
            totalTeams,
            rankedTeams,
            mappedTeams,
            avgElo,
            topTeam,
            lastUpdated
        ] = await Promise.all([
            collection.countDocuments(),
            collection.countDocuments({ rank: { $ne: null } }),
            mappingsCollection.countDocuments({ confidence: { $gte: 0.8 } }),
            collection.aggregate([{ $group: { _id: null, avg: { $avg: '$elo' } } }]).toArray(),
            collection.findOne({}, { sort: { elo: -1 } }),
            collection.findOne({}, { sort: { lastUpdated: -1 } })
        ]);
        
        res.json({
            success: true,
            data: {
                totalTeams,
                rankedTeams,
                mappedTeams,
                averageElo: avgElo[0]?.avg || 0,
                topTeam: topTeam ? { club: topTeam.club, elo: topTeam.elo } : null,
                lastUpdated: lastUpdated?.lastUpdated
            }
        });
        
    } catch (error) {
        console.error('Error fetching ELO stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch ELO statistics'
        });
    }
});

export default router;
