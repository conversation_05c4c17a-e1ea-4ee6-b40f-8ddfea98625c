import express, { Router, RequestHandler, Request, Response } from 'express'; // Added Request, Response
import { getTeamsCollection, Team } from '../models/Team';
import { fetchTeamSeasons, fetchTeamCountries } from '../services/apiFootball'; // Import the service functions
import { getRedisClient } from '../config/redis';
import { Filter } from 'mongodb';

const router: Router = express.Router();
const TEAMS_BASE_CACHE_KEY = 'teams:';
const TEAM_SEASONS_CACHE_KEY_PREFIX = 'teams:seasons:';
const TEAM_COUNTRIES_CACHE_KEY = 'teams:countries';
const CACHE_TTL_SECONDS = 60 * 10; // Cache for 10 minutes (adjust as needed)
const TEAM_SEASONS_CACHE_TTL_SECONDS = 60 * 60 * 24; // Cache team seasons for 24 hours
const TEAM_COUNTRIES_CACHE_TTL_SECONDS = 60 * 60 * 24; // Cache team countries for 24 hours

// Define the handler function with RequestHandler type
const getTeamsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const queryParams = req.query;

    // Build MongoDB filter dynamically based on query params
    const filter: Filter<Team> = {};
    if (queryParams.id) filter._id = parseInt(queryParams.id as string); // Use _id for team ID
    if (queryParams.name) filter['team.name'] = new RegExp(queryParams.name as string, 'i');
    if (queryParams.country) filter['team.country'] = new RegExp(queryParams.country as string, 'i');
    if (queryParams.code) filter['team.code'] = (queryParams.code as string).toUpperCase();
    if (queryParams.venue) filter['venue.name'] = new RegExp(queryParams.venue as string, 'i');
    if (queryParams.search) {
        const searchRegex = new RegExp(queryParams.search as string, 'i');
        filter.$or = [
            { 'team.name': searchRegex },
            { 'team.country': searchRegex } // Search team country field
        ];
    }

    // Filter by league and season if provided
    if (queryParams.league && queryParams.season) {
        const leagueId = parseInt(queryParams.league as string);
        const season = parseInt(queryParams.season as string);

        if (!isNaN(leagueId) && !isNaN(season)) {
            filter['leagues'] = {
                $elemMatch: {
                    apiId: leagueId,
                    season: season
                }
            };
        }
    }

    // If team parameter is provided, filter by team ID
    if (queryParams.team) {
        const teamId = parseInt(queryParams.team as string);
        if (!isNaN(teamId)) {
            filter._id = teamId;
        }
    }

    // Generate cache key based on query params (simple version: only cache unfiltered/by ID)
    let cacheKey: string | null = null;
    const isFiltered = Object.keys(queryParams).length > 0;
    if (!isFiltered) {
        cacheKey = `${TEAMS_BASE_CACHE_KEY}all`;
    } else if (queryParams.id && Object.keys(queryParams).length === 1) {
        cacheKey = `${TEAMS_BASE_CACHE_KEY}id:${queryParams.id}`;
    } // Add more specific cache keys for common filter combinations if needed

    try {
        // 1. Check cache
        if (cacheKey) {
            const cachedTeams = await redisClient.get(cacheKey);
            if (cachedTeams) {
                console.log(`Serving teams from cache (Key: ${cacheKey})`);
                res.status(200).json(JSON.parse(cachedTeams));
                return;
            }
        }

        // 2. Fetch from DB using the constructed filter
        console.log('Fetching teams from DB with filter:', JSON.stringify(filter));
        const collection = getTeamsCollection();
        const projection = { lastUpdated: 0 }; // Exclude lastUpdated field
        // Decide if you want _id in the response if not filtering by it
        // if (!filter._id) projection._id = 0;

        const teams = await collection.find(filter, { projection }).sort({ 'team.name': 1 }).toArray();

        // 3. Store in cache (only for cached keys)
        if (cacheKey && teams.length > 0) {
            // For single team ID fetch, cache result might be a single object, not array
            const dataToCache = (queryParams.id && teams.length === 1) ? teams[0] : teams;
            await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(dataToCache));
            console.log(`Teams stored in cache (Key: ${cacheKey})`);
        }

        // If filtering by ID and no team found, return 404
        if (queryParams.id && teams.length === 0) {
            res.status(404).json({ message: 'Team not found' }); // Removed return
            return; // Exit function
        }
        // If filtering by ID and team found, return the single team object
        if (queryParams.id && teams.length === 1) {
            res.status(200).json(teams[0]); // Removed return
            return; // Exit function
        }

        // Otherwise, return the array of teams (could be empty if filters match nothing)
        res.status(200).json(teams);

    } catch (error) {
        console.error('Error fetching teams:', error);
        res.status(500).json({ message: 'Failed to fetch teams' });
    }
};

// GET /api/teams - Fetches teams with optional filtering
router.get('/', getTeamsHandler);


// --- Handler for Team Seasons ---
const getTeamSeasonsHandler: RequestHandler<{ teamId: string }> = async (req, res) => {
    const redisClient = getRedisClient();
    const teamIdParam = req.params.teamId;
    const teamId = parseInt(teamIdParam);

    if (isNaN(teamId)) {
        res.status(400).json({ message: 'Invalid team ID provided.' });
        return;
    }

    const cacheKey = `${TEAM_SEASONS_CACHE_KEY_PREFIX}${teamId}`;

    try {
        // 1. Check cache
        const cachedSeasons = await redisClient.get(cacheKey);
        if (cachedSeasons) {
            console.log(`Serving team seasons from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedSeasons));
            return;
        }

        // 2. Fetch from API service if not cached
        console.log(`Fetching team seasons from API for team ID: ${teamId}`);
        const seasons = await fetchTeamSeasons({ team: teamId });

        // Sort seasons numerically (descending) before caching/returning
        const sortedSeasons = seasons.sort((a, b) => b - a);

        // 3. Store in cache
        await redisClient.setex(cacheKey, TEAM_SEASONS_CACHE_TTL_SECONDS, JSON.stringify(sortedSeasons));
        console.log(`Team seasons stored in cache (Key: ${cacheKey})`);

        res.status(200).json(sortedSeasons);

    } catch (error: any) {
        console.error(`Error fetching team seasons for team ID ${teamId}:`, error);
        // Check if the error is because the team doesn't exist (e.g., API returns 404 or specific error)
        // Axios errors might have a response status
        if (error.response?.status === 404 || error.message?.includes('not found')) { // Adjust error checking as needed
             res.status(404).json({ message: `Seasons not found for team ID ${teamId}. Team might not exist.` });
        } else {
             res.status(500).json({ message: 'Failed to fetch team seasons' });
        }
    }
};

// GET /api/teams/:teamId/seasons - Fetches seasons for a specific team
router.get('/:teamId/seasons', getTeamSeasonsHandler);

// --- Handler for Team Countries ---
const getTeamCountriesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const cacheKey = TEAM_COUNTRIES_CACHE_KEY;

    try {
        // 1. Check cache
        const cachedCountries = await redisClient.get(cacheKey);
        if (cachedCountries) {
            console.log(`Serving team countries from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedCountries));
            return;
        }

        // 2. Fetch from API service if not cached
        console.log(`Fetching team countries from API`);
        const countries = await fetchTeamCountries();

        // 3. Store in cache
        await redisClient.setex(cacheKey, TEAM_COUNTRIES_CACHE_TTL_SECONDS, JSON.stringify(countries));
        console.log(`Team countries stored in cache (Key: ${cacheKey})`);

        res.status(200).json(countries);

    } catch (error) {
        console.error(`Error fetching team countries:`, error);
        res.status(500).json({ message: 'Failed to fetch team countries' });
    }
};

// GET /api/teams/countries - Fetches all countries available for the teams endpoints
router.get('/countries', getTeamCountriesHandler);

export default router;
