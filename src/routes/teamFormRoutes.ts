import express, { Router, RequestHandler } from 'express';
import { fetchLeagues, fetchTeamStatistics, fetchFixtures } from '../services/apiFootball';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();
const FORM_CACHE_KEY = 'form:team:';
const CACHE_TTL_SECONDS = 60 * 60 * 24; // Cache for 24 hours

// Helper function to parse form string into an array of results
function parseFormString(form: string | null): { result: string, isRecent: boolean }[] {
    if (!form) return [];

    // Convert form string (e.g., "WDLWW") to array of results
    // Most recent result is at the end of the string
    const results = form.split('').map((result, index, array) => ({
        result,
        isRecent: index === array.length - 1 // Mark the most recent result
    }));

    return results;
}

// Define a type for detailed form information
interface DetailedFormResult {
    result: string;
    fixtureId: number;
    date: string;
    homeTeam: {
        id: number;
        name: string;
        logo: string;
    };
    awayTeam: {
        id: number;
        name: string;
        logo: string;
    };
    score: {
        home: number;
        away: number;
    };
    isHomeTeam: boolean;
}

// Helper function to combine form results from multiple leagues
async function combineFormResults(
    formArrays: { result: string, isRecent: boolean, league: string }[][],
    teamId: number,
    season: number,
    excludeFixtureId?: number
): Promise<{ form: string, detailedForm: DetailedFormResult[] }> {
    // Flatten all form arrays
    const allResults = formArrays.flat();

    // If we have no results, return empty string and empty array
    if (allResults.length === 0) {
        return { form: '', detailedForm: [] };
    }

    try {
        // Fetch the team's recent fixtures to get accurate chronological order
        // Include all completed match statuses: FT (Full Time), AET (After Extra Time),
        // PEN (After Penalties), WO (Walk Over), AWD (Awarded)
        const fixtures = await fetchFixtures({
            team: teamId,
            season: season,
            last: 20, // Get more than we need to ensure we have enough
            status: 'FT-AET-PEN-WO-AWD' // All completed fixtures (including AET, penalties, etc.)
        });

        // If we have fixtures, use them to determine the most recent form
        if (fixtures && fixtures.length > 0) {
            // Filter out the excluded fixture if provided
            const filteredFixtures = excludeFixtureId
                ? fixtures.filter(fixture => fixture.fixture.id !== excludeFixtureId)
                : fixtures;

            // Get the 5 most recent fixtures
            const recentFixtures = filteredFixtures.slice(0, 5).reverse();

            // Create detailed form information
            const detailedForm = recentFixtures.map(fixture => {
                const isHomeTeam = fixture.teams.home.id === teamId;
                const teamScore = isHomeTeam ? fixture.goals.home : fixture.goals.away;
                const opponentScore = isHomeTeam ? fixture.goals.away : fixture.goals.home;

                let result = 'D';
                if (teamScore > opponentScore) result = 'W';
                if (teamScore < opponentScore) result = 'L';

                return {
                    result,
                    fixtureId: fixture.fixture.id,
                    date: fixture.fixture.date,
                    homeTeam: {
                        id: fixture.teams.home.id,
                        name: fixture.teams.home.name,
                        logo: fixture.teams.home.logo
                    },
                    awayTeam: {
                        id: fixture.teams.away.id,
                        name: fixture.teams.away.name,
                        logo: fixture.teams.away.logo
                    },
                    score: {
                        home: fixture.goals.home,
                        away: fixture.goals.away
                    },
                    isHomeTeam
                };
            });

            // Create form string
            const formString = detailedForm.map(item => item.result).join('');

            return {
                form: formString,
                detailedForm
            };
        }
    } catch (error) {
        console.error('Error fetching recent fixtures for form calculation:', error);
        // Fall back to the original method if fixture fetching fails
    }

    // Fallback to the original method if we couldn't get fixtures
    // Sort by isRecent flag (most recent first)
    allResults.sort((a, b) => {
        if (a.isRecent && !b.isRecent) return -1;
        if (!a.isRecent && b.isRecent) return 1;
        return 0;
    });

    // Take the 5 most recent results
    const recentResults = allResults.slice(0, 5);

    // Reverse the results so oldest is first and newest is last
    recentResults.reverse();

    // Convert back to a form string (e.g., "WDLWW")
    const formString = recentResults.map(r => r.result).join('');

    // In fallback mode, we don't have detailed information
    return {
        form: formString,
        detailedForm: []
    };
}

// GET /api/teams/form?teamId=X&excludeFixtureId=Y
const getTeamFormHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const teamId = req.query.teamId;
    const season = req.query.season || new Date().getFullYear(); // Default to current year
    const excludeFixtureId = req.query.excludeFixtureId;

    // Validate required parameters
    if (!teamId) {
        res.status(400).json({ message: 'Missing required query parameter: teamId' });
        return;
    }

    const teamIdNum = parseInt(teamId as string);
    const seasonNum = parseInt(season as string);
    const excludeFixtureIdNum = excludeFixtureId ? parseInt(excludeFixtureId as string) : undefined;

    if (isNaN(teamIdNum) || isNaN(seasonNum) || (excludeFixtureId && isNaN(excludeFixtureIdNum!))) {
        res.status(400).json({ message: 'Invalid numeric value for teamId, season, or excludeFixtureId' });
        return;
    }

    // Create a cache key that includes the excludeFixtureId if provided
    const cacheKey = excludeFixtureIdNum
        ? `${FORM_CACHE_KEY}${teamIdNum}:${seasonNum}:exclude:${excludeFixtureIdNum}`
        : `${FORM_CACHE_KEY}${teamIdNum}:${seasonNum}`;

    try {
        // 1. Check cache
        const cachedForm = await redisClient.get(cacheKey);
        if (cachedForm) {
            console.log(`Serving team form from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedForm));
            return;
        }

        // 2. Fetch leagues for the team
        console.log(`Fetching leagues for team ID: ${teamIdNum}`);
        const leaguesResponse = await fetchLeagues({ team: teamIdNum, season: seasonNum });

        if (!leaguesResponse || leaguesResponse.length === 0) {
            res.status(404).json({ message: 'No leagues found for the specified team and season.' });
            return;
        }

        // 3. Fetch team statistics for each league
        const formPromises = leaguesResponse.map(async (leagueData) => {
            const leagueId = leagueData.league.id;
            const leagueName = leagueData.league.name;

            try {
                const stats = await fetchTeamStatistics({
                    team: teamIdNum,
                    league: leagueId,
                    season: seasonNum
                });

                if (!stats || !stats.form) {
                    return [];
                }

                // Parse form string and add league info
                return parseFormString(stats.form).map(result => ({
                    ...result,
                    league: leagueName
                }));
            } catch (error) {
                console.error(`Error fetching team statistics for team ${teamIdNum} in league ${leagueId}:`, error);
                return [];
            }
        });

        // 4. Wait for all promises to resolve
        const formResults = await Promise.all(formPromises);

        // 5. Combine form results from all leagues
        const { form, detailedForm } = await combineFormResults(formResults, teamIdNum, seasonNum, excludeFixtureIdNum);

        // 6. Prepare response
        const response = {
            teamId: teamIdNum,
            season: seasonNum,
            form,
            detailedForm
        };

        // 7. Store in cache
        await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(response));
        console.log(`Team form stored in cache (Key: ${cacheKey})`);

        res.status(200).json(response);

    } catch (error) {
        console.error(`Error fetching team form for team ${teamIdNum}:`, error);
        res.status(500).json({ message: 'Failed to fetch team form' });
    }
};

router.get('/', getTeamFormHandler);

export default router;
