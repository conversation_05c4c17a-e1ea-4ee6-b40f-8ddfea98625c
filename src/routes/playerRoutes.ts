import express, { Router, RequestHandler } from 'express';
import { getPlayersCollection, getPlayerSquadsCollection, getTopPlayersCollection, createTopPlayersKey } from '../models/Player';
import { getRedisClient } from '../config/redis';
import { Filter, Document } from 'mongodb';

const router: Router = express.Router();
const PLAYERS_BASE_CACHE_KEY = 'players:';
const PLAYER_PROFILES_CACHE_KEY = 'players:profiles:';
const PLAYER_STATS_CACHE_KEY = 'players:stats:';
const PLAYER_SQUADS_CACHE_KEY = 'players:squads:';
const PLAYER_TEAMS_CACHE_KEY = 'players:teams:';
const PLAYER_SEASONS_CACHE_KEY = 'players:seasons:'; // Added cache key for seasons
const TOP_PLAYERS_CACHE_KEY = 'players:top:';

// Cache durations
const CACHE_TTL_MINUTE = 60;
const CACHE_TTL_HOUR = 60 * 60;
const CACHE_TTL_DAY = 60 * 60 * 24;
const CACHE_TTL_WEEK = CACHE_TTL_DAY * 7;

// GET /api/players/profiles - Get player profiles
const getPlayerProfilesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { player, search, page } = req.query;

    // Parse and validate parameters
    const playerId = player ? parseInt(player as string) : undefined;
    const searchTerm = search as string | undefined;
    const pageNum = page ? parseInt(page as string) : 1;

    if (player && isNaN(playerId as number)) {
        res.status(400).json({ message: 'Invalid player ID.' });
        return;
    }

    if (page && isNaN(pageNum)) {
        res.status(400).json({ message: 'Invalid page number.' });
        return;
    }

    if (search && (typeof searchTerm !== 'string' || searchTerm.length < 3)) {
        res.status(400).json({ message: 'Search term must be at least 3 characters.' });
        return;
    }

    // Build cache key
    let cacheKey = `${PLAYER_PROFILES_CACHE_KEY}`;
    if (playerId) cacheKey += `id:${playerId}`;
    else if (searchTerm) cacheKey += `search:${searchTerm}`;
    else cacheKey += `page:${pageNum}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving player profiles from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Build MongoDB filter
        const filter: Filter<any> = {};
        if (playerId) filter._id = playerId;
        else if (searchTerm) {
            // Search in player name (case-insensitive)
            filter['profile.name'] = { $regex: searchTerm, $options: 'i' };
        }

        // Fetch from database
        const collection = getPlayersCollection();
        const projection = { 'profile': 1, '_id': 1 };

        let query = collection.find(filter, { projection });

        // Apply pagination if not searching by ID
        if (!playerId) {
            const limit = 20; // Number of results per page
            const skip = (pageNum - 1) * limit;
            query = query.skip(skip).limit(limit);
        }

        const players = await query.toArray();

        // Transform to match API response format
        const response = players.map(player => player.profile);

        // Cache the result
        const cacheTTL = playerId ? CACHE_TTL_WEEK : CACHE_TTL_DAY;
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(response));

        if (playerId && players.length === 0) {
            res.status(404).json({ message: 'Player not found' });
            return;
        }

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching player profiles:', error);
        res.status(500).json({ message: 'Failed to fetch player profiles' });
    }
};

// GET /api/players/statistics - Get player statistics
const getPlayerStatisticsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { id, team, league, season, search, page } = req.query;

    // Parse and validate parameters
    const playerId = id ? parseInt(id as string) : undefined;
    const teamId = team ? parseInt(team as string) : undefined;
    const leagueId = league ? parseInt(league as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;
    const searchTerm = search as string | undefined;
    const pageNum = page ? parseInt(page as string) : 1;

    // Validate required parameters
    if (!seasonYear) {
        res.status(400).json({ message: 'Season parameter is required.' });
        return;
    }

    if (!playerId && !teamId && !leagueId) {
        res.status(400).json({ message: 'At least one of id, team, or league parameter is required.' });
        return;
    }

    // Validate parameter types
    if (playerId && isNaN(playerId)) {
        res.status(400).json({ message: 'Invalid player ID.' });
        return;
    }

    if (teamId && isNaN(teamId)) {
        res.status(400).json({ message: 'Invalid team ID.' });
        return;
    }

    if (leagueId && isNaN(leagueId)) {
        res.status(400).json({ message: 'Invalid league ID.' });
        return;
    }

    if (isNaN(seasonYear)) {
        res.status(400).json({ message: 'Invalid season year.' });
        return;
    }

    if (page && isNaN(pageNum)) {
        res.status(400).json({ message: 'Invalid page number.' });
        return;
    }

    if (search && (typeof searchTerm !== 'string' || searchTerm.length < 4)) {
        res.status(400).json({ message: 'Search term must be at least 4 characters.' });
        return;
    }

    // Build cache key
    let cacheKey = `${PLAYER_STATS_CACHE_KEY}season:${seasonYear}:`;
    if (playerId) cacheKey += `id:${playerId}`;
    else if (teamId) cacheKey += `team:${teamId}`;
    else if (leagueId) cacheKey += `league:${leagueId}`;

    if (searchTerm) cacheKey += `:search:${searchTerm}`;
    if (pageNum > 1) cacheKey += `:page:${pageNum}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving player statistics from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Build MongoDB filter
        const filter: Filter<any> = {};

        if (playerId) {
            filter._id = playerId;
        }

        // For team and league filters, we need to search in the statistics object
        // This is more complex and might require a different approach depending on your data structure
        // This is a simplified example
        if (teamId) {
            filter[`statistics.*.team.id`] = teamId;
        }

        if (leagueId) {
            filter[`statistics.*.league.id`] = leagueId;
        }

        if (searchTerm) {
            filter['profile.name'] = { $regex: searchTerm, $options: 'i' };
        }

        // Fetch from database
        const collection = getPlayersCollection();

        // Apply pagination if not searching by ID
        const limit = 20; // Number of results per page
        const skip = (pageNum - 1) * limit;

        let players;
        if (playerId) {
            // For a specific player, we can directly fetch the complete document
            players = await collection.find(filter).toArray();
        } else {
            // For team/league queries with pagination
            players = await collection.find(filter).skip(skip).limit(limit).toArray();
        }

        // Transform to match API response format
        // This transformation depends on your data structure
        const response = players.map(player => {
            return {
                player: player.profile,
                statistics: player.statistics ? Object.values(player.statistics).flat() : []
            };
        });

        // Cache the result
        const cacheTTL = playerId ? CACHE_TTL_DAY : CACHE_TTL_HOUR;
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(response));

        if (playerId && players.length === 0) {
            res.status(404).json({ message: 'Player statistics not found' });
            return;
        }

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching player statistics:', error);
        res.status(500).json({ message: 'Failed to fetch player statistics' });
    }
};

// GET /api/players/squads - Get player squads
const getPlayerSquadsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { team, player } = req.query;

    // Parse and validate parameters
    const teamId = team ? parseInt(team as string) : undefined;
    const playerId = player ? parseInt(player as string) : undefined;

    // Validate required parameters
    if (!teamId && !playerId) {
        res.status(400).json({ message: 'Either team or player parameter is required.' });
        return;
    }

    // Validate parameter types
    if (teamId && isNaN(teamId)) {
        res.status(400).json({ message: 'Invalid team ID.' });
        return;
    }

    if (playerId && isNaN(playerId)) {
        res.status(400).json({ message: 'Invalid player ID.' });
        return;
    }

    // Build cache key
    let cacheKey = `${PLAYER_SQUADS_CACHE_KEY}`;
    if (teamId) cacheKey += `team:${teamId}`;
    else if (playerId) cacheKey += `player:${playerId}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving player squads from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Build MongoDB filter
        const filter: Filter<any> = {};

        if (teamId) {
            filter['team.id'] = teamId;
        } else if (playerId) {
            filter['players.player.id'] = playerId;
        }

        // Fetch from database
        const collection = getPlayerSquadsCollection();
        const squads = await collection.find(filter).toArray();

        // Cache the result
        await redisClient.setex(cacheKey, CACHE_TTL_DAY, JSON.stringify(squads));

        if (squads.length === 0) {
            res.status(404).json({ message: 'Squad information not found' });
            return;
        }

        res.status(200).json(squads);

    } catch (error) {
        console.error('Error fetching player squads:', error);
        res.status(500).json({ message: 'Failed to fetch player squads' });
    }
};

// GET /api/players/topscorers - Get top scorers
const getTopScorersHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { league, season } = req.query;

    // Parse and validate parameters
    const leagueId = league ? parseInt(league as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;

    // Validate required parameters
    if (!leagueId || !seasonYear) {
        res.status(400).json({ message: 'League and season parameters are required.' });
        return;
    }

    // Validate parameter types
    if (isNaN(leagueId)) {
        res.status(400).json({ message: 'Invalid league ID.' });
        return;
    }

    if (isNaN(seasonYear)) {
        res.status(400).json({ message: 'Invalid season year.' });
        return;
    }

    // Build cache key
    const cacheKey = `${TOP_PLAYERS_CACHE_KEY}scorers:league:${leagueId}:season:${seasonYear}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving top scorers from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Fetch from database
        const collection = getTopPlayersCollection();
        const key = createTopPlayersKey(leagueId, seasonYear, 'scorers');
        const result = await collection.findOne({ _id: key });

        if (!result || !result.players || result.players.length === 0) {
            res.status(404).json({ message: 'Top scorers not found for the specified league and season' });
            return;
        }

        // Cache the result
        await redisClient.setex(cacheKey, CACHE_TTL_HOUR, JSON.stringify(result.players));

        res.status(200).json(result.players);

    } catch (error) {
        console.error('Error fetching top scorers:', error);
        res.status(500).json({ message: 'Failed to fetch top scorers' });
    }
};

// GET /api/players/topassists - Get top assists
const getTopAssistsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { league, season } = req.query;

    // Parse and validate parameters
    const leagueId = league ? parseInt(league as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;

    // Validate required parameters
    if (!leagueId || !seasonYear) {
        res.status(400).json({ message: 'League and season parameters are required.' });
        return;
    }

    // Validate parameter types
    if (isNaN(leagueId)) {
        res.status(400).json({ message: 'Invalid league ID.' });
        return;
    }

    if (isNaN(seasonYear)) {
        res.status(400).json({ message: 'Invalid season year.' });
        return;
    }

    // Build cache key
    const cacheKey = `${TOP_PLAYERS_CACHE_KEY}assists:league:${leagueId}:season:${seasonYear}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving top assists from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Fetch from database
        const collection = getTopPlayersCollection();
        const key = createTopPlayersKey(leagueId, seasonYear, 'assists');
        const result = await collection.findOne({ _id: key });

        if (!result || !result.players || result.players.length === 0) {
            res.status(404).json({ message: 'Top assists not found for the specified league and season' });
            return;
        }

        // Cache the result
        await redisClient.setex(cacheKey, CACHE_TTL_HOUR, JSON.stringify(result.players));

        res.status(200).json(result.players);

    } catch (error) {
        console.error('Error fetching top assists:', error);
        res.status(500).json({ message: 'Failed to fetch top assists' });
    }
};

// GET /api/players/topyellowcards - Get top yellow cards
const getTopYellowCardsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { league, season } = req.query;

    // Parse and validate parameters
    const leagueId = league ? parseInt(league as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;

    // Validate required parameters
    if (!leagueId || !seasonYear) {
        res.status(400).json({ message: 'League and season parameters are required.' });
        return;
    }

    // Validate parameter types
    if (isNaN(leagueId)) {
        res.status(400).json({ message: 'Invalid league ID.' });
        return;
    }

    if (isNaN(seasonYear)) {
        res.status(400).json({ message: 'Invalid season year.' });
        return;
    }

    // Build cache key
    const cacheKey = `${TOP_PLAYERS_CACHE_KEY}yellowcards:league:${leagueId}:season:${seasonYear}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving top yellow cards from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Fetch from database
        const collection = getTopPlayersCollection();
        const key = createTopPlayersKey(leagueId, seasonYear, 'yellowcards');
        const result = await collection.findOne({ _id: key });

        if (!result || !result.players || result.players.length === 0) {
            res.status(404).json({ message: 'Top yellow cards not found for the specified league and season' });
            return;
        }

        // Cache the result
        await redisClient.setex(cacheKey, CACHE_TTL_HOUR, JSON.stringify(result.players));

        res.status(200).json(result.players);

    } catch (error) {
        console.error('Error fetching top yellow cards:', error);
        res.status(500).json({ message: 'Failed to fetch top yellow cards' });
    }
};

// GET /api/players/topredcards - Get top red cards
const getTopRedCardsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { league, season } = req.query;

    // Parse and validate parameters
    const leagueId = league ? parseInt(league as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;

    // Validate required parameters
    if (!leagueId || !seasonYear) {
        res.status(400).json({ message: 'League and season parameters are required.' });
        return;
    }

    // Validate parameter types
    if (isNaN(leagueId)) {
        res.status(400).json({ message: 'Invalid league ID.' });
        return;
    }

    if (isNaN(seasonYear)) {
        res.status(400).json({ message: 'Invalid season year.' });
        return;
    }

    // Build cache key
    const cacheKey = `${TOP_PLAYERS_CACHE_KEY}redcards:league:${leagueId}:season:${seasonYear}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving top red cards from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Fetch from database
        const collection = getTopPlayersCollection();
        const key = createTopPlayersKey(leagueId, seasonYear, 'redcards');
        const result = await collection.findOne({ _id: key });

        if (!result || !result.players || result.players.length === 0) {
            res.status(404).json({ message: 'Top red cards not found for the specified league and season' });
            return;
        }

        // Cache the result
        await redisClient.setex(cacheKey, CACHE_TTL_HOUR, JSON.stringify(result.players));

        res.status(200).json(result.players);

    } catch (error) {
        console.error('Error fetching top red cards:', error);
        res.status(500).json({ message: 'Failed to fetch top red cards' });
    }
};

// GET /api/players/teams - Get player teams history
const getPlayerTeamsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { player } = req.query;

    // Parse and validate parameters
    const playerId = player ? parseInt(player as string) : undefined;

    // Validate required parameters
    if (!playerId) {
        res.status(400).json({ message: 'Player parameter is required.' });
        return;
    }

    // Validate parameter types
    if (isNaN(playerId)) {
        res.status(400).json({ message: 'Invalid player ID.' });
        return;
    }

    // Build cache key
    const cacheKey = `${PLAYER_TEAMS_CACHE_KEY}player:${playerId}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving player teams from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Fetch from database
        const collection = getPlayersCollection();
        const player = await collection.findOne({ _id: playerId }, { projection: { 'teams': 1 } });

        if (!player || !player.teams || player.teams.length === 0) {
            res.status(404).json({ message: 'Player teams history not found' });
            return;
        }

        // Cache the result
        await redisClient.setex(cacheKey, CACHE_TTL_WEEK, JSON.stringify(player.teams));

        res.status(200).json(player.teams);

    } catch (error) {
        console.error('Error fetching player teams history:', error);
        res.status(500).json({ message: 'Failed to fetch player teams history' });
    }
}; // Added semicolon

// GET /api/players/seasons - Get available seasons
const getPlayerSeasonsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { player } = req.query;

    // Parse and validate parameters
    const playerId = player ? parseInt(player as string) : undefined;

    if (player && isNaN(playerId as number)) {
        res.status(400).json({ message: 'Invalid player ID.' });
        return;
    }

    // Build cache key
    let cacheKey = `${PLAYER_SEASONS_CACHE_KEY}`;
    if (playerId) cacheKey += `player:${playerId}`;
    else cacheKey += 'all';

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving player seasons from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        const collection = getPlayersCollection();
        let seasons: number[] = [];

        if (playerId) {
            // Fetch seasons for a specific player
            const playerDoc = await collection.findOne({ _id: playerId }, { projection: { statistics: 1 } });

            if (!playerDoc) {
                res.status(404).json({ message: 'Player not found' });
                return;
            }

            // Assuming statistics object keys are season years (as strings)
            if (playerDoc.statistics) {
                seasons = Object.keys(playerDoc.statistics)
                                .map(year => parseInt(year))
                                .filter(year => !isNaN(year)) // Ensure keys are valid numbers
                                .sort((a, b) => b - a); // Sort descending
            }
        } else {
            // Fetch all unique seasons across all players using aggregation
            const pipeline: Document[] = [
                { $match: { statistics: { $exists: true, $ne: null } } }, // Ensure statistics field exists and is not null
                { $project: { seasonKeys: { $objectToArray: "$statistics" } } }, // Convert statistics object to array of key-value pairs
                { $unwind: "$seasonKeys" }, // Deconstruct the array
                { $group: { _id: null, uniqueSeasons: { $addToSet: "$seasonKeys.k" } } } // Group to find unique keys (seasons)
            ];

            const result = await collection.aggregate(pipeline).toArray();

            if (result.length > 0 && result[0].uniqueSeasons) {
                seasons = result[0].uniqueSeasons
                                .map((year: string) => parseInt(year))
                                .filter((year: number) => !isNaN(year)) // Ensure keys are valid numbers
                                .sort((a: number, b: number) => b - a); // Sort descending
            }
        }

        // Cache the result
        const cacheTTL = playerId ? CACHE_TTL_WEEK : CACHE_TTL_DAY;
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(seasons));

        res.status(200).json(seasons);

    } catch (error) {
        console.error('Error fetching player seasons:', error);
        res.status(500).json({ message: 'Failed to fetch player seasons' });
    }
};

// GET /api/players/profiles - Get all available players (Renamed from previous getAllPlayerProfilesHandler)
const searchPlayerProfilesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { search } = req.query;

    // Build cache key
    const cacheKey = search
        ? `${PLAYER_PROFILES_CACHE_KEY}search:${search}`
        : PLAYER_PROFILES_CACHE_KEY;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving player profiles from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Fetch from database
        const collection = getPlayersCollection();
        let query = {};

        // Add search filter if provided
        if (search) {
            query = {
                $or: [
                    { name: { $regex: search, $options: 'i' } },
                    { firstname: { $regex: search, $options: 'i' } },
                    { lastname: { $regex: search, $options: 'i' } }
                ]
            };
        }

        const players = await collection.find(query, {
            projection: {
                _id: 1,
                name: 1,
                firstname: 1,
                lastname: 1,
                age: 1,
                nationality: 1,
                height: 1,
                weight: 1,
                photo: 1
            }
        }).toArray();

        if (!players || players.length === 0) {
            res.status(404).json({ message: 'No players found' });
            return;
        }

        // Cache the result
        await redisClient.setex(cacheKey, CACHE_TTL_DAY, JSON.stringify(players));

        res.status(200).json(players);

    } catch (error) {
        console.error('Error fetching player profiles:', error);
        res.status(500).json({ message: 'Failed to fetch player profiles' });
    }
};

// Register routes
router.get('/profiles', getPlayerProfilesHandler); // Handler for player profiles with filtering by ID or search term
router.get('/all-profiles', searchPlayerProfilesHandler); // Handler for searching player profiles (previously getAllPlayerProfilesHandler)
router.get('/statistics', getPlayerStatisticsHandler);
router.get('/squads', getPlayerSquadsHandler);
router.get('/seasons', getPlayerSeasonsHandler); // Added route for player seasons
router.get('/topscorers', getTopScorersHandler);
router.get('/topassists', getTopAssistsHandler);
router.get('/topyellowcards', getTopYellowCardsHandler);
router.get('/topredcards', getTopRedCardsHandler);
router.get('/teams', getPlayerTeamsHandler);

export default router;
