import express, { Router, RequestHandler } from 'express'; // Import RequestHandler
import { getCountriesCollection, Country } from '../models/Country';
import { getRedisClient } from '../config/redis';
import { Filter } from 'mongodb';

const router: Router = express.Router();
const COUNTRIES_BASE_CACHE_KEY = 'countries:';
const COUNTRIES_ALL_CACHE_KEY = 'countries:all';
const CACHE_TTL_SECONDS = 60 * 60; // Cache for 1 hour

// Define the handler function with RequestHandler type
const getCountriesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const queryParams = req.query;

    // Build filter based on query parameters
    const filter: Filter<Country> = {};
    let cacheKey: string | null = null;

    // Check if we have any query parameters
    const isFiltered = Object.keys(queryParams).length > 0;

    if (queryParams.name) {
        filter.name = queryParams.name as string;
        cacheKey = `${COUNTRIES_BASE_CACHE_KEY}name:${queryParams.name}`;
    } else if (queryParams.code) {
        filter.code = queryParams.code as string;
        cacheKey = `${COUNTRIES_BASE_CACHE_KEY}code:${queryParams.code}`;
    } else if (queryParams.search) {
        const searchTerm = queryParams.search as string;
        filter.name = { $regex: searchTerm, $options: 'i' }; // Case-insensitive search
        cacheKey = `${COUNTRIES_BASE_CACHE_KEY}search:${searchTerm}`;
    } else {
        // No filters, use the all countries cache key
        cacheKey = COUNTRIES_ALL_CACHE_KEY;
    }

    try {
        // 1. Check cache first if we have a cache key
        if (cacheKey) {
            const cachedCountries = await redisClient.get(cacheKey);
            if (cachedCountries) {
                console.log(`Serving countries from cache (Key: ${cacheKey})`);
                res.status(200).json(JSON.parse(cachedCountries));
                return; // Exit function after sending response
            }
        }

        // 2. If not in cache, fetch from DB
        console.log('Fetching countries from DB with filter:', JSON.stringify(filter));
        const collection = getCountriesCollection();
        // Fetch countries with filter, sort by name, exclude MongoDB _id and lastUpdated
        const countries = await collection.find(filter, { projection: { _id: 0, lastUpdated: 0 } }).sort({ name: 1 }).toArray();

        // 3. Store in cache if we have a cache key
        if (cacheKey && countries.length > 0) {
            await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(countries));
            console.log(`Countries stored in cache (Key: ${cacheKey})`);
        }

        // 4. Return empty array with 404 if no countries found with filter
        if (isFiltered && countries.length === 0) {
            res.status(404).json({ message: 'No countries found matching the criteria' });
            return;
        }

        res.status(200).json(countries);

    } catch (error) {
        console.error('Error fetching countries:', error);
        res.status(500).json({ message: 'Failed to fetch countries' });
    }
};

// GET /api/countries - Fetches all countries (with caching)
router.get('/', getCountriesHandler); // Use the defined handler

// Add other country-related routes here if needed (e.g., GET /api/countries/:code)

export default router;
