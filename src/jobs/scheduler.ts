import cron from 'node-cron';
import { getRedisClient } from '../config/redis'; // For potential locking or status checks
// getDb might not be needed directly here if jobs import models/collections
// import { getDb } from '../config/database';
import { fetchAndUpdateCountries } from './countryJobs';
import { fetchAndUpdateLeagueSeasons } from './leagueSeasonJobs';
import { fetchAndCacheTimezones } from './timezoneJobs';
import { monitorApiStatus } from './statusJobs';
import { fetchAndUpdateLeagues } from './leagueJobs';
import { fetchAndUpdateLeagueCoverage } from './leagueCoverageJobs';
import { fetchAndUpdateTeamsForActiveLeagues } from './teamJobs';
import { fetchAndUpdateTeamStatistics } from './teamStatisticJobs';
import { fetchAndUpdateVenuesForActiveTeams } from './venueJobs';
import { fetchAndUpdateStandings } from './standingJobs';
import { fetchAndUpdateFixtureRounds } from './fixtureRoundJobs';
import { fetchAndUpdateLiveFixtures, fetchAndUpdateUpcomingFixtures, fetchAndUpdateUpcomingFixtureLineups, fetchAndUpdateCupCompetitionFixtures } from './fixtureJobs';
import { fetchAndUpdateInjuriesForUpcomingFixtures } from './injuryJobs';
import { fetchAndUpdatePlayerProfiles, fetchAndUpdatePlayerStatistics, fetchAndUpdatePlayerSquads, fetchAndUpdateTopPlayers } from './playerJobs';
import { fetchAndUpdateCoaches } from './coachJobs';
import { fetchAndUpdateTeamTransfers, fetchAndUpdatePlayerTransfers } from './transferJobs';
import { fetchAndUpdateTeamSidelined, fetchAndUpdateLeagueSidelined } from './sidelinedJobs';
import { fetchAndUpdateBookmakers, fetchAndUpdateBets, fetchAndUpdateOdds, fetchAndUpdateLiveOdds } from './oddsJobs';
import { fetchAndUpdatePredictionsForUpcomingFixtures } from './predictionJobs'; // Import the prediction job
import { trainDixonColesModelsForActiveLeagues, generateEnhancedPredictionsForUpcomingFixtures, refreshDixonColesModels } from './enhancedPredictionJobs'; // Import enhanced prediction jobs
import { upcomingFixtureNotificationJob } from './notificationJobs'; // Import the notification job
import { chatManagementJob } from './chatJobs'; // Import the chat job
import { initializeEloJobs } from './eloJobs'; // Import ELO jobs

console.log('🔧 Initializing job scheduler...');

// Track scheduler initialization
let jobsInitialized = 0;

// Example structure for adding jobs:
/*
cron.schedule('0 * * * *', async () => { // Example: Run every hour at minute 0
  console.log('Running hourly job...');
  try {
    // const redis = getRedisClient();
    // const db = getDb();
    // Add job logic here: fetch data, update DB, update cache
    // Example: await fetchAndUpdateLeagues();
  } catch (error) {
    console.error('Error running hourly job:', error);
  }
});
*/

// Add more jobs here based on API endpoint recommendations...

// --- Jobs for Infrequently Updated Data ---

// Fetch Timezones (Run weekly since timezones rarely change)
cron.schedule('0 2 * * 0', async () => { // Run weekly on Sunday at 2:00 AM
  console.log('Running weekly job: Fetch Timezones...');
  try {
    await fetchAndCacheTimezones();
  } catch (error) {
    console.error('Error running fetchTimezones job:', error);
  }
});

// Monitor API Status (Run every hour to track usage)
cron.schedule('0 * * * *', async () => { // Run every hour
  console.log('Running hourly job: Monitor API Status...');
  try {
    await monitorApiStatus();
  } catch (error) {
    console.error('Error running API status monitoring job:', error);
  }
});

// Fetch Countries (Recommended: 1 call per day)
cron.schedule('0 3 * * *', async () => { // Run daily at 3:00 AM
  console.log('Running daily job: Fetch Countries...');
  try {
    await fetchAndUpdateCountries(); // Call the imported job function
  } catch (error) {
    console.error('Error running fetchCountries job:', error);
  }
});

// Fetch League Seasons (Recommended: 1 call per day)
cron.schedule('5 3 * * *', async () => { // Run daily at 3:05 AM (staggered after countries)
  console.log('Running daily job: Fetch League Seasons...');
  try {
    await fetchAndUpdateLeagueSeasons(); // Call the imported job function
  } catch (error) {
    console.error('Error running fetchLeagueSeasons job:', error);
  }
});


// --- Jobs for More Frequently Updated Data ---

// Fetch Leagues (Recommended: 1 call per hour)
cron.schedule('10 * * * *', async () => { // Run hourly at minute 10 (staggered)
  console.log('Running hourly job: Fetch Leagues...');
  try {
    await fetchAndUpdateLeagues(); // Call the imported job function
  } catch (error) {
    console.error('Error running fetchLeagues job:', error);
  }
});

// Fetch League Coverage (Recommended: Once per week)
cron.schedule('0 2 * * 1', async () => { // Run weekly on Monday at 2:00 AM
  console.log('Running weekly job: Fetch League Coverage...');
  try {
    await fetchAndUpdateLeagueCoverage(); // Call the imported job function
  } catch (error) {
    console.error('Error running fetchAndUpdateLeagueCoverage job:', error);
  }
});


// Fetch Teams for Active Leagues (Recommended: 1 call per day - but needs active leagues first)
// Run after fetching leagues/seasons
cron.schedule('15 3 * * *', async () => { // Run daily at 3:15 AM (staggered)
  console.log('Running daily job: Fetch Teams for Active Leagues...');
  try {
    await fetchAndUpdateTeamsForActiveLeagues(); // Call the imported job function
  } catch (error) {
    console.error('Error running fetchTeams job:', error);
  }
});

// Fetch Team Statistics (Optimized: Once per day, with weekly full refresh on Mondays)
// Run after teams have been updated
cron.schedule('0 4 * * *', async () => { // Run daily at 4:00 AM
  console.log('Running daily job: Fetch Team Statistics (optimized for API usage)...');
  try {
    await fetchAndUpdateTeamStatistics(); // Call the optimized job function
  } catch (error) {
    console.error('Error running fetchTeamStatistics job:', error);
  }
});

// Fetch Venues for Active Teams (Recommended: 1 call per day)
// Run after teams have been updated
cron.schedule('20 3 * * *', async () => { // Run daily at 3:20 AM (staggered)
  console.log('Running daily job: Fetch Venues for Active Teams...');
  try {
    await fetchAndUpdateVenuesForActiveTeams(); // Call the imported job function
  } catch (error) {
    console.error('Error running fetchVenues job:', error);
  }
});

// Fetch Standings (Recommended: 1 call per hour for active leagues)
cron.schedule('15 * * * *', async () => { // Run hourly at minute 15 (staggered)
  console.log('Running hourly job: Fetch Standings...');
  try {
    await fetchAndUpdateStandings(); // Call the imported job function
  } catch (error) {
    console.error('Error running fetchStandings job:', error);
  }
});

// Fetch Fixture Rounds (Recommended: 1 call per day)
cron.schedule('25 3 * * *', async () => { // Run daily at 3:25 AM (staggered)
  console.log('Running daily job: Fetch Fixture Rounds...');
  try {
    await fetchAndUpdateFixtureRounds(); // Call the imported job function
  } catch (error) {
    console.error('Error running fetchFixtureRounds job:', error);
  }
});

// Note: The 'Fetch Today's Fixtures' job has been removed as the enhanced 15-second live fixtures job
// now handles both live fixtures and the transition to finished matches. The daily 'Fetch Upcoming Fixtures'
// job ensures all fixtures for the current date are updated at least once per day.

// Fetch Live Fixtures (Run very frequently for live updates)
// Using a custom interval for 15 seconds since cron doesn't support seconds natively
setInterval(async () => {
  console.log('Running live fixtures job (15-second interval)...');
  try {
    await fetchAndUpdateLiveFixtures();
  } catch (error) {
    console.error('Error running fetchAndUpdateLiveFixtures job:', error);
  }
}, 15 * 1000); // 15 seconds in milliseconds

// Fetch Upcoming Fixtures (Run daily to keep upcoming fixtures updated)
cron.schedule('0 2 * * *', async () => { // Run daily at 2:00 AM
  console.log('Running daily job: Fetch Upcoming Fixtures...');
  try {
    // Fetch fixtures for the next 7 days
    await fetchAndUpdateUpcomingFixtures(7);
  } catch (error) {
    console.error('Error running fetchAndUpdateUpcomingFixtures job:', error);
  }
});

// Fetch Cup Competition Fixtures (Run twice daily to ensure we get all rounds including future ones)
cron.schedule('30 2 * * *', async () => { // Run daily at 2:30 AM
  console.log('Running daily job: Fetch Cup Competition Fixtures...');
  try {
    await fetchAndUpdateCupCompetitionFixtures();
  } catch (error) {
    console.error('Error running fetchAndUpdateCupCompetitionFixtures job:', error);
  }
});

// Additional Cup Competition Fixtures job (Run twice daily for important competitions)
cron.schedule('30 14 * * *', async () => { // Run daily at 2:30 PM
  console.log('Running afternoon job: Fetch Cup Competition Fixtures...');
  try {
    await fetchAndUpdateCupCompetitionFixtures();
  } catch (error) {
    console.error('Error running fetchAndUpdateCupCompetitionFixtures job:', error);
  }
});

// Fetch Injuries for Upcoming Fixtures (Recommended: Every 4 hours)
cron.schedule('0 */4 * * *', async () => { // Run every 4 hours (at minute 0)
  console.log('Running 4-hourly job: Fetch Injuries...');
  try {
    await fetchAndUpdateInjuriesForUpcomingFixtures();
  } catch (error) {
    console.error('Error running fetchInjuries job:', error);
  }
});


// --- Player Jobs ---

// Fetch Player Profiles (Recommended: Weekly or less frequently)
cron.schedule('0 1 * * 0', async () => { // Run weekly on Sunday at 1:00 AM
  console.log('Running weekly job: Fetch Player Profiles...');
  try {
    // Fetch first 10 pages of player profiles (adjust as needed)
    await fetchAndUpdatePlayerProfiles(10);
  } catch (error) {
    console.error('Error running fetchAndUpdatePlayerProfiles job:', error);
  }
});

// Fetch Player Statistics (Recommended: Daily)
cron.schedule('30 3 * * *', async () => { // Run daily at 3:30 AM
  console.log('Running daily job: Fetch Player Statistics...');
  try {
    await fetchAndUpdatePlayerStatistics();
  } catch (error) {
    console.error('Error running fetchAndUpdatePlayerStatistics job:', error);
  }
});

// Fetch Player Squads (Recommended: Weekly)
cron.schedule('0 2 * * 0', async () => { // Run weekly on Sunday at 2:00 AM
  console.log('Running weekly job: Fetch Player Squads...');
  try {
    await fetchAndUpdatePlayerSquads();
  } catch (error) {
    console.error('Error running fetchAndUpdatePlayerSquads job:', error);
  }
});

// Fetch Top Players (Recommended: Daily)
cron.schedule('0 4 * * *', async () => { // Run daily at 4:00 AM
  console.log('Running daily job: Fetch Top Players...');
  try {
    await fetchAndUpdateTopPlayers();
  } catch (error) {
    console.error('Error running fetchAndUpdateTopPlayers job:', error);
  }
});

// --- Coach Jobs ---

// Fetch Coaches (Recommended: Weekly)
cron.schedule('0 3 * * 0', async () => { // Run weekly on Sunday at 3:00 AM
  console.log('Running weekly job: Fetch Coaches...');
  try {
    await fetchAndUpdateCoaches();
  } catch (error) {
    console.error('Error running fetchAndUpdateCoaches job:', error);
  }
});

// --- Transfer Jobs ---

// Fetch Team Transfers (Recommended: Weekly)
cron.schedule('0 4 * * 0', async () => { // Run weekly on Sunday at 4:00 AM
  console.log('Running weekly job: Fetch Team Transfers...');
  try {
    await fetchAndUpdateTeamTransfers();
  } catch (error) {
    console.error('Error running fetchAndUpdateTeamTransfers job:', error);
  }
});

// Fetch Player Transfers (Recommended: Weekly)
cron.schedule('30 4 * * 0', async () => { // Run weekly on Sunday at 4:30 AM
  console.log('Running weekly job: Fetch Player Transfers...');
  try {
    await fetchAndUpdatePlayerTransfers();
  } catch (error) {
    console.error('Error running fetchAndUpdatePlayerTransfers job:', error);
  }
});

// --- Sidelined Jobs ---

// Fetch Team Sidelined (Recommended: Daily)
cron.schedule('0 5 * * *', async () => { // Run daily at 5:00 AM
  console.log('Running daily job: Fetch Team Sidelined...');
  try {
    await fetchAndUpdateTeamSidelined();
  } catch (error) {
    console.error('Error running fetchAndUpdateTeamSidelined job:', error);
  }
});

// Fetch League Sidelined (Recommended: Daily)
cron.schedule('30 5 * * *', async () => { // Run daily at 5:30 AM
  console.log('Running daily job: Fetch League Sidelined...');
  try {
    await fetchAndUpdateLeagueSidelined();
  } catch (error) {
    console.error('Error running fetchAndUpdateLeagueSidelined job:', error);
  }
});

// --- Odds Jobs ---

// Fetch Bookmakers (Recommended: Daily)
cron.schedule('0 6 * * *', async () => { // Run daily at 6:00 AM
  console.log('Running daily job: Fetch Bookmakers...');
  try {
    await fetchAndUpdateBookmakers();
  } catch (error) {
    console.error('Error running fetchAndUpdateBookmakers job:', error);
  }
});

// Fetch Bet Types (Recommended: Daily)
cron.schedule('30 6 * * *', async () => { // Run daily at 6:30 AM
  console.log('Running daily job: Fetch Bet Types...');
  try {
    await fetchAndUpdateBets();
  } catch (error) {
    console.error('Error running fetchAndUpdateBets job:', error);
  }
});

// Fetch Pre-match Odds (Recommended: Every 3 hours)
cron.schedule('0 */3 * * *', async () => { // Run every 3 hours
  console.log('Running 3-hourly job: Fetch Pre-match Odds...');
  try {
    await fetchAndUpdateOdds();
  } catch (error) {
    console.error('Error running fetchAndUpdateOdds job:', error);
  }
});

// Fetch Live Odds (Updated: Every 5 minutes for active fixtures)
cron.schedule('*/5 * * * *', async () => { // Run every 5 minutes
  console.log('Running live odds job (5-minute interval)...');
  try {
    await fetchAndUpdateLiveOdds();
  } catch (error) {
    console.error('Error running fetchAndUpdateLiveOdds job:', error);
  }
});

// Fetch Lineups for Upcoming Fixtures (Run every 10 minutes to catch pre-match lineups)
cron.schedule('*/10 * * * *', async () => { // Run every 10 minutes
  console.log('Running upcoming fixture lineups job (10-minute interval)...');
  try {
    // Check for lineups of fixtures starting in the next 3 hours
    await fetchAndUpdateUpcomingFixtureLineups(3);
  } catch (error) {
    console.error('Error running fetchAndUpdateUpcomingFixtureLineups job:', error);
  }
});

// --- Prediction Jobs ---

// Fetch Predictions for Upcoming Fixtures (Recommended: Every hour based on API documentation)
console.log('📅 Scheduling basic prediction job: Every hour at minute 20');
cron.schedule('20 * * * *', async () => { // Run hourly at minute 20 (staggered)
  console.log('🔄 Running hourly job: Fetch Predictions for Upcoming Fixtures...');
  try {
    // Fetch predictions for fixtures in the next 2 days
    await fetchAndUpdatePredictionsForUpcomingFixtures(2);
    console.log('✅ Basic prediction job completed successfully');
  } catch (error) {
    console.error('❌ Error running fetchAndUpdatePredictionsForUpcomingFixtures job:', error);
  }
});
jobsInitialized++;

// --- Enhanced Prediction Jobs ---

// Train Dixon-Coles Models for Active Leagues (Run daily to keep team strengths current)
console.log('📅 Scheduling enhanced model training job: Daily at 1:00 AM');
cron.schedule('0 1 * * *', async () => { // Run daily at 1:00 AM
  console.log('🧠 Running daily job: Train Dixon-Coles Models for Active Leagues...');
  try {
    await trainDixonColesModelsForActiveLeagues();
    console.log('✅ Enhanced model training job completed successfully');
  } catch (error) {
    console.error('❌ Error running trainDixonColesModelsForActiveLeagues job:', error);
  }
});
jobsInitialized++;

// Generate Enhanced Predictions for Upcoming Fixtures (Run daily to ensure predictions are available)
console.log('📅 Scheduling enhanced prediction generation job: Daily at 4:00 AM');
cron.schedule('0 4 * * *', async () => { // Run daily at 4:00 AM (after model training)
  console.log('🎯 Running daily job: Generate Enhanced Predictions for Upcoming Fixtures...');
  try {
    // Generate predictions for fixtures in the next 7 days
    await generateEnhancedPredictionsForUpcomingFixtures(7);
    console.log('✅ Enhanced prediction generation job completed successfully');
  } catch (error) {
    console.error('❌ Error running generateEnhancedPredictionsForUpcomingFixtures job:', error);
  }
});
jobsInitialized++;

// Refresh Dixon-Coles Models (Run weekly to retrain models with latest match results)
cron.schedule('0 2 * * 0', async () => { // Run weekly on Sunday at 2:00 AM
  console.log('Running weekly job: Refresh Dixon-Coles Models...');
  try {
    await refreshDixonColesModels();
  } catch (error) {
    console.error('Error running refreshDixonColesModels job:', error);
  }
});

// --- Custom Notification Jobs ---

// Start the upcoming fixture notification job (defined in notificationJobs.ts)
upcomingFixtureNotificationJob.start();
console.log(`Upcoming fixture notification job scheduled: ${upcomingFixtureNotificationJob.nextDate().toString()}`); // Use toString() for Luxon DateTime

// Start the chat jobs (defined in chatJobs.ts)
// Note: The chatManagementJob is now started in server.ts after Socket.IO is initialized
// This ensures that the Socket.IO instance is available for sending notifications

// Initialize ELO rating jobs
console.log('🎯 Initializing ELO rating jobs...');
initializeEloJobs();

console.log(`✅ Job scheduler initialized successfully! ${jobsInitialized} prediction jobs scheduled:`);
console.log('   📅 Basic Predictions: Every hour at :20');
console.log('   🧠 Enhanced Model Training: Daily at 1:00 AM');
console.log('   🎯 Enhanced Predictions: Daily at 4:00 AM');
console.log('   🔄 Model Refresh: Weekly Sunday at 2:00 AM');
console.log('   ⚡ ELO Ratings Sync: Daily at 2:00 AM');

// Export a function to potentially stop jobs if needed, though usually not required
export function stopScheduler() {
  cron.getTasks().forEach(task => task.stop());
  console.log('Job scheduler stopped.');
}
