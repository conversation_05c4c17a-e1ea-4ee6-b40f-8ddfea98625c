/**
 * ELO Rating Jobs
 * 
 * Scheduled jobs for ELO rating management:
 * 1. Daily sync of ELO ratings from ClubElo API
 * 2. Team name mapping updates
 * 3. Enhanced strength calculations
 */

import cron from 'node-cron';
import { syncEloRatings } from '../services/eloService';

/**
 * Schedule daily ELO ratings sync
 * Runs every day at 2:00 AM UTC
 */
export function scheduleEloSync(): void {
    console.log('Scheduling daily ELO ratings sync...');
    
    // Run at 2:00 AM UTC daily
    cron.schedule('0 2 * * *', async () => {
        console.log('Starting scheduled ELO ratings sync...');
        
        try {
            await syncEloRatings();
            console.log('Scheduled ELO ratings sync completed successfully');
        } catch (error) {
            console.error('Error in scheduled ELO ratings sync:', error);
        }
    }, {
        timezone: 'UTC'
    });
    
    console.log('ELO ratings sync scheduled for 2:00 AM UTC daily');
}

/**
 * Manual ELO sync trigger (for testing/manual updates)
 */
export async function triggerManualEloSync(): Promise<void> {
    console.log('Triggering manual ELO ratings sync...');
    
    try {
        await syncEloRatings();
        console.log('Manual ELO ratings sync completed successfully');
    } catch (error) {
        console.error('Error in manual ELO ratings sync:', error);
        throw error;
    }
}

/**
 * Initialize ELO jobs
 */
export function initializeEloJobs(): void {
    console.log('Initializing ELO rating jobs...');
    
    // Schedule daily sync
    scheduleEloSync();
    
    // Run initial sync if no data exists
    setTimeout(async () => {
        try {
            const { getEloRatingsCollection } = await import('../models/EloRating');
            const collection = getEloRatingsCollection();
            const count = await collection.countDocuments();
            
            if (count === 0) {
                console.log('No ELO ratings found, running initial sync...');
                await syncEloRatings();
            } else {
                console.log(`Found ${count} existing ELO ratings`);
            }
        } catch (error) {
            console.error('Error checking ELO ratings:', error);
        }
    }, 5000); // Wait 5 seconds for database connection
    
    console.log('ELO rating jobs initialized');
}
