import { <PERSON><PERSON><PERSON><PERSON> } from 'cron';
import { getFixturesCollection } from '../models/Fixture';
import { closeChat, openChat } from '../models/Message';
import { getSocketIO } from '../server';
import { closeFixtureChat, notifyFixtureChatClosing, notifyFixtureChatOpening } from '../services/chatService';

// Check for upcoming and finished fixtures every minute
export const chatManagementJob = new CronJob(
  '* * * * *', // Every minute
  async function() {
    try {
      console.log('Running chatManagementJob...');

      const fixtureCollection = getFixturesCollection();
      const now = new Date();

      // PART 1: OPEN CHATS FOR UPCOMING FIXTURES
      // Find fixtures that are about to start in 10 minutes and haven't been processed for chat opening yet
      const tenMinutesFromNow = new Date(now.getTime() + 10 * 60 * 1000);
      const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

      const upcomingFixtures = await fixtureCollection.find({
        'fixture.status.short': 'NS',
        'fixture.date': { $gte: fiveMinutesFromNow, $lte: tenMinutesFromNow },
        'chatOpeningNotified': { $ne: true }
      }).toArray();

      console.log(`Found ${upcomingFixtures.length} fixtures to open chat`);

      for (const fixture of upcomingFixtures) {
        // Open the chat
        await openChat(fixture._id);

        // Mark the fixture as notified about chat opening
        await fixtureCollection.updateOne(
          { _id: fixture._id },
          { $set: { chatOpeningNotified: true } }
        );

        // Notify users that chat is now open
        const io = getSocketIO();
        if (io) {
          notifyFixtureChatOpening(io, fixture._id);
        }

        console.log(`Opened chat for fixture ${fixture._id}`);
      }

      // PART 2: CLOSE CHATS FOR FINISHED FIXTURES
      // Find fixtures that have just finished (status changed to FT, AET, PEN, etc.)
      // and haven't been processed for chat closing yet
      const finishedFixtures = await fixtureCollection.find({
        'fixture.status.short': { $in: ['FT', 'AET', 'PEN'] },
        'chatClosingNotified': { $ne: true }
      }).toArray();

      console.log(`Found ${finishedFixtures.length} newly finished fixtures`);

      for (const fixture of finishedFixtures) {
        // Mark the fixture as notified about chat closing
        await fixtureCollection.updateOne(
          { _id: fixture._id },
          { $set: { chatClosingNotified: true, chatClosingTime: new Date(now.getTime() + 20 * 60 * 1000) } }
        );

        // Notify users that chat will close in 20 minutes
        const io = getSocketIO();
        if (io) {
          notifyFixtureChatClosing(io, fixture._id);
        }

        console.log(`Notified chat closing for fixture ${fixture._id}`);
      }

      // Find fixtures whose chat should be closed now (20 minutes after being marked for closing)
      const fixturesToCloseChat = await fixtureCollection.find({
        'chatClosingNotified': true,
        'chatClosed': { $ne: true },
        'chatClosingTime': { $lte: now }
      }).toArray();

      console.log(`Found ${fixturesToCloseChat.length} fixtures to close chat`);

      for (const fixture of fixturesToCloseChat) {
        // Close the chat
        await closeChat(fixture._id);

        // Mark the fixture as chat closed
        await fixtureCollection.updateOne(
          { _id: fixture._id },
          { $set: { chatClosed: true } }
        );

        // Notify users that chat is now closed
        const io = getSocketIO();
        if (io) {
          closeFixtureChat(io, fixture._id);
        }

        console.log(`Closed chat for fixture ${fixture._id}`);
      }
    } catch (error) {
      console.error('Error in chatManagementJob:', error);
    }
  },
  null, // onComplete
  false, // start
  'UTC' // timezone
);
