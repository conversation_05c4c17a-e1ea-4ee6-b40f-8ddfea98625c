import { fetchStandings } from '../services/apiFootball';
import { getStandingsCollection, Standing, createStandingId } from '../models/Standing';
import { getLeaguesCollection } from '../models/League';
import { getLeagueCoverageCollection } from '../models/LeagueCoverage';
import { targetedLeagues } from '../config/targetedLeagues';
import { getDb } from '../config/database';
import { shouldUpdateStandings, recordStandingsUpdate } from '../models/StandingUpdate';
import dayjs from 'dayjs';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export async function fetchAndUpdateStandings() {
    console.log('Starting fetchAndUpdateStandings job...');
    try {
        const leaguesCollection = getLeaguesCollection();
        const coverageCollection = getLeagueCoverageCollection();
        const standingsCollection = getStandingsCollection();
        const now = new Date();
        const today = dayjs().format('YYYY-MM-DD');

        // Array to store leagues that need updates
        const leagueUpdates = [];

        // 1. Get fixtures for today that have completed
        const fixturesCollection = getDb().collection('fixtures');
        const todaysFixtures = await fixturesCollection.find(
            {
                'fixture.date': { $regex: `^${today}` },
                'fixture.status.short': { $in: ['FT', 'AET', 'PEN', 'AWD', 'WO'] } // Only completed fixtures
            },
            {
                projection: {
                    'league.id': 1,
                    'fixture.id': 1,
                    'fixture.date': 1,
                    'fixture.status.short': 1,
                    'league.season': 1
                }
            }
        ).toArray();

        let fixtureBasedUpdates = false;
        if (todaysFixtures.length > 0) {
            console.log(`Found ${todaysFixtures.length} completed fixtures for today.`);
            fixtureBasedUpdates = true;
        } else {
            console.log('No completed fixtures found for today. Will check for time-based updates.');
        }

        // 2. Find all targeted leagues with coverage for standings
        const leaguesWithCoverage = await coverageCollection.find(
            {
                _id: { $in: targetedLeagues }, // Only include targeted leagues
                'coverage.standings': true // Only fetch if standings are covered
            },
            { projection: { _id: 1 } }
        ).toArray();

        const leagueIdsWithCoverage = leaguesWithCoverage.map(l => l._id);

        // 3. Process fixture-based updates if we have fixtures
        if (fixtureBasedUpdates) {
            // Group fixtures by league
            const leagueFixturesMap = new Map();
            
            for (const fixture of todaysFixtures) {
                const leagueId = fixture.league?.id;
                const season = fixture.league?.season;

                if (!leagueId || !season || !leagueIdsWithCoverage.includes(leagueId)) {
                    continue;
                }

                if (!leagueFixturesMap.has(leagueId)) {
                    leagueFixturesMap.set(leagueId, []);
                }

                leagueFixturesMap.get(leagueId).push({
                    fixtureId: fixture.fixture.id,
                    fixtureDate: fixture.fixture.date,
                    fixtureStatus: fixture.fixture.status.short,
                    season: season
                });
            }

            // 4. Check which leagues need standings updates based on completed fixtures
            for (const [leagueId, fixtures] of leagueFixturesMap.entries()) {
                // Sort fixtures by date (newest first)
                fixtures.sort((a, b) => new Date(b.fixtureDate).getTime() - new Date(a.fixtureDate).getTime());

                // Check if we need to update standings based on the most recent fixture
                const mostRecentFixture = fixtures[0];
                const needsUpdate = await shouldUpdateStandings(
                    leagueId,
                    mostRecentFixture.season,
                    mostRecentFixture.fixtureDate,
                    mostRecentFixture.fixtureId,
                    mostRecentFixture.fixtureStatus
                );

                if (needsUpdate) {
                    leagueUpdates.push({
                        leagueId,
                        season: mostRecentFixture.season,
                        fixtureDate: mostRecentFixture.fixtureDate,
                        fixtureId: mostRecentFixture.fixtureId,
                        fixtureStatus: mostRecentFixture.fixtureStatus,
                        updateSource: 'fixture'
                    });
                }
            }
        }

        // 5. Add time-based updates for active leagues if we don't have enough fixture-based updates
        // This ensures we always check for standings updates even if there are no fixtures
        if (leagueUpdates.length === 0) {
            console.log('No fixture-based updates found. Adding time-based update checks for active leagues.');

            // Get all active leagues with standings coverage
            const activeLeagues = await leaguesCollection.find(
                {
                    _id: { $in: leagueIdsWithCoverage },
                    'seasons.current': true
                },
                { projection: { _id: 1, 'seasons.year': 1, 'seasons.current': 1 } }
            ).toArray();

            for (const league of activeLeagues) {
                const currentSeason = league.seasons.find(s => s.current);
                if (!currentSeason) continue;

                // Check when was the last time we updated standings for this league
                const standingId = createStandingId(league._id, currentSeason.year);
                const lastStandings = await standingsCollection.findOne(
                    { _id: standingId },
                    { projection: { lastUpdated: 1 } }
                );

                const shouldCheckUpdate = !lastStandings || 
                    !lastStandings.lastUpdated || 
                    (now.getTime() - new Date(lastStandings.lastUpdated).getTime() > 6 * 60 * 60 * 1000); // 6 hours

                if (shouldCheckUpdate) {
                    leagueUpdates.push({
                        leagueId: league._id,
                        season: currentSeason.year,
                        fixtureDate: now.toISOString(),
                        fixtureId: null,
                        fixtureStatus: null,
                        updateSource: 'time'
                    });
                }
            }
        }

        if (leagueUpdates.length === 0) {
            console.log('No leagues need standings updates based on completed fixtures.');
            return;
        }

        console.log(`Found ${leagueUpdates.length} leagues that need standings updates.`);

        // 5. Get active season information for these leagues
        const leagueIdsToUpdate = leagueUpdates.map(l => l.leagueId);
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: leagueIdsToUpdate },
                "seasons.current": true
            },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        if (!activeLeagues || activeLeagues.length === 0) {
            console.log('No active leagues found that need standings updates.');
            return;
        }

        console.log(`Found ${activeLeagues.length} active leagues that need standings updates.`);

        // 2. Iterate through active leagues/seasons and fetch standings
        let totalUpserted = 0;
        let totalModified = 0;

        // Create a map of league updates for quick lookup
        const leagueUpdateMap = new Map();
        leagueUpdates.forEach(update => {
            leagueUpdateMap.set(update.leagueId, update);
        });

        for (const league of activeLeagues) {
            const currentSeason = league.seasons.find(s => s.current);
            if (!currentSeason) continue;

            const leagueId = league._id;
            const seasonYear = currentSeason.year;
            const updateInfo = leagueUpdateMap.get(leagueId);

            if (!updateInfo) {
                console.log(`No update info found for League ID: ${leagueId}. Skipping.`);
                continue;
            }

            console.log(`Fetching standings for League ID: ${leagueId}, Season: ${seasonYear}`);

            try {
                // Fetch standings data from API
                const standingsDataArray = await fetchStandings({ league: leagueId, season: seasonYear });

                // API returns an array, usually with one element containing the league/standings
                if (!standingsDataArray || standingsDataArray.length === 0 || !standingsDataArray[0].league) {
                    console.log(`No standings data received for League ${leagueId}, Season ${seasonYear}.`);
                    continue;
                }

                // Extract the relevant part (the standings array itself)
                const standingsResult = standingsDataArray[0]; // Get the first element
                const standingsGroups = standingsResult.league.standings; // This is TeamStanding[][]

                const standingId = createStandingId(leagueId, seasonYear);
                const updateDoc: Standing = {
                    _id: standingId,
                    league: {
                        apiId: leagueId,
                        season: seasonYear
                    },
                    leagueId: leagueId, // For backward compatibility
                    season: seasonYear, // For backward compatibility
                    standings: standingsGroups, // Store the array of arrays
                    lastUpdated: now,
                };

                // Upsert the entire standing document for the league/season
                const result = await standingsCollection.updateOne(
                    { _id: standingId },
                    { $set: updateDoc },
                    { upsert: true }
                );

                if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                    // Record that we updated the standings for this league
                    await recordStandingsUpdate(
                        leagueId,
                        seasonYear,
                        updateInfo.fixtureDate,
                        updateInfo.fixtureId
                    );

                    if (result.upsertedCount > 0) totalUpserted++;
                    if (result.modifiedCount > 0) totalModified++;
                }

                // Delay between fetches
                await delay(400); // Adjust delay

            } catch (standingError) {
                console.error(`Error fetching standings for League ${leagueId}, Season ${seasonYear}:`, standingError);
                // Continue to the next league
            }
        }

        console.log(`fetchAndUpdateStandings job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateStandings job:', error);
    }
}
