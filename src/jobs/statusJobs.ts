import { checkApiStatus } from '../services/apiFootball';
import { getRedisClient } from '../config/redis';
import connectDB from '../config/database';

/**
 * Monitor API-Football status and usage
 * This job helps track API usage and detect potential issues
 */
export async function monitorApiStatus(): Promise<void> {
    console.log('Starting API status monitoring job...');
    
    try {
        // Ensure database connection
        await connectDB();
        const redisClient = getRedisClient();
        
        // Check API status
        console.log('Checking API-Football status...');
        const status = await checkApiStatus();
        
        if (!status) {
            console.warn('No status data received from API');
            return;
        }
        
        // Log important status information
        console.log('API Status Information:');
        console.log(`- Account: ${status.account?.firstname} ${status.account?.lastname}`);
        console.log(`- Plan: ${status.subscription?.plan}`);
        console.log(`- Requests Today: ${status.requests?.current}/${status.requests?.limit_day}`);
        console.log(`- Requests This Hour: ${status.requests?.current}/${status.requests?.limit_minute ? status.requests.limit_minute * 60 : 'N/A'}`);
        
        // Calculate usage percentages
        const dailyUsagePercent = status.requests?.limit_day ? 
            Math.round((status.requests.current / status.requests.limit_day) * 100) : 0;
        
        console.log(`- Daily Usage: ${dailyUsagePercent}%`);
        
        // Alert if usage is high
        if (dailyUsagePercent > 80) {
            console.warn(`⚠️  HIGH API USAGE: ${dailyUsagePercent}% of daily limit used!`);
        } else if (dailyUsagePercent > 60) {
            console.warn(`⚠️  Moderate API usage: ${dailyUsagePercent}% of daily limit used`);
        }
        
        // Cache the status with short TTL
        const cacheKey = 'api:status';
        const cacheTTL = 60 * 5; // 5 minutes
        
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(status));
        console.log('API status cached successfully');
        
        // Store historical usage data for tracking
        const historyKey = `api:usage:${new Date().toISOString().split('T')[0]}`; // YYYY-MM-DD
        const usageData = {
            timestamp: new Date().toISOString(),
            current: status.requests?.current || 0,
            limit: status.requests?.limit_day || 0,
            percentage: dailyUsagePercent
        };
        
        await redisClient.setex(historyKey, 60 * 60 * 24 * 7, JSON.stringify(usageData)); // Keep for 1 week
        
        console.log('API status monitoring job completed successfully');
        
    } catch (error) {
        console.error('Error in API status monitoring job:', error);
        
        // Don't throw error for status monitoring - it shouldn't break other jobs
        // Just log and continue
        console.warn('API status monitoring failed, but continuing with other jobs...');
    }
}

/**
 * Get API usage history from cache
 * @param days Number of days to retrieve (default: 7)
 * @returns Array of usage data
 */
export async function getApiUsageHistory(days: number = 7): Promise<any[]> {
    const redisClient = getRedisClient();
    const history = [];
    
    for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateKey = date.toISOString().split('T')[0];
        const historyKey = `api:usage:${dateKey}`;
        
        try {
            const data = await redisClient.get(historyKey);
            if (data) {
                history.push(JSON.parse(data));
            }
        } catch (error) {
            console.error(`Error retrieving usage data for ${dateKey}:`, error);
        }
    }
    
    return history.reverse(); // Return in chronological order
}
