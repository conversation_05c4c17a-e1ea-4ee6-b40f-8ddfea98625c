import { fetchTimezones } from '../services/apiFootball';
import { getRedisClient } from '../config/redis';
import connectDB from '../config/database';

/**
 * Fetch and cache timezone data from API-Football
 * This job runs very infrequently since timezones rarely change
 */
export async function fetchAndCacheTimezones(): Promise<void> {
    console.log('Starting timezone fetch job...');
    
    try {
        // Ensure database connection
        await connectDB();
        const redisClient = getRedisClient();
        
        // Fetch timezones from API
        console.log('Fetching timezones from API-Football...');
        const timezones = await fetchTimezones();
        
        if (!timezones || timezones.length === 0) {
            console.warn('No timezones received from API');
            return;
        }
        
        console.log(`Received ${timezones.length} timezones from API`);
        
        // Cache the timezones with a long TTL (1 week)
        const cacheKey = 'timezones:all';
        const cacheTTL = 60 * 60 * 24 * 7; // 1 week
        
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(timezones));
        console.log(`Cached ${timezones.length} timezones with TTL ${cacheTTL} seconds`);
        
        // Log some sample timezones for verification
        const sampleTimezones = timezones.slice(0, 5);
        console.log('Sample timezones:', sampleTimezones);
        
        console.log('Timezone fetch job completed successfully');
        
    } catch (error) {
        console.error('Error in timezone fetch job:', error);
        throw error; // Re-throw to let the scheduler handle it
    }
}
