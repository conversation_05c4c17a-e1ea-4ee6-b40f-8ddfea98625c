import { AnyBulkWriteOperation } from 'mongodb';
import {
    getOddsCollection,
    getLiveOddsCollection,
    getBookmakersCollection,
    getBetsCollection,
    Odds,
    LiveOdds,
    BookmakerInfo,
    BetInfo,
    createOddsId
} from '../models/Odds';
import {
    fetchOdds,
    fetchLiveOdds,
    fetchBookmakers,
    fetchBets,
    fetchLiveBets,
    fetchOddsMapping,
    OddsResponse
} from '../services/oddsService';
import { getFixturesCollection } from '../models/Fixture';
import { getLeaguesCollection } from '../models/League';
import { delay } from '../utils/helpers';
import { targetedLeagues } from '../config/targetedLeagues';
import { targetedBetTypeIds, targetedBetTypeMap } from '../config/targetedBetTypes';
import connectDB from '../config/database';

// Fetch and update bookmakers
export async function fetchAndUpdateBookmakers() {
    console.log('Starting fetchAndUpdateBookmakers job...');
    try {
        const bookmakersCollection = getBookmakersCollection();
        const now = new Date();

        // Fetch bookmakers from API
        const bookmakers = await fetchBookmakers();

        if (!bookmakers || bookmakers.length === 0) {
            console.log('No bookmakers received from API.');
            return;
        }

        console.log(`Received ${bookmakers.length} bookmakers from API.`);

        // Prepare bulk operations
        const bulkOps: AnyBulkWriteOperation<BookmakerInfo>[] = [];

        for (const bookmaker of bookmakers) {
            const bookmakerDoc: BookmakerInfo = {
                _id: bookmaker.id,
                apiId: bookmaker.id,
                name: bookmaker.name,
                lastUpdated: now
            };

            bulkOps.push({
                updateOne: {
                    filter: { _id: bookmaker.id },
                    update: { $set: bookmakerDoc },
                    upsert: true
                }
            });
        }

        if (bulkOps.length > 0) {
            const result = await bookmakersCollection.bulkWrite(bulkOps);
            console.log(`Bookmakers updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }

    } catch (error) {
        console.error('Error in fetchAndUpdateBookmakers job:', error);
    }
}

// Fetch and update bet types
export async function fetchAndUpdateBets() {
    console.log('Starting fetchAndUpdateBets job...');
    try {
        const betsCollection = getBetsCollection();
        const now = new Date();

        // Fetch pre-match bet types from API
        const bets = await fetchBets();

        if (!bets || bets.length === 0) {
            console.log('No bet types received from API.');
            return;
        }

        console.log(`Received ${bets.length} bet types from API.`);

        // Filter to only include targeted bet types
        const targetedBets = bets.filter(bet => targetedBetTypeIds.includes(bet.id));
        console.log(`Filtered down to ${targetedBets.length} targeted bet types.`);

        // Prepare bulk operations
        const bulkOps: AnyBulkWriteOperation<BetInfo>[] = [];

        for (const bet of targetedBets) {
            const betDoc: BetInfo = {
                _id: bet.id,
                apiId: bet.id,
                name: bet.name,
                lastUpdated: now
            };

            bulkOps.push({
                updateOne: {
                    filter: { _id: bet.id },
                    update: { $set: betDoc },
                    upsert: true
                }
            });
        }

        if (bulkOps.length > 0) {
            const result = await betsCollection.bulkWrite(bulkOps);
            console.log(`Bet types updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }

        // Fetch live bet types from API
        const liveBets = await fetchLiveBets();

        if (!liveBets || liveBets.length === 0) {
            console.log('No live bet types received from API.');
            return;
        }

        console.log(`Received ${liveBets.length} live bet types from API.`);

        // Filter to only include targeted bet types
        const targetedLiveBets = liveBets.filter(bet => targetedBetTypeIds.includes(bet.id));
        console.log(`Filtered down to ${targetedLiveBets.length} targeted live bet types.`);

        // Prepare bulk operations for live bets
        const liveBulkOps: AnyBulkWriteOperation<BetInfo>[] = [];

        for (const bet of targetedLiveBets) {
            const betDoc: BetInfo = {
                _id: bet.id,
                apiId: bet.id,
                name: bet.name,
                lastUpdated: now
            };

            liveBulkOps.push({
                updateOne: {
                    filter: { _id: bet.id },
                    update: { $set: { ...betDoc, isLive: true } },
                    upsert: true
                }
            });
        }

        if (liveBulkOps.length > 0) {
            const result = await betsCollection.bulkWrite(liveBulkOps);
            console.log(`Live bet types updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }

    } catch (error) {
        console.error('Error in fetchAndUpdateBets job:', error);
    }
}

// Fetch and update pre-match odds for upcoming fixtures
export async function fetchAndUpdateOdds() {
    console.log('Starting fetchAndUpdateOdds job...');
    try {
        const fixturesCollection = getFixturesCollection();
        const oddsCollection = getOddsCollection();
        const now = new Date();

        // Get upcoming fixtures (next 7 days)
        const today = new Date();
        const nextWeek = new Date(today);
        nextWeek.setDate(today.getDate() + 7);

        const upcomingFixtures = await fixturesCollection.find({
            'fixture.date': {
                $gte: today.toISOString(),
                $lte: nextWeek.toISOString()
            },
            'league.id': { $in: targetedLeagues } // Only include targeted leagues
        }).toArray();

        if (!upcomingFixtures || upcomingFixtures.length === 0) {
            console.log('No upcoming fixtures found.');
            return;
        }

        console.log(`Found ${upcomingFixtures.length} upcoming fixtures.`);

        // Process fixtures in batches to respect API rate limits
        const batchSize = 10;
        let totalOddsUpdated = 0;

        for (let i = 0; i < upcomingFixtures.length; i += batchSize) {
            const batch = upcomingFixtures.slice(i, i + batchSize);
            console.log(`Processing batch ${i / batchSize + 1} of ${Math.ceil(upcomingFixtures.length / batchSize)}`);

            for (const fixture of batch) {
                try {
                    const fixtureId = fixture._id;
                    console.log(`Fetching odds for fixture ID: ${fixtureId}`);

                    const oddsFromApi = await fetchOdds({ fixture: fixtureId });

                    if (!oddsFromApi || oddsFromApi.length === 0) {
                        console.log(`No odds received for fixture ${fixtureId}.`);
                        continue;
                    }

                    console.log(`Received odds for fixture ${fixtureId}.`);

                    // Process each odds response
                    for (const oddsResponse of oddsFromApi) {
                        const leagueId = oddsResponse.league.id;
                        const update = oddsResponse.update;
                        const bookmakers = oddsResponse.bookmakers;

                        // Process each bookmaker
                        for (const bookmaker of bookmakers) {
                            const bookmakerId = bookmaker.id;
                            const bookmakerName = bookmaker.name;

                            // Filter to only include targeted bet types
                            const targetedBets = bookmaker.bets.filter(bet => targetedBetTypeIds.includes(bet.id));

                            // Process each targeted bet type
                            for (const bet of targetedBets) {
                                const betId = bet.id;
                                const betName = bet.name;
                                const oddsId = createOddsId(fixtureId, bookmakerName, betName);

                                // Create a modified bookmaker object with only this bet
                                const filteredBookmaker = {
                                    ...bookmaker,
                                    bets: [bet]
                                };

                                // Create odds document
                                const oddsDoc: Odds = {
                                    _id: oddsId,
                                    apiId: oddsId,
                                    fixtureId,
                                    leagueId,
                                    update,
                                    bookmakers: [filteredBookmaker],
                                    lastUpdated: now
                                };

                                // Update or insert odds
                                await oddsCollection.updateOne(
                                    { _id: oddsId },
                                    { $set: oddsDoc },
                                    { upsert: true }
                                );

                                totalOddsUpdated++;
                            }
                        }
                    }

                    // Add a delay between fixtures to respect API rate limits
                    await delay(1000);

                } catch (fixtureError) {
                    console.error(`Error fetching odds for fixture ${fixture._id}:`, fixtureError);
                    // Continue to the next fixture even if one fails
                    await delay(2000); // Longer delay after an error
                }
            }

            // Add a delay between batches to respect API rate limits
            await delay(5000);
        }

        console.log(`fetchAndUpdateOdds job finished. Total odds updated: ${totalOddsUpdated}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateOdds job:', error);
    }
}

// Fetch and update live odds for ongoing fixtures
export async function fetchAndUpdateLiveOdds() {
    console.log('Starting fetchAndUpdateLiveOdds job...');
    try {
        const liveOddsCollection = getLiveOddsCollection();
        const now = new Date();

        // Fetch live odds from API
        const allLiveOdds = await fetchLiveOdds();

        if (!allLiveOdds || allLiveOdds.length === 0) {
            console.log('No live odds received from API.');
            return;
        }

        // Filter live odds to only include targeted leagues
        const liveOdds = allLiveOdds.filter(odds => targetedLeagues.includes(odds.league.id));
        console.log(`Filtered ${allLiveOdds.length} live odds down to ${liveOdds.length} from targeted leagues.`);

        // Process each fixture's live odds
        for (const oddsResponse of liveOdds) {
            const fixtureId = oddsResponse.fixture.id;
            const leagueId = oddsResponse.league.id;
            const update = oddsResponse.update;
            const odds = oddsResponse.odds || [];

            // Skip if no odds
            if (!odds || !Array.isArray(odds) || odds.length === 0) {
                console.log(`No odds found for fixture ${fixtureId}. Skipping.`);
                continue;
            }

            // Filter to only include targeted bet types
            const targetedOdds = odds.filter(bet => targetedBetTypeIds.includes(bet.id));

            if (targetedOdds.length === 0) {
                console.log(`No targeted bet types found for fixture ${fixtureId}. Skipping.`);
                continue;
            }

            console.log(`Filtered ${odds.length} bet types down to ${targetedOdds.length} targeted bet types for fixture ${fixtureId}.`);

            // Create a synthetic bookmaker for each fixture
            // Since the API doesn't provide bookmaker information in live odds
            const syntheticBookmaker = {
                id: 1, // Use a default ID
                name: "API-Football Live", // Use a default name
                bets: targetedOdds.map(bet => ({
                    id: bet.id,
                    name: bet.name,
                    values: bet.values
                }))
            };

            // Process each bet type
            for (const bet of syntheticBookmaker.bets) {
                const betId = bet.id;
                const betName = bet.name;
                const oddsId = createOddsId(fixtureId, syntheticBookmaker.name, betName);

                // Create live odds document
                const liveOddsDoc: LiveOdds = {
                    _id: oddsId,
                    apiId: oddsId,
                    fixtureId,
                    leagueId,
                    update,
                    bookmakers: [{
                        id: syntheticBookmaker.id,
                        name: syntheticBookmaker.name,
                        bets: [bet]
                    }],
                    lastUpdated: now
                };

                // Update or insert live odds
                await liveOddsCollection.updateOne(
                    { _id: oddsId },
                    { $set: liveOddsDoc },
                    { upsert: true }
                );
            }
        }

        console.log(`fetchAndUpdateLiveOdds job finished. Updated live odds for ${liveOdds.length} fixtures.`);

    } catch (error) {
        console.error('Error in fetchAndUpdateLiveOdds job:', error);
    }
}

// Main function to run jobs from command line
async function main() {
    try {
        // Connect to database
        await connectDB();
        console.log('Connected to database');

        // Check command line arguments
        const args = process.argv.slice(2);

        if (args.includes('--fetch-bookmakers')) {
            await fetchAndUpdateBookmakers();
        }

        if (args.includes('--fetch-bets')) {
            await fetchAndUpdateBets();
        }

        if (args.includes('--fetch-odds')) {
            await fetchAndUpdateOdds();
        }

        if (args.includes('--fetch-live-odds')) {
            await fetchAndUpdateLiveOdds();
        }

        // If no specific job is requested, show usage
        if (args.length === 0) {
            console.log('Usage: ts-node src/jobs/oddsJobs.ts [options]');
            console.log('Options:');
            console.log('  --fetch-bookmakers   Fetch and update bookmakers');
            console.log('  --fetch-bets         Fetch and update bet types');
            console.log('  --fetch-odds         Fetch and update pre-match odds');
            console.log('  --fetch-live-odds    Fetch and update live odds');
        }

    } catch (error) {
        console.error('Error running jobs:', error);
    } finally {
        process.exit(0);
    }
}

// Run the main function if this file is executed directly
if (require.main === module) {
    main();
}
