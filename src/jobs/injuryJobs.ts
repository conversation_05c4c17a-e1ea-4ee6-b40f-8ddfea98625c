import { fetchInjuries } from '../services/apiFootball';
import { getInjuriesCollection, Injury, createInjuryId } from '../models/Injury';
import { getFixturesCollection } from '../models/Fixture'; // Need fixtures to get IDs
import { getLeagueCoverageCollection } from '../models/LeagueCoverage';
import { AnyBulkWriteOperation } from 'mongodb';
import dayjs from 'dayjs';
import { targetedLeagues } from '../config/targetedLeagues';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export async function fetchAndUpdateInjuriesForUpcomingFixtures() {
    console.log('Starting fetchAndUpdateInjuriesForUpcomingFixtures job...');
    try {
        const fixturesCollection = getFixturesCollection();
        const injuriesCollection = getInjuriesCollection();
        const coverageCollection = getLeagueCoverageCollection();
        const now = new Date();
        const today = dayjs().format('YYYY-MM-DD');
        const tomorrow = dayjs().add(1, 'day').format('YYYY-MM-DD');

        // 1. Get leagues with injuries coverage
        const leaguesWithCoverage = await coverageCollection.find(
            {
                _id: { $in: targetedLeagues },
                'coverage.injuries': true
            },
            { projection: { _id: 1 } }
        ).toArray();

        const leagueIdsWithCoverage = leaguesWithCoverage.map(l => l._id);

        if (leagueIdsWithCoverage.length === 0) {
            console.log('No leagues with injuries coverage found.');
            return;
        }

        console.log(`Found ${leagueIdsWithCoverage.length} leagues with injuries coverage.`);

        // 2. Find fixture IDs for today and tomorrow that are not finished/cancelled etc.
        const relevantStatuses = ['TBD', 'NS', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT', 'LIVE']; // Statuses where injuries are relevant
        const upcomingFixtures = await fixturesCollection.find(
            {
                'fixture.date': { $regex: `^(${today}|${tomorrow})` }, // Match dates starting with today or tomorrow
                'fixture.status.short': { $in: relevantStatuses },
                'league.id': { $in: leagueIdsWithCoverage } // Only include leagues with injuries coverage
            },
            { projection: { _id: 1 } } // Only need the fixture IDs
        ).toArray();

        const fixtureIds = upcomingFixtures.map(f => f._id);

        if (!fixtureIds || fixtureIds.length === 0) {
            console.log('No upcoming fixtures found to fetch injuries for.');
            return;
        }

        console.log(`Found ${fixtureIds.length} upcoming fixtures for injury update.`);

        // 2. Fetch injuries in batches using the 'ids' parameter (max 20 per request)
        const batchSize = 20;
        let totalUpserted = 0;
        let totalModified = 0;

        for (let i = 0; i < fixtureIds.length; i += batchSize) {
            const batchIds = fixtureIds.slice(i, i + batchSize);
            const idsString = batchIds.join('-');
            console.log(`  Fetching injuries for fixture batch: ${idsString}`);

            try {
                const injuriesFromApi = await fetchInjuries({ ids: idsString });

                if (!injuriesFromApi || injuriesFromApi.length === 0) {
                    console.log(`  No injuries received for batch: ${idsString}`);
                    continue; // Move to the next batch
                }

                const bulkOps: AnyBulkWriteOperation<Injury>[] = [];
                for (const injuryApi of injuriesFromApi) {
                    // Create a unique ID for the injury record
                    const injuryId = createInjuryId(injuryApi.fixture.id, injuryApi.team.id, injuryApi.player.id);
                    const filter = { _id: injuryId };
                    const updateDoc: Injury = {
                        ...injuryApi,
                        _id: injuryId,
                        lastUpdated: now,
                    };

                    bulkOps.push({
                        updateOne: {
                            filter: filter,
                            update: { $set: updateDoc },
                            upsert: true,
                        },
                    });
                }

                if (bulkOps.length > 0) {
                    const result = await injuriesCollection.bulkWrite(bulkOps);
                    console.log(`  Batch ${idsString}: Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                    totalUpserted += result.upsertedCount;
                    totalModified += result.modifiedCount;
                }

                // Delay between batches
                await delay(1000); // Add delay between batch requests

            } catch (batchError) {
                console.error(`  Error fetching injuries for batch ${idsString}:`, batchError);
                // Continue to the next batch
            }
        }

        console.log(`fetchAndUpdateInjuriesForUpcomingFixtures job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

        // Optional: Clean up old injury records? (e.g., for fixtures older than X days)

    } catch (error) {
        console.error('Error in fetchAndUpdateInjuriesForUpcomingFixtures job:', error);
    }
}
