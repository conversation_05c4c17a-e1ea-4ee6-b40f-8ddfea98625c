import { fetchPredictions } from '../services/apiFootball';
import { getPredictionsCollection, Prediction } from '../models/Prediction';
import { getFixturesCollection } from '../models/Fixture';
import { getLeagueCoverageCollection } from '../models/LeagueCoverage';
import { getRedisClient } from '../config/redis';
import { AnyBulkWriteOperation } from 'mongodb';
import dayjs from 'dayjs';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Fetches and updates predictions for upcoming fixtures
 * Based on the API documentation, predictions are updated hourly
 * This job should be run hourly to keep predictions up-to-date
 * 
 * @param days Number of days to look ahead for upcoming fixtures (default: 2)
 */
export async function fetchAndUpdatePredictionsForUpcomingFixtures(days: number = 2) {
    console.log(`Starting fetchAndUpdatePredictionsForUpcomingFixtures job for the next ${days} days...`);
    try {
        const fixturesCollection = getFixturesCollection();
        const predictionsCollection = getPredictionsCollection();
        const coverageCollection = getLeagueCoverageCollection();
        const redisClient = getRedisClient();
        const now = new Date();
        let totalProcessed = 0;
        let totalUpdated = 0;
        let totalSkipped = 0;

        // 1. Find leagues with prediction coverage
        const leaguesWithCoverage = await coverageCollection.find(
            { 'coverage.predictions': true },
            { projection: { _id: 1 } }
        ).toArray();

        const leagueIdsWithCoverage = leaguesWithCoverage.map(l => l._id);

        if (leagueIdsWithCoverage.length === 0) {
            console.log('No leagues with prediction coverage found.');
            return;
        }

        console.log(`Found ${leagueIdsWithCoverage.length} leagues with prediction coverage.`);

        // 2. Find upcoming fixtures in leagues with prediction coverage
        const startDate = dayjs().startOf('day').toDate();
        const endDate = dayjs().add(days, 'day').endOf('day').toDate();

        const upcomingFixtures = await fixturesCollection.find({
            'league.id': { $in: leagueIdsWithCoverage },
            'fixture.date': { $gte: startDate, $lte: endDate },
            'fixture.status.short': 'NS' // Not Started fixtures only
        }, {
            projection: { _id: 1 }
        }).toArray();

        if (upcomingFixtures.length === 0) {
            console.log(`No upcoming fixtures found for the next ${days} days in leagues with prediction coverage.`);
            return;
        }

        console.log(`Found ${upcomingFixtures.length} upcoming fixtures for prediction updates.`);

        // 3. Process fixtures in batches to avoid rate limiting
        const BATCH_SIZE = 10;
        const fixtureIds = upcomingFixtures.map(f => f._id);
        const batches = [];

        for (let i = 0; i < fixtureIds.length; i += BATCH_SIZE) {
            batches.push(fixtureIds.slice(i, i + BATCH_SIZE));
        }

        console.log(`Processing ${batches.length} batches of fixtures...`);

        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            console.log(`Processing batch ${i + 1}/${batches.length} (${batch.length} fixtures)...`);
            
            const bulkOps: AnyBulkWriteOperation<Prediction>[] = [];

            for (const fixtureId of batch) {
                try {
                    totalProcessed++;
                    
                    // Check if we already have a recent prediction (less than 1 hour old)
                    const existingPrediction = await predictionsCollection.findOne(
                        { _id: fixtureId },
                        { projection: { lastUpdated: 1 } }
                    );

                    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
                    
                    if (existingPrediction && existingPrediction.lastUpdated > oneHourAgo) {
                        // Skip if we have a recent prediction
                        console.log(`Skipping fixture ${fixtureId}: prediction updated recently.`);
                        totalSkipped++;
                        continue;
                    }

                    // Fetch prediction from API
                    console.log(`Fetching prediction for fixture ${fixtureId}...`);
                    const predictionsFromApi = await fetchPredictions({ fixture: fixtureId });

                    if (!predictionsFromApi || predictionsFromApi.length === 0) {
                        console.log(`No prediction available for fixture ${fixtureId}.`);
                        continue;
                    }

                    const predictionData = predictionsFromApi[0];
                    
                    // Prepare document for upsert
                    const predictionToStore: Prediction = {
                        ...predictionData,
                        _id: fixtureId, // Use fixture ID as _id
                        lastUpdated: now,
                    };

                    // Add to bulk operations
                    bulkOps.push({
                        updateOne: {
                            filter: { _id: fixtureId },
                            update: { $set: predictionToStore },
                            upsert: true
                        }
                    });

                    // Invalidate cache
                    const cacheKey = `predictions:${fixtureId}`;
                    await redisClient.del(cacheKey);
                    
                    totalUpdated++;

                    // Add a small delay between API calls to avoid rate limiting
                    await delay(200);
                    
                } catch (error) {
                    console.error(`Error processing prediction for fixture ${fixtureId}:`, error);
                }
            }

            // Execute bulk operations if any
            if (bulkOps.length > 0) {
                try {
                    const result = await predictionsCollection.bulkWrite(bulkOps);
                    console.log(`Batch ${i + 1} completed. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                } catch (bulkError) {
                    console.error(`Error executing bulk operations for batch ${i + 1}:`, bulkError);
                }
            }

            // Add a delay between batches to respect API rate limits
            if (i < batches.length - 1) {
                console.log('Waiting between batches...');
                await delay(2000);
            }
        }

        console.log(`Prediction job completed. Processed: ${totalProcessed}, Updated: ${totalUpdated}, Skipped: ${totalSkipped}`);

    } catch (error) {
        console.error('Error in fetchAndUpdatePredictionsForUpcomingFixtures job:', error);
    }
}
