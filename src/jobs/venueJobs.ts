import { fetchVenues } from '../services/apiFootball';
import { getVenuesCollection, Venue } from '../models/Venue';
import { getTeamsCollection } from '../models/Team'; // Need teams to know which venues to fetch
import { getLeaguesCollection } from '../models/League';
import { AnyBulkWriteOperation } from 'mongodb';
import { targetedLeagues } from '../config/targetedLeagues';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export async function fetchAndUpdateVenuesForActiveTeams() {
    console.log('Starting fetchAndUpdateVenuesForActiveTeams job...');
    try {
        const teamsCollection = getTeamsCollection();
        const venuesCollection = getVenuesCollection();
        const now = new Date();

        // Get teams from targeted leagues
        const leaguesCollection = getLeaguesCollection();

        // Get all teams (we'll filter by venue.id being non-null)
        const teams = await teamsCollection.find({ 'venue.id': { $ne: null } }).toArray();
        const teamIds = teams.map(team => team._id);

        // 1. Get all unique, non-null venue IDs from our stored teams in targeted leagues
        // Use aggregation pipeline for efficiency
        const venueIdsResult = await teamsCollection.aggregate<{ _id: number | null }>([
            { $match: { _id: { $in: teamIds }, 'venue.id': { $ne: null } } }, // Only teams from targeted leagues with a venue ID
            { $group: { _id: '$venue.id' } }, // Group by venue ID to get unique IDs
            { $match: { _id: { $ne: null } } } // Ensure grouped ID is not null
        ]).toArray();

        const venueIds = venueIdsResult.map(v => v._id).filter((id): id is number => id !== null); // Extract non-null IDs

        if (!venueIds || venueIds.length === 0) {
            console.log('No venue IDs found associated with stored teams.');
            return;
        }

        console.log(`Found ${venueIds.length} unique venue IDs from teams to fetch/update.`);

        // 2. Iterate through venue IDs and fetch details
        let totalUpserted = 0;
        let totalModified = 0;
        const bulkOps: AnyBulkWriteOperation<Venue>[] = [];

        for (const venueId of venueIds) {
             console.log(`  Fetching details for Venue ID: ${venueId}`);
            try {
                // Fetch venue by ID
                const venuesFromApi = await fetchVenues({ id: venueId });

                // Should return an array with one venue if found
                if (!venuesFromApi || venuesFromApi.length === 0) {
                    console.log(`  Venue details not found for ID: ${venueId}`);
                    continue;
                }

                const venueApi = venuesFromApi[0]; // Get the first (and only) venue

                const filter = { _id: venueApi.id }; // Use venue ID as _id
                const { id, ...venueData } = venueApi; // Separate id from the rest of the data
                const updateDoc: Omit<Venue, '_id'> & { _id: number } = {
                    _id: venueApi.id,
                    ...venueData, // Spread the rest of the venue fields
                    lastUpdated: now,
                };

                bulkOps.push({
                    updateOne: {
                        filter: filter,
                        update: { $set: updateDoc },
                        upsert: true,
                    },
                });

                // Delay between fetches
                await delay(200); // Shorter delay might be okay for venues

            } catch (venueError) {
                console.error(`  Error fetching venue details for ID ${venueId}:`, venueError);
                // Continue to the next venue
            }
        }

        // 3. Perform bulk write if there are operations
        if (bulkOps.length > 0) {
            const result = await venuesCollection.bulkWrite(bulkOps);
            totalUpserted = result.upsertedCount;
            totalModified = result.modifiedCount;
            console.log(`fetchAndUpdateVenuesForActiveTeams job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);
        } else {
             console.log('fetchAndUpdateVenuesForActiveTeams job: No venue updates to perform.');
        }

    } catch (error) {
        console.error('Error in fetchAndUpdateVenuesForActiveTeams job:', error);
    }
}
