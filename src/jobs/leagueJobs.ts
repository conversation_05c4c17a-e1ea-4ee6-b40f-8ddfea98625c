import { fetchLeagues } from '../services/apiFootball';
import { getLeaguesCollection, League } from '../models/League';
import { AnyBulkWriteOperation } from 'mongodb';
import { isTargetedLeague } from '../config/targetedLeagues';

export async function fetchAndUpdateLeagues() {
    console.log('Starting fetchAndUpdateLeagues job...');
    try {
        // Fetch all leagues - API might paginate, but typically returns all in one go unless filtered heavily.
        // If pagination becomes an issue for the raw /leagues endpoint, this needs adjustment.
        const allLeaguesFromApi = await fetchLeagues(); // Fetch all leagues

        if (!allLeaguesFromApi || allLeaguesFromApi.length === 0) {
            console.log('No leagues received from API.');
            return;
        }

        // Filter leagues to only include targeted leagues
        const leaguesFromApi = allLeaguesFromApi.filter(league => isTargetedLeague(league.league.id));
        console.log(`Filtered ${allLeaguesFromApi.length} leagues down to ${leaguesFromApi.length} targeted leagues.`);

        const collection = getLeaguesCollection();
        const bulkOps: AnyBulkWriteOperation<League>[] = [];
        const now = new Date();

        for (const leagueApi of leaguesFromApi) {
            const leagueId = leagueApi.league.id;

            // Use league ID as the unique identifier (_id) for upsert
            const filter = { _id: leagueId };

            // Construct the document according to the League model
            const updateDoc = {
                $set: {
                    apiId: leagueId, // Set apiId to match the _id for the unique index
                    league: leagueApi.league,
                    country: leagueApi.country,
                    seasons: leagueApi.seasons,
                    lastUpdated: now
                }
            };

            bulkOps.push({
                updateOne: {
                    filter: filter,
                    update: updateDoc,
                    upsert: true // Insert if not found, update if found
                }
            });
        }

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`fetchAndUpdateLeagues job finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        } else {
            console.log('fetchAndUpdateLeagues job: No changes detected.');
        }

    } catch (error) {
        console.error('Error in fetchAndUpdateLeagues job:', error);
    }
}
