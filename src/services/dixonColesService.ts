/**
 * <PERSON><PERSON><PERSON> Mathematical Model Implementation
 * 
 * Based on the research paper by Dixon & Coles (1997):
 * "Modelling Association Football Scores and Inefficiencies in the Football Betting Market"
 * 
 * This implementation provides:
 * 1. Rho correction for low-scoring games (0-0, 1-0, 0-1, 1-1)
 * 2. Time decay weighting for recent form
 * 3. Poisson distribution calculations
 * 4. Probability matrix generation for correct scores
 * 5. BTTS (Both Teams To Score) calculations
 */

export interface DixonColesParams {
  homeAttack: number;
  homeDefense: number;
  awayAttack: number;
  awayDefense: number;
  homeAdvantage: number;
  rho: number; // Correlation parameter (typically ~-0.13)
}

export interface MatchPrediction {
  homeExpectedGoals: number;
  awayExpectedGoals: number;
  probabilities: {
    homeWin: number;
    draw: number;
    awayWin: number;
  };
  correctScore: {
    mostLikely: { home: number; away: number; probability: number };
    top5Scores: Array<{ home: number; away: number; probability: number }>;
    matrix: number[][]; // [home][away] probability matrix
  };
  bothTeamsToScore: {
    yes: number;
    no: number;
  };
  goalDistribution: {
    under05: number;
    under15: number;
    under25: number;
    under35: number;
    over05: number;
    over15: number;
    over25: number;
    over35: number;
  };
}

export class DixonColesService {
  private static readonly DEFAULT_RHO = -0.13;
  private static readonly TIME_DECAY_XI = 0.0025; // Time decay parameter
  private static readonly MAX_GOALS = 6; // Maximum goals to calculate in matrix

  /**
   * Calculate Poisson probability
   * P(X = k) = (λ^k * e^(-λ)) / k!
   */
  private static poissonProbability(k: number, lambda: number): number {
    if (lambda <= 0) return k === 0 ? 1 : 0;
    
    // Use log space to avoid overflow for large values
    const logProb = k * Math.log(lambda) - lambda - this.logFactorial(k);
    return Math.exp(logProb);
  }

  /**
   * Calculate log factorial using Stirling's approximation for large numbers
   */
  private static logFactorial(n: number): number {
    if (n <= 1) return 0;
    if (n <= 12) {
      // Use exact calculation for small numbers
      let result = 0;
      for (let i = 2; i <= n; i++) {
        result += Math.log(i);
      }
      return result;
    }
    // Stirling's approximation for larger numbers
    return n * Math.log(n) - n + 0.5 * Math.log(2 * Math.PI * n);
  }

  /**
   * Dixon-Coles rho correction function (τ)
   * Adjusts probabilities for low-scoring games
   */
  private static rhoCorrection(
    homeGoals: number,
    awayGoals: number,
    lambda: number,
    mu: number,
    rho: number
  ): number {
    if (homeGoals === 0 && awayGoals === 0) {
      return 1 - lambda * mu * rho;
    }
    if (homeGoals === 0 && awayGoals === 1) {
      return 1 + lambda * rho;
    }
    if (homeGoals === 1 && awayGoals === 0) {
      return 1 + mu * rho;
    }
    if (homeGoals === 1 && awayGoals === 1) {
      return 1 - rho;
    }
    return 1; // No correction for other scores
  }

  /**
   * Calculate time decay weight
   * w(t) = exp(-ξt) where t is days since match
   */
  private static timeDecayWeight(daysSinceMatch: number): number {
    return Math.exp(-this.TIME_DECAY_XI * daysSinceMatch);
  }

  /**
   * Calculate expected goals using Dixon-Coles parameters
   */
  private static calculateExpectedGoals(params: DixonColesParams): {
    homeExpected: number;
    awayExpected: number;
  } {
    // λ = exp(α_home + β_away + γ) (home expected goals)
    // μ = exp(α_away + β_home) (away expected goals)
    const homeExpected = Math.exp(
      params.homeAttack + params.awayDefense + Math.log(params.homeAdvantage)
    );
    const awayExpected = Math.exp(
      params.awayAttack + params.homeDefense
    );

    return { homeExpected, awayExpected };
  }

  /**
   * Generate probability matrix for all score combinations
   */
  private static generateProbabilityMatrix(
    lambda: number,
    mu: number,
    rho: number = this.DEFAULT_RHO
  ): number[][] {
    const matrix: number[][] = [];

    for (let home = 0; home <= this.MAX_GOALS; home++) {
      matrix[home] = [];
      for (let away = 0; away <= this.MAX_GOALS; away++) {
        const poissonProb = 
          this.poissonProbability(home, lambda) * 
          this.poissonProbability(away, mu);
        
        const rhoCorr = this.rhoCorrection(home, away, lambda, mu, rho);
        
        matrix[home][away] = poissonProb * rhoCorr;
      }
    }

    return matrix;
  }

  /**
   * Find the most likely scores from probability matrix
   */
  private static findTopScores(
    matrix: number[][],
    count: number = 5
  ): Array<{ home: number; away: number; probability: number }> {
    const scores: Array<{ home: number; away: number; probability: number }> = [];

    for (let home = 0; home <= this.MAX_GOALS; home++) {
      for (let away = 0; away <= this.MAX_GOALS; away++) {
        scores.push({
          home,
          away,
          probability: matrix[home][away]
        });
      }
    }

    return scores
      .sort((a, b) => b.probability - a.probability)
      .slice(0, count);
  }

  /**
   * Calculate match outcome probabilities from matrix
   */
  private static calculateMatchOutcomes(matrix: number[][]): {
    homeWin: number;
    draw: number;
    awayWin: number;
  } {
    let homeWin = 0;
    let draw = 0;
    let awayWin = 0;

    for (let home = 0; home <= this.MAX_GOALS; home++) {
      for (let away = 0; away <= this.MAX_GOALS; away++) {
        const prob = matrix[home][away];
        
        if (home > away) {
          homeWin += prob;
        } else if (home === away) {
          draw += prob;
        } else {
          awayWin += prob;
        }
      }
    }

    return { homeWin, draw, awayWin };
  }

  /**
   * Calculate Both Teams To Score probabilities
   */
  private static calculateBTTS(lambda: number, mu: number, rho: number): {
    yes: number;
    no: number;
  } {
    // P(BTTS = No) = P(Home = 0) + P(Away = 0) - P(Both = 0)
    const homeZero = this.poissonProbability(0, lambda);
    const awayZero = this.poissonProbability(0, mu);
    const bothZero = homeZero * awayZero * this.rhoCorrection(0, 0, lambda, mu, rho);
    
    const bttsNo = homeZero + awayZero - bothZero;
    const bttsYes = 1 - bttsNo;

    return { yes: bttsYes, no: bttsNo };
  }

  /**
   * Calculate goal distribution probabilities
   */
  private static calculateGoalDistribution(matrix: number[][]): {
    under05: number;
    under15: number;
    under25: number;
    under35: number;
    over05: number;
    over15: number;
    over25: number;
    over35: number;
  } {
    let under05 = 0; // 0 total goals
    let under15 = 0; // 0-1 total goals
    let under25 = 0; // 0-2 total goals
    let under35 = 0; // 0-3 total goals

    for (let home = 0; home <= this.MAX_GOALS; home++) {
      for (let away = 0; away <= this.MAX_GOALS; away++) {
        const totalGoals = home + away;
        const prob = matrix[home][away];

        if (totalGoals < 0.5) under05 += prob;
        if (totalGoals < 1.5) under15 += prob;
        if (totalGoals < 2.5) under25 += prob;
        if (totalGoals < 3.5) under35 += prob;
      }
    }

    return {
      under05,
      under15,
      under25,
      under35,
      over05: 1 - under05,
      over15: 1 - under15,
      over25: 1 - under25,
      over35: 1 - under35
    };
  }

  /**
   * Generate complete match prediction using Dixon-Coles model
   */
  public static generatePrediction(params: DixonColesParams): MatchPrediction {
    const { homeExpected, awayExpected } = this.calculateExpectedGoals(params);
    
    const matrix = this.generateProbabilityMatrix(
      homeExpected,
      awayExpected,
      params.rho
    );

    const topScores = this.findTopScores(matrix, 5);
    const outcomes = this.calculateMatchOutcomes(matrix);
    const btts = this.calculateBTTS(homeExpected, awayExpected, params.rho);
    const goalDistribution = this.calculateGoalDistribution(matrix);

    return {
      homeExpectedGoals: homeExpected,
      awayExpectedGoals: awayExpected,
      probabilities: outcomes,
      correctScore: {
        mostLikely: topScores[0],
        top5Scores: topScores,
        matrix
      },
      bothTeamsToScore: btts,
      goalDistribution
    };
  }

  /**
   * Validate Dixon-Coles parameters
   */
  public static validateParams(params: DixonColesParams): boolean {
    return (
      isFinite(params.homeAttack) &&
      isFinite(params.homeDefense) &&
      isFinite(params.awayAttack) &&
      isFinite(params.awayDefense) &&
      params.homeAdvantage > 0 &&
      params.rho >= -1 &&
      params.rho <= 1
    );
  }
}
