import { getPlayersCollection, Player } from '../models/Player';
import { getRedisClient } from '../config/redis';

// Cache keys
const PLAYER_ID_MAPPING_CACHE_KEY = 'player:id:mapping:';
const CACHE_TTL_DAY = 86400; // 24 hours in seconds

// Interface for player ID mapping
interface PlayerIdMapping {
    apiId: number;
    internalId: number;
    name: string;
    teamId?: number;
}

/**
 * Get the mapping between API player ID and internal player ID
 * @param id The player ID (either API or internal)
 * @param isApiId Whether the provided ID is an API ID (true) or internal ID (false)
 * @returns The mapping object or null if not found
 */
export async function getPlayerIdMapping(id: number, isApiId: boolean = true): Promise<PlayerIdMapping | null> {
    const redisClient = getRedisClient();
    const cacheKey = `${PLAYER_ID_MAPPING_CACHE_KEY}${isApiId ? 'api' : 'internal'}:${id}`;
    
    try {
        // Check cache first
        const cachedMapping = await redisClient.get(cacheKey);
        if (cachedMapping) {
            return JSON.parse(cachedMapping);
        }
        
        // If not in cache, query the database
        const playersCollection = getPlayersCollection();
        let player: Player | null = null;
        
        if (isApiId) {
            // Find by API ID
            player = await playersCollection.findOne({ apiId: id });
        } else {
            // Find by internal ID (_id)
            player = await playersCollection.findOne({ _id: id });
        }
        
        if (!player) {
            return null;
        }
        
        // Create mapping object
        const mapping: PlayerIdMapping = {
            apiId: player.apiId,
            internalId: player._id,
            name: player.profile?.name || `Player ${player._id}`,
            teamId: player.statistics ? 
                Object.values(player.statistics)[0]?.[0]?.team?.id : 
                undefined
        };
        
        // Cache the mapping
        await redisClient.setex(cacheKey, CACHE_TTL_DAY, JSON.stringify(mapping));
        
        return mapping;
    } catch (error) {
        console.error(`Error getting player ID mapping for ${isApiId ? 'API' : 'internal'} ID ${id}:`, error);
        return null;
    }
}

/**
 * Convert an API player ID to an internal player ID
 * @param apiId The API player ID
 * @returns The internal player ID or null if not found
 */
export async function apiIdToInternalId(apiId: number): Promise<number | null> {
    const mapping = await getPlayerIdMapping(apiId, true);
    return mapping ? mapping.internalId : null;
}

/**
 * Convert an internal player ID to an API player ID
 * @param internalId The internal player ID
 * @returns The API player ID or null if not found
 */
export async function internalIdToApiId(internalId: number): Promise<number | null> {
    const mapping = await getPlayerIdMapping(internalId, false);
    return mapping ? mapping.apiId : null;
}

/**
 * Get player information by ID (either API or internal)
 * @param id The player ID
 * @param isApiId Whether the provided ID is an API ID (true) or internal ID (false)
 * @returns Basic player information or null if not found
 */
export async function getPlayerInfoById(id: number, isApiId: boolean = true): Promise<{id: number, name: string, teamId?: number} | null> {
    const mapping = await getPlayerIdMapping(id, isApiId);
    if (!mapping) return null;
    
    return {
        id: isApiId ? mapping.internalId : mapping.apiId,
        name: mapping.name,
        teamId: mapping.teamId
    };
}

/**
 * Add a known player ID mapping to the cache
 * This is useful for hardcoded mappings that we know are correct
 * @param apiId The API player ID
 * @param internalId The internal player ID
 * @param name The player name
 * @param teamId Optional team ID
 */
export async function addKnownPlayerMapping(apiId: number, internalId: number, name: string, teamId?: number): Promise<void> {
    const redisClient = getRedisClient();
    const mapping: PlayerIdMapping = {
        apiId,
        internalId,
        name,
        teamId
    };
    
    // Cache both directions
    await redisClient.setex(`${PLAYER_ID_MAPPING_CACHE_KEY}api:${apiId}`, CACHE_TTL_DAY, JSON.stringify(mapping));
    await redisClient.setex(`${PLAYER_ID_MAPPING_CACHE_KEY}internal:${internalId}`, CACHE_TTL_DAY, JSON.stringify(mapping));
}

// Initialize known player mappings
export async function initializeKnownPlayerMappings(): Promise<void> {
    // Add known mappings for players with ID discrepancies
    await addKnownPlayerMapping(278132, 19366, 'Cole Palmer', 49); // Chelsea
    await addKnownPlayerMapping(284364, 19366, 'Cole Palmer', 49); // Chelsea
    await addKnownPlayerMapping(1440, 19545, 'Reece James', 49);   // Chelsea
    await addKnownPlayerMapping(2280, 19545, 'Reece James', 49);   // Chelsea
    
    // Add more known mappings as needed
    console.log('Initialized known player ID mappings');
}
