import { getUsersCollection } from '../models/User';
import { getAnonymousDeviceTokensCollection } from '../models/AnonymousDeviceToken';
import { getFixturesCollection } from '../models/Fixture';
import { sendPushNotification } from './notificationService';

// Helper function to send notifications to authenticated users
export async function sendToAuthenticatedUsers(
  fixtureId: number,
  title: string,
  body: string,
  data: any,
  notificationType: 'allEvents' | 'goals' | 'matchStatus' | 'redCards'
): Promise<void> {
  try {
    const usersCollection = getUsersCollection();
    const fixturesCollection = getFixturesCollection();

    // Get fixture details to find team IDs
    const fixture = await fixturesCollection.findOne({ _id: fixtureId });
    if (!fixture) {
      console.error(`Cannot send notifications: Fixture ${fixtureId} not found`);
      return;
    }

    const teamIds = [fixture.teams.home.id, fixture.teams.away.id];
    console.log(`Looking for users who favorited teams: ${teamIds.join(', ')} for fixture ${fixtureId}`);

    // Find users who:
    // 1. Have favorited one of the teams in the fixture
    // 2. Have at least one device token
    // 3. Have enabled the specific notification type OR allEvents
    const query = {
      'favorites.teams': { $in: teamIds },
      'deviceTokens.0': { $exists: true },
      $or: [
        { 'preferences.notifications.allEvents': true },
        { [`preferences.notifications.${notificationType}`]: true }
      ]
    };

    console.log(`User query for notifications: ${JSON.stringify(query)}`);

    const usersToNotify = await usersCollection.find(query)
      .project({ _id: 1, deviceTokens: 1, 'preferences.notifications': 1 })
      .toArray();

    console.log(`Found ${usersToNotify.length} authenticated users to notify for ${notificationType} in fixture ${fixtureId}`);

    // Send notifications to each user
    for (const user of usersToNotify) {
      const userId = user._id.toString();
      console.log(`Preparing to send notifications to user ${userId} with ${user.deviceTokens.length} device tokens`);

      if (user.preferences && user.preferences.notifications) {
        console.log(`User ${userId} notification preferences: ${JSON.stringify(user.preferences.notifications)}`);
      }

      // Track success/failure stats
      let successCount = 0;
      let failureCount = 0;

      for (const token of user.deviceTokens) {
        try {
          console.log(`Sending notification to token ${token} for user ${userId}`);
          const success = await sendPushNotification(token, title, body, data, userId);
          if (success) {
            successCount++;
            console.log(`Successfully sent notification to token ${token} for user ${userId}`);
          } else {
            failureCount++;
            console.error(`Failed to send notification to token ${token} for user ${userId}`);
          }
        } catch (error) {
          console.error(`Error sending notification to token ${token} for user ${userId}:`, error);
          failureCount++;
        }
      }

      console.log(`Notification results for User ${userId}: ${successCount} sent, ${failureCount} failed`);
    }
  } catch (error) {
    console.error('Error sending notifications to authenticated users:', error);
  }
}

// Helper function to send notifications to anonymous users
export async function sendToAnonymousUsers(
  fixtureId: number,
  title: string,
  body: string,
  data: any,
  notificationType: 'allEvents' | 'goals' | 'matchStatus' | 'redCards'
): Promise<void> {
  try {
    const anonymousTokensCollection = getAnonymousDeviceTokensCollection();
    const fixturesCollection = getFixturesCollection();

    // Get fixture details to find team IDs
    const fixture = await fixturesCollection.findOne({ _id: fixtureId });
    if (!fixture) {
      console.error(`Cannot send anonymous notifications: Fixture ${fixtureId} not found`);
      return;
    }

    const teamIds = [fixture.teams.home.id, fixture.teams.away.id];
    console.log(`Looking for anonymous tokens subscribed to fixture ${fixtureId} or teams: ${teamIds.join(', ')}`);

    // Find anonymous tokens that:
    // 1. Have subscribed to this fixture OR one of the teams in the fixture
    // 2. Have enabled the specific notification type OR allEvents
    const query = {
      $and: [
        { $or: [
            { subscribedFixtures: fixtureId },
            { subscribedTeams: { $in: teamIds } }
          ]
        },
        { $or: [
            { 'notificationPreferences.allEvents': true },
            { [`notificationPreferences.${notificationType}`]: true }
          ]
        }
      ]
    };

    console.log(`Anonymous token query for notifications: ${JSON.stringify(query)}`);

    const tokensToNotify = await anonymousTokensCollection.find(query).toArray();

    console.log(`Found ${tokensToNotify.length} anonymous tokens to notify for ${notificationType} in fixture ${fixtureId}`);

    // Send notifications to each token
    let successCount = 0;
    let failureCount = 0;

    for (const tokenDoc of tokensToNotify) {
      try {
        console.log(`Sending notification to anonymous token ${tokenDoc.token} with preferences: ${JSON.stringify(tokenDoc.notificationPreferences)}`);
        const success = await sendPushNotification(tokenDoc.token, title, body, data);
        if (success) {
          successCount++;
          console.log(`Successfully sent notification to anonymous token ${tokenDoc.token}`);
        } else {
          failureCount++;
          console.error(`Failed to send notification to anonymous token ${tokenDoc.token}`);
        }
      } catch (error) {
        console.error(`Error sending notification to anonymous token ${tokenDoc.token}:`, error);
        failureCount++;
      }
    }

    console.log(`Anonymous notification results: ${successCount} sent, ${failureCount} failed`);
  } catch (error) {
    console.error('Error sending notifications to anonymous users:', error);
  }
}
