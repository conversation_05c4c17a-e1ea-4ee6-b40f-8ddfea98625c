import { fetchData } from './apiFootball';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CoachCareer } from '../models/Coach';

// Define the coach response structure from the API
export interface CoachResponse {
    id: number;
    name: string;
    firstname: string | null;
    lastname: string | null;
    age: number | null;
    birth: {
        date: string | null;
        place: string | null;
        country: string | null;
    };
    nationality: string | null;
    height: string | null;
    weight: string | null;
    photo: string | null;
    team: {
        id: number;
        name: string;
        logo: string | null;
    } | null;
    career: CoachCareer[];
}

// Define allowed query parameters based on documentation
interface FetchCoachesParams {
    id?: number;     // Coach ID
    team?: number;   // Team ID
    search?: string; // Search by coach name (>= 3 chars)
}

export async function fetchCoaches(params: FetchCoachesParams): Promise<CoachResponse[]> {
    // Validate parameters
    if (!params.id && !params.team && !params.search) {
        throw new Error("At least one parameter (id, team, or search) is required.");
    }
    
    if (params.search && params.search.length < 3) {
        throw new Error("Search parameter must be at least 3 characters.");
    }
    
    return fetchData<CoachResponse[]>('/coachs', params);
}
