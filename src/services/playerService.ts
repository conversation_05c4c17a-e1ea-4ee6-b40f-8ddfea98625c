import { fetchData, apiFootballClient } from './apiFootball';
import { PlayerProfile, PlayerWithStats, TeamSquad, PlayerTeam, TopPlayer } from '../models/Player';
import { AxiosResponse } from 'axios';

// --- Player Seasons Function ---
// The API returns an array of numbers (years) directly in the 'response' field
interface FetchPlayerSeasonsParams {
    player?: number; // Optional: Get seasons specifically for this player
}

export async function fetchPlayerSeasons(params?: FetchPlayerSeasonsParams): Promise<number[]> {
    return fetchData<number[]>('/players/seasons', params);
}

// --- Player Profiles Function ---
// Define allowed query parameters based on documentation
interface FetchPlayerProfilesParams {
    player?: number; // Get profile for a specific player ID
    search?: string; // Search by player lastname (>= 3 chars)
    page?: number;   // Page number for pagination (250 results per page)
}

export async function fetchPlayerProfiles(params: FetchPlayerProfilesParams): Promise<PlayerProfile[]> {
    return fetchData<PlayerProfile[]>('/players/profiles', params);
}

// --- Player Statistics Function ---
// Define allowed query parameters based on documentation
interface FetchPlayerStatisticsParams {
    id?: number;     // Get stats for a specific player ID (requires season)
    team?: number;   // Get stats for all players in a team (requires season)
    league?: number; // Get stats for all players in a league (requires season)
    season: number;  // Required with id, league, or team. Season year.
    search?: string; // Search player names within a league or team context (requires league or team, and season)
    page?: number;   // Page number for pagination (20 results per page)
}

export async function fetchPlayerStatistics(params: FetchPlayerStatisticsParams): Promise<PlayerWithStats[]> {
    // Validate required parameters
    if (!params.season) {
        throw new Error("Parameter 'season' is required.");
    }
    if (!params.id && !params.team && !params.league) {
        throw new Error("At least one of 'id', 'team', or 'league' is required.");
    }
    
    return fetchData<PlayerWithStats[]>('/players', params);
}

// --- Player Squads Function ---
// Define allowed query parameters based on documentation
interface FetchPlayerSquadsParams {
    team?: number;   // Get squad for this team ID
    player?: number; // Get teams associated with this player ID
}

export async function fetchPlayerSquads(params: FetchPlayerSquadsParams): Promise<TeamSquad[]> {
    // Validate required parameters
    if (!params.team && !params.player) {
        throw new Error("Either 'team' or 'player' parameter is required.");
    }
    
    return fetchData<TeamSquad[]>('/players/squads', params);
}

// --- Player Teams Function ---
// Define allowed query parameters based on documentation
interface FetchPlayerTeamsParams {
    player: number; // Required: Player ID
}

export async function fetchPlayerTeams(params: FetchPlayerTeamsParams): Promise<PlayerTeam[]> {
    // Validate required parameters
    if (!params.player) {
        throw new Error("Parameter 'player' is required.");
    }
    
    return fetchData<PlayerTeam[]>('/players/teams', params);
}

// --- Top Scorers Function ---
// Define allowed query parameters based on documentation
interface FetchTopScorersParams {
    league: number; // Required: League ID
    season: number; // Required: Season year
}

export async function fetchTopScorers(params: FetchTopScorersParams): Promise<TopPlayer[]> {
    // Validate required parameters
    if (!params.league || !params.season) {
        throw new Error("Parameters 'league' and 'season' are required.");
    }
    
    return fetchData<TopPlayer[]>('/players/topscorers', params);
}

// --- Top Assists Function ---
// Define allowed query parameters based on documentation
interface FetchTopAssistsParams {
    league: number; // Required: League ID
    season: number; // Required: Season year
}

export async function fetchTopAssists(params: FetchTopAssistsParams): Promise<TopPlayer[]> {
    // Validate required parameters
    if (!params.league || !params.season) {
        throw new Error("Parameters 'league' and 'season' are required.");
    }
    
    return fetchData<TopPlayer[]>('/players/topassists', params);
}

// --- Top Yellow Cards Function ---
// Define allowed query parameters based on documentation
interface FetchTopYellowCardsParams {
    league: number; // Required: League ID
    season: number; // Required: Season year
}

export async function fetchTopYellowCards(params: FetchTopYellowCardsParams): Promise<TopPlayer[]> {
    // Validate required parameters
    if (!params.league || !params.season) {
        throw new Error("Parameters 'league' and 'season' are required.");
    }
    
    return fetchData<TopPlayer[]>('/players/topyellowcards', params);
}

// --- Top Red Cards Function ---
// Define allowed query parameters based on documentation
interface FetchTopRedCardsParams {
    league: number; // Required: League ID
    season: number; // Required: Season year
}

export async function fetchTopRedCards(params: FetchTopRedCardsParams): Promise<TopPlayer[]> {
    // Validate required parameters
    if (!params.league || !params.season) {
        throw new Error("Parameters 'league' and 'season' are required.");
    }
    
    return fetchData<TopPlayer[]>('/players/topredcards', params);
}
