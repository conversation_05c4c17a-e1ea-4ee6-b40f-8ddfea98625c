/**
 * Validation Service for Dixon-Coles Model
 * 
 * Implements the validation methodology from the Dixon-Coles paper:
 * - Time-series cross-validation
 * - Predicted profile log-likelihood calculation
 * - Model performance measurement
 */

import { TeamStrengthService, MatchData } from './teamStrengthService';
import { DixonColesService, DixonColesParams } from './dixonColesService';
import connectDB from '../config/database';

export interface ValidationResult {
  totalLogLikelihood: number;
  averageLogLikelihood: number;
  predictionsCount: number;
  accuracy: {
    correctResults: number;
    totalPredictions: number;
    percentage: number;
  };
  calibration: {
    homeWinAccuracy: number;
    drawAccuracy: number;
    awayWinAccuracy: number;
  };
  timeRange: {
    from: Date;
    to: Date;
  };
}

export interface PredictionValidation {
  fixtureId: number;
  actualResult: 'H' | 'D' | 'A';
  predictedProbabilities: {
    homeWin: number;
    draw: number;
    awayWin: number;
  };
  logLikelihood: number;
  date: Date;
}

export class ValidationService {
  private static readonly VALIDATION_WINDOW_DAYS = 100; // Last 100 days of season
  private static readonly MIN_TRAINING_MATCHES = 50; // Minimum matches for training
  private static readonly PREDICTION_BATCH_DAYS = 3; // Predict 3 days at a time

  /**
   * Validate model performance using time-series cross-validation
   */
  public static async validateModelPerformance(
    leagueId: number,
    seasonStartDate: Date,
    seasonEndDate: Date
  ): Promise<ValidationResult> {
    console.log(`Starting validation for league ${leagueId}...`);

    const db = await connectDB();
    const validationResults: PredictionValidation[] = [];

    // Calculate validation period (last 100 days of season)
    const validationStartDate = new Date(seasonEndDate);
    validationStartDate.setDate(validationStartDate.getDate() - this.VALIDATION_WINDOW_DAYS);

    // Get all matches for the season
    const allMatches = await this.getSeasonMatches(leagueId, seasonStartDate, seasonEndDate);
    
    if (allMatches.length < this.MIN_TRAINING_MATCHES) {
      throw new Error(`Insufficient data: only ${allMatches.length} matches available`);
    }

    console.log(`Validating on ${allMatches.length} matches from ${seasonStartDate.toISOString().split('T')[0]} to ${seasonEndDate.toISOString().split('T')[0]}`);

    // Time-series cross-validation
    let currentDate = new Date(validationStartDate);
    
    while (currentDate < seasonEndDate) {
      const batchEndDate = new Date(currentDate);
      batchEndDate.setDate(batchEndDate.getDate() + this.PREDICTION_BATCH_DAYS);

      // Get training data (all matches before current date)
      const trainingMatches = allMatches.filter(m => m.date < currentDate);
      
      if (trainingMatches.length < this.MIN_TRAINING_MATCHES) {
        currentDate = batchEndDate;
        continue;
      }

      // Get validation matches (matches in current batch)
      const validationMatches = allMatches.filter(m => 
        m.date >= currentDate && m.date < batchEndDate
      );

      if (validationMatches.length === 0) {
        currentDate = batchEndDate;
        continue;
      }

      // Build model on training data
      const { teamStrengths, leagueParams } = await TeamStrengthService.calculateFromMatches(trainingMatches);

      // Make predictions on validation matches
      for (const match of validationMatches) {
        const prediction = await this.makePrediction(match, teamStrengths, leagueParams);
        if (prediction) {
          validationResults.push(prediction);
        }
      }

      console.log(`Processed batch ${currentDate.toISOString().split('T')[0]} - ${batchEndDate.toISOString().split('T')[0]}: ${validationMatches.length} matches`);
      
      currentDate = batchEndDate;
    }

    // Calculate validation metrics
    return this.calculateValidationMetrics(validationResults, validationStartDate, seasonEndDate);
  }

  /**
   * Get all matches for a season from database
   */
  private static async getSeasonMatches(
    leagueId: number,
    startDate: Date,
    endDate: Date
  ): Promise<MatchData[]> {
    const db = await connectDB();
    
    const fixtures = await db.collection('fixtures').find({
      'league.id': leagueId,
      'fixture.status.short': { $in: ['FT', 'AET', 'PEN'] },
      'date': {
        $gte: startDate.toISOString(),
        $lte: endDate.toISOString()
      },
      'goals.home': { $exists: true, $ne: null },
      'goals.away': { $exists: true, $ne: null }
    }).sort({ date: 1 }).toArray();

    return fixtures.map(fixture => ({
      homeTeamId: fixture.teams.home.id,
      awayTeamId: fixture.teams.away.id,
      homeGoals: fixture.goals.home,
      awayGoals: fixture.goals.away,
      date: new Date(fixture.date),
      leagueId: fixture.league.id
    }));
  }

  /**
   * Make a prediction for a single match
   */
  private static async makePrediction(
    match: MatchData,
    teamStrengths: Map<number, any>,
    leagueParams: any
  ): Promise<PredictionValidation | null> {
    try {
      // Calculate Dixon-Coles parameters
      const dixonColesParams = TeamStrengthService.calculateMatchParams(
        match.homeTeamId,
        match.awayTeamId,
        match.leagueId,
        teamStrengths,
        leagueParams
      );

      if (!dixonColesParams) {
        return null;
      }

      // Generate prediction
      const prediction = DixonColesService.generatePrediction(dixonColesParams);

      // Determine actual result
      let actualResult: 'H' | 'D' | 'A';
      if (match.homeGoals > match.awayGoals) {
        actualResult = 'H';
      } else if (match.homeGoals < match.awayGoals) {
        actualResult = 'A';
      } else {
        actualResult = 'D';
      }

      // Calculate log-likelihood for actual result
      let actualProbability: number;
      switch (actualResult) {
        case 'H':
          actualProbability = prediction.probabilities.homeWin;
          break;
        case 'D':
          actualProbability = prediction.probabilities.draw;
          break;
        case 'A':
          actualProbability = prediction.probabilities.awayWin;
          break;
      }

      const logLikelihood = actualProbability > 0 ? Math.log(actualProbability) : -10; // Cap very low probabilities

      return {
        fixtureId: 0, // Would be actual fixture ID in real implementation
        actualResult,
        predictedProbabilities: {
          homeWin: prediction.probabilities.homeWin,
          draw: prediction.probabilities.draw,
          awayWin: prediction.probabilities.awayWin
        },
        logLikelihood,
        date: match.date
      };

    } catch (error) {
      console.error('Error making prediction:', error);
      return null;
    }
  }

  /**
   * Calculate validation metrics from prediction results
   */
  private static calculateValidationMetrics(
    predictions: PredictionValidation[],
    startDate: Date,
    endDate: Date
  ): ValidationResult {
    if (predictions.length === 0) {
      throw new Error('No predictions available for validation');
    }

    // Calculate log-likelihood metrics
    const totalLogLikelihood = predictions.reduce((sum, p) => sum + p.logLikelihood, 0);
    const averageLogLikelihood = totalLogLikelihood / predictions.length;

    // Calculate accuracy (most likely outcome)
    let correctResults = 0;
    const resultCounts = { H: 0, D: 0, A: 0 };
    const correctByResult = { H: 0, D: 0, A: 0 };

    for (const prediction of predictions) {
      resultCounts[prediction.actualResult]++;

      // Determine predicted result (highest probability)
      const probs = prediction.predictedProbabilities;
      let predictedResult: 'H' | 'D' | 'A';
      
      if (probs.homeWin >= probs.draw && probs.homeWin >= probs.awayWin) {
        predictedResult = 'H';
      } else if (probs.draw >= probs.awayWin) {
        predictedResult = 'D';
      } else {
        predictedResult = 'A';
      }

      if (predictedResult === prediction.actualResult) {
        correctResults++;
        correctByResult[prediction.actualResult]++;
      }
    }

    return {
      totalLogLikelihood,
      averageLogLikelihood,
      predictionsCount: predictions.length,
      accuracy: {
        correctResults,
        totalPredictions: predictions.length,
        percentage: (correctResults / predictions.length) * 100
      },
      calibration: {
        homeWinAccuracy: resultCounts.H > 0 ? (correctByResult.H / resultCounts.H) * 100 : 0,
        drawAccuracy: resultCounts.D > 0 ? (correctByResult.D / resultCounts.D) * 100 : 0,
        awayWinAccuracy: resultCounts.A > 0 ? (correctByResult.A / resultCounts.A) * 100 : 0
      },
      timeRange: {
        from: startDate,
        to: endDate
      }
    };
  }

  /**
   * Compare two models using validation results
   */
  public static compareModels(
    baseline: ValidationResult,
    improved: ValidationResult
  ): {
    logLikelihoodImprovement: number;
    accuracyImprovement: number;
    isSignificantImprovement: boolean;
  } {
    const logLikelihoodImprovement = improved.averageLogLikelihood - baseline.averageLogLikelihood;
    const accuracyImprovement = improved.accuracy.percentage - baseline.accuracy.percentage;
    
    // Simple significance test (in practice, you'd use proper statistical tests)
    const isSignificantImprovement = logLikelihoodImprovement > 0.01 && accuracyImprovement > 1.0;

    return {
      logLikelihoodImprovement,
      accuracyImprovement,
      isSignificantImprovement
    };
  }
}
