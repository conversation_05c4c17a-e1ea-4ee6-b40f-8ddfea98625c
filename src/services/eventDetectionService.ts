import { Fixture, EventInfo } from '../models/Fixture';
import { sendGoalNotification, sendGoalDisallowedNotification, sendRedCardNotification, sendMatchStatusNotification } from './notificationService';

// Store the last known state of fixtures to detect changes
const fixtureStates = new Map<number, {
  status: string;
  homeGoals: number;
  awayGoals: number;
  events: EventInfo[];
}>();

/**
 * Process fixture updates to detect events that should trigger notifications
 * @param fixture The updated fixture data
 */
export async function processFixtureUpdate(fixture: Fixture): Promise<void> {
  const fixtureId = fixture._id;
  const currentStatus = fixture.fixture.status.short;
  const homeGoals = fixture.goals.home;
  const awayGoals = fixture.goals.away;

  // Use the events directly from the fixture without any processing
  const events = fixture.events || [];

  console.log(`Processing fixture update for ID ${fixtureId}, status: ${currentStatus}, score: ${homeGoals}-${awayGoals}, events: ${events.length}`);

  // Get previous state or initialize if not exists
  const previousState = fixtureStates.get(fixtureId) || {
    status: '',
    homeGoals: 0,
    awayGoals: 0,
    events: []
  };

  console.log(`Previous state for fixture ${fixtureId}: status: ${previousState.status}, score: ${previousState.homeGoals}-${previousState.awayGoals}, events: ${previousState.events.length}`);

  // Check for status changes
  if (previousState.status !== currentStatus) {
    console.log(`Status changed for fixture ${fixtureId}: ${previousState.status} -> ${currentStatus}`);

    // Send notifications for specific status changes including extra time and penalties
    if (['1H', '2H', 'HT', 'FT', 'ET', 'BT', 'P', 'AET', 'PEN'].includes(currentStatus)) {
      // Add more detailed logging for extended match scenarios
      if (['ET', 'BT', 'P', 'AET', 'PEN'].includes(currentStatus)) {
        console.log(`Sending EXTENDED MATCH notification for fixture ${fixtureId}: ${currentStatus} (previous: ${previousState.status})`);
      } else {
        console.log(`Sending status notification for fixture ${fixtureId}: ${currentStatus}`);
      }
      await sendMatchStatusNotification(fixtureId, currentStatus);
    }
  }

  // Check for new goals
  if (previousState.homeGoals !== homeGoals || previousState.awayGoals !== awayGoals) {
    console.log(`Score changed for fixture ${fixtureId}: ${previousState.homeGoals}-${previousState.awayGoals} -> ${homeGoals}-${awayGoals}`);

    // Find new goal events
    const newGoalEvents = findNewGoalEvents(previousState.events, events);
    console.log(`Found ${newGoalEvents.length} new goal events for fixture ${fixtureId}`);

    // Send notifications for each new goal
    for (const goalEvent of newGoalEvents) {
      console.log(`Sending goal notification for fixture ${fixtureId}, event time: ${goalEvent.time.elapsed}'${goalEvent.time.extra ? `+${goalEvent.time.extra}` : ''}, player: ${goalEvent.player.name || 'Unknown'}`);
      await sendGoalNotification(fixtureId, goalEvent);
    }
  }

  // Check for red cards
  const newRedCardEvents = findNewRedCardEvents(previousState.events, events);
  console.log(`Found ${newRedCardEvents.length} new red card events for fixture ${fixtureId}`);

  // Send notifications for each new red card
  for (const redCardEvent of newRedCardEvents) {
    console.log(`Sending red card notification for fixture ${fixtureId}, event time: ${redCardEvent.time.elapsed}'${redCardEvent.time.extra ? `+${redCardEvent.time.extra}` : ''}, player: ${redCardEvent.player.name || 'Unknown'}`);
    await sendRedCardNotification(fixtureId, redCardEvent);
  }

  // Check for disallowed goals (VAR decisions)
  const newDisallowedGoalEvents = findNewDisallowedGoalEvents(previousState.events, events);
  console.log(`Found ${newDisallowedGoalEvents.length} new disallowed goal events for fixture ${fixtureId}`);

  // Send notifications for each disallowed goal
  for (const disallowedGoalEvent of newDisallowedGoalEvents) {
    console.log(`Sending goal disallowed notification for fixture ${fixtureId}, event time: ${disallowedGoalEvent.time.elapsed}'${disallowedGoalEvent.time.extra ? `+${disallowedGoalEvent.time.extra}` : ''}, player: ${disallowedGoalEvent.player.name || 'Unknown'}`);
    await sendGoalDisallowedNotification(fixtureId, disallowedGoalEvent);
  }

  // Update the stored state with cleaned events
  fixtureStates.set(fixtureId, {
    status: currentStatus,
    homeGoals: homeGoals,
    awayGoals: awayGoals,
    events: events
  });

  console.log(`Updated stored state for fixture ${fixtureId}`);
}

/**
 * Find new goal events by comparing previous and current events
 * @param previousEvents Previous events array
 * @param currentEvents Current events array
 * @returns Array of new goal events
 */
function findNewGoalEvents(previousEvents: EventInfo[], currentEvents: EventInfo[]): EventInfo[] {
  // Get all goal events from current events
  const goalEvents = currentEvents.filter(event =>
    event.type === 'Goal' &&
    !['Missed Penalty', 'Penalty confirmed', 'Goal Disallowed'].includes(event.detail || '')
  );

  // Get all goal events from previous events
  const previousGoalEvents = previousEvents.filter(event =>
    event.type === 'Goal' &&
    !['Missed Penalty', 'Penalty confirmed', 'Goal Disallowed'].includes(event.detail || '')
  );

  // Find new goal events by comparing time and player
  return goalEvents.filter(goalEvent =>
    !previousGoalEvents.some(prevGoalEvent =>
      prevGoalEvent.time.elapsed === goalEvent.time.elapsed &&
      prevGoalEvent.time.extra === goalEvent.time.extra &&
      prevGoalEvent.player.id === goalEvent.player.id
    )
  );
}

/**
 * Find new disallowed goal events by comparing previous and current events
 * @param previousEvents Previous events array
 * @param currentEvents Current events array
 * @returns Array of new disallowed goal events
 */
function findNewDisallowedGoalEvents(previousEvents: EventInfo[], currentEvents: EventInfo[]): EventInfo[] {
  // Get all disallowed goal events from current events
  // These can be either:
  // 1. Goal events with detail "Goal Disallowed"
  // 2. VAR events that have "Goal Disallowed" in the detail field
  // 3. VAR events that mention "goal" and "disallowed" in either detail or comments
  const disallowedGoalEvents = currentEvents.filter(event =>
    (event.type === 'Goal' && event.detail === 'Goal Disallowed') ||
    (event.type === 'Var' && event.detail?.toLowerCase().includes('goal disallowed')) ||
    (event.type === 'Var' && (
      (event.detail?.toLowerCase().includes('disallowed')) ||
      (event.comments?.toLowerCase().includes('disallowed'))
    ))
  );

  // Get all disallowed goal events from previous events
  const previousDisallowedGoalEvents = previousEvents.filter(event =>
    (event.type === 'Goal' && event.detail === 'Goal Disallowed') ||
    (event.type === 'Var' && event.detail?.toLowerCase().includes('goal disallowed')) ||
    (event.type === 'Var' && (
      (event.detail?.toLowerCase().includes('disallowed')) ||
      (event.comments?.toLowerCase().includes('disallowed'))
    ))
  );

  // Find new disallowed goal events by comparing time and player
  return disallowedGoalEvents.filter(disallowedGoalEvent =>
    !previousDisallowedGoalEvents.some(prevDisallowedGoalEvent =>
      prevDisallowedGoalEvent.time.elapsed === disallowedGoalEvent.time.elapsed &&
      prevDisallowedGoalEvent.time.extra === disallowedGoalEvent.time.extra &&
      prevDisallowedGoalEvent.player.id === disallowedGoalEvent.player.id
    )
  );
}

/**
 * Find new red card events by comparing previous and current events
 * @param previousEvents Previous events array
 * @param currentEvents Current events array
 * @returns Array of new red card events
 */
function findNewRedCardEvents(previousEvents: EventInfo[], currentEvents: EventInfo[]): EventInfo[] {
  // Get all red card events from current events (including second yellow cards)
  const redCardEvents = currentEvents.filter(event =>
    (event.type === 'Card' && event.detail === 'Red Card') ||
    (event.type === 'Card' && event.detail === 'Second Yellow card')
  );

  // Get all red card events from previous events
  const previousRedCardEvents = previousEvents.filter(event =>
    (event.type === 'Card' && event.detail === 'Red Card') ||
    (event.type === 'Card' && event.detail === 'Second Yellow card')
  );

  // Create a map of players who already have red card events in the previous state
  // This helps us avoid duplicate notifications for the same player
  const playersWithRedCards = new Map<number, boolean>();

  // Track players who already had red cards in previous events
  previousRedCardEvents.forEach(event => {
    if (event.player && event.player.id) {
      playersWithRedCards.set(event.player.id, true);
    }
  });

  // First, filter out events for players who already had red cards in previous state
  const potentialNewRedCardEvents = redCardEvents.filter(event => {
    if (!event.player || !event.player.id) return false;
    return !playersWithRedCards.has(event.player.id);
  });

  // Then, deduplicate events for the same player in the current batch
  // (e.g., if there's both a "Second Yellow card" and a "Red Card" for the same player)
  const processedPlayerIds = new Set<number>();
  const uniqueRedCardEvents: EventInfo[] = [];

  // Prioritize "Second Yellow card" events over direct "Red Card" events
  // Sort events to process "Second Yellow card" first
  const sortedEvents = [...potentialNewRedCardEvents].sort((a, b) => {
    // Prioritize "Second Yellow card" over "Red Card"
    if (a.detail === 'Second Yellow card' && b.detail !== 'Second Yellow card') return -1;
    if (a.detail !== 'Second Yellow card' && b.detail === 'Second Yellow card') return 1;
    return 0;
  });

  // Add only one red card event per player
  for (const event of sortedEvents) {
    if (!event.player || !event.player.id) continue;

    const playerId = event.player.id;
    if (!processedPlayerIds.has(playerId)) {
      uniqueRedCardEvents.push(event);
      processedPlayerIds.add(playerId);
    }
  }

  return uniqueRedCardEvents;
}

/**
 * Clear stored state for a fixture
 * @param fixtureId The fixture ID to clear
 */
export function clearFixtureState(fixtureId: number): void {
  fixtureStates.delete(fixtureId);
}

/**
 * Clear all stored fixture states
 */
export function clearAllFixtureStates(): void {
  fixtureStates.clear();
}

// Note: We now use the events directly from the API without any processing
