/**
 * ELO Rating Service
 * 
 * This service handles:
 * 1. Daily sync of ELO ratings from ClubElo API
 * 2. Team name matching between ClubElo and API-Football
 * 3. ELO-enhanced strength calculations for predictions
 */

import axios from 'axios';
import csv from 'csv-parser';
import { Readable } from 'stream';
import { 
    EloRating, 
    TeamNameMapping, 
    EloEnhancedStrength,
    getEloRatingsCollection,
    getTeamNameMappingsCollection,
    getEloEnhancedStrengthCollection,
    createMappingId,
    normalizeTeamName
} from '../models/EloRating';
import { getTeamsCollection } from '../models/Team';
import { getRedisClient } from '../config/redis';

const CLUBELO_BASE_URL = 'http://api.clubelo.com';
const ELO_CACHE_TTL = 60 * 60 * 24; // 24 hours

/**
 * Fetch and parse ELO ratings from ClubElo API
 */
export async function fetchEloRatings(date?: string): Promise<EloRating[]> {
    const targetDate = date || new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const url = `${CLUBELO_BASE_URL}/${targetDate}`;
    
    console.log(`Fetching ELO ratings from: ${url}`);
    
    try {
        const response = await axios.get(url, {
            timeout: 30000,
            headers: {
                'User-Agent': 'KickoffScore-Predictions/1.0'
            }
        });
        
        const csvData = response.data;
        const eloRatings: EloRating[] = [];
        
        return new Promise((resolve, reject) => {
            const stream = Readable.from([csvData]);
            
            stream
                .pipe(csv())
                .on('data', (row) => {
                    const eloRating: EloRating = {
                        _id: row.Club,
                        club: row.Club,
                        country: row.Country,
                        level: parseInt(row.Level) || 1,
                        elo: parseFloat(row.Elo) || 0,
                        rank: row.Rank === 'None' ? null : parseInt(row.Rank),
                        from: row.From,
                        to: row.To,
                        lastUpdated: new Date()
                    };
                    eloRatings.push(eloRating);
                })
                .on('end', () => {
                    console.log(`Parsed ${eloRatings.length} ELO ratings`);
                    resolve(eloRatings);
                })
                .on('error', (error) => {
                    console.error('Error parsing CSV:', error);
                    reject(error);
                });
        });
        
    } catch (error) {
        console.error('Error fetching ELO ratings:', error);
        throw error;
    }
}

/**
 * Store ELO ratings in database (replace all data)
 */
export async function storeEloRatings(eloRatings: EloRating[]): Promise<void> {
    const collection = getEloRatingsCollection();
    
    try {
        // Clear existing data
        await collection.deleteMany({});
        console.log('Cleared existing ELO ratings');
        
        // Insert new data
        if (eloRatings.length > 0) {
            await collection.insertMany(eloRatings);
            console.log(`Stored ${eloRatings.length} ELO ratings`);
        }
        
        // Create indexes for efficient querying
        await collection.createIndex({ club: 1 });
        await collection.createIndex({ country: 1 });
        await collection.createIndex({ elo: -1 });
        await collection.createIndex({ rank: 1 });
        
    } catch (error) {
        console.error('Error storing ELO ratings:', error);
        throw error;
    }
}

/**
 * Manual ELO sync trigger (for testing/manual updates)
 */
export async function triggerManualEloSync(): Promise<void> {
    console.log('Triggering manual ELO ratings sync...');

    try {
        await syncEloRatings();
        console.log('Manual ELO ratings sync completed successfully');
    } catch (error) {
        console.error('Error in manual ELO ratings sync:', error);
        throw error;
    }
}

/**
 * Daily ELO sync job with incremental mapping
 */
export async function syncEloRatings(): Promise<void> {
    console.log('🚀 Starting daily ELO ratings sync...');

    try {
        // Step 1: Fetch and store latest ELO ratings
        const eloRatings = await fetchEloRatings();
        await storeEloRatings(eloRatings);

        // Step 2: Incremental team mapping (preserves existing mappings)
        await updateTeamMappingsIncremental();

        // Step 3: Generate enhanced strength data for all mapped teams
        await generateEloEnhancedStrength();

        console.log('✅ ELO ratings sync completed successfully');

    } catch (error) {
        console.error('❌ Error in ELO ratings sync:', error);
        throw error;
    }
}

/**
 * Force full remapping (use with caution - will lose manual mappings)
 * Only use this for initial setup or when you need to completely rebuild mappings
 */
export async function syncEloRatingsWithFullRemapping(): Promise<void> {
    console.log('⚠️  Starting ELO sync with FULL REMAPPING (will lose manual mappings)...');

    try {
        const eloRatings = await fetchEloRatings();
        await storeEloRatings(eloRatings);

        // Use legacy full remapping (destructive)
        await updateTeamMappings();

        await generateEloEnhancedStrength();

        console.log('✅ ELO ratings sync with full remapping completed');

    } catch (error) {
        console.error('❌ Error in ELO ratings sync with full remapping:', error);
        throw error;
    }
}

/**
 * Smart team name matching between ClubElo and API-Football (LEGACY - Full Remapping)
 * @deprecated Use updateTeamMappingsIncremental instead
 */
export async function updateTeamMappings(): Promise<void> {
    console.log('⚠️  WARNING: Using legacy full remapping. Consider using updateTeamMappingsIncremental()');

    const eloCollection = getEloRatingsCollection();
    const teamsCollection = getTeamsCollection();
    const mappingsCollection = getTeamNameMappingsCollection();

    const eloRatings = await eloCollection.find({}).toArray();
    const apiTeams = await teamsCollection.find({}).toArray();

    const newMappings: TeamNameMapping[] = [];

    for (const eloTeam of eloRatings) {
        // Skip teams with null or empty club names
        if (!eloTeam.club) continue;

        const normalizedEloName = normalizeTeamName(eloTeam.club);

        // Find best matching API team
        let bestMatch = null;
        let bestScore = 0;

        for (const apiTeam of apiTeams) {
            if (!apiTeam.team || !apiTeam.team.name) continue;
            const normalizedApiName = normalizeTeamName(apiTeam.team.name);
            const score = calculateNameSimilarity(normalizedEloName, normalizedApiName, eloTeam.country, apiTeam.team.country);

            if (score > bestScore && score > 0.7) { // Minimum confidence threshold
                bestScore = score;
                bestMatch = apiTeam;
            }
        }

        if (bestMatch) {
            const mappingId = createMappingId(eloTeam.club, bestMatch._id);
            const mapping: TeamNameMapping = {
                _id: mappingId,
                eloName: eloTeam.club,
                apiTeamId: bestMatch._id,
                apiTeamName: bestMatch.team.name,
                confidence: bestScore,
                country: eloTeam.country,
                verified: false,
                createdAt: new Date(),
                lastUpdated: new Date()
            };

            newMappings.push(mapping);
        }
    }

    // Store mappings
    if (newMappings.length > 0) {
        await mappingsCollection.deleteMany({}); // Clear existing
        await mappingsCollection.insertMany(newMappings);
        console.log(`Created ${newMappings.length} team mappings`);
    }
}

/**
 * Incremental team name mapping - only processes new/unmapped teams
 * Preserves existing mappings, especially manual/verified ones
 */
export async function updateTeamMappingsIncremental(): Promise<void> {
    console.log('🔄 Updating team name mappings (incremental)...');

    const eloCollection = getEloRatingsCollection();
    const teamsCollection = getTeamsCollection();
    const mappingsCollection = getTeamNameMappingsCollection();

    // Get existing mappings to preserve them
    const existingMappings = await mappingsCollection.find({}).toArray();
    const mappedEloNames = new Set(existingMappings.map(m => m.eloName));

    console.log(`Found ${existingMappings.length} existing mappings to preserve`);

    // Only get ELO teams that are NOT already mapped
    const unmappedEloTeams = await eloCollection.find({
        club: { $nin: Array.from(mappedEloNames) }
    }).toArray();

    console.log(`Found ${unmappedEloTeams.length} unmapped ELO teams to process`);

    if (unmappedEloTeams.length === 0) {
        console.log('✅ No new teams to map - all existing mappings preserved');
        return;
    }

    // Get all API teams for matching
    const apiTeams = await teamsCollection.find({}).toArray();
    const newMappings: TeamNameMapping[] = [];

    for (const eloTeam of unmappedEloTeams) {
        // Skip teams with null or empty club names
        if (!eloTeam.club) continue;

        const normalizedEloName = normalizeTeamName(eloTeam.club);

        // Find best matching API team
        let bestMatch = null;
        let bestScore = 0;

        for (const apiTeam of apiTeams) {
            if (!apiTeam.team || !apiTeam.team.name) continue;
            const normalizedApiName = normalizeTeamName(apiTeam.team.name);
            const score = calculateNameSimilarity(normalizedEloName, normalizedApiName, eloTeam.country, apiTeam.team.country);

            if (score > bestScore && score > 0.7) { // Minimum confidence threshold
                bestScore = score;
                bestMatch = apiTeam;
            }
        }

        if (bestMatch) {
            const mappingId = createMappingId(eloTeam.club, bestMatch._id);

            // Double-check this mapping doesn't already exist (safety check)
            const existingMapping = await mappingsCollection.findOne({ _id: mappingId });
            if (!existingMapping) {
                const mapping: TeamNameMapping = {
                    _id: mappingId,
                    eloName: eloTeam.club,
                    apiTeamId: bestMatch._id,
                    apiTeamName: bestMatch.team.name,
                    confidence: bestScore,
                    country: eloTeam.country,
                    verified: false,
                    createdAt: new Date(),
                    lastUpdated: new Date()
                };

                newMappings.push(mapping);
            }
        }
    }

    // Insert only new mappings (preserve existing ones)
    if (newMappings.length > 0) {
        await mappingsCollection.insertMany(newMappings);
        console.log(`✅ Added ${newMappings.length} new team mappings`);
        console.log(`📊 Total mappings: ${existingMappings.length + newMappings.length}`);
    } else {
        console.log('ℹ️  No new mappings created - no suitable matches found');
    }
}

/**
 * Calculate name similarity score
 */
function calculateNameSimilarity(name1: string, name2: string, country1?: string | null, country2?: string | null): number {
    // Exact match
    if (name1 === name2) return 1.0;

    // Country mismatch penalty
    let countryBonus = 0;
    if (country1 && country2) {
        if (country1.toLowerCase() === country2.toLowerCase()) {
            countryBonus = 0.2;
        } else {
            return 0; // Different countries, no match
        }
    }
    
    // Levenshtein distance similarity
    const distance = levenshteinDistance(name1, name2);
    const maxLength = Math.max(name1.length, name2.length);
    const similarity = 1 - (distance / maxLength);
    
    // Common abbreviations and variations
    const abbreviationBonus = checkAbbreviations(name1, name2);
    
    return Math.min(1.0, similarity + countryBonus + abbreviationBonus);
}

/**
 * Levenshtein distance calculation
 */
function levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }
    
    return matrix[str2.length][str1.length];
}

/**
 * Check for common team name abbreviations
 */
function checkAbbreviations(name1: string, name2: string): number {
    const commonMappings = {
        'man city': 'manchester city',
        'man united': 'manchester united',
        'tottenham': 'tottenham hotspur',
        'arsenal': 'arsenal fc',
        'chelsea': 'chelsea fc',
        'liverpool': 'liverpool fc',
        'real madrid': 'real madrid cf',
        'barcelona': 'fc barcelona',
        'bayern': 'bayern munich',
        'psg': 'paris sg',
        'paris sg': 'paris saint germain'
    };
    
    for (const [short, full] of Object.entries(commonMappings)) {
        if ((name1.includes(short) && name2.includes(full)) || 
            (name1.includes(full) && name2.includes(short))) {
            return 0.3;
        }
    }
    
    return 0;
}

/**
 * Generate ELO-enhanced strength data for all mapped teams
 */
export async function generateEloEnhancedStrength(): Promise<void> {
    console.log('Generating ELO-enhanced strength data...');
    
    const mappingsCollection = getTeamNameMappingsCollection();
    const eloCollection = getEloRatingsCollection();
    const strengthCollection = getEloEnhancedStrengthCollection();
    
    const mappings = await mappingsCollection.find({}).toArray(); // Removed confidence threshold
    const allEloRatings = await eloCollection.find({}).sort({ elo: -1 }).toArray();
    
    const strengthData: EloEnhancedStrength[] = [];
    
    for (const mapping of mappings) {
        const eloRating = await eloCollection.findOne({ club: mapping.eloName });
        
        if (eloRating) {
            // Calculate relative strength (normalize ELO to 0-2 scale)
            const minElo = Math.min(...allEloRatings.map(r => r.elo));
            const maxElo = Math.max(...allEloRatings.map(r => r.elo));
            const relativeStrength = 0.5 + (1.5 * (eloRating.elo - minElo) / (maxElo - minElo));
            
            // Calculate global percentile
            const betterTeams = allEloRatings.filter(r => r.elo > eloRating.elo).length;
            const globalPercentile = ((allEloRatings.length - betterTeams) / allEloRatings.length) * 100;
            
            const strength: EloEnhancedStrength = {
                teamId: mapping.apiTeamId,
                eloRating: eloRating.elo,
                eloRank: eloRating.rank,
                relativeStrength,
                globalPercentile,
                lastUpdated: new Date()
            };
            
            strengthData.push(strength);
        }
    }
    
    // Store strength data
    if (strengthData.length > 0) {
        await strengthCollection.deleteMany({});
        await strengthCollection.insertMany(strengthData);
        await strengthCollection.createIndex({ teamId: 1 });
        console.log(`Generated ELO-enhanced strength for ${strengthData.length} teams`);
    }
}

/**
 * Get ELO-enhanced strength for a team
 */
export async function getEloStrength(teamId: number): Promise<EloEnhancedStrength | null> {
    const collection = getEloEnhancedStrengthCollection();
    return await collection.findOne({ teamId });
}

/**
 * Get ELO ratings with caching
 */
export async function getCachedEloRatings(): Promise<EloRating[]> {
    const redisClient = getRedisClient();
    const cacheKey = 'elo_ratings_all';
    
    try {
        const cached = await redisClient.get(cacheKey);
        if (cached) {
            return JSON.parse(cached);
        }
        
        const collection = getEloRatingsCollection();
        const ratings = await collection.find({}).sort({ elo: -1 }).toArray();
        
        await redisClient.setex(cacheKey, ELO_CACHE_TTL, JSON.stringify(ratings));
        return ratings;
        
    } catch (error) {
        console.error('Error getting cached ELO ratings:', error);
        const collection = getEloRatingsCollection();
        return await collection.find({}).sort({ elo: -1 }).toArray();
    }
}
