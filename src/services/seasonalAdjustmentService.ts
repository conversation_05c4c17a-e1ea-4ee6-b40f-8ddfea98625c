/**
 * Seasonal Adjustment Service
 * 
 * Applies seasonal adjustments to prediction parameters based on time of season
 * Accounts for early season unpredictability, mid-season stability, and late season pressure
 */

export type SeasonPeriod = 'early' | 'mid' | 'late';

export interface SeasonalAdjustments {
  homeAdvantageMultiplier: number;
  confidenceMultiplier: number;
  xiMultiplier: number;
  rhoAdjustment: number;
  description: string;
}

export interface SeasonContext {
  period: SeasonPeriod;
  monthOfSeason: number;
  isEuropeanSeason: boolean;
  adjustments: SeasonalAdjustments;
}

export class SeasonalAdjustmentService {
  
  /**
   * Seasonal adjustment configurations
   */
  private static readonly SEASONAL_ADJUSTMENTS: Record<SeasonPeriod, SeasonalAdjustments> = {
    early: {
      homeAdvantageMultiplier: 0.95, // Slightly lower home advantage
      confidenceMultiplier: 0.85,    // Lower confidence in predictions
      xiMultiplier: 1.1,              // Recent form slightly more important
      rhoAdjustment: 0.02,            // Slightly more low-scoring games
      description: 'Early season: Teams adapting, more unpredictable'
    },
    
    mid: {
      homeAdvantageMultiplier: 1.0,   // Normal home advantage
      confidenceMultiplier: 1.0,      // Normal confidence
      xiMultiplier: 1.0,              // Normal time decay
      rhoAdjustment: 0.0,             // Normal low-scoring frequency
      description: 'Mid season: Peak performance, most predictable'
    },
    
    late: {
      homeAdvantageMultiplier: 1.08,  // Higher home advantage (pressure)
      confidenceMultiplier: 0.92,     // Slightly lower confidence (fatigue/motivation)
      xiMultiplier: 1.15,             // Recent form more important (momentum)
      rhoAdjustment: -0.01,           // Slightly fewer low-scoring games (desperation)
      description: 'Late season: Pressure situations, fatigue effects'
    }
  };

  /**
   * European season months (August to May)
   */
  private static readonly EUROPEAN_SEASON_MONTHS = [8, 9, 10, 11, 12, 1, 2, 3, 4, 5];
  
  /**
   * Southern Hemisphere season months (March to November)
   */
  private static readonly SOUTHERN_SEASON_MONTHS = [3, 4, 5, 6, 7, 8, 9, 10, 11];

  /**
   * Get seasonal context for a given date and league
   */
  public static getSeasonalContext(date: Date, leagueId: number): SeasonContext {
    const isEuropeanSeason = this.isEuropeanSeasonLeague(leagueId);
    const monthOfSeason = this.getMonthOfSeason(date, isEuropeanSeason);
    const period = this.getSeasonPeriod(monthOfSeason, isEuropeanSeason);
    const adjustments = this.SEASONAL_ADJUSTMENTS[period];

    return {
      period,
      monthOfSeason,
      isEuropeanSeason,
      adjustments
    };
  }

  /**
   * Apply seasonal adjustments to home advantage
   */
  public static adjustHomeAdvantage(baseHomeAdvantage: number, seasonContext: SeasonContext): number {
    return baseHomeAdvantage * seasonContext.adjustments.homeAdvantageMultiplier;
  }

  /**
   * Apply seasonal adjustments to xi parameter
   */
  public static adjustXiParameter(baseXi: number, seasonContext: SeasonContext): number {
    return baseXi * seasonContext.adjustments.xiMultiplier;
  }

  /**
   * Apply seasonal adjustments to rho parameter
   */
  public static adjustRhoParameter(baseRho: number, seasonContext: SeasonContext): number {
    return baseRho + seasonContext.adjustments.rhoAdjustment;
  }

  /**
   * Apply seasonal adjustments to prediction confidence
   */
  public static adjustConfidence(baseConfidence: number, seasonContext: SeasonContext): number {
    return Math.max(50, Math.min(95, baseConfidence * seasonContext.adjustments.confidenceMultiplier));
  }

  /**
   * Determine if a league follows European season pattern
   */
  private static isEuropeanSeasonLeague(leagueId: number): boolean {
    // European leagues (August-May season)
    const europeanLeagues = [
      // Big 5 European
      39, 140, 78, 135, 61,
      // Other European leagues
      88, 94, 203, 144, 179, 218, 207, 197, 106, 119, 235, 286, 210, 333, 283, 271, 345, 332, 315,
      // European cups
      2, 3, 848, 45, 143, 81, 137, 66, 48,
      // European second tiers
      40, 79, 141, 136, 62, 89, 95, 204, 145, 219, 208, 107, 120, 236, 287, 211, 334, 284, 272, 346, 506,
      // Smaller European leagues
      310, 342, 419, 116, 172, 318, 329, 367, 327, 373, 365, 362, 261, 393, 394, 355, 371, 357
    ];

    return europeanLeagues.includes(leagueId);
  }

  /**
   * Get the month within the season (1-10 for European, 1-9 for others)
   */
  private static getMonthOfSeason(date: Date, isEuropeanSeason: boolean): number {
    const month = date.getMonth() + 1; // JavaScript months are 0-indexed
    
    if (isEuropeanSeason) {
      // European season: August (8) to May (5)
      if (month >= 8) {
        return month - 7; // August = 1, September = 2, etc.
      } else if (month <= 5) {
        return month + 5; // January = 6, February = 7, ..., May = 10
      } else {
        return 5; // June/July - treat as late season
      }
    } else {
      // Southern Hemisphere/Other: March (3) to November (11)
      if (month >= 3 && month <= 11) {
        return month - 2; // March = 1, April = 2, ..., November = 9
      } else {
        return 5; // December/January/February - treat as mid season
      }
    }
  }

  /**
   * Determine season period based on month of season
   */
  private static getSeasonPeriod(monthOfSeason: number, isEuropeanSeason: boolean): SeasonPeriod {
    if (isEuropeanSeason) {
      // European season (10 months)
      if (monthOfSeason <= 3) return 'early';    // Aug-Oct
      if (monthOfSeason <= 7) return 'mid';      // Nov-Feb
      return 'late';                             // Mar-May
    } else {
      // Other seasons (9 months)
      if (monthOfSeason <= 2) return 'early';    // Mar-Apr
      if (monthOfSeason <= 6) return 'mid';      // May-Aug
      return 'late';                             // Sep-Nov
    }
  }

  /**
   * Get seasonal adjustment summary for debugging
   */
  public static getAdjustmentSummary(date: Date, leagueId: number): {
    context: SeasonContext;
    adjustedValues: {
      homeAdvantage: { base: number; adjusted: number };
      xi: { base: number; adjusted: number };
      rho: { base: number; adjusted: number };
      confidence: { base: number; adjusted: number };
    };
  } {
    const context = this.getSeasonalContext(date, leagueId);
    
    // Example base values
    const baseHomeAdvantage = 1.3;
    const baseXi = 0.0025;
    const baseRho = -0.13;
    const baseConfidence = 75;

    return {
      context,
      adjustedValues: {
        homeAdvantage: {
          base: baseHomeAdvantage,
          adjusted: this.adjustHomeAdvantage(baseHomeAdvantage, context)
        },
        xi: {
          base: baseXi,
          adjusted: this.adjustXiParameter(baseXi, context)
        },
        rho: {
          base: baseRho,
          adjusted: this.adjustRhoParameter(baseRho, context)
        },
        confidence: {
          base: baseConfidence,
          adjusted: this.adjustConfidence(baseConfidence, context)
        }
      }
    };
  }

  /**
   * Validate seasonal adjustments across a year
   */
  public static validateSeasonalPattern(leagueId: number): {
    leagueId: number;
    isEuropeanSeason: boolean;
    monthlyAdjustments: Array<{
      month: number;
      monthName: string;
      seasonMonth: number;
      period: SeasonPeriod;
      adjustments: SeasonalAdjustments;
    }>;
  } {
    const isEuropeanSeason = this.isEuropeanSeasonLeague(leagueId);
    const monthlyAdjustments = [];

    for (let month = 1; month <= 12; month++) {
      const testDate = new Date(2024, month - 1, 15); // 15th of each month
      const context = this.getSeasonalContext(testDate, leagueId);
      
      monthlyAdjustments.push({
        month,
        monthName: testDate.toLocaleString('default', { month: 'long' }),
        seasonMonth: context.monthOfSeason,
        period: context.period,
        adjustments: context.adjustments
      });
    }

    return {
      leagueId,
      isEuropeanSeason,
      monthlyAdjustments
    };
  }

  /**
   * Check if seasonal adjustments should be applied for a league
   */
  public static shouldApplySeasonalAdjustments(leagueId: number): boolean {
    // Apply to most leagues except friendlies and some international competitions
    const excludedLeagues = [
      667, // Friendlies Clubs
      10,  // Friendlies International
      1,   // World Cup (tournament format)
      8,   // World Cup Women
      4,   // European Championship
      480, // Olympic Games Men
      524  // Olympic Games Women
    ];

    return !excludedLeagues.includes(leagueId);
  }

  /**
   * Get all seasonal periods and their characteristics
   */
  public static getSeasonalPeriods(): Record<SeasonPeriod, SeasonalAdjustments> {
    return this.SEASONAL_ADJUSTMENTS;
  }
}
