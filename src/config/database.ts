import { MongoClient, Db } from 'mongodb';
import config from './index';

let db: Db | null = null;

async function connectDB(): Promise<Db> {
  if (db) {
    return db;
  }

  if (!config.database.mongoUri) {
    console.error('MongoDB URI is not defined in the configuration.');
    throw new Error('MongoDB URI is not defined.');
  }

  try {
    const client = new MongoClient(config.database.mongoUri);
    await client.connect();
    console.log('MongoDB connected successfully.');

    // Extract database name from URI or use a default
    const dbName = config.database.mongoUri.split('/').pop()?.split('?')[0] || 'kickoffscore';
    db = client.db(dbName);

    // Optional: Add listeners for connection events
    client.on('close', () => {
      console.log('MongoDB connection closed.');
      db = null; // Reset db variable on close
    });
    client.on('error', (err) => {
      console.error('MongoDB connection error:', err);
      db = null; // Reset db variable on error
    });

    return db;
  } catch (error) {
    console.error('Could not connect to MongoDB:', error);
    process.exit(1); // Exit process with failure in case of connection error
  }
}

// Function to get the database instance
export function getDb(): Db {
  if (!db) {
    throw new Error('Database not initialized. Call connectDB first.');
  }
  return db;
}

export default connectDB;
