import Redis from 'ioredis';
import config from './index';

let redisClient: Redis | null = null;

function connectRedis(): Redis {
  if (redisClient) {
    return redisClient;
  }

  if (!config.cache.redisUri) {
    console.error('Redis URI is not defined in the configuration.');
    throw new Error('Redis URI is not defined.');
  }

  try {
    // ioredis automatically handles reconnections by default
    redisClient = new Redis(config.cache.redisUri, {
      // Optional: Add connection options if needed
      // maxRetriesPerRequest: 3, // Example option
      // enableReadyCheck: true, // Example option
    });

    redisClient.on('connect', () => {
      console.log('Redis connected successfully.');
    });

    redisClient.on('error', (err) => {
      console.error('Redis connection error:', err);
      // ioredis handles reconnection attempts, so we might not need to exit
      // Consider logging or alerting mechanisms here
    });

    redisClient.on('close', () => {
      console.log('Redis connection closed.');
      // Optionally set redisClient = null if you want to force re-creation on next call
    });

    redisClient.on('reconnecting', () => {
      console.log('Redis reconnecting...');
    });

    return redisClient;

  } catch (error) {
    console.error('Could not create Redis client:', error);
    process.exit(1); // Exit if initial client creation fails
  }
}

// Function to get the Redis client instance
export function getRedisClient(): Redis {
  if (!redisClient) {
    // Attempt to connect if not already connected (e.g., if called before server start finishes)
    // Or throw an error if strict initialization order is required
    return connectRedis();
    // throw new Error('Redis client not initialized. Call connectRedis first.');
  }
  return redisClient;
}

// Helper function to invalidate fixture cache entries
export async function invalidateFixtureCache(fixtureId: number): Promise<void> {
  if (!redisClient) {
    console.warn('Redis client not initialized. Cannot invalidate fixture cache.');
    return;
  }

  try {
    // Delete the fixture cache entry
    const cacheKey = `fixtures:id:${fixtureId}`;
    await redisClient.del(cacheKey);
    console.log(`Cache invalidated for fixture ${fixtureId}`);
  } catch (error) {
    console.error(`Error invalidating cache for fixture ${fixtureId}:`, error);
  }
}

// Helper function to invalidate multiple fixture cache entries
export async function invalidateFixtureCaches(fixtureIds: number[]): Promise<void> {
  if (!redisClient || fixtureIds.length === 0) {
    return;
  }

  try {
    // Create an array of cache keys to delete
    const cacheKeys = fixtureIds.map(id => `fixtures:id:${id}`);

    // Delete all cache keys in a single operation
    if (cacheKeys.length > 0) {
      await redisClient.del(...cacheKeys);
      console.log(`Cache invalidated for ${cacheKeys.length} fixtures`);
    }
  } catch (error) {
    console.error(`Error invalidating cache for multiple fixtures:`, error);
  }
}

export default connectRedis;
