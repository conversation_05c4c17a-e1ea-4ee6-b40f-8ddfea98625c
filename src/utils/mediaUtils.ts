import config from '../config';

/**
 * Utility functions for handling API-Sports media URLs
 * These functions help construct proper media URLs for flags, logos, and photos
 * using the official API-Sports media CDN
 */

/**
 * Get country flag URL from API-Sports media CDN
 * @param countryCode - 2-letter country code (e.g., 'GB', 'FR')
 * @returns Full URL to the country flag SVG
 */
export function getCountryFlagUrl(countryCode: string): string {
    if (!countryCode || countryCode.length < 2) {
        return '';
    }
    return `${config.apiFootball.mediaBaseUrl}/flags/${countryCode.toLowerCase()}.svg`;
}

/**
 * Get league logo URL from API-Sports media CDN
 * @param leagueId - League ID from API-Football
 * @returns Full URL to the league logo PNG
 */
export function getLeagueLogoUrl(leagueId: number): string {
    if (!leagueId || leagueId <= 0) {
        return '';
    }
    return `${config.apiFootball.mediaBaseUrl}/football/leagues/${leagueId}.png`;
}

/**
 * Get team logo URL from API-Sports media CDN
 * @param teamId - Team ID from API-Football
 * @returns Full URL to the team logo PNG
 */
export function getTeamLogoUrl(teamId: number): string {
    if (!teamId || teamId <= 0) {
        return '';
    }
    return `${config.apiFootball.mediaBaseUrl}/football/teams/${teamId}.png`;
}

/**
 * Get venue image URL from API-Sports media CDN
 * @param venueId - Venue ID from API-Football
 * @returns Full URL to the venue image PNG
 */
export function getVenueImageUrl(venueId: number): string {
    if (!venueId || venueId <= 0) {
        return '';
    }
    return `${config.apiFootball.mediaBaseUrl}/football/venues/${venueId}.png`;
}

/**
 * Get player photo URL from API-Sports media CDN
 * @param playerId - Player ID from API-Football
 * @returns Full URL to the player photo PNG
 */
export function getPlayerPhotoUrl(playerId: number): string {
    if (!playerId || playerId <= 0) {
        return '';
    }
    return `${config.apiFootball.mediaBaseUrl}/football/players/${playerId}.png`;
}

/**
 * Get coach photo URL from API-Sports media CDN
 * @param coachId - Coach ID from API-Football
 * @returns Full URL to the coach photo PNG
 */
export function getCoachPhotoUrl(coachId: number): string {
    if (!coachId || coachId <= 0) {
        return '';
    }
    return `${config.apiFootball.mediaBaseUrl}/football/coachs/${coachId}.png`;
}

/**
 * Validate if a URL is from the official API-Sports media CDN
 * @param url - URL to validate
 * @returns True if the URL is from the official media CDN
 */
export function isOfficialMediaUrl(url: string): boolean {
    if (!url) return false;
    return url.startsWith(config.apiFootball.mediaBaseUrl);
}

/**
 * Convert any media URL to use the official API-Sports CDN if possible
 * This is useful for migrating from other CDNs or ensuring consistency
 * @param url - Original media URL
 * @param mediaType - Type of media ('flag', 'league', 'team', 'venue', 'player', 'coach')
 * @param id - ID for the media (country code for flags, numeric ID for others)
 * @returns Official media URL or original URL if conversion not possible
 */
export function normalizeMediaUrl(url: string, mediaType: 'flag' | 'league' | 'team' | 'venue' | 'player' | 'coach', id: string | number): string {
    if (!url || isOfficialMediaUrl(url)) {
        return url; // Already using official CDN or empty
    }

    // Convert to official CDN based on media type
    switch (mediaType) {
        case 'flag':
            return typeof id === 'string' ? getCountryFlagUrl(id) : url;
        case 'league':
            return typeof id === 'number' ? getLeagueLogoUrl(id) : url;
        case 'team':
            return typeof id === 'number' ? getTeamLogoUrl(id) : url;
        case 'venue':
            return typeof id === 'number' ? getVenueImageUrl(id) : url;
        case 'player':
            return typeof id === 'number' ? getPlayerPhotoUrl(id) : url;
        case 'coach':
            return typeof id === 'number' ? getCoachPhotoUrl(id) : url;
        default:
            return url; // Return original if type not recognized
    }
}

/**
 * Get media URL with fallback options
 * @param primaryUrl - Primary media URL
 * @param mediaType - Type of media for fallback generation
 * @param id - ID for fallback URL generation
 * @returns Primary URL or fallback URL if primary is empty/invalid
 */
export function getMediaUrlWithFallback(primaryUrl: string | null, mediaType: 'flag' | 'league' | 'team' | 'venue' | 'player' | 'coach', id: string | number): string {
    if (primaryUrl && primaryUrl.trim() !== '') {
        return primaryUrl;
    }

    // Generate fallback URL using official CDN
    return normalizeMediaUrl('', mediaType, id);
}
