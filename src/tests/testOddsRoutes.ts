import express from 'express';
import { Router } from 'express';
import * as fs from 'fs';
import * as path from 'path';

// Create a simple Express app
const app = express();
const port = 3001;

// Function to check if a file exists
function fileExists(filePath: string): boolean {
  try {
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
}

// Function to check if a module has a default export
async function checkModuleExports(modulePath: string): Promise<void> {
  try {
    const fullPath = path.resolve(__dirname, '..', modulePath + '.ts');
    console.log(`Checking module at path: ${fullPath}`);

    if (!fileExists(fullPath)) {
      console.error(`File does not exist: ${fullPath}`);
      return;
    }

    // Try to import the module
    try {
      const module = require('../' + modulePath);
      console.log('Module imported successfully');

      // Check if it has a default export
      if (module.default) {
        console.log('Module has a default export');

        // Check if the default export is a Router
        if (module.default instanceof Router) {
          console.log('Default export is a Router');
        } else {
          console.log(`Default export is not a Router, it's a: ${typeof module.default}`);
        }
      } else {
        console.log('Module does not have a default export');

        // Check if the module itself is a Router
        if (module instanceof Router) {
          console.log('Module itself is a Router');
        } else {
          console.log(`Module is not a Router, it's a: ${typeof module}`);
        }
      }

      // Log all exports
      console.log('All exports:', Object.keys(module));
    } catch (error) {
      console.error('Error importing module:', error);
    }
  } catch (error) {
    console.error('Error checking module exports:', error);
  }
}

// Main function
async function main() {
  try {
    // Check oddsRoutes module
    await checkModuleExports('./routes/oddsRoutes');

    // Start the server
    app.listen(port, () => {
      console.log(`Test server listening at http://localhost:${port}`);
    });
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the main function
main();
