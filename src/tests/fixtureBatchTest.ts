import axios from 'axios';
import dotenv from 'dotenv';
import { writeFileSync } from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Base URL for the API
const BASE_URL = `http://localhost:${process.env.PORT || 3000}/api`;

// Fixture IDs to test (up to 20)
const FIXTURE_IDS = [1361163]; // Using the valid fixture ID we found

// Main test function
async function testFixtureBatch() {
  try {
    const idsString = FIXTURE_IDS.join('-');
    console.log(`Testing fixture batch for IDs: ${idsString}`);

    // Make the request
    const response = await axios.get(`${BASE_URL}/fixtures`, {
      params: { ids: idsString }
    });

    // Check if we got a response
    if (!response.data) {
      console.error('No fixture data returned');
      return;
    }

    // Handle both array and single object responses
    const fixtures = Array.isArray(response.data) ? response.data : [response.data];

    // Save the fixture data for inspection
    const filePath = path.join(__dirname, `../../fixtures-batch.json`);
    writeFileSync(filePath, JSON.stringify(fixtures, null, 2));
    console.log(`Fixture batch data saved to ${filePath}`);

    console.log('\nFixture Batch Test Summary:');
    console.log(`Requested ${FIXTURE_IDS.length} fixtures, received ${fixtures.length} fixtures`);

    // Check which IDs were returned
    const returnedIds = fixtures.map(f => f.fixture.id);
    console.log('\nReturned Fixture IDs:');
    returnedIds.forEach(id => console.log(`- ${id}`));

    // Check for missing IDs
    const missingIds = FIXTURE_IDS.filter(id => !returnedIds.includes(id));
    if (missingIds.length > 0) {
      console.log('\nMissing Fixture IDs:');
      missingIds.forEach(id => console.log(`- ${id}`));
    } else {
      console.log('\nAll requested fixture IDs were returned');
    }

  } catch (error: any) {
    console.error('Error testing fixture batch:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
console.log('Starting fixture batch test...');
testFixtureBatch()
  .then(() => console.log('Test completed.'))
  .catch(error => console.error('Error running test:', error));
