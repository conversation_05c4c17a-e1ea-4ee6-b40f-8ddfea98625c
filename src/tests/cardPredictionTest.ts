/**
 * Card Prediction Test
 * 
 * Tests the card prediction system implementation
 * Verifies data extraction, team discipline calculation, and prediction generation
 */

import { CardDataService } from '../services/cardDataService';
import { TeamDisciplineService } from '../services/teamDisciplineService';
import { CardPredictionService } from '../services/cardPredictionService';
import connectDB from '../config/database';

async function testCardPredictionSystem() {
  console.log('🃏 Starting Card Prediction System Test...\n');

  try {
    // Test 1: Check leagues with card support
    console.log('📊 Test 1: Checking leagues with card statistics support...');
    const supportedLeagues = await CardDataService.getLeaguesWithCardSupport();
    console.log(`✅ Found ${supportedLeagues.length} leagues with card support:`);
    console.log(supportedLeagues.slice(0, 10)); // Show first 10
    console.log('');

    if (supportedLeagues.length === 0) {
      console.log('❌ No leagues with card support found. Cannot proceed with tests.');
      return;
    }

    // Test 2: Extract card data for a supported league
    console.log('📈 Test 2: Extracting card data for a supported league...');
    const testLeagueId = 253; // Major League Soccer - known to have card data
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - 6); // Last 6 months

    const cardData = await CardDataService.getLeagueCardData(
      testLeagueId,
      cutoffDate
    );

    console.log(`✅ Extracted card data for league ${testLeagueId}:`);
    console.log(`   - Total matches: ${cardData.length}`);
    
    if (cardData.length > 0) {
      const totalCards = cardData.map(match => match.totalCards);
      const totalYellow = cardData.map(match => match.totalYellowCards);
      const totalRed = cardData.map(match => match.totalRedCards);
      
      const avgCards = totalCards.reduce((sum, cards) => sum + cards, 0) / totalCards.length;
      const avgYellow = totalYellow.reduce((sum, cards) => sum + cards, 0) / totalYellow.length;
      const avgRed = totalRed.reduce((sum, cards) => sum + cards, 0) / totalRed.length;
      const minCards = Math.min(...totalCards);
      const maxCards = Math.max(...totalCards);
      
      console.log(`   - Average cards per match: ${avgCards.toFixed(1)} (${avgYellow.toFixed(1)} yellow, ${avgRed.toFixed(1)} red)`);
      console.log(`   - Card range: ${minCards} - ${maxCards}`);
      console.log(`   - Sample matches:`);
      
      cardData.slice(0, 3).forEach(match => {
        console.log(`     * Fixture ${match.fixtureId}: ${match.homeYellowCards}Y+${match.homeRedCards}R vs ${match.awayYellowCards}Y+${match.awayRedCards}R (Total: ${match.totalCards})`);
      });
    }
    console.log('');

    if (cardData.length < 5) {
      console.log('⚠️  Insufficient card data for comprehensive testing. Need at least 5 matches.');
      return;
    }

    // Test 3: Calculate team discipline
    console.log('💪 Test 3: Calculating team card discipline...');
    const teamDiscipline = await TeamDisciplineService.calculateTeamCardDiscipline(
      testLeagueId,
      cutoffDate
    );

    console.log(`✅ Calculated card discipline for ${teamDiscipline.size} teams:`);
    
    if (teamDiscipline.size > 0) {
      const disciplineArray = Array.from(teamDiscipline.values());
      const avgAttack = disciplineArray.reduce((sum, team) => sum + team.cardAttack, 0) / disciplineArray.length;
      const avgDefense = disciplineArray.reduce((sum, team) => sum + team.cardDefense, 0) / disciplineArray.length;
      const avgHomeCardPenalty = disciplineArray.reduce((sum, team) => sum + team.homeCardPenalty, 0) / disciplineArray.length;
      const avgAwayCardPenalty = disciplineArray.reduce((sum, team) => sum + team.awayCardPenalty, 0) / disciplineArray.length;
      
      console.log(`   - Average card attack: ${avgAttack.toFixed(2)}`);
      console.log(`   - Average card defense: ${avgDefense.toFixed(2)}`);
      console.log(`   - Average home card penalty: ${avgHomeCardPenalty.toFixed(2)}`);
      console.log(`   - Average away card penalty: ${avgAwayCardPenalty.toFixed(2)}`);
      
      console.log(`   - Top 3 most disciplined teams (lowest card attack):`);
      disciplineArray
        .sort((a, b) => a.cardAttack - b.cardAttack)
        .slice(0, 3)
        .forEach((team, index) => {
          console.log(`     ${index + 1}. Team ${team.teamId}: ${team.cardAttack.toFixed(2)} cards/game (${team.matchCount} matches)`);
        });
        
      console.log(`   - Top 3 least disciplined teams (highest card attack):`);
      disciplineArray
        .sort((a, b) => b.cardAttack - a.cardAttack)
        .slice(0, 3)
        .forEach((team, index) => {
          console.log(`     ${index + 1}. Team ${team.teamId}: ${team.cardAttack.toFixed(2)} cards/game (${team.matchCount} matches)`);
        });
    }
    console.log('');

    // Test 4: Generate card prediction
    console.log('🎯 Test 4: Generating card prediction...');
    
    if (teamDiscipline.size >= 2) {
      const teamIds = Array.from(teamDiscipline.keys());
      const homeTeamId = teamIds[0];
      const awayTeamId = teamIds[1];
      
      console.log(`   Testing prediction for Team ${homeTeamId} (home) vs Team ${awayTeamId} (away)`);
      
      const prediction = await CardPredictionService.generateCardPrediction(
        homeTeamId,
        awayTeamId,
        testLeagueId,
        new Date()
      );

      if (prediction) {
        console.log('✅ Card prediction generated successfully:');
        console.log(`   - Expected total cards: ${prediction.expectedTotalCards.toFixed(1)}`);
        console.log(`   - Expected yellow cards: ${prediction.expectedYellowCards.toFixed(1)}`);
        console.log(`   - Expected red cards: ${prediction.expectedRedCards.toFixed(1)}`);
        console.log(`   - Home team expected: ${prediction.homeExpectedCards.toFixed(1)}`);
        console.log(`   - Away team expected: ${prediction.awayExpectedCards.toFixed(1)}`);
        console.log(`   - Confidence: ${prediction.confidence}%`);
        console.log(`   - Algorithm: ${prediction.algorithm}`);
        
        console.log('   - Market predictions:');
        Object.entries(prediction.markets).forEach(([market, data]) => {
          console.log(`     * ${market}: Over ${(data.overProbability * 100).toFixed(1)}% | Under ${(data.underProbability * 100).toFixed(1)}% | Confidence: ${data.confidence}`);
        });
        
        console.log(`   - Card distribution (top 5 probabilities):`);
        prediction.distribution
          .sort((a, b) => b.probability - a.probability)
          .slice(0, 5)
          .forEach(item => {
            console.log(`     * ${item.cards} cards: ${(item.probability * 100).toFixed(1)}%`);
          });
          
        // Validate prediction
        const isValid = CardPredictionService.validateCardPrediction(prediction);
        console.log(`   - Prediction validation: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
        
      } else {
        console.log('❌ Failed to generate card prediction');
      }
    } else {
      console.log('⚠️  Insufficient teams with card data for prediction testing');
    }
    console.log('');

    // Test 5: Test league without card support
    console.log('🚫 Test 5: Testing league without card support...');
    const db = await connectDB();
    
    // Find a league that doesn't support card statistics
    const allLeagues = await db.collection('leagues').find({
      'seasons.coverage.fixtures.statistics_fixtures': { $ne: true }
    }).limit(1).toArray();
    
    if (allLeagues.length > 0) {
      const unsupportedLeagueId = allLeagues[0].league.id;
      console.log(`   Testing unsupported league: ${unsupportedLeagueId}`);
      
      const hasSupport = await CardDataService.checkLeagueCardSupport(unsupportedLeagueId);
      console.log(`   - Card support check: ${hasSupport ? '❌ Unexpected support' : '✅ Correctly unsupported'}`);
      
      const prediction = await CardPredictionService.generateCardPrediction(
        1, 2, unsupportedLeagueId, new Date()
      );
      console.log(`   - Prediction result: ${prediction ? '❌ Unexpected prediction' : '✅ Correctly null'}`);
    } else {
      console.log('   - All leagues appear to support card statistics');
    }
    console.log('');

    // Test 6: Performance test
    console.log('⚡ Test 6: Performance test...');
    const startTime = Date.now();
    
    if (teamDiscipline.size >= 2) {
      const teamIds = Array.from(teamDiscipline.keys());
      const predictions = [];
      
      // Generate 5 predictions
      for (let i = 0; i < Math.min(5, teamIds.length - 1); i++) {
        const prediction = await CardPredictionService.generateCardPrediction(
          teamIds[i],
          teamIds[i + 1],
          testLeagueId,
          new Date()
        );
        if (prediction) predictions.push(prediction);
      }
      
      const endTime = Date.now();
      const avgTime = (endTime - startTime) / predictions.length;
      
      console.log(`✅ Generated ${predictions.length} predictions in ${endTime - startTime}ms`);
      console.log(`   - Average time per prediction: ${avgTime.toFixed(1)}ms`);
      console.log(`   - All predictions valid: ${predictions.every(p => CardPredictionService.validateCardPrediction(p))}`);
    }
    console.log('');

    console.log('🎉 Card Prediction System Test Completed Successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log(`   - Leagues with card support: ${supportedLeagues.length}`);
    console.log(`   - Card data extracted: ${cardData.length} matches`);
    console.log(`   - Team discipline calculated: ${teamDiscipline.size} teams`);
    console.log(`   - Prediction system: ✅ Working`);
    console.log(`   - Validation system: ✅ Working`);

  } catch (error) {
    console.error('❌ Error during card prediction test:', error);
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace available');
  }
}

// Run the test
if (require.main === module) {
  testCardPredictionSystem()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

export { testCardPredictionSystem };
