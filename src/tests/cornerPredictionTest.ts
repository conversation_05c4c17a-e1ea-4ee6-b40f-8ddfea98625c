/**
 * Corner Prediction Test
 * 
 * Tests the corner prediction system implementation
 * Verifies data extraction, team strength calculation, and prediction generation
 */

import { CornerDataService } from '../services/cornerDataService';
import { TeamCornerStrengthService } from '../services/teamCornerStrengthService';
import { CornerPredictionService } from '../services/cornerPredictionService';
import connectDB from '../config/database';

async function testCornerPredictionSystem() {
  console.log('🏈 Starting Corner Prediction System Test...\n');

  try {
    // Test 1: Check leagues with corner support
    console.log('📊 Test 1: Checking leagues with corner statistics support...');
    const supportedLeagues = await CornerDataService.getLeaguesWithCornerSupport();
    console.log(`✅ Found ${supportedLeagues.length} leagues with corner support:`);
    console.log(supportedLeagues.slice(0, 10)); // Show first 10
    console.log('');

    if (supportedLeagues.length === 0) {
      console.log('❌ No leagues with corner support found. Cannot proceed with tests.');
      return;
    }

    // Test 2: Extract corner data for a supported league
    console.log('📈 Test 2: Extracting corner data for a supported league...');
    const testLeagueId = 253; // Major League Soccer - known to have corner data
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - 6); // Last 6 months

    const cornerData = await CornerDataService.getLeagueCornerData(
      testLeagueId,
      cutoffDate
    );

    console.log(`✅ Extracted corner data for league ${testLeagueId}:`);
    console.log(`   - Total matches: ${cornerData.length}`);
    
    if (cornerData.length > 0) {
      const totalCorners = cornerData.map(match => match.totalCorners);
      const avgCorners = totalCorners.reduce((sum, corners) => sum + corners, 0) / totalCorners.length;
      const minCorners = Math.min(...totalCorners);
      const maxCorners = Math.max(...totalCorners);
      
      console.log(`   - Average corners per match: ${avgCorners.toFixed(1)}`);
      console.log(`   - Corner range: ${minCorners} - ${maxCorners}`);
      console.log(`   - Sample matches:`);
      
      cornerData.slice(0, 3).forEach(match => {
        console.log(`     * Fixture ${match.fixtureId}: ${match.homeCorners} - ${match.awayCorners} (Total: ${match.totalCorners})`);
      });
    }
    console.log('');

    if (cornerData.length < 5) {
      console.log('⚠️  Insufficient corner data for comprehensive testing. Need at least 5 matches.');
      return;
    }

    // Test 3: Calculate team corner strengths
    console.log('💪 Test 3: Calculating team corner strengths...');
    const teamStrengths = await TeamCornerStrengthService.calculateTeamCornerStrengths(
      testLeagueId,
      cutoffDate
    );

    console.log(`✅ Calculated corner strengths for ${teamStrengths.size} teams:`);
    
    if (teamStrengths.size > 0) {
      const strengthArray = Array.from(teamStrengths.values());
      const avgAttack = strengthArray.reduce((sum, team) => sum + team.cornerAttack, 0) / strengthArray.length;
      const avgDefense = strengthArray.reduce((sum, team) => sum + team.cornerDefense, 0) / strengthArray.length;
      const avgHomeBonus = strengthArray.reduce((sum, team) => sum + team.homeCornerBonus, 0) / strengthArray.length;
      
      console.log(`   - Average corner attack: ${avgAttack.toFixed(2)}`);
      console.log(`   - Average corner defense: ${avgDefense.toFixed(2)}`);
      console.log(`   - Average home bonus: ${avgHomeBonus.toFixed(2)}`);
      
      console.log(`   - Top 3 corner attacking teams:`);
      strengthArray
        .sort((a, b) => b.cornerAttack - a.cornerAttack)
        .slice(0, 3)
        .forEach((team, index) => {
          console.log(`     ${index + 1}. Team ${team.teamId}: ${team.cornerAttack.toFixed(2)} (${team.matchCount} matches)`);
        });
    }
    console.log('');

    // Test 4: Generate corner prediction
    console.log('🎯 Test 4: Generating corner prediction...');
    
    if (teamStrengths.size >= 2) {
      const teamIds = Array.from(teamStrengths.keys());
      const homeTeamId = teamIds[0];
      const awayTeamId = teamIds[1];
      
      console.log(`   Testing prediction for Team ${homeTeamId} (home) vs Team ${awayTeamId} (away)`);
      
      const prediction = await CornerPredictionService.generateCornerPrediction(
        homeTeamId,
        awayTeamId,
        testLeagueId,
        new Date()
      );

      if (prediction) {
        console.log('✅ Corner prediction generated successfully:');
        console.log(`   - Expected total corners: ${prediction.expectedTotal.toFixed(1)}`);
        console.log(`   - Home team expected: ${prediction.homeExpected.toFixed(1)}`);
        console.log(`   - Away team expected: ${prediction.awayExpected.toFixed(1)}`);
        console.log(`   - Confidence: ${prediction.confidence}%`);
        console.log(`   - Algorithm: ${prediction.algorithm}`);
        
        console.log('   - Market predictions:');
        Object.entries(prediction.markets).forEach(([market, data]) => {
          console.log(`     * ${market}: Over ${(data.overProbability * 100).toFixed(1)}% | Under ${(data.underProbability * 100).toFixed(1)}% | Confidence: ${data.confidence}`);
        });
        
        console.log(`   - Corner distribution (top 5 probabilities):`);
        prediction.distribution
          .sort((a, b) => b.probability - a.probability)
          .slice(0, 5)
          .forEach(item => {
            console.log(`     * ${item.corners} corners: ${(item.probability * 100).toFixed(1)}%`);
          });
          
        // Validate prediction
        const isValid = CornerPredictionService.validateCornerPrediction(prediction);
        console.log(`   - Prediction validation: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
        
      } else {
        console.log('❌ Failed to generate corner prediction');
      }
    } else {
      console.log('⚠️  Insufficient teams with corner data for prediction testing');
    }
    console.log('');

    // Test 5: Test league without corner support
    console.log('🚫 Test 5: Testing league without corner support...');
    const db = await connectDB();
    
    // Find a league that doesn't support corner statistics
    const allLeagues = await db.collection('leagues').find({
      'seasons.coverage.fixtures.statistics_fixtures': { $ne: true }
    }).limit(1).toArray();
    
    if (allLeagues.length > 0) {
      const unsupportedLeagueId = allLeagues[0].league.id;
      console.log(`   Testing unsupported league: ${unsupportedLeagueId}`);
      
      const hasSupport = await CornerDataService.checkLeagueCornerSupport(unsupportedLeagueId);
      console.log(`   - Corner support check: ${hasSupport ? '❌ Unexpected support' : '✅ Correctly unsupported'}`);
      
      const prediction = await CornerPredictionService.generateCornerPrediction(
        1, 2, unsupportedLeagueId, new Date()
      );
      console.log(`   - Prediction result: ${prediction ? '❌ Unexpected prediction' : '✅ Correctly null'}`);
    } else {
      console.log('   - All leagues appear to support corner statistics');
    }
    console.log('');

    // Test 6: Performance test
    console.log('⚡ Test 6: Performance test...');
    const startTime = Date.now();
    
    if (teamStrengths.size >= 2) {
      const teamIds = Array.from(teamStrengths.keys());
      const predictions = [];
      
      // Generate 5 predictions
      for (let i = 0; i < Math.min(5, teamIds.length - 1); i++) {
        const prediction = await CornerPredictionService.generateCornerPrediction(
          teamIds[i],
          teamIds[i + 1],
          testLeagueId,
          new Date()
        );
        if (prediction) predictions.push(prediction);
      }
      
      const endTime = Date.now();
      const avgTime = (endTime - startTime) / predictions.length;
      
      console.log(`✅ Generated ${predictions.length} predictions in ${endTime - startTime}ms`);
      console.log(`   - Average time per prediction: ${avgTime.toFixed(1)}ms`);
      console.log(`   - All predictions valid: ${predictions.every(p => CornerPredictionService.validateCornerPrediction(p))}`);
    }
    console.log('');

    console.log('🎉 Corner Prediction System Test Completed Successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log(`   - Leagues with corner support: ${supportedLeagues.length}`);
    console.log(`   - Corner data extracted: ${cornerData.length} matches`);
    console.log(`   - Team strengths calculated: ${teamStrengths.size} teams`);
    console.log(`   - Prediction system: ✅ Working`);
    console.log(`   - Validation system: ✅ Working`);

  } catch (error) {
    console.error('❌ Error during corner prediction test:', error);
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace available');
  }
}

// Run the test
if (require.main === module) {
  testCornerPredictionSystem()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

export { testCornerPredictionSystem };
