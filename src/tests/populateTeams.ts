import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { fetchTeams } from '../services/apiFootball';
import { getTeamsCollection, Team } from '../models/Team';
import { getLeaguesCollection } from '../models/League';

// Load environment variables
dotenv.config();

// Main function
async function populateTeams() {
  try {
    console.log('Connecting to database...');
    await connectDB();
    connectRedis();

    // Get active leagues
    const leaguesCollection = getLeaguesCollection();
    const activeLeagues = await leaguesCollection.find(
      { "seasons.current": true },
      { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
    ).toArray();

    if (!activeLeagues || activeLeagues.length === 0) {
      console.log('No active leagues found.');
      process.exit(0);
    }

    console.log(`Found ${activeLeagues.length} active leagues.`);

    // Get teams for each active league
    const teamsCollection = getTeamsCollection();
    const now = new Date();
    let totalTeams = 0;

    for (const league of activeLeagues) {
      const currentSeason = league.seasons.find(s => s.current);
      if (!currentSeason) continue;

      const leagueId = league._id;
      const seasonYear = currentSeason.year;

      console.log(`Fetching teams for League ID: ${leagueId}, Season: ${seasonYear}`);

      try {
        const teams = await fetchTeams({ league: leagueId, season: seasonYear });

        if (!teams || teams.length === 0) {
          console.log(`No teams found for League ${leagueId}, Season ${seasonYear}.`);
          continue;
        }

        console.log(`Found ${teams.length} teams for League ${leagueId}, Season ${seasonYear}.`);

        // Insert teams into database
        for (const team of teams) {
          try {
            // Check if team already exists by apiId
            const existingTeam = await teamsCollection.findOne({ apiId: team.team.id });

            if (existingTeam) {
              // Update existing team
              await teamsCollection.updateOne(
                { apiId: team.team.id },
                {
                  $set: {
                    team: team.team,
                    venue: team.venue,
                    lastUpdated: now
                  }
                }
              );
            } else {
              // Insert new team
              const teamDoc: Team = {
                _id: team.team.id,
                apiId: team.team.id, // Add apiId field to match existing database index
                team: team.team,
                venue: team.venue,
                lastUpdated: now
              };

              await teamsCollection.insertOne(teamDoc);
            }
          } catch (error) {
            console.error(`Error inserting/updating team ${team.team.id} (${team.team.name}):`, error);
          }
        }

        totalTeams += teams.length;
        console.log(`Inserted/updated ${teams.length} teams for League ${leagueId}, Season ${seasonYear}.`);

        // Add a delay to respect API rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`Error fetching teams for League ${leagueId}, Season ${seasonYear}:`, error);
      }
    }

    console.log(`Total teams inserted/updated: ${totalTeams}`);
    process.exit(0);

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the function
populateTeams();
