import axios from 'axios';
import dotenv from 'dotenv';
import { writeFileSync } from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Base URL for the API
const BASE_URL = `http://localhost:${process.env.PORT || 3000}/api`;

// Helper function to make API requests
async function makeRequest(endpoint: string, params: Record<string, any> = {}) {
  try {
    console.log(`Testing endpoint: ${endpoint} with params:`, params);
    const url = `${BASE_URL}${endpoint}`;
    const response = await axios.get(url, { params });
    return {
      success: true,
      status: response.status,
      data: response.data,
      endpoint,
      params
    };
  } catch (error: any) {
    return {
      success: false,
      status: error.response?.status || 500,
      message: error.response?.data?.message || error.message,
      endpoint,
      params
    };
  }
}

// Helper function to save test results
function saveResults(results: any[]) {
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const filePath = path.join(__dirname, `../../test-results-${timestamp}.json`);
  writeFileSync(filePath, JSON.stringify(results, null, 2));
  console.log(`Test results saved to ${filePath}`);
}

// Main test function
async function runTests() {
  const results: any[] = [];
  
  // Test fixtures endpoint with ID
  results.push(await makeRequest('/fixtures', { id: 1273685 }));
  
  // Test fixtures endpoint with multiple IDs
  results.push(await makeRequest('/fixtures', { ids: '1273685,1273686,1273687' }));
  
  // Test fixtures endpoint with date
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  results.push(await makeRequest('/fixtures', { date: today }));
  
  // Test fixtures/rounds endpoint
  results.push(await makeRequest('/fixtures/rounds', { league: 39, season: 2023 }));
  
  // Test players/profiles endpoint
  results.push(await makeRequest('/players/profiles', { player: 276 })); // Example: Neymar
  
  // Test players/statistics endpoint
  results.push(await makeRequest('/players/statistics', { id: 276, season: 2023 }));
  
  // Test players/squads endpoint
  results.push(await makeRequest('/players/squads', { team: 85 })); // Example: PSG
  
  // Test players/topscorers endpoint
  results.push(await makeRequest('/players/topscorers', { league: 39, season: 2023 }));
  
  // Test coaches endpoint
  results.push(await makeRequest('/coaches', { team: 85 })); // Example: PSG
  
  // Test transfers endpoint
  results.push(await makeRequest('/transfers', { player: 276 })); // Example: Neymar
  
  // Test sidelined endpoint
  results.push(await makeRequest('/sidelined', { team: 85 })); // Example: PSG
  
  // Save results
  saveResults(results);
  
  // Print summary
  console.log('\nTest Summary:');
  results.forEach(result => {
    console.log(`${result.endpoint} (${JSON.stringify(result.params)}): ${result.success ? 'SUCCESS' : 'FAILED'} - Status: ${result.status}`);
  });
}

// Run the tests
console.log('Starting endpoint tests...');
runTests()
  .then(() => console.log('Tests completed.'))
  .catch(error => console.error('Error running tests:', error));
