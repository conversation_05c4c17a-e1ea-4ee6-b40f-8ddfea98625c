import dotenv from 'dotenv';
import connectDB from '../config/database';
import { getCoachesCollection } from '../models/Coach';

// Load environment variables
dotenv.config();

// Main function
async function checkCoaches() {
  try {
    console.log('Connecting to database...');
    await connectDB();
    
    const coachesCollection = getCoachesCollection();
    
    // Count coaches
    const count = await coachesCollection.countDocuments();
    console.log(`Total coaches in database: ${count}`);
    
    // Get a sample coach
    if (count > 0) {
      const coach = await coachesCollection.findOne({});
      console.log('Sample coach:');
      console.log(JSON.stringify(coach, null, 2));
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the function
checkCoaches();
