import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateStandings } from '../jobs/standingJobs';

// Load environment variables
dotenv.config();

// Main function
async function runStandingsJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateStandings job...');
        await fetchAndUpdateStandings();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runStandingsJob();
