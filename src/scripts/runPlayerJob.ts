import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdatePlayerProfiles, fetchAndUpdatePlayerStatistics, fetchAndUpdatePlayerSquads, fetchAndUpdateTopPlayers } from '../jobs/playerJobs';

// Load environment variables
dotenv.config();

// Main function
async function runPlayerJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdatePlayerProfiles job...');
        await fetchAndUpdatePlayerProfiles();
        
        console.log('Running fetchAndUpdatePlayerStatistics job...');
        await fetchAndUpdatePlayerStatistics();
        
        console.log('Running fetchAndUpdatePlayerSquads job...');
        await fetchAndUpdatePlayerSquads();
        
        console.log('Running fetchAndUpdateTopPlayers job...');
        await fetchAndUpdateTopPlayers();
        
        console.log('All player jobs completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runPlayerJob();
