import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateTopPlayers } from '../jobs/playerJobs';

// Load environment variables
dotenv.config();

// Main function
async function runTopPlayersJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateTopPlayers job...');
        await fetchAndUpdateTopPlayers();
        
        console.log('Top players job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runTopPlayersJob();
