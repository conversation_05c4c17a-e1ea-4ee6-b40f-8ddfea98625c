import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateTeamsForActiveLeagues } from '../jobs/teamJobs';

// Load environment variables
dotenv.config();

// Main function
async function runTeamJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateTeamsForActiveLeagues job...');
        await fetchAndUpdateTeamsForActiveLeagues();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runTeamJob();
