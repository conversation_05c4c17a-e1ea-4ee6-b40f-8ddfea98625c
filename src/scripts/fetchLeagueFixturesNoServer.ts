import dotenv from 'dotenv';
import { MongoClient } from 'mongodb';
import { fetchFixtures, fetchLeagues } from '../services/apiFootball';
import { Fixture } from '../models/Fixture';
import { AnyBulkWriteOperation } from 'mongodb';
import { filterByTargetedLeagues } from '../config/targetedLeagues';

// Load environment variables
dotenv.config();

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Function to get the current season for a league
async function getCurrentSeasonForLeague(leagueId: number): Promise<number> {
    try {
        // Fetch league information from the API
        const leaguesData = await fetchLeagues({ id: leagueId });

        if (!leaguesData || leaguesData.length === 0) {
            console.log(`No league data found for league ID ${leagueId}. Using default season 2024.`);
            return 2024; // Default to 2024 if no data is found
        }

        const league = leaguesData[0];

        // Find the current season
        const currentSeason = league.seasons.find(season => season.current);

        if (currentSeason) {
            console.log(`Found current season ${currentSeason.year} for league ID ${leagueId}`);
            return currentSeason.year;
        }

        // If no current season is found, use the most recent season
        const sortedSeasons = [...league.seasons].sort((a, b) => b.year - a.year);
        if (sortedSeasons.length > 0) {
            console.log(`No current season found for league ID ${leagueId}. Using most recent season ${sortedSeasons[0].year}.`);
            return sortedSeasons[0].year;
        }

        // If no seasons are found, default to 2024
        console.log(`No seasons found for league ID ${leagueId}. Using default season 2024.`);
        return 2024;
    } catch (error) {
        console.error(`Error fetching current season for league ID ${leagueId}:`, error);
        console.log(`Using default season 2024 for league ID ${leagueId}.`);
        return 2024; // Default to 2024 in case of error
    }
}

// MongoDB connection
let client: MongoClient | null = null;
let db: any = null;

// Connect to MongoDB directly without starting the server
async function connectToMongoDB() {
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/kickoffscore';
    client = new MongoClient(mongoUri);
    await client.connect();
    console.log('MongoDB connected successfully.');

    // Extract database name from URI or use a default
    const dbName = mongoUri.split('/').pop()?.split('?')[0] || 'kickoffscore';
    db = client.db(dbName);

    return db;
}

// Get fixtures collection
function getFixturesCollection() {
    if (!db) {
        throw new Error('Database not initialized. Call connectToMongoDB first.');
    }
    return db.collection('fixtures');
}

// Helper function to create bulk operations
function createFixtureBulkOps(fixturesFromApi: any[], now: Date): AnyBulkWriteOperation<Fixture>[] {
    const bulkOps: AnyBulkWriteOperation<Fixture>[] = [];

    for (const fixtureApi of fixturesFromApi) {
        // Use fixture ID as the unique identifier (_id) for upsert
        const filter = { _id: fixtureApi.fixture.id };

        // Extract the date from the fixture timestamp for easier querying
        const fixtureDate = new Date(fixtureApi.fixture.timestamp * 1000);
        // Reset hours, minutes, seconds, and milliseconds to get just the date
        fixtureDate.setHours(0, 0, 0, 0);

        // Create an update document with only the fields that are present in the API response
        // This ensures we don't overwrite existing detailed data with undefined values
        const baseUpdateDoc = {
            apiId: fixtureApi.fixture.id,
            fixture: fixtureApi.fixture,
            league: fixtureApi.league,
            teams: fixtureApi.teams,
            goals: fixtureApi.goals,
            score: fixtureApi.score,
            date: fixtureDate, // Add the extracted date field for easier querying
            lastUpdated: now,
        };

        // Only include detailed fields if they are present in the API response
        // This prevents overwriting existing data with undefined values
        const updateDoc: any = { ...baseUpdateDoc };

        if (fixtureApi.events) updateDoc.events = fixtureApi.events;
        if (fixtureApi.lineups) updateDoc.lineups = fixtureApi.lineups;
        if (fixtureApi.statistics) updateDoc.statistics = fixtureApi.statistics;
        if (fixtureApi.players) updateDoc.players = fixtureApi.players;

        bulkOps.push({
            updateOne: {
                filter: filter,
                update: { $set: updateDoc },
                upsert: true, // Insert if not found, update if found
            },
        });
    }

    return bulkOps;
}

// Fetch detailed fixture information by ID or batch of IDs
async function fetchAndUpdateFixturesById(fixtureIds: number[] | number) {
    // Convert single ID to array for consistent handling
    const ids = Array.isArray(fixtureIds) ? fixtureIds : [fixtureIds];

    // API has a limit of 20 IDs per request
    const MAX_BATCH_SIZE = 20;

    console.log(`Starting fetchAndUpdateFixturesById job for ${ids.length} fixture(s)...`);
    try {
        const collection = getFixturesCollection();
        const now = new Date();
        let totalUpserted = 0;
        let totalModified = 0;

        // Process in batches of MAX_BATCH_SIZE
        for (let i = 0; i < ids.length; i += MAX_BATCH_SIZE) {
            const batchIds = ids.slice(i, i + MAX_BATCH_SIZE);
            const idString = batchIds.join('-');

            console.log(`Fetching batch ${Math.floor(i/MAX_BATCH_SIZE) + 1}: ${idString}`);

            const fixturesFromApi = await fetchFixtures({ ids: idString });

            if (!fixturesFromApi || fixturesFromApi.length === 0) {
                console.log(`No fixtures received from API for batch: ${idString}.`);
                continue;
            }

            console.log(`Received ${fixturesFromApi.length} fixtures for batch: ${idString}.`);

            const bulkOps = createFixtureBulkOps(fixturesFromApi, now);

            if (bulkOps.length > 0) {
                const result = await collection.bulkWrite(bulkOps);
                console.log(`Batch ${Math.floor(i/MAX_BATCH_SIZE) + 1} finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                totalUpserted += result.upsertedCount;
                totalModified += result.modifiedCount;
            }

            // Add a delay between batches to respect API rate limits
            if (i + MAX_BATCH_SIZE < ids.length) {
                await delay(500);
            }
        }

        console.log(`fetchAndUpdateFixturesById job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error(`Error in fetchAndUpdateFixturesById job:`, error);
    }
}

// Main function to fetch fixtures for a single league
async function fetchLeagueFixtures(leagueId: number, season?: number) {
    try {
        // If no season is provided, get the current season for the league
        if (!season) {
            season = await getCurrentSeasonForLeague(leagueId);
        }

        console.log(`Fetching all fixtures for League ID ${leagueId} for season ${season}...`);

        // Fetch fixtures from API-Football
        const allFixturesFromApi = await fetchFixtures({
            league: leagueId,
            season: season
        });

        if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
            console.log(`No fixtures received from API for League ID ${leagueId}, season ${season}.`);
            return 0;
        }

        console.log(`Received ${allFixturesFromApi.length} fixtures for League ID ${leagueId}, season ${season}.`);

        // Filter fixtures if needed
        const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);

        if (fixturesFromApi.length < allFixturesFromApi.length) {
            console.log(`Filtered down to ${fixturesFromApi.length} fixtures based on targeted leagues.`);
        }

        // Create bulk operations for MongoDB
        const collection = getFixturesCollection();
        const now = new Date();
        const bulkOps = createFixtureBulkOps(fixturesFromApi, now);

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`League ${leagueId} fixtures update finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);

            // Find finished fixtures to fetch detailed data for them
            const finishedFixtures = fixturesFromApi.filter(fixture =>
                ['FT', 'AET', 'PEN', 'WO', 'AWD'].includes(fixture.fixture.status.short)
            );

            if (finishedFixtures.length > 0) {
                console.log(`Found ${finishedFixtures.length} finished fixtures. Fetching detailed data...`);
                const finishedFixtureIds = finishedFixtures.map(fixture => fixture.fixture.id);

                // Fetch detailed data for finished fixtures
                await fetchAndUpdateFixturesById(finishedFixtureIds);
                console.log(`Detailed data fetch completed for ${finishedFixtures.length} finished fixtures.`);
            }

            return finishedFixtures.length;
        } else {
            console.log(`No changes needed for League ${leagueId} fixtures.`);
            return 0;
        }

    } catch (error) {
        console.error(`Error fetching fixtures for League ${leagueId}:`, error);
        return 0;
    }
}

// Function to fetch fixtures for multiple leagues
async function fetchMultipleLeagueFixtures() {
    try {
        // Import the leagueTierMap to get all league IDs
        const { leagueTierMap } = require('../config/leagueTiers');

        // Extract all league IDs from the leagueTierMap
        const leagueIds = Object.keys(leagueTierMap).map(id => parseInt(id));

        // Create an array of league objects with IDs
        // We'll fetch the current season for each league individually
        const leaguesToFetch = leagueIds.map(id => {
            return { id };
        });

        console.log(`Found ${leaguesToFetch.length} leagues to process`);

        let totalFinishedFixtures = 0;
        let processedCount = 0;

        // Process each league
        for (const { id } of leaguesToFetch) {
            processedCount++;
            console.log(`\n--- Processing League ID ${id} (${processedCount}/${leaguesToFetch.length}) ---`);

            try {
                // fetchLeagueFixtures will automatically get the current season
                const finishedFixturesCount = await fetchLeagueFixtures(id);
                totalFinishedFixtures += finishedFixturesCount;
            } catch (leagueError) {
                console.error(`Error processing league ID ${id}:`, leagueError);
                // Continue with the next league even if this one fails
            }

            // Add a delay between leagues to respect API rate limits
            if (processedCount < leaguesToFetch.length) {
                console.log('Waiting 3 seconds before processing next league...');
                await delay(3000);
            }
        }

        console.log(`\nAll leagues processed successfully. Total finished fixtures with detailed data: ${totalFinishedFixtures}`);

    } catch (error) {
        console.error('Error fetching multiple league fixtures:', error);
    }
}

// Function to fetch fixtures for leagues by tier
async function fetchLeagueFixturesByTier(tier: number) {
    try {
        // Import the leagueTierMap to get all league IDs
        const { leagueTierMap } = require('../config/leagueTiers');

        // Filter leagues by the specified tier
        const leagueIds = Object.entries(leagueTierMap)
            .filter(([_, leagueTier]) => leagueTier === tier)
            .map(([id, _]) => parseInt(id));

        // Create an array of league objects with IDs
        // We'll fetch the current season for each league individually
        const leaguesToFetch = leagueIds.map(id => {
            return { id };
        });

        console.log(`Found ${leaguesToFetch.length} leagues in tier ${tier} to process`);

        let totalFinishedFixtures = 0;
        let processedCount = 0;

        // Process each league
        for (const { id } of leaguesToFetch) {
            processedCount++;
            console.log(`\n--- Processing League ID ${id} (${processedCount}/${leaguesToFetch.length}) ---`);

            try {
                // fetchLeagueFixtures will automatically get the current season
                const finishedFixturesCount = await fetchLeagueFixtures(id);
                totalFinishedFixtures += finishedFixturesCount;
            } catch (leagueError) {
                console.error(`Error processing league ID ${id}:`, leagueError);
                // Continue with the next league even if this one fails
            }

            // Add a delay between leagues to respect API rate limits
            if (processedCount < leaguesToFetch.length) {
                console.log('Waiting 3 seconds before processing next league...');
                await delay(3000);
            }
        }

        console.log(`\nAll tier ${tier} leagues processed successfully. Total finished fixtures with detailed data: ${totalFinishedFixtures}`);
        return totalFinishedFixtures;

    } catch (error) {
        console.error(`Error fetching tier ${tier} league fixtures:`, error);
        return 0;
    }
}

// Function to run the script with command line arguments
async function run() {
    try {
        console.log('Connecting to database...');
        await connectToMongoDB();

        // Get command line arguments
        const args = process.argv.slice(2);

        // Check the first argument to determine the mode
        if (args.length > 0) {
            const firstArg = args[0];

            if (firstArg === 'tier' && args.length >= 2) {
                // Fetch fixtures for a specific tier
                const tier = parseInt(args[1]);

                if (isNaN(tier) || tier < 1 || tier > 6) {
                    console.log('Error: Tier must be a number between 1 and 6');
                    process.exit(1);
                }

                await fetchLeagueFixturesByTier(tier);
            } else if (firstArg === 'all') {
                // Fetch fixtures for all leagues
                await fetchMultipleLeagueFixtures();
            } else {
                // Assume it's a league ID
                const leagueId = parseInt(firstArg);

                if (isNaN(leagueId)) {
                    console.log('Error: First argument must be a league ID, "tier", or "all"');
                    process.exit(1);
                }

                // Check if a season is provided
                const season = args.length >= 2 ? parseInt(args[1]) : 2024; // Use 2024 as default season

                if (isNaN(season)) {
                    console.log('Error: Season must be a number');
                    process.exit(1);
                }

                await fetchLeagueFixtures(leagueId, season);
            }
        } else {
            // No arguments provided, fetch top 5 leagues by default
            console.log('No arguments provided. Fetching top 5 leagues by default...');

            const topLeagueIds = [
                39,  // Premier League
                140, // La Liga
                78,  // Bundesliga
                135, // Serie A
                61   // Ligue 1
            ];

            let totalFinishedFixtures = 0;

            for (const id of topLeagueIds) {
                console.log(`\n--- Processing League ID ${id} ---`);

                try {
                    // fetchLeagueFixtures will automatically get the current season
                    const finishedFixturesCount = await fetchLeagueFixtures(id);
                    totalFinishedFixtures += finishedFixturesCount;
                } catch (leagueError) {
                    console.error(`Error processing league ID ${id}:`, leagueError);
                }

                // Add a delay between leagues
                if (id !== topLeagueIds[topLeagueIds.length - 1]) {
                    console.log('Waiting 3 seconds before processing next league...');
                    await delay(3000);
                }
            }

            console.log(`\nTop 5 leagues processed successfully. Total finished fixtures with detailed data: ${totalFinishedFixtures}`);
        }

        // Close MongoDB connection
        if (client) {
            await client.close();
            console.log('MongoDB connection closed.');
        }

        process.exit(0);
    } catch (error) {
        console.error('Error running script:', error);
        process.exit(1);
    }
}

// Run the script
run();
