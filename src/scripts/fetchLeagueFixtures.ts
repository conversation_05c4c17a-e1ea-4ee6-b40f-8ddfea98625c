import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchFixtures } from '../services/apiFootball';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { fetchAndUpdateFixturesById } from '../jobs/fixtureJobs';
import { filterByTargetedLeagues } from '../config/targetedLeagues';
import { AnyBulkWriteOperation } from 'mongodb';

// Load environment variables
dotenv.config();

// Helper function to create bulk operations (copied from fixtureJobs.ts)
function createFixtureBulkOps(fixturesFromApi: any[], now: Date): AnyBulkWriteOperation<Fixture>[] {
    const bulkOps: AnyBulkWriteOperation<Fixture>[] = [];

    for (const fixtureApi of fixturesFromApi) {
        // Use fixture ID as the unique identifier (_id) for upsert
        const filter = { _id: fixtureApi.fixture.id };

        // Extract the date from the fixture timestamp for easier querying
        const fixtureDate = new Date(fixtureApi.fixture.timestamp * 1000);
        // Reset hours, minutes, seconds, and milliseconds to get just the date
        fixtureDate.setHours(0, 0, 0, 0);

        // Create an update document with only the fields that are present in the API response
        // This ensures we don't overwrite existing detailed data with undefined values
        const baseUpdateDoc = {
            apiId: fixtureApi.fixture.id,
            fixture: fixtureApi.fixture,
            league: fixtureApi.league,
            teams: fixtureApi.teams,
            goals: fixtureApi.goals,
            score: fixtureApi.score,
            date: fixtureDate, // Add the extracted date field for easier querying
            lastUpdated: now,
        };

        // Only include detailed fields if they are present in the API response
        // This prevents overwriting existing data with undefined values
        const updateDoc: any = { ...baseUpdateDoc };

        if (fixtureApi.events) updateDoc.events = fixtureApi.events;
        if (fixtureApi.lineups) updateDoc.lineups = fixtureApi.lineups;
        if (fixtureApi.statistics) updateDoc.statistics = fixtureApi.statistics;
        if (fixtureApi.players) updateDoc.players = fixtureApi.players;

        bulkOps.push({
            updateOne: {
                filter: filter,
                update: { $set: updateDoc },
                upsert: true, // Insert if not found, update if found
            },
        });
    }

    return bulkOps;
}

// Main function
async function fetchLeagueFixtures(leagueId: number, season: number) {
    try {
        console.log('Connecting to database...');
        await connectDB();

        console.log(`Fetching all fixtures for League ID ${leagueId} for season ${season}...`);

        // Fetch fixtures from API-Football
        const allFixturesFromApi = await fetchFixtures({
            league: leagueId,
            season: season
        });

        if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
            console.log(`No fixtures received from API for League ID ${leagueId}, season ${season}.`);
            return;
        }

        console.log(`Received ${allFixturesFromApi.length} fixtures for League ID ${leagueId}, season ${season}.`);

        // Filter fixtures if needed
        const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);

        if (fixturesFromApi.length < allFixturesFromApi.length) {
            console.log(`Filtered down to ${fixturesFromApi.length} fixtures based on targeted leagues.`);
        }

        // Create bulk operations for MongoDB
        const collection = getFixturesCollection();
        const now = new Date();
        const bulkOps = createFixtureBulkOps(fixturesFromApi, now);

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`League ${leagueId} fixtures update finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);

            // Find finished fixtures to fetch detailed data for them
            const finishedFixtures = fixturesFromApi.filter(fixture =>
                ['FT', 'AET', 'PEN', 'WO', 'AWD'].includes(fixture.fixture.status.short)
            );

            if (finishedFixtures.length > 0) {
                console.log(`Found ${finishedFixtures.length} finished fixtures. Fetching detailed data...`);
                const finishedFixtureIds = finishedFixtures.map(fixture => fixture.fixture.id);

                // Fetch detailed data for finished fixtures
                await fetchAndUpdateFixturesById(finishedFixtureIds);
                console.log(`Detailed data fetch completed for ${finishedFixtures.length} finished fixtures.`);
            }
        } else {
            console.log(`No changes needed for League ${leagueId} fixtures.`);
        }

        console.log(`League ${leagueId} fixtures fetch completed successfully`);

    } catch (error) {
        console.error(`Error fetching fixtures for League ${leagueId}:`, error);
    }
}

// Function to run the script with command line arguments
async function run() {
    // Get league ID and season from command line arguments
    const args = process.argv.slice(2);

    if (args.length < 2) {
        console.log('Usage: node fetchLeagueFixtures.js <leagueId> <season>');
        console.log('Example: node fetchLeagueFixtures.js 39 2024');
        process.exit(1);
    }

    const leagueId = parseInt(args[0]);
    const season = parseInt(args[1]);

    if (isNaN(leagueId) || isNaN(season)) {
        console.log('Error: League ID and season must be numbers');
        process.exit(1);
    }

    await fetchLeagueFixtures(leagueId, season);
    process.exit(0);
}

// Run the script
run();
