import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchSidelined } from '../services/sidelinedService';
import { getSidelinedCollection, Sidelined, createSidelinedId } from '../models/Sidelined';
import { getPlayersCollection } from '../models/Player';
import { getTeamsCollection } from '../models/Team';
import { AnyBulkWriteOperation } from 'mongodb';

// Load environment variables
dotenv.config();

// Premier League ID
const PREMIER_LEAGUE_ID = 39;
const CURRENT_SEASON = 2024;

// Main function
async function fetchPremierLeagueSidelined() {
    try {
        console.log('Connecting to database...');
        await connectDB();

        // Get Premier League teams
        const teamsCollection = getTeamsCollection();
        const teams = await teamsCollection.find({
            'league.id': PREMIER_LEAGUE_ID,
            'league.season': CURRENT_SEASON
        }).toArray();

        if (!teams || teams.length === 0) {
            console.log(`No teams found for Premier League (ID: ${PREMIER_LEAGUE_ID}) in season ${CURRENT_SEASON}.`);
            return;
        }

        console.log(`Found ${teams.length} Premier League teams.`);

        // Get players from these teams
        const playersCollection = getPlayersCollection();
        const sidelinedCollection = getSidelinedCollection();
        const now = new Date();
        let totalUpserted = 0;
        let totalModified = 0;

        // Process each team
        for (const team of teams) {
            const teamId = team._id;
            const teamName = team.team?.name || `Team ${teamId}`;

            console.log(`Processing team: ${teamName} (ID: ${teamId})`);

            // Create team object for sidelined records
            const teamObj = {
                id: teamId,
                name: teamName,
                logo: team.team?.logo || null
            };

            // Get players for this team
            const players = await playersCollection.find({
                'statistics.*.team.id': teamId,
                apiId: { $type: 16 } // Only get players with numeric apiId
            }).limit(20).toArray(); // Limit to 20 players per team (max batch size)

            if (!players || players.length === 0) {
                console.log(`No players found for Team ${teamName}.`);
                continue;
            }

            console.log(`Found ${players.length} players for Team ${teamName}.`);

            // Extract player IDs for batch request
            const playerIds = players.map(player => player.apiId || player._id);

            // Use batch request with 'ids' parameter (max 20 players)
            const idsString = playerIds.join('-');
            console.log(`Fetching sidelined data for players: ${idsString}`);

            try {
                // Use the 'players' parameter for batch request
                const sidelinedPlayers = await fetchSidelined({ players: idsString });

                if (!sidelinedPlayers || sidelinedPlayers.length === 0) {
                    console.log(`No sidelined data found for players from Team ${teamName}.`);
                    continue;
                }

                console.log(`Received ${sidelinedPlayers.length} sidelined records for Team ${teamName}.`);

                // The response format for 'players' parameter is different
                // Each item in the response has an 'id' and 'sidelined' array
                const sidelinedByPlayer: Record<number, any[]> = {};

                for (const playerSidelined of sidelinedPlayers) {
                    const playerId = playerSidelined.id;
                    sidelinedByPlayer[playerId] = playerSidelined.sidelined;
                }

                // Process each player's sidelined records
                const bulkOps: AnyBulkWriteOperation<Sidelined>[] = [];

                for (const player of players) {
                    const playerId = player.apiId || player._id;
                    const playerName = player.profile?.name || `Player ${playerId}`;
                    const playerPhoto = player.profile?.photo || null;

                    // Get sidelined records for this player
                    const playerSidelined = sidelinedByPlayer[playerId] || [];

                    if (playerSidelined.length === 0) {
                        console.log(`No sidelined records for Player ${playerName}.`);
                        continue;
                    }

                    console.log(`Found ${playerSidelined.length} sidelined record(s) for Player ${playerName}.`);

                    // Process each sidelined record
                    for (const sidelined of playerSidelined) {
                        const type = sidelined.type || 'Unknown';
                        const start = sidelined.start || new Date().toISOString();
                        const sidelinedId = createSidelinedId(playerId, type, start);

                        // Create sidelined document
                        const sidelinedDoc: Sidelined = {
                            _id: sidelinedId,
                            player: {
                                id: playerId,
                                name: playerName,
                                photo: playerPhoto
                            },
                            team: teamObj,
                            type: type,
                            reason: null, // The batch API doesn't return reason
                            start: start,
                            end: sidelined.end || null,
                            lastUpdated: now
                        };

                        bulkOps.push({
                            updateOne: {
                                filter: { _id: sidelinedId },
                                update: { $set: sidelinedDoc },
                                upsert: true
                            }
                        });
                    }
                }

                // Execute bulk operations if any
                if (bulkOps.length > 0) {
                    const result = await sidelinedCollection.bulkWrite(bulkOps);
                    console.log(`Bulk write result for Team ${teamName}:`, {
                        upserted: result.upsertedCount,
                        modified: result.modifiedCount
                    });

                    totalUpserted += result.upsertedCount;
                    totalModified += result.modifiedCount;
                }

            } catch (error) {
                console.error(`Error fetching sidelined data for Team ${teamName}:`, error);
            }

            // Add a small delay between teams to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        console.log(`Job completed. Total records: Upserted=${totalUpserted}, Modified=${totalModified}`);
        process.exit(0);

    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
fetchPremierLeagueSidelined();
