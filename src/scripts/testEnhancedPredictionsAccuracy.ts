/**
 * Enhanced Predictions Accuracy Test Script
 * 
 * Tests enhanced predictions against real match results from a specific date
 * Selects 30 random fixtures and compares predictions with actual outcomes
 */

import axios from 'axios';
import connectDB from '../config/database';

const API_BASE_URL = 'https://api.kickoffpredictions.com';
const API_KEY = '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756';
const TEST_DATE = '2025-05-11';

// Major leagues to focus on for testing
const MAJOR_LEAGUES = [
  39,   // Premier League (England)
  140,  // La Liga (Spain)
  78,   // Bundesliga (Germany)
  135,  // Serie A (Italy)
  61,   // Ligue 1 (France)
  88,   // Eredivisie (Netherlands)
  94,   // Liga Portugal (Portugal)
  203,  // Super Lig (Turkey)
  144,  // Pro League (Belgium)
  179,  // Premiership (Scotland)
  253,  // Major League Soccer (USA)
  71,   // Serie A (Brazil)
  128,  // Liga Profesional (Argentina)
  2,    // Champions League
  3,    // Europa League
  848   // Conference League
];

interface TestFixture {
  fixture: {
    id: number;
    date: string;
    status: {
      short: string;
      long: string;
    };
  };
  league: {
    id: number;
    name: string;
    country: string;
  };
  teams: {
    home: { id: number; name: string };
    away: { id: number; name: string };
  };
  goals: {
    home: number | null;
    away: number | null;
  };
  score: {
    fulltime: {
      home: number | null;
      away: number | null;
    };
  };
}

interface EnhancedPrediction {
  predictions: {
    correctScore: {
      mostLikely: {
        home: number;
        away: number;
        probability: number;
      };
      top5Scores: Array<{
        home: number;
        away: number;
        probability: number;
      }>;
    };
    bothTeamsToScore: {
      prediction: boolean;
      probability: number;
    };
    matchOutcome: {
      homeWin: number;
      draw: number;
      awayWin: number;
    };
    expectedGoals: {
      home: number;
      away: number;
    };
    corners?: {
      expectedTotal: number;
      homeExpected: number;
      awayExpected: number;
      confidence: number;
      markets: {
        over8_5: { threshold: number; overProbability: number; underProbability: number; confidence: string };
        over9_5: { threshold: number; overProbability: number; underProbability: number; confidence: string };
        over10_5: { threshold: number; overProbability: number; underProbability: number; confidence: string };
        over11_5: { threshold: number; overProbability: number; underProbability: number; confidence: string };
        over12_5: { threshold: number; overProbability: number; underProbability: number; confidence: string };
      };
      algorithm: string;
      dataSource: string;
    };
    cards?: {
      expectedTotalCards: number;
      expectedYellowCards: number;
      expectedRedCards: number;
      homeExpectedCards: number;
      awayExpectedCards: number;
      confidence: number;
      markets: {
        over2_5: { threshold: number; overProbability: number; underProbability: number; confidence: string };
        over3_5: { threshold: number; overProbability: number; underProbability: number; confidence: string };
        over4_5: { threshold: number; overProbability: number; underProbability: number; confidence: string };
        over5_5: { threshold: number; overProbability: number; underProbability: number; confidence: string };
      };
      algorithm: string;
      dataSource: string;
    };
  };
  metadata: {
    confidence: number;
    dataQuality: string;
    matchesUsed: number;
    eloInfluence?: number;
  };
}

interface TestResult {
  fixtureId: number;
  matchInfo: string;
  actualScore: string;
  actualResult: 'H' | 'D' | 'A';
  actualBTTS: boolean;

  // Predictions
  predictedScore: string;
  predictedResult: 'H' | 'D' | 'A';
  predictedBTTS: boolean;

  // Accuracy
  correctScoreMatch: boolean;
  correctResultMatch: boolean;
  correctBTTSMatch: boolean;

  // Probabilities
  resultProbability: number;
  bttsConfidence: number;
  overallConfidence: number;

  // Metadata
  dataQuality: string;
  matchesUsed: number;
  eloInfluence?: number;

  // Corners (if available)
  corners?: {
    actualCorners?: number;
    predictedCorners: number;
    homeExpected: number;
    awayExpected: number;
    confidence: number;
    over10_5Probability: number;
    dataSource: string;
  };

  // Cards (if available)
  cards?: {
    actualCards?: number;
    predictedCards: number;
    homeExpected: number;
    awayExpected: number;
    confidence: number;
    over3_5Probability: number;
    dataSource: string;
  };
}

class EnhancedPredictionAccuracyTest {
  
  /**
   * Main test function
   */
  static async runAccuracyTest(): Promise<void> {
    console.log('🎯 Enhanced Predictions Accuracy Test');
    console.log(`📅 Testing date: ${TEST_DATE}`);
    console.log('=' .repeat(60));
    
    try {
      // Step 1: Fetch all fixtures from the test date
      console.log('\n📥 Step 1: Fetching fixtures...');
      const fixtures = await this.fetchFixtures();
      
      if (fixtures.length === 0) {
        console.log('❌ No fixtures found for the test date');
        return;
      }
      
      console.log(`✅ Found ${fixtures.length} fixtures`);
      
      // Step 2: Filter finished fixtures with valid scores
      console.log('\n🔍 Step 2: Filtering finished fixtures...');
      const finishedFixtures = this.filterFinishedFixtures(fixtures);
      console.log(`✅ Found ${finishedFixtures.length} finished fixtures with scores`);
      
      if (finishedFixtures.length === 0) {
        console.log('❌ No finished fixtures with scores found');
        return;
      }
      
      // Step 3: Filter for major leagues
      console.log('\n🏆 Step 3: Filtering for major leagues...');
      const majorLeagueFixtures = this.filterMajorLeagues(finishedFixtures);
      console.log(`✅ Found ${majorLeagueFixtures.length} fixtures from major leagues`);

      // Step 4: Select 40 random fixtures (or all if less than 40)
      console.log('\n🎲 Step 4: Selecting test fixtures...');
      const testFixtures = this.selectRandomFixtures(majorLeagueFixtures, 40);
      console.log(`✅ Selected ${testFixtures.length} fixtures for testing`);
      
      // Step 5: Test each fixture
      console.log('\n🧪 Step 5: Testing predictions...');
      const results: TestResult[] = [];
      
      for (let i = 0; i < testFixtures.length; i++) {
        const fixture = testFixtures[i];
        console.log(`\n[${i + 1}/${testFixtures.length}] Testing: ${fixture.teams.home.name} vs ${fixture.teams.away.name}`);
        
        const result = await this.testFixture(fixture);
        if (result) {
          results.push(result);
          console.log(`   ✅ Result: ${result.correctResultMatch ? '✓' : '✗'} | Score: ${result.correctScoreMatch ? '✓' : '✗'} | BTTS: ${result.correctBTTSMatch ? '✓' : '✗'}`);
        } else {
          console.log('   ⚠️  Skipped (insufficient data)');
        }
      }
      
      // Step 6: Analyze results
      console.log('\n📊 Step 6: Analyzing results...');
      this.analyzeResults(results);
      
    } catch (error) {
      console.error('❌ Test failed:', error);
    }
  }
  
  /**
   * Fetch fixtures from API
   */
  private static async fetchFixtures(): Promise<TestFixture[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/fixtures`, {
        params: { date: TEST_DATE },
        headers: { 'X-API-Key': API_KEY },
        timeout: 30000
      });
      
      return response.data || [];
    } catch (error) {
      console.error('Error fetching fixtures:', error);
      return [];
    }
  }
  
  /**
   * Filter fixtures that are finished and have valid scores
   */
  private static filterFinishedFixtures(fixtures: TestFixture[]): TestFixture[] {
    return fixtures.filter(fixture => {
      // Must be finished
      if (!['FT', 'AET', 'PEN'].includes(fixture.fixture.status.short)) {
        return false;
      }

      // Must have valid scores
      if (fixture.goals.home === null || fixture.goals.away === null) {
        return false;
      }

      // Must have fulltime scores
      if (fixture.score.fulltime.home === null || fixture.score.fulltime.away === null) {
        return false;
      }

      return true;
    });
  }

  /**
   * Filter fixtures from major leagues only
   */
  private static filterMajorLeagues(fixtures: TestFixture[]): TestFixture[] {
    return fixtures.filter(fixture => {
      return MAJOR_LEAGUES.includes(fixture.league.id);
    });
  }
  
  /**
   * Select random fixtures from the list
   */
  private static selectRandomFixtures(fixtures: TestFixture[], count: number): TestFixture[] {
    const shuffled = [...fixtures].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, fixtures.length));
  }
  
  /**
   * Test a single fixture
   */
  private static async testFixture(fixture: TestFixture): Promise<TestResult | null> {
    try {
      // Get enhanced prediction
      const prediction = await this.getEnhancedPrediction(fixture.fixture.id);

      if (!prediction) {
        return null;
      }

      // Get detailed fixture data with statistics
      const detailedFixture = await this.getFixtureWithStatistics(fixture.fixture.id);

      // Extract actual results
      const actualHomeGoals = fixture.score.fulltime.home!;
      const actualAwayGoals = fixture.score.fulltime.away!;
      const actualScore = `${actualHomeGoals}-${actualAwayGoals}`;

      let actualResult: 'H' | 'D' | 'A';
      if (actualHomeGoals > actualAwayGoals) {
        actualResult = 'H';
      } else if (actualHomeGoals < actualAwayGoals) {
        actualResult = 'A';
      } else {
        actualResult = 'D';
      }

      const actualBTTS = actualHomeGoals > 0 && actualAwayGoals > 0;

      // Extract predictions
      const predictedHomeGoals = prediction.predictions.correctScore.mostLikely.home;
      const predictedAwayGoals = prediction.predictions.correctScore.mostLikely.away;
      const predictedScore = `${predictedHomeGoals}-${predictedAwayGoals}`;

      // Determine predicted result from predicted score (same logic as actual result)
      let predictedResult: 'H' | 'D' | 'A';
      if (predictedHomeGoals > predictedAwayGoals) {
        predictedResult = 'H';
      } else if (predictedHomeGoals < predictedAwayGoals) {
        predictedResult = 'A';
      } else {
        predictedResult = 'D';
      }

      const outcomes = prediction.predictions.matchOutcome;

      const predictedBTTS = prediction.predictions.bothTeamsToScore.prediction;

      // Calculate accuracy
      const correctScoreMatch = actualScore === predictedScore;
      const correctResultMatch = actualResult === predictedResult;
      const correctBTTSMatch = actualBTTS === predictedBTTS;

      // Get result probability
      let resultProbability: number;
      if (predictedResult === 'H') {
        resultProbability = outcomes.homeWin;
      } else if (predictedResult === 'A') {
        resultProbability = outcomes.awayWin;
      } else {
        resultProbability = outcomes.draw;
      }

      // Extract corners and cards data if available
      let cornersData: any = undefined;
      let cardsData: any = undefined;

      // Check for corner predictions and actual data
      if (prediction.predictions.corners && detailedFixture) {
        const actualCorners = this.extractActualCorners(detailedFixture);
        cornersData = {
          actualCorners,
          predictedCorners: Math.round(prediction.predictions.corners.expectedTotal),
          homeExpected: prediction.predictions.corners.homeExpected,
          awayExpected: prediction.predictions.corners.awayExpected,
          confidence: prediction.predictions.corners.confidence,
          over10_5Probability: prediction.predictions.corners.markets.over10_5?.overProbability || 0,
          dataSource: prediction.predictions.corners.dataSource
        };
      }

      // Check for card predictions and actual data
      if (prediction.predictions.cards && detailedFixture) {
        const actualCards = this.extractActualCards(detailedFixture);
        cardsData = {
          actualCards,
          predictedCards: Math.round(prediction.predictions.cards.expectedTotalCards),
          homeExpected: prediction.predictions.cards.homeExpectedCards,
          awayExpected: prediction.predictions.cards.awayExpectedCards,
          confidence: prediction.predictions.cards.confidence,
          over3_5Probability: prediction.predictions.cards.markets.over3_5?.overProbability || 0,
          dataSource: prediction.predictions.cards.dataSource
        };
      }

      const result: TestResult = {
        fixtureId: fixture.fixture.id,
        matchInfo: `${fixture.teams.home.name} vs ${fixture.teams.away.name} (${fixture.league.name})`,
        actualScore,
        actualResult,
        actualBTTS,
        predictedScore,
        predictedResult,
        predictedBTTS,
        correctScoreMatch,
        correctResultMatch,
        correctBTTSMatch,
        resultProbability,
        bttsConfidence: prediction.predictions.bothTeamsToScore.probability,
        overallConfidence: prediction.metadata?.confidence || 0,
        dataQuality: prediction.metadata?.dataQuality || 'unknown',
        matchesUsed: prediction.metadata?.matchesUsed || 0,
        eloInfluence: prediction.metadata?.eloInfluence
      };

      if (cornersData) result.corners = cornersData;
      if (cardsData) result.cards = cardsData;

      return result;

    } catch (error) {
      console.error(`Error testing fixture ${fixture.fixture.id}:`, error);
      return null;
    }
  }

  /**
   * Get enhanced prediction for a fixture
   */
  private static async getEnhancedPrediction(fixtureId: number): Promise<EnhancedPrediction | null> {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/enhanced-predictions/${fixtureId}`, {
        headers: { 'X-API-Key': API_KEY },
        timeout: 30000
      });

      if (response.data.success && response.data.data) {
        // Debug: Log the structure for the first few predictions to understand what's available
        if (fixtureId === 1411548 || fixtureId === 1411549) {
          console.log(`   🔍 Debug - Prediction structure for ${fixtureId}:`);
          console.log(`      - Has corners: ${!!response.data.data.predictions.corners}`);
          console.log(`      - Has cards: ${!!response.data.data.predictions.cards}`);
          if (response.data.data.predictions.corners) {
            console.log(`      - Corners expected: ${response.data.data.predictions.corners.expectedTotal}`);
          }
          if (response.data.data.predictions.cards) {
            console.log(`      - Cards expected: ${response.data.data.predictions.cards.expectedTotalCards}`);
          }
        }
        return response.data.data;
      } else {
        console.log(`   ⚠️  Enhanced prediction not available for fixture ${fixtureId}`);
        return null;
      }
    } catch (error) {
      console.log(`   ⚠️  Error fetching prediction for fixture ${fixtureId}: ${error}`);
      return null;
    }
  }

  /**
   * Get fixture with detailed statistics
   */
  private static async getFixtureWithStatistics(fixtureId: number): Promise<any | null> {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/fixtures`, {
        params: { id: fixtureId },
        headers: { 'X-API-Key': API_KEY },
        timeout: 30000
      });

      if (response.data && response.data.length > 0) {
        return response.data[0];
      } else {
        return null;
      }
    } catch (error) {
      console.log(`   ⚠️  Error fetching fixture statistics for ${fixtureId}: ${error}`);
      return null;
    }
  }

  /**
   * Extract actual corners from fixture statistics
   */
  private static extractActualCorners(fixture: any): number | undefined {
    try {
      if (!fixture.statistics || fixture.statistics.length < 2) {
        return undefined;
      }

      const homeStats = fixture.statistics[0]?.statistics || [];
      const awayStats = fixture.statistics[1]?.statistics || [];

      const homeCornerStat = homeStats.find((s: any) => s.type === "Corner Kicks");
      const awayCornerStat = awayStats.find((s: any) => s.type === "Corner Kicks");

      if (!homeCornerStat?.value || !awayCornerStat?.value) {
        return undefined;
      }

      const homeCorners = parseInt(homeCornerStat.value);
      const awayCorners = parseInt(awayCornerStat.value);

      if (isNaN(homeCorners) || isNaN(awayCorners)) {
        return undefined;
      }

      return homeCorners + awayCorners;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Extract actual cards from fixture statistics
   */
  private static extractActualCards(fixture: any): number | undefined {
    try {
      if (!fixture.statistics || fixture.statistics.length < 2) {
        return undefined;
      }

      const homeStats = fixture.statistics[0]?.statistics || [];
      const awayStats = fixture.statistics[1]?.statistics || [];

      const homeYellowStat = homeStats.find((s: any) => s.type === "Yellow Cards");
      const awayYellowStat = awayStats.find((s: any) => s.type === "Yellow Cards");
      const homeRedStat = homeStats.find((s: any) => s.type === "Red Cards");
      const awayRedStat = awayStats.find((s: any) => s.type === "Red Cards");

      const homeYellowCards = parseInt(homeYellowStat?.value) || 0;
      const awayYellowCards = parseInt(awayYellowStat?.value) || 0;
      const homeRedCards = parseInt(homeRedStat?.value) || 0;
      const awayRedCards = parseInt(awayRedStat?.value) || 0;

      return homeYellowCards + awayYellowCards + homeRedCards + awayRedCards;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Analyze and display test results
   */
  private static analyzeResults(results: TestResult[]): void {
    if (results.length === 0) {
      console.log('❌ No valid test results to analyze');
      return;
    }

    console.log('\n📈 ACCURACY ANALYSIS');
    console.log('=' .repeat(60));

    // Overall accuracy
    const correctScores = results.filter(r => r.correctScoreMatch).length;
    const correctResults = results.filter(r => r.correctResultMatch).length;
    const correctBTTS = results.filter(r => r.correctBTTSMatch).length;

    const scoreAccuracy = (correctScores / results.length) * 100;
    const resultAccuracy = (correctResults / results.length) * 100;
    const bttsAccuracy = (correctBTTS / results.length) * 100;

    console.log(`\n🎯 Overall Accuracy (${results.length} fixtures):`);
    console.log(`   Correct Score: ${correctScores}/${results.length} (${scoreAccuracy.toFixed(1)}%)`);
    console.log(`   Match Result:  ${correctResults}/${results.length} (${resultAccuracy.toFixed(1)}%)`);
    console.log(`   BTTS:          ${correctBTTS}/${results.length} (${bttsAccuracy.toFixed(1)}%)`);

    // Corners and Cards analysis
    const cornersResults = results.filter(r => r.corners && r.corners.actualCorners !== undefined);
    const cardsResults = results.filter(r => r.cards && r.cards.actualCards !== undefined);

    if (cornersResults.length > 0) {
      const cornersAccuracy = this.analyzeCornersPredictions(cornersResults);
      console.log(`   Corners:       ${cornersResults.length} fixtures analyzed`);
      console.log(`                  Avg Error: ${cornersAccuracy.avgError.toFixed(1)} corners`);
      console.log(`                  Within ±2: ${cornersAccuracy.within2.toFixed(1)}%`);
    }

    if (cardsResults.length > 0) {
      const cardsAccuracy = this.analyzeCardsPredictions(cardsResults);
      console.log(`   Cards:         ${cardsResults.length} fixtures analyzed`);
      console.log(`                  Avg Error: ${cardsAccuracy.avgError.toFixed(1)} cards`);
      console.log(`                  Within ±1: ${cardsAccuracy.within1.toFixed(1)}%`);
    }

    // Confidence analysis
    const avgConfidence = results.reduce((sum, r) => sum + r.overallConfidence, 0) / results.length;
    const avgMatchesUsed = results.reduce((sum, r) => sum + r.matchesUsed, 0) / results.length;

    console.log(`\n📊 Data Quality:`);
    console.log(`   Average Confidence: ${avgConfidence.toFixed(1)}%`);
    console.log(`   Average Matches Used: ${avgMatchesUsed.toFixed(1)}`);

    // ELO influence analysis
    const eloResults = results.filter(r => r.eloInfluence !== undefined);
    if (eloResults.length > 0) {
      const avgEloInfluence = eloResults.reduce((sum, r) => sum + (r.eloInfluence || 0), 0) / eloResults.length;
      console.log(`   Average ELO Influence: ${(avgEloInfluence * 100).toFixed(1)}%`);
    }

    // Data quality breakdown
    const dataQualityBreakdown = results.reduce((acc, r) => {
      acc[r.dataQuality] = (acc[r.dataQuality] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log(`\n📋 Data Quality Distribution:`);
    Object.entries(dataQualityBreakdown).forEach(([quality, count]) => {
      console.log(`   ${quality}: ${count} fixtures (${((count / results.length) * 100).toFixed(1)}%)`);
    });

    // Detailed results
    console.log(`\n📝 Detailed Results:`);
    console.log('=' .repeat(120));
    console.log('Match'.padEnd(35) + 'Score'.padEnd(12) + 'R'.padEnd(3) + 'S'.padEnd(3) + 'B'.padEnd(3) + 'Corners'.padEnd(12) + 'Cards'.padEnd(10) + 'Conf%');
    console.log('-' .repeat(120));

    results.forEach(result => {
      const match = result.matchInfo.length > 32 ? result.matchInfo.substring(0, 32) + '...' : result.matchInfo;
      const score = `${result.actualScore}/${result.predictedScore}`;
      const resultIcon = result.correctResultMatch ? '✓' : '✗';
      const scoreIcon = result.correctScoreMatch ? '✓' : '✗';
      const bttsIcon = result.correctBTTSMatch ? '✓' : '✗';

      let cornersInfo = 'N/A';
      if (result.corners && result.corners.actualCorners !== undefined) {
        cornersInfo = `${result.corners.actualCorners}/${result.corners.predictedCorners}`;
      }

      let cardsInfo = 'N/A';
      if (result.cards && result.cards.actualCards !== undefined) {
        cardsInfo = `${result.cards.actualCards}/${result.cards.predictedCards}`;
      }

      console.log(
        match.padEnd(35) +
        score.padEnd(12) +
        resultIcon.padEnd(3) +
        scoreIcon.padEnd(3) +
        bttsIcon.padEnd(3) +
        cornersInfo.padEnd(12) +
        cardsInfo.padEnd(10) +
        result.overallConfidence.toFixed(0)
      );
    });

    // Performance by confidence level
    console.log(`\n🎯 Performance by Confidence Level:`);
    const highConfidence = results.filter(r => r.overallConfidence >= 70);
    const mediumConfidence = results.filter(r => r.overallConfidence >= 50 && r.overallConfidence < 70);
    const lowConfidence = results.filter(r => r.overallConfidence < 50);

    if (highConfidence.length > 0) {
      const highAccuracy = (highConfidence.filter(r => r.correctResultMatch).length / highConfidence.length) * 100;
      console.log(`   High Confidence (≥70%): ${highAccuracy.toFixed(1)}% accuracy (${highConfidence.length} fixtures)`);
    }

    if (mediumConfidence.length > 0) {
      const mediumAccuracy = (mediumConfidence.filter(r => r.correctResultMatch).length / mediumConfidence.length) * 100;
      console.log(`   Medium Confidence (50-69%): ${mediumAccuracy.toFixed(1)}% accuracy (${mediumConfidence.length} fixtures)`);
    }

    if (lowConfidence.length > 0) {
      const lowAccuracy = (lowConfidence.filter(r => r.correctResultMatch).length / lowConfidence.length) * 100;
      console.log(`   Low Confidence (<50%): ${lowAccuracy.toFixed(1)}% accuracy (${lowConfidence.length} fixtures)`);
    }

    console.log('\n✅ Analysis complete!');
  }

  /**
   * Analyze corners predictions accuracy
   */
  private static analyzeCornersPredictions(results: TestResult[]): { avgError: number; within2: number } {
    let totalError = 0;
    let within2Count = 0;

    for (const result of results) {
      if (result.corners && result.corners.actualCorners !== undefined) {
        const error = Math.abs(result.corners.actualCorners - result.corners.predictedCorners);
        totalError += error;

        if (error <= 2) {
          within2Count++;
        }
      }
    }

    return {
      avgError: totalError / results.length,
      within2: (within2Count / results.length) * 100
    };
  }

  /**
   * Analyze cards predictions accuracy
   */
  private static analyzeCardsPredictions(results: TestResult[]): { avgError: number; within1: number } {
    let totalError = 0;
    let within1Count = 0;

    for (const result of results) {
      if (result.cards && result.cards.actualCards !== undefined) {
        const error = Math.abs(result.cards.actualCards - result.cards.predictedCards);
        totalError += error;

        if (error <= 1) {
          within1Count++;
        }
      }
    }

    return {
      avgError: totalError / results.length,
      within1: (within1Count / results.length) * 100
    };
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  EnhancedPredictionAccuracyTest.runAccuracyTest()
    .then(() => {
      console.log('\n🎉 Enhanced Predictions Accuracy Test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export default EnhancedPredictionAccuracyTest;
