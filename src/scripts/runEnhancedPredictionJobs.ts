/**
 * <PERSON><PERSON><PERSON> to manually run enhanced prediction background jobs
 * Useful for testing and initial setup
 */

import { trainDixonColesModelsForActiveLeagues, generateEnhancedPredictionsForUpcomingFixtures, refreshDixonColesModels } from '../jobs/enhancedPredictionJobs';

async function runEnhancedPredictionJobs() {
    console.log('🚀 Running Enhanced Prediction Background Jobs...\n');

    const args = process.argv.slice(2);
    const jobType = args[0] || 'all';

    try {
        switch (jobType) {
            case 'train':
                console.log('📊 Running Model Training Job...');
                await trainDixonColesModelsForActiveLeagues();
                break;

            case 'predict':
                const days = parseInt(args[1]) || 7;
                console.log(`🎯 Running Prediction Generation Job (${days} days)...`);
                await generateEnhancedPredictionsForUpcomingFixtures(days);
                break;

            case 'refresh':
                console.log('🔄 Running Model Refresh Job...');
                await refreshDixonColesModels();
                break;

            case 'all':
                console.log('📊 Step 1: Training Models...');
                await trainDixonColesModelsForActiveLeagues();
                
                console.log('\n🎯 Step 2: Generating Predictions...');
                await generateEnhancedPredictionsForUpcomingFixtures(7);
                
                console.log('\n✅ All jobs completed successfully!');
                break;

            default:
                console.log('❌ Invalid job type. Use: train, predict, refresh, or all');
                console.log('Examples:');
                console.log('  npm run enhanced-jobs train');
                console.log('  npm run enhanced-jobs predict 3');
                console.log('  npm run enhanced-jobs refresh');
                console.log('  npm run enhanced-jobs all');
                process.exit(1);
        }

        console.log('\n🎉 Enhanced prediction jobs completed successfully!');
        process.exit(0);

    } catch (error) {
        console.error('\n❌ Error running enhanced prediction jobs:', error);
        process.exit(1);
    }
}

// Run if this file is executed directly
if (require.main === module) {
    runEnhancedPredictionJobs();
}
