/**
 * <PERSON><PERSON>t to manually trigger sidelined jobs
 * 
 * Usage: 
 * ts-node src/scripts/runSidelinedJobs.ts [job-type]
 * 
 * job-type options:
 * - team: Run fetchAndUpdateTeamSidelined only
 * - league: Run fetchAndUpdateLeagueSidelined only
 * - all: Run both jobs (default)
 */

import { fetchAndUpdateTeamSidelined, fetchAndUpdateLeagueSidelined } from '../jobs/sidelinedJobs';
import connectDB from '../config/database';
import connectRedis from '../config/redis';

// Process command line arguments
const jobType = process.argv[2] || 'all';

async function runJobs() {
  try {
    console.log('Initializing database and Redis connections...');
    await connectDB();
    connectRedis();
    
    const startTime = new Date();
    console.log(`Starting sidelined jobs at ${startTime.toISOString()}...`);
    
    if (jobType === 'team' || jobType === 'all') {
      console.log('\n--- Running Team Sidelined Job ---');
      await fetchAndUpdateTeamSidelined();
      console.log('--- Team Sidelined Job Completed ---\n');
    }
    
    if (jobType === 'league' || jobType === 'all') {
      console.log('\n--- Running League Sidelined Job ---');
      await fetchAndUpdateLeagueSidelined();
      console.log('--- League Sidelined Job Completed ---\n');
    }
    
    const endTime = new Date();
    const durationMs = endTime.getTime() - startTime.getTime();
    const durationMinutes = Math.floor(durationMs / 60000);
    const durationSeconds = ((durationMs % 60000) / 1000).toFixed(2);
    
    console.log(`Sidelined jobs completed at ${endTime.toISOString()}`);
    console.log(`Total execution time: ${durationMinutes} minutes and ${durationSeconds} seconds`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error running sidelined jobs:', error);
    process.exit(1);
  }
}

// Validate job type
if (!['team', 'league', 'all'].includes(jobType)) {
  console.error('Invalid job type. Use "team", "league", or "all".');
  process.exit(1);
}

console.log(`Running sidelined jobs with type: ${jobType}`);
runJobs();
