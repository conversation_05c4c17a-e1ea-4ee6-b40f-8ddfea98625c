import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchFixtures } from '../services/apiFootball';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { filterByTargetedLeagues } from '../config/targetedLeagues';
import { AnyBulkWriteOperation } from 'mongodb';
import dayjs from 'dayjs';

// Load environment variables
dotenv.config();

// Helper function to create bulk operations (copied from fixtureJobs.ts)
function createFixtureBulkOps(fixturesFromApi: any[], now: Date): AnyBulkWriteOperation<Fixture>[] {
    const bulkOps: AnyBulkWriteOperation<Fixture>[] = [];

    fixturesFromApi.forEach(fixtureData => {
        const fixtureId = fixtureData.fixture.id;
        
        // Create the fixture document
        const fixtureDoc: Fixture = {
            _id: fixtureId,
            apiId: fixtureId,
            fixture: fixtureData.fixture,
            league: fixtureData.league,
            teams: fixtureData.teams,
            goals: fixtureData.goals,
            score: fixtureData.score,
            events: fixtureData.events || [],
            lineups: fixtureData.lineups || [],
            statistics: fixtureData.statistics || [],
            players: fixtureData.players || [],
            date: new Date(fixtureData.fixture.date),
            lastUpdated: now
        };

        // Use upsert to insert or update
        bulkOps.push({
            replaceOne: {
                filter: { _id: fixtureId },
                replacement: fixtureDoc,
                upsert: true
            }
        });
    });

    return bulkOps;
}

// Function to fetch fixtures for friendly leagues
async function fetchFriendlyFixtures() {
    try {
        console.log('Connecting to database...');
        await connectDB();

        const friendlyLeagues = [667, 10]; // Club Friendlies and International Friendlies
        const collection = getFixturesCollection();
        const now = new Date();
        
        for (const leagueId of friendlyLeagues) {
            console.log(`\n=== Fetching fixtures for League ${leagueId} ===`);

            // Try fetching for current season (2024) and next season (2025)
            const seasons = [2024, 2025];

            for (const season of seasons) {
                try {
                    console.log(`Fetching fixtures for League ${leagueId}, season ${season}...`);
                    const allFixturesFromApi = await fetchFixtures({
                        league: leagueId,
                        season: season
                    });

                    if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
                        console.log(`No fixtures received from API for League ${leagueId}, season ${season}.`);
                        continue;
                    }

                    console.log(`Received ${allFixturesFromApi.length} fixtures for League ${leagueId}, season ${season}.`);

                    // Filter fixtures if needed
                    const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);

                    if (fixturesFromApi.length < allFixturesFromApi.length) {
                        console.log(`Filtered down to ${fixturesFromApi.length} fixtures based on targeted leagues.`);
                    }

                    // Create bulk operations for MongoDB
                    const bulkOps = createFixtureBulkOps(fixturesFromApi, now);

                    if (bulkOps.length > 0) {
                        const result = await collection.bulkWrite(bulkOps);
                        console.log(`League ${leagueId} (season ${season}) finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                    } else {
                        console.log(`League ${leagueId} (season ${season}): No changes detected.`);
                    }

                } catch (error) {
                    console.error(`Error fetching fixtures for League ${leagueId}, season ${season}:`, error);
                }

                // Add delay between requests to respect API rate limits
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        console.log('\n=== Verifying results ===');
        
        // Check if we now have fixtures for these leagues
        for (const leagueId of friendlyLeagues) {
            const count = await collection.countDocuments({ 'league.id': leagueId });
            console.log(`League ${leagueId} now has ${count} fixtures in database`);
            
            if (count > 0) {
                const sample = await collection.findOne({ 'league.id': leagueId });
                console.log(`  Sample: ${sample?.teams.home.name} vs ${sample?.teams.away.name} on ${sample?.fixture.date}`);
            }
        }

        process.exit(0);

    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

// Run the function
fetchFriendlyFixtures();
