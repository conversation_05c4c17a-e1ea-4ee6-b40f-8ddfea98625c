import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateFixturesByDate } from '../jobs/fixtureJobs';
import dayjs from 'dayjs';

// Load environment variables
dotenv.config();

// Main function
async function runFixtureJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        const today = dayjs().format('YYYY-MM-DD');
        console.log(`Running fetchAndUpdateFixturesByDate job for date: ${today}...`);
        await fetchAndUpdateFixturesByDate(today);
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runFixtureJob();
