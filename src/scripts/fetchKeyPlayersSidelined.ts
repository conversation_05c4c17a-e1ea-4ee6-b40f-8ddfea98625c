import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchSidelined } from '../services/sidelinedService';
import { getSidelinedCollection, Sidelined, createSidelinedId } from '../models/Sidelined';
import { getTeamsCollection } from '../models/Team';
import { AnyBulkWriteOperation } from 'mongodb';

// Load environment variables
dotenv.config();

// Key Premier League players (ID, name, team)
const KEY_PLAYERS = [
    { id: 19545, name: '<PERSON>', team: { id: 49, name: 'Chelsea' } },
    { id: 1100, name: '<PERSON>', team: { id: 50, name: 'Manchester City' } },
    { id: 909, name: '<PERSON>', team: { id: 40, name: 'Liverpool' } },
    { id: 1485, name: '<PERSON>', team: { id: 47, name: 'Tottenham' } },
    { id: 2413, name: '<PERSON>', team: { id: 33, name: 'Manchester United' } },
    { id: 18846, name: '<PERSON><PERSON><PERSON>', team: { id: 42, name: 'Arsenal' } },
    { id: 19194, name: '<PERSON><PERSON><PERSON>', team: { id: 50, name: 'Manchester City' } },
    { id: 1460, name: '<PERSON> <PERSON>-min', team: { id: 47, name: 'Tottenham' } },
    { id: 19599, name: 'Phil Foden', team: { id: 50, name: 'Manchester City' } },
    { id: 2939, name: '<PERSON> Øde<PERSON>', team: { id: 42, name: 'Arsenal' } },
    { id: 642, name: '<PERSON> van Dijk', team: { id: 40, name: 'Liverpool' } },
    { id: 629, name: '<PERSON>sson', team: { id: 40, name: 'Liverpool' } },
    { id: 882, name: 'Trent Alexander-Arnold', team: { id: 40, name: 'Liverpool' } },
    { id: 2778, name: 'Rodri', team: { id: 50, name: 'Manchester City' } },
    { id: 1422, name: 'Declan Rice', team: { id: 42, name: 'Arsenal' } },
    { id: 19366, name: 'Cole Palmer', team: { id: 49, name: 'Chelsea' } },
    { id: 19431, name: 'William Saliba', team: { id: 42, name: 'Arsenal' } },
    { id: 19760, name: 'Rasmus Højlund', team: { id: 33, name: 'Manchester United' } },
    { id: 19619, name: 'Moisés Caicedo', team: { id: 49, name: 'Chelsea' } },
    { id: 19765, name: 'Enzo Fernández', team: { id: 49, name: 'Chelsea' } }
];

// Main function
async function fetchKeyPlayersSidelined() {
    try {
        console.log('Connecting to database...');
        await connectDB();

        // Get team logos from database
        const teamsCollection = getTeamsCollection();
        const sidelinedCollection = getSidelinedCollection();
        const now = new Date();

        // Process players in batches of 20 (API limit)
        const BATCH_SIZE = 20;
        let totalUpserted = 0;
        let totalModified = 0;

        for (let i = 0; i < KEY_PLAYERS.length; i += BATCH_SIZE) {
            const playerBatch = KEY_PLAYERS.slice(i, i + BATCH_SIZE);
            const playerIds = playerBatch.map(player => player.id);
            const idsString = playerIds.join('-');

            console.log(`Fetching sidelined data for batch ${Math.floor(i/BATCH_SIZE) + 1}: ${idsString}`);

            try {
                // Use the 'players' parameter for batch request
                const sidelinedPlayers = await fetchSidelined({ players: idsString });

                if (!sidelinedPlayers || sidelinedPlayers.length === 0) {
                    console.log(`No sidelined data found for players in batch ${Math.floor(i/BATCH_SIZE) + 1}.`);
                    continue;
                }

                console.log(`Received ${sidelinedPlayers.length} sidelined records for batch ${Math.floor(i/BATCH_SIZE) + 1}.`);

                // The response format for 'players' parameter is different
                // Each item in the response has an 'id' and 'sidelined' array
                const sidelinedByPlayer: Record<number, any[]> = {};

                for (const playerSidelined of sidelinedPlayers) {
                    const playerId = playerSidelined.id;
                    sidelinedByPlayer[playerId] = playerSidelined.sidelined;
                }

                // Process each player's sidelined records
                const bulkOps: AnyBulkWriteOperation<Sidelined>[] = [];

                for (const player of playerBatch) {
                    const playerId = player.id;
                    const playerName = player.name;
                    const teamId = player.team.id;
                    const teamName = player.team.name;

                    // Get team logo from database
                    const teamData = await teamsCollection.findOne({ _id: teamId });
                    const teamLogo = teamData?.team?.logo || null;

                    // Create team object for sidelined records
                    const teamObj = {
                        id: teamId,
                        name: teamName,
                        logo: teamLogo
                    };

                    // Get sidelined records for this player
                    const playerSidelined = sidelinedByPlayer[playerId] || [];

                    if (playerSidelined.length === 0) {
                        console.log(`No sidelined records for Player ${playerName}.`);
                        continue;
                    }

                    console.log(`Found ${playerSidelined.length} sidelined record(s) for Player ${playerName}.`);

                    // Process each sidelined record
                    for (const sidelined of playerSidelined) {
                        const type = sidelined.type || 'Unknown';
                        const start = sidelined.start || new Date().toISOString();
                        const sidelinedId = createSidelinedId(playerId, type, start);

                        // Create sidelined document
                        const sidelinedDoc: Sidelined = {
                            _id: sidelinedId,
                            player: {
                                id: playerId,
                                name: playerName,
                                photo: `https://media.api-sports.io/football/players/${playerId}.png`
                            },
                            team: teamObj,
                            type: type,
                            reason: null, // The batch API doesn't return reason
                            start: start,
                            end: sidelined.end || null,
                            lastUpdated: now
                        };

                        bulkOps.push({
                            updateOne: {
                                filter: { _id: sidelinedId },
                                update: { $set: sidelinedDoc },
                                upsert: true
                            }
                        });
                    }
                }

                // Execute bulk operations if any
                if (bulkOps.length > 0) {
                    const result = await sidelinedCollection.bulkWrite(bulkOps);
                    console.log(`Bulk write result for batch ${Math.floor(i/BATCH_SIZE) + 1}:`, {
                        upserted: result.upsertedCount,
                        modified: result.modifiedCount
                    });

                    totalUpserted += result.upsertedCount;
                    totalModified += result.modifiedCount;
                }

            } catch (error) {
                console.error(`Error fetching sidelined data for batch ${Math.floor(i/BATCH_SIZE) + 1}:`, error);
            }

            // Add a small delay between batches to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        console.log(`Job completed. Total records: Upserted=${totalUpserted}, Modified=${totalModified}`);
        process.exit(0);

    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
fetchKeyPlayersSidelined();
