import dotenv from 'dotenv';
import connectDB from '../config/database';
import { getFixturesCollection } from '../models/Fixture';

// Load environment variables
dotenv.config();

/**
 * Script to clean up fixtures that are stuck in live statuses but are actually future fixtures
 * This addresses the issue where fixtures with future dates (2025+) are showing as live
 */
async function cleanupStuckFixtures() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        const collection = getFixturesCollection();
        const now = new Date();
        const maxValidLiveDate = new Date(now.getTime() + (24 * 60 * 60 * 1000)); // 24 hours from now
        
        console.log('Searching for stuck fixtures...');
        
        // Find fixtures that have live statuses but are scheduled for the future
        const liveStatuses = ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT', 'Aw. ET'];
        const stuckFixtures = await collection.find({
            'fixture.status.short': { $in: liveStatuses },
            'fixture.timestamp': { $gt: Math.floor(maxValidLiveDate.getTime() / 1000) }
        }).toArray();
        
        console.log(`Found ${stuckFixtures.length} stuck fixtures with future dates:`);
        
        if (stuckFixtures.length > 0) {
            stuckFixtures.forEach(fixture => {
                const fixtureDate = new Date(fixture.fixture.timestamp * 1000);
                console.log(`- ID: ${fixture._id}, Status: ${fixture.fixture.status.short}, Date: ${fixtureDate.toISOString()}, Teams: ${fixture.teams.home.name} vs ${fixture.teams.away.name}`);
            });
            
            // Ask for confirmation before updating
            console.log('\nThese fixtures appear to be test/demo data or have incorrect dates.');
            console.log('They will be updated to "NS" (Not Started) status to remove them from live results.');
            
            // Update the stuck fixtures to "Not Started" status
            const updateResult = await collection.updateMany(
                {
                    'fixture.status.short': { $in: liveStatuses },
                    'fixture.timestamp': { $gt: Math.floor(maxValidLiveDate.getTime() / 1000) }
                },
                {
                    $set: {
                        'fixture.status.short': 'NS',
                        'fixture.status.long': 'Not Started',
                        'fixture.status.elapsed': null,
                        'lastUpdated': new Date()
                    }
                }
            );
            
            console.log(`\n✅ Updated ${updateResult.modifiedCount} fixtures to "Not Started" status.`);
            
            // Also check for any fixtures with 'Aw. ET' status specifically
            const awETFixtures = await collection.find({
                'fixture.status.short': 'Aw. ET'
            }).toArray();
            
            console.log(`\nFound ${awETFixtures.length} fixtures with 'Aw. ET' status:`);
            awETFixtures.forEach(fixture => {
                const fixtureDate = new Date(fixture.fixture.timestamp * 1000);
                console.log(`- ID: ${fixture._id}, Date: ${fixtureDate.toISOString()}, Teams: ${fixture.teams.home.name} vs ${fixture.teams.away.name}`);
            });
            
        } else {
            console.log('✅ No stuck fixtures found.');
        }
        
        console.log('\nCleanup completed successfully.');
        process.exit(0);
        
    } catch (error) {
        console.error('Error during cleanup:', error);
        process.exit(1);
    }
}

// Run the cleanup
cleanupStuckFixtures();
