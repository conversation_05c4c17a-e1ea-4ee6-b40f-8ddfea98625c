import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchTeamStatistics } from '../services/apiFootball';
import { getTeamStatisticsCollection, createTeamStatisticId } from '../models/TeamStatistic';

// Load environment variables
dotenv.config();

// Main function
async function fetchAndSaveTeamStatistics() {
    const teamId = 49; // Chelsea
    const leagueId = 39; // Premier League
    const seasonYear = 2024; // Current season

    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log(`Fetching team statistics for Team ID: ${teamId}, League: ${leagueId}, Season: ${seasonYear}`);
        
        const statsFromApi = await fetchTeamStatistics({ league: leagueId, season: seasonYear, team: teamId });
        
        // API returns null or empty object if no stats found for the combo
        if (!statsFromApi || !statsFromApi.league || !statsFromApi.team) {
            console.log(`No stats found for Team ${teamId}, League ${leagueId}, Season ${seasonYear}.`);
            process.exit(1);
        }
        
        const statId = createTeamStatisticId(teamId, leagueId, seasonYear);
        const statsCollection = getTeamStatisticsCollection();
        
        const updateDoc = {
            ...statsFromApi, // Spread the fields from the API response
            _id: statId,
            lastUpdated: new Date(),
        };
        
        // Use updateOne with upsert for each stat record
        const result = await statsCollection.updateOne(
            { _id: statId },
            { $set: updateDoc },
            { upsert: true }
        );
        
        console.log(`Team statistics saved. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        process.exit(0);
        
    } catch (error) {
        console.error('Error fetching team statistics:', error);
        process.exit(1);
    }
}

// Run the script
fetchAndSaveTeamStatistics();
