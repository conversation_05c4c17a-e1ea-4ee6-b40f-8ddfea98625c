import mongoose from 'mongoose';
import axios from 'axios';
import dotenv from 'dotenv';

// Import models
const Sidelined = require('../models/Sidelined');
const Player = require('../models/Player');
const Team = require('../models/Team');

dotenv.config();

const API_FOOTBALL_KEY = process.env.API_FOOTBALL_KEY;
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/kickoffscore';

// Function to fetch sidelined data for a player
async function fetchSidelinedByPlayer(playerId: number) {
  try {
    console.log(`Fetching sidelined data for player ID: ${playerId}`);

    // Connect to MongoDB
    await mongoose.connect(MONGO_URI);
    console.log('MongoDB connected successfully');

    // Fetch player data to get name and photo
    const playerData = await Player.findOne({ 'player.id': playerId });

    if (!playerData) {
      console.log(`Player with ID ${playerId} not found in database. Fetching from API...`);

      // Fetch player data from API
      const playerResponse = await axios.get(`https://v3.football.api-sports.io/players`, {
        params: {
          id: playerId,
          season: new Date().getFullYear()
        },
        headers: {
          'x-apisports-key': API_FOOTBALL_KEY
        }
      });

      if (playerResponse.data.response.length === 0) {
        console.error(`No player found with ID ${playerId}`);
        await mongoose.disconnect();
        return;
      }

      const apiPlayerData = playerResponse.data.response[0];

      // Get team data
      const teamId = apiPlayerData.statistics[0]?.team?.id;
      let teamData = null;

      if (teamId) {
        teamData = await Team.findOne({ 'team.id': teamId });

        if (!teamData) {
          console.log(`Team with ID ${teamId} not found in database. Fetching from API...`);

          // Fetch team data from API
          const teamResponse = await axios.get(`https://v3.football.api-sports.io/teams`, {
            params: {
              id: teamId
            },
            headers: {
              'x-apisports-key': API_FOOTBALL_KEY
            }
          });

          if (teamResponse.data.response.length > 0) {
            teamData = teamResponse.data.response[0];
          }
        }
      }

      // Fetch sidelined data from API
      const sidelinedResponse = await axios.get(`https://v3.football.api-sports.io/sidelined`, {
        params: {
          player: playerId
        },
        headers: {
          'x-apisports-key': API_FOOTBALL_KEY
        }
      });

      console.log(`API Rate Limit Info - Daily Remaining: ${sidelinedResponse.headers['x-ratelimit-remaining-day']}, Minute Remaining: ${sidelinedResponse.headers['x-ratelimit-remaining-minute']}`);

      if (sidelinedResponse.data.response.length === 0) {
        console.log(`No sidelined data found for player ID ${playerId}`);
        await mongoose.disconnect();
        return;
      }

      // Process and save sidelined data
      const sidelinedData = sidelinedResponse.data.response;
      console.log(`Found ${sidelinedData.length} sidelined records for player ID ${playerId}`);

      const playerObj = {
        id: playerId,
        name: apiPlayerData.player.name,
        photo: apiPlayerData.player.photo
      };

      const teamObj = teamData ? {
        id: teamId,
        name: teamData.team?.name || teamData.name,
        logo: teamData.team?.logo || teamData.logo
      } : null;

      // Save each sidelined record
      for (const record of sidelinedData) {
        const sidelinedId = `${playerId}:${record.type}:${record.end}`;

        await Sidelined.findOneAndUpdate(
          { _id: sidelinedId },
          {
            _id: sidelinedId,
            player: playerObj,
            team: teamObj,
            type: record.type,
            start: record.start,
            end: record.end,
            reason: record.reason,
            lastUpdated: new Date()
          },
          { upsert: true, new: true }
        );
      }

      console.log(`Successfully saved ${sidelinedData.length} sidelined records for player ID ${playerId}`);
    } else {
      // Player exists in database
      console.log(`Found player ${playerData.player.name} (ID: ${playerId}) in database`);

      // Get team data
      const teamId = playerData.statistics?.[0]?.team?.id || playerData.player?.team?.id;
      let teamData = null;

      if (teamId) {
        teamData = await Team.findOne({ 'team.id': teamId });
      }

      // Fetch sidelined data from API
      const sidelinedResponse = await axios.get(`https://v3.football.api-sports.io/sidelined`, {
        params: {
          player: playerId
        },
        headers: {
          'x-apisports-key': API_FOOTBALL_KEY
        }
      });

      console.log(`API Rate Limit Info - Daily Remaining: ${sidelinedResponse.headers['x-ratelimit-remaining-day']}, Minute Remaining: ${sidelinedResponse.headers['x-ratelimit-remaining-minute']}`);

      if (sidelinedResponse.data.response.length === 0) {
        console.log(`No sidelined data found for player ID ${playerId}`);
        await mongoose.disconnect();
        return;
      }

      // Process and save sidelined data
      const sidelinedData = sidelinedResponse.data.response;
      console.log(`Found ${sidelinedData.length} sidelined records for player ID ${playerId}`);

      const playerObj = {
        id: playerId,
        name: playerData.player.name,
        photo: playerData.player.photo
      };

      const teamObj = teamData ? {
        id: teamId,
        name: teamData.team?.name || teamData.name,
        logo: teamData.team?.logo || teamData.logo
      } : null;

      // Save each sidelined record
      for (const record of sidelinedData) {
        const sidelinedId = `${playerId}:${record.type}:${record.end}`;

        await Sidelined.findOneAndUpdate(
          { _id: sidelinedId },
          {
            _id: sidelinedId,
            player: playerObj,
            team: teamObj,
            type: record.type,
            start: record.start,
            end: record.end,
            reason: record.reason,
            lastUpdated: new Date()
          },
          { upsert: true, new: true }
        );
      }

      console.log(`Successfully saved ${sidelinedData.length} sidelined records for player ID ${playerId}`);
    }

    await mongoose.disconnect();
    console.log('MongoDB disconnected');

  } catch (error) {
    console.error('Error fetching sidelined data:', error);
    await mongoose.disconnect();
  }
}

// Get player ID from command line arguments
const playerId = process.argv[2];

if (!playerId) {
  console.error('Please provide a player ID as a command line argument');
  process.exit(1);
}

fetchSidelinedByPlayer(parseInt(playerId));
