import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { fetchAndUpdateCupCompetitionFixtures } from '../jobs/fixtureJobs';

// Load environment variables
dotenv.config();

// Main function
async function runCupCompetitionFixturesJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Connecting to Redis...');
        await connectRedis();
        
        console.log('Running fetchAndUpdateCupCompetitionFixtures job...');
        await fetchAndUpdateCupCompetitionFixtures();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runCupCompetitionFixturesJob();
