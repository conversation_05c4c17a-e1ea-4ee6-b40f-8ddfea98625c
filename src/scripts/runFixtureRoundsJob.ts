import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateFixtureRounds } from '../jobs/fixtureRoundJobs';

// Load environment variables
dotenv.config();

// Main function
async function runFixtureRoundsJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateFixtureRounds job...');
        await fetchAndUpdateFixtureRounds();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runFixtureRoundsJob();
