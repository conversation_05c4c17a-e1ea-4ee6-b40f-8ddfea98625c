import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { targetedLeagues } from '../config/targetedLeagues';

// Import all the job functions we need
import { fetchAndUpdateLeagues } from '../jobs/leagueJobs';
import { fetchAndUpdateLeagueSeasons } from '../jobs/leagueSeasonJobs';
import { fetchAndUpdateLeagueCoverage } from '../jobs/leagueCoverageJobs';
import { fetchAndUpdateTeamsForActiveLeagues } from '../jobs/teamJobs';
import { fetchAndUpdateStandings } from '../jobs/standingJobs';
import { fetchAndUpdateFixtureRounds } from '../jobs/fixtureRoundJobs';
import { fetchAndUpdatePlayerProfiles, fetchAndUpdatePlayerStatistics, fetchAndUpdatePlayerSquads, fetchAndUpdateTopPlayers } from '../jobs/playerJobs';
import { fetchAndUpdateCoaches } from '../jobs/coachJobs';
import { fetchAndUpdateTeamStatistics } from '../jobs/teamStatisticJobs';
import { fetchAndUpdateTeamTransfers, fetchAndUpdatePlayerTransfers } from '../jobs/transferJobs';
import { fetchAndUpdateTeamSidelined, fetchAndUpdateLeagueSidelined } from '../jobs/sidelinedJobs';

// Import fixture fetching functions
import { fetchFixtures } from '../services/apiFootball';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { getLeaguesCollection } from '../models/League';
import { filterByTargetedLeagues } from '../config/targetedLeagues';
import { AnyBulkWriteOperation } from 'mongodb';
import { fetchAndUpdateFixturesById } from '../jobs/fixtureJobs';

// Load environment variables
dotenv.config();

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to create bulk operations for fixtures
function createFixtureBulkOps(fixturesFromApi: any[], now: Date): AnyBulkWriteOperation<Fixture>[] {
    const bulkOps: AnyBulkWriteOperation<Fixture>[] = [];

    for (const fixtureApi of fixturesFromApi) {
        const filter = { _id: fixtureApi.fixture.id };
        const fixtureDate = new Date(fixtureApi.fixture.timestamp * 1000);
        fixtureDate.setHours(0, 0, 0, 0);

        const baseUpdateDoc = {
            apiId: fixtureApi.fixture.id,
            fixture: fixtureApi.fixture,
            league: fixtureApi.league,
            teams: fixtureApi.teams,
            goals: fixtureApi.goals,
            score: fixtureApi.score,
            date: fixtureDate,
            lastUpdated: now,
        };

        const updateDoc: any = { ...baseUpdateDoc };
        if (fixtureApi.events) updateDoc.events = fixtureApi.events;
        if (fixtureApi.lineups) updateDoc.lineups = fixtureApi.lineups;
        if (fixtureApi.statistics) updateDoc.statistics = fixtureApi.statistics;
        if (fixtureApi.players) updateDoc.players = fixtureApi.players;

        bulkOps.push({
            updateOne: {
                filter: filter,
                update: { $set: updateDoc },
                upsert: true,
            },
        });
    }

    return bulkOps;
}

// Function to fetch fixtures for a specific league and season
async function fetchLeagueFixtures(leagueId: number, season: number): Promise<number> {
    try {
        console.log(`  📅 Fetching fixtures for League ${leagueId}, Season ${season}...`);

        const allFixturesFromApi = await fetchFixtures({
            league: leagueId,
            season: season
        });

        if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
            console.log(`    ⚠️  No fixtures found for League ${leagueId}, Season ${season}`);
            return 0;
        }

        console.log(`    ✅ Found ${allFixturesFromApi.length} fixtures`);

        const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);
        const collection = getFixturesCollection();
        const now = new Date();
        const bulkOps = createFixtureBulkOps(fixturesFromApi, now);

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`    💾 Saved: ${result.upsertedCount} new, ${result.modifiedCount} updated`);

            // Fetch detailed data for finished fixtures
            const finishedFixtures = fixturesFromApi.filter(fixture =>
                ['FT', 'AET', 'PEN', 'WO', 'AWD'].includes(fixture.fixture.status.short)
            );

            if (finishedFixtures.length > 0) {
                console.log(`    🔍 Fetching detailed data for ${finishedFixtures.length} finished fixtures...`);
                const finishedFixtureIds = finishedFixtures.map(fixture => fixture.fixture.id);
                await fetchAndUpdateFixturesById(finishedFixtureIds);
            }

            return finishedFixtures.length;
        }

        return 0;
    } catch (error) {
        console.error(`    ❌ Error fetching fixtures for League ${leagueId}, Season ${season}:`, error);
        return 0;
    }
}

// Function to check if a league has data for a specific season
async function checkLeagueSeasonAvailability(leagueId: number, season: number): Promise<boolean> {
    try {
        const leaguesCollection = getLeaguesCollection();
        const league = await leaguesCollection.findOne({ _id: leagueId });
        
        if (!league || !league.seasons) {
            return false;
        }

        // Check if the season exists for this league
        const seasonExists = league.seasons.some(s => s.year === season);
        return seasonExists;
    } catch (error) {
        console.error(`Error checking season availability for League ${leagueId}, Season ${season}:`, error);
        return false;
    }
}

// Main function to fetch complete season data
async function fetchCompleteSeasonData(season: number, specificLeagues?: number[]) {
    console.log(`\n🚀 Starting Complete Season Data Fetch for Season ${season}`);
    console.log(`📊 Processing ${specificLeagues ? specificLeagues.length : targetedLeagues.length} leagues\n`);

    const startTime = Date.now();
    const leaguesToProcess = specificLeagues || targetedLeagues;

    try {
        // Step 1: Update foundational data
        console.log('📋 Step 1: Updating foundational data...');
        
        console.log('  🌍 Updating league seasons...');
        await fetchAndUpdateLeagueSeasons();
        await delay(1000);

        console.log('  🏆 Updating leagues information...');
        await fetchAndUpdateLeagues();
        await delay(1000);

        console.log('  📈 Updating league coverage...');
        await fetchAndUpdateLeagueCoverage();
        await delay(2000);

        // Step 2: Update teams
        console.log('\n⚽ Step 2: Updating teams...');

        console.log('  ⚽ Updating teams...');
        await fetchAndUpdateTeamsForActiveLeagues();
        await delay(2000);

        // Step 3: Fetch fixtures for each league
        console.log('\n📅 Step 3: Fetching fixtures for all leagues...');
        let totalFixtures = 0;
        let processedLeagues = 0;
        let skippedLeagues = 0;

        for (const leagueId of leaguesToProcess) {
            console.log(`\n🏆 Processing League ${leagueId} (${processedLeagues + 1}/${leaguesToProcess.length})`);
            
            // Check if this league has data for the specified season
            const hasSeasonData = await checkLeagueSeasonAvailability(leagueId, season);
            
            if (!hasSeasonData) {
                console.log(`  ⚠️  League ${leagueId} does not have data for season ${season}, skipping...`);
                skippedLeagues++;
                continue;
            }

            const fixturesCount = await fetchLeagueFixtures(leagueId, season);
            totalFixtures += fixturesCount;
            processedLeagues++;

            // Add delay between leagues to respect API rate limits
            if (processedLeagues < leaguesToProcess.length - skippedLeagues) {
                console.log('  ⏳ Waiting 2 seconds before next league...');
                await delay(2000);
            }
        }

        console.log(`\n📊 Fixtures Summary: ${totalFixtures} finished fixtures processed across ${processedLeagues} leagues`);
        console.log(`⚠️  Skipped ${skippedLeagues} leagues (no data for season ${season})`);

        // Step 4: Update standings and rounds
        console.log('\n📊 Step 4: Updating standings and fixture rounds...');

        console.log('  🏅 Updating standings...');
        await fetchAndUpdateStandings();
        await delay(2000);

        console.log('  🔄 Updating fixture rounds...');
        await fetchAndUpdateFixtureRounds();
        await delay(2000);

        // Step 5: Update player data
        console.log('\n👥 Step 5: Updating player data...');

        console.log('  👤 Updating player profiles...');
        await fetchAndUpdatePlayerProfiles();
        await delay(3000);

        console.log('  📊 Updating player statistics...');
        await fetchAndUpdatePlayerStatistics();
        await delay(3000);

        console.log('  🏃 Updating player squads...');
        await fetchAndUpdatePlayerSquads();
        await delay(3000);

        console.log('  🏆 Updating top players...');
        await fetchAndUpdateTopPlayers();
        await delay(2000);

        // Step 6: Update coaches and team statistics
        console.log('\n👨‍💼 Step 6: Updating coaches and team statistics...');

        console.log('  👨‍💼 Updating coaches...');
        await fetchAndUpdateCoaches();
        await delay(3000);

        console.log('  📈 Updating team statistics...');
        await fetchAndUpdateTeamStatistics();
        await delay(3000);

        // Step 7: Update transfers and injuries
        console.log('\n🔄 Step 7: Updating transfers and injuries...');

        console.log('  🔄 Updating team transfers...');
        await fetchAndUpdateTeamTransfers();
        await delay(3000);

        console.log('  🔄 Updating player transfers...');
        await fetchAndUpdatePlayerTransfers();
        await delay(3000);

        console.log('  🏥 Updating team sidelined players...');
        await fetchAndUpdateTeamSidelined();
        await delay(2000);

        console.log('  🏥 Updating league sidelined players...');
        await fetchAndUpdateLeagueSidelined();
        await delay(2000);

        console.log('\n✅ Skipping odds and predictions (not relevant for historical/future seasons)');

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000 / 60); // Convert to minutes

        console.log(`\n✅ Complete Season Data Fetch Completed Successfully!`);
        console.log(`⏱️  Total time: ${duration} minutes`);
        console.log(`🏆 Processed ${processedLeagues} leagues for season ${season}`);
        console.log(`📅 Total finished fixtures: ${totalFixtures}`);
        console.log(`⚠️  Skipped leagues: ${skippedLeagues}`);

    } catch (error) {
        console.error('\n❌ Error during complete season data fetch:', error);
        throw error;
    }
}

// Function to run the script with command line arguments
async function run() {
    try {
        console.log('🔌 Connecting to database and Redis...');
        await connectDB();
        await connectRedis();

        const args = process.argv.slice(2);

        if (args.length === 0) {
            console.log('\n❌ Error: Season is required');
            console.log('\n📖 Usage:');
            console.log('  npm run fetch-complete-season <season>');
            console.log('  npm run fetch-complete-season <season> <league1,league2,league3>');
            console.log('\n📝 Examples:');
            console.log('  npm run fetch-complete-season 2024');
            console.log('  npm run fetch-complete-season 2025');
            console.log('  npm run fetch-complete-season 2024 39,140,78,135,61');
            console.log('  npm run fetch-complete-season 2015 39,140');
            process.exit(1);
        }

        const season = parseInt(args[0]);
        if (isNaN(season)) {
            console.log('❌ Error: Season must be a number');
            process.exit(1);
        }

        let specificLeagues: number[] | undefined;
        if (args.length > 1) {
            const leagueIds = args[1].split(',').map(id => parseInt(id.trim()));
            if (leagueIds.some(id => isNaN(id))) {
                console.log('❌ Error: All league IDs must be numbers');
                process.exit(1);
            }
            specificLeagues = leagueIds;
            console.log(`🎯 Processing specific leagues: ${specificLeagues.join(', ')}`);
        }

        await fetchCompleteSeasonData(season, specificLeagues);

        console.log('\n🎉 Script completed successfully!');
        process.exit(0);

    } catch (error) {
        console.error('\n💥 Fatal error:', error);
        process.exit(1);
    }
}

// Run the script
run();
