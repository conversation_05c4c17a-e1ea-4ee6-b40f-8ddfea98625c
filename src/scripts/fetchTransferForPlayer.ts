import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchTransfers } from '../services/transferService';
import { getTransfersCollection, Transfer, createTransferId } from '../models/Transfer';

// Load environment variables
dotenv.config();

// Main function
async function fetchTransferForPlayer() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        const playerId = 18; // Paul <PERSON>
        
        console.log(`Fetching transfers for Player ID: ${playerId}`);
        const transfers = await fetchTransfers({ player: playerId });
        
        if (!transfers || transfers.length === 0) {
            console.log(`No transfer information received for Player ${playerId}.`);
            return;
        }
        
        console.log(`Received ${transfers.length} transfer record(s) for Player ${playerId}:`);
        console.log(JSON.stringify(transfers, null, 2));
        
        // Store in database
        const transfersCollection = getTransfersCollection();
        const now = new Date();
        
        for (const transfer of transfers) {
            const updateDate = transfer.update;
            const transferId = createTransferId(playerId, updateDate);
            
            // Create transfer document
            const transferDoc: Transfer = {
                _id: transferId,
                player: transfer.player,
                update: transfer.update,
                transfers: transfer.transfers,
                lastUpdated: now
            };
            
            const result = await transfersCollection.updateOne(
                { _id: transferId },
                { $set: transferDoc },
                { upsert: true }
            );
            
            console.log(`Transfer ${transferId} updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }
        
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
fetchTransferForPlayer();
