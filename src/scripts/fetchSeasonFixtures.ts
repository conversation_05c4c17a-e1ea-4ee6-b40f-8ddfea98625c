import dotenv from 'dotenv';
import connectDB from '../config/database';
import { targetedLeagues } from '../config/targetedLeagues';
import { fetchFixtures } from '../services/apiFootball';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { getLeaguesCollection } from '../models/League';
import { filterByTargetedLeagues } from '../config/targetedLeagues';
import { AnyBulkWriteOperation } from 'mongodb';
import { fetchAndUpdateFixturesById } from '../jobs/fixtureJobs';

// Load environment variables
dotenv.config();

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to create bulk operations for fixtures
function createFixtureBulkOps(fixturesFromApi: any[], now: Date): AnyBulkWriteOperation<Fixture>[] {
    const bulkOps: AnyBulkWriteOperation<Fixture>[] = [];

    for (const fixtureApi of fixturesFromApi) {
        const filter = { _id: fixtureApi.fixture.id };
        const fixtureDate = new Date(fixtureApi.fixture.timestamp * 1000);
        fixtureDate.setHours(0, 0, 0, 0);

        const baseUpdateDoc = {
            apiId: fixtureApi.fixture.id,
            fixture: fixtureApi.fixture,
            league: fixtureApi.league,
            teams: fixtureApi.teams,
            goals: fixtureApi.goals,
            score: fixtureApi.score,
            date: fixtureDate,
            lastUpdated: now,
        };

        const updateDoc: any = { ...baseUpdateDoc };
        if (fixtureApi.events) updateDoc.events = fixtureApi.events;
        if (fixtureApi.lineups) updateDoc.lineups = fixtureApi.lineups;
        if (fixtureApi.statistics) updateDoc.statistics = fixtureApi.statistics;
        if (fixtureApi.players) updateDoc.players = fixtureApi.players;

        bulkOps.push({
            updateOne: {
                filter: filter,
                update: { $set: updateDoc },
                upsert: true,
            },
        });
    }

    return bulkOps;
}

// Function to fetch fixtures for a specific league and season
async function fetchLeagueFixtures(leagueId: number, season: number): Promise<{ total: number, finished: number }> {
    try {
        console.log(`  📅 Fetching fixtures for League ${leagueId}, Season ${season}...`);

        const allFixturesFromApi = await fetchFixtures({
            league: leagueId,
            season: season
        });

        if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
            console.log(`    ⚠️  No fixtures found for League ${leagueId}, Season ${season}`);
            return { total: 0, finished: 0 };
        }

        console.log(`    ✅ Found ${allFixturesFromApi.length} fixtures`);

        const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);
        const collection = getFixturesCollection();
        const now = new Date();
        const bulkOps = createFixtureBulkOps(fixturesFromApi, now);

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`    💾 Saved: ${result.upsertedCount} new, ${result.modifiedCount} updated`);

            // Fetch detailed data for finished fixtures
            const finishedFixtures = fixturesFromApi.filter(fixture =>
                ['FT', 'AET', 'PEN', 'WO', 'AWD'].includes(fixture.fixture.status.short)
            );

            if (finishedFixtures.length > 0) {
                console.log(`    🔍 Fetching detailed data for ${finishedFixtures.length} finished fixtures...`);
                const finishedFixtureIds = finishedFixtures.map(fixture => fixture.fixture.id);
                await fetchAndUpdateFixturesById(finishedFixtureIds);
            }

            return { total: fixturesFromApi.length, finished: finishedFixtures.length };
        }

        return { total: fixturesFromApi.length, finished: 0 };
    } catch (error) {
        console.error(`    ❌ Error fetching fixtures for League ${leagueId}, Season ${season}:`, error);
        return { total: 0, finished: 0 };
    }
}

// Function to check if a league has data for a specific season
async function checkLeagueSeasonAvailability(leagueId: number, season: number): Promise<boolean> {
    try {
        const leaguesCollection = getLeaguesCollection();
        const league = await leaguesCollection.findOne({ _id: leagueId });
        
        if (!league || !league.seasons) {
            return false;
        }

        // Check if the season exists for this league
        const seasonExists = league.seasons.some(s => s.year === season);
        return seasonExists;
    } catch (error) {
        console.error(`Error checking season availability for League ${leagueId}, Season ${season}:`, error);
        return false;
    }
}

// Main function to fetch season fixtures
async function fetchSeasonFixtures(season: number, specificLeagues?: number[]) {
    console.log(`\n🚀 Starting Season Fixtures Fetch for Season ${season}`);
    console.log(`📊 Processing ${specificLeagues ? specificLeagues.length : targetedLeagues.length} leagues\n`);

    const startTime = Date.now();
    const leaguesToProcess = specificLeagues || targetedLeagues;

    try {
        let totalFixtures = 0;
        let totalFinishedFixtures = 0;
        let processedLeagues = 0;
        let skippedLeagues = 0;

        for (const leagueId of leaguesToProcess) {
            console.log(`\n🏆 Processing League ${leagueId} (${processedLeagues + skippedLeagues + 1}/${leaguesToProcess.length})`);
            
            // Check if this league has data for the specified season
            const hasSeasonData = await checkLeagueSeasonAvailability(leagueId, season);
            
            if (!hasSeasonData) {
                console.log(`  ⚠️  League ${leagueId} does not have data for season ${season}, skipping...`);
                skippedLeagues++;
                continue;
            }

            const { total, finished } = await fetchLeagueFixtures(leagueId, season);
            totalFixtures += total;
            totalFinishedFixtures += finished;
            processedLeagues++;

            // Add delay between leagues to respect API rate limits
            if (processedLeagues + skippedLeagues < leaguesToProcess.length) {
                console.log('  ⏳ Waiting 2 seconds before next league...');
                await delay(2000);
            }
        }

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000 / 60); // Convert to minutes

        console.log(`\n✅ Season Fixtures Fetch Completed Successfully!`);
        console.log(`⏱️  Total time: ${duration} minutes`);
        console.log(`🏆 Processed ${processedLeagues} leagues for season ${season}`);
        console.log(`📅 Total fixtures: ${totalFixtures}`);
        console.log(`🏁 Finished fixtures with detailed data: ${totalFinishedFixtures}`);
        console.log(`⚠️  Skipped leagues: ${skippedLeagues}`);

    } catch (error) {
        console.error('\n❌ Error during season fixtures fetch:', error);
        throw error;
    }
}

// Function to run the script with command line arguments
async function run() {
    try {
        console.log('🔌 Connecting to database...');
        await connectDB();

        const args = process.argv.slice(2);

        if (args.length === 0) {
            console.log('\n❌ Error: Season is required');
            console.log('\n📖 Usage:');
            console.log('  npm run fetch-season-fixtures <season>');
            console.log('  npm run fetch-season-fixtures <season> <league1,league2,league3>');
            console.log('\n📝 Examples:');
            console.log('  npm run fetch-season-fixtures 2024');
            console.log('  npm run fetch-season-fixtures 2025');
            console.log('  npm run fetch-season-fixtures 2024 39,140,78,135,61');
            console.log('  npm run fetch-season-fixtures 2015 39,140');
            console.log('\n💡 This script only fetches fixtures. For complete season data, use:');
            console.log('  npm run fetch-complete-season <season>');
            process.exit(1);
        }

        const season = parseInt(args[0]);
        if (isNaN(season)) {
            console.log('❌ Error: Season must be a number');
            process.exit(1);
        }

        let specificLeagues: number[] | undefined;
        if (args.length > 1) {
            const leagueIds = args[1].split(',').map(id => parseInt(id.trim()));
            if (leagueIds.some(id => isNaN(id))) {
                console.log('❌ Error: All league IDs must be numbers');
                process.exit(1);
            }
            specificLeagues = leagueIds;
            console.log(`🎯 Processing specific leagues: ${specificLeagues.join(', ')}`);
        }

        await fetchSeasonFixtures(season, specificLeagues);
        
        console.log('\n🎉 Script completed successfully!');
        process.exit(0);

    } catch (error) {
        console.error('\n💥 Fatal error:', error);
        process.exit(1);
    }
}

// Run the script
run();
