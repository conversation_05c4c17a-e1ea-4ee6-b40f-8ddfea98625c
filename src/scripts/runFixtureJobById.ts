/**
 * <PERSON><PERSON><PERSON> to run the fixture job for a specific fixture ID
 * Usage: node dist/scripts/runFixtureJobById.js <fixtureId>
 */

import connectToDatabase from '../config/database';
import { fetchAndUpdateFixturesById } from '../jobs/fixtureJobs';
import connectToRedis from '../config/redis';

const fixtureId = parseInt(process.argv[2]);

if (!fixtureId) {
  console.error('Please provide a fixture ID');
  process.exit(1);
}

async function main() {
  console.log('Connecting to database...');
  await connectToDatabase();
  console.log('MongoDB connected successfully.');

  console.log('Connecting to Redis...');
  await connectToRedis();
  console.log('Redis connected successfully.');

  console.log(`Running fixture job for fixture ID: ${fixtureId}`);
  await fetchAndUpdateFixturesById([fixtureId]);
  console.log('Job completed successfully');

  process.exit(0);
}

main().catch(error => {
  console.error('Error running fixture job:', error);
  process.exit(1);
});
