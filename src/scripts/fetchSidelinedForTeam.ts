import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchSidelined } from '../services/sidelinedService';
import { getSidelinedCollection, Sidelined, createSidelinedId } from '../models/Sidelined';

// Load environment variables
dotenv.config();

// Main function
async function fetchSidelinedForTeam() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        const teamId = 33; // Manchester United
        
        console.log(`Fetching sidelined players for Team ID: ${teamId}`);
        const sidelinedPlayers = await fetchSidelined({ team: teamId });
        
        if (!sidelinedPlayers || sidelinedPlayers.length === 0) {
            console.log(`No sidelined players received for Team ${teamId}.`);
            return;
        }
        
        console.log(`Received ${sidelinedPlayers.length} sidelined player(s) for Team ${teamId}:`);
        console.log(JSON.stringify(sidelinedPlayers.slice(0, 2), null, 2)); // Show first 2 sidelined players
        
        // Store in database
        const sidelinedCollection = getSidelinedCollection();
        const now = new Date();
        
        for (const sidelined of sidelinedPlayers) {
            const playerId = sidelined.player.id;
            const type = sidelined.type;
            const start = sidelined.start;
            const sidelinedId = createSidelinedId(playerId, type, start);
            
            // Create sidelined document
            const sidelinedDoc: Sidelined = {
                _id: sidelinedId,
                player: sidelined.player,
                team: sidelined.team,
                type: sidelined.type,
                reason: sidelined.reason,
                start: sidelined.start,
                end: sidelined.end,
                lastUpdated: now
            };
            
            const result = await sidelinedCollection.updateOne(
                { _id: sidelinedId },
                { $set: sidelinedDoc },
                { upsert: true }
            );
            
            console.log(`Sidelined ${sidelinedId} updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }
        
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
fetchSidelinedForTeam();
