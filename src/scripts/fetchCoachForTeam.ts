import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchCoaches } from '../services/coachService';
import { getCoachesCollection, Coach } from '../models/Coach';

// Load environment variables
dotenv.config();

// Main function
async function fetchCoachForTeam() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        const teamId = 33; // Manchester United
        
        console.log(`Fetching coach for Team ID: ${teamId}`);
        const coaches = await fetchCoaches({ team: teamId });
        
        if (!coaches || coaches.length === 0) {
            console.log(`No coach information received for Team ${teamId}.`);
            return;
        }
        
        console.log(`Received ${coaches.length} coach(es) for Team ${teamId}:`);
        console.log(JSON.stringify(coaches, null, 2));
        
        // Store in database
        const coachesCollection = getCoachesCollection();
        const now = new Date();
        
        for (const coach of coaches) {
            const coachId = coach.id;
            
            // Create coach document
            const coachDoc: Coach = {
                _id: coachId,
                profile: {
                    id: coach.id,
                    name: coach.name,
                    firstname: coach.firstname,
                    lastname: coach.lastname,
                    age: coach.age,
                    birth: coach.birth,
                    nationality: coach.nationality,
                    height: coach.height,
                    weight: coach.weight,
                    photo: coach.photo
                },
                career: coach.career || [],
                lastUpdated: now
            };
            
            const result = await coachesCollection.updateOne(
                { _id: coachId },
                { $set: coachDoc },
                { upsert: true }
            );
            
            console.log(`Coach ${coachId} updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }
        
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
fetchCoachForTeam();
