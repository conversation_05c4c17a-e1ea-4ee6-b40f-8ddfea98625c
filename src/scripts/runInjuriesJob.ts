import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateInjuriesForUpcomingFixtures } from '../jobs/injuryJobs';

// Load environment variables
dotenv.config();

// Main function
async function runInjuriesJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateInjuriesForUpcomingFixtures job...');
        await fetchAndUpdateInjuriesForUpcomingFixtures();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runInjuriesJob();
