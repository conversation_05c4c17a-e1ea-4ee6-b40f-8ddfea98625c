import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateCoaches } from '../jobs/coachJobs';

// Load environment variables
dotenv.config();

// Main function
async function runCoachJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateCoaches job...');
        await fetchAndUpdateCoaches();
        
        console.log('Coach job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runCoachJob();
