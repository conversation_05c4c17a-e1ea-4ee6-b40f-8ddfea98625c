import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { fetchAndUpdatePredictionsForUpcomingFixtures } from '../jobs/predictionJobs';

// Load environment variables
dotenv.config();

// Main function
async function runPredictionJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Connecting to Redis...');
        connectRedis();
        
        console.log('Running fetchAndUpdatePredictionsForUpcomingFixtures job...');
        // Fetch predictions for fixtures in the next 2 days
        await fetchAndUpdatePredictionsForUpcomingFixtures(2);
        
        console.log('Prediction job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running prediction job:', error);
        process.exit(1);
    }
}

// Run the job
runPredictionJob();
