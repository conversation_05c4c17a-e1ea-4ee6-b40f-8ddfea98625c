import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateCountries } from '../jobs/countryJobs';

// Load environment variables
dotenv.config();

// Main function
async function runCountryJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateCountries job...');
        await fetchAndUpdateCountries();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runCountryJob();
