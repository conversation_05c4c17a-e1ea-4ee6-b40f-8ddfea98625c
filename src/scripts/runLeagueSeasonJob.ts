import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateLeagueSeasons } from '../jobs/leagueSeasonJobs';

// Load environment variables
dotenv.config();

// Main function
async function runLeagueSeasonJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateLeagueSeasons job...');
        await fetchAndUpdateLeagueSeasons();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runLeagueSeasonJob();
