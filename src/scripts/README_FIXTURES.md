# Fixture Fetching Scripts

This directory contains scripts to fetch fixtures from the API-Football service and store them in your MongoDB database.

## fetchLeagueFixturesNoServer.ts

This script fetches fixtures for leagues without starting the Express server, making it suitable for running on a server where the API is already running.

### Features

- Fetches fixtures for specific leagues and seasons
- Fetches fixtures for all leagues in a specific tier
- Fetches fixtures for all leagues defined in `leagueTiers.ts`
- Fetches detailed data (events, lineups, statistics, player stats) for finished fixtures
- Connects directly to MongoDB without starting the Express server
- Respects API rate limits with delays between requests

### Usage

After building the project with `npm run build`, you can run the script with the following commands:

#### Fetch fixtures for a specific league

```bash
# Format: node dist/scripts/fetchLeagueFixturesNoServer.js <leagueId> [season]
# Example for Premier League (ID 39) using the current season:
node dist/scripts/fetchLeagueFixturesNoServer.js 39

# Example for Premier League (ID 39) for a specific season:
node dist/scripts/fetchLeagueFixturesNoServer.js 39 2024
```

#### Fetch fixtures for all leagues in a specific tier

```bash
# Format: node dist/scripts/fetchLeagueFixturesNoServer.js tier <tierNumber>
# Example for Tier 2 leagues (Premier League, La Liga, Bundesliga, Serie A, Ligue 1):
node dist/scripts/fetchLeagueFixturesNoServer.js tier 2
```

#### Fetch fixtures for all leagues

```bash
# This will fetch fixtures for all leagues defined in leagueTiers.ts
node dist/scripts/fetchLeagueFixturesNoServer.js all
```

#### Default behavior (no arguments)

```bash
# This will fetch fixtures for the top 5 leagues (Premier League, La Liga, Bundesliga, Serie A, Ligue 1)
node dist/scripts/fetchLeagueFixturesNoServer.js
```

### Important Notes

1. **API Rate Limits**: The script includes delays between requests to respect API-Football's rate limits. If you encounter rate limit errors, you may need to increase the delay time.

2. **Current Season Detection**: The script automatically detects the current season for each league by querying the API-Football service. This ensures that you always get fixtures for the correct season, even if different leagues use different season years (e.g., 2024 for some leagues, 2025 for others).

3. **Database Connection**: The script connects directly to MongoDB using the connection string from your environment variables. Make sure your `.env` file has the correct `MONGO_URI` value.

4. **Detailed Data**: For finished fixtures, the script fetches detailed data including events, lineups, statistics, and player stats. This data is stored in the corresponding fields in your Fixture model.

5. **Error Handling**: The script includes error handling to continue processing leagues even if one fails. Check the console output for any errors.

### Examples

#### Fetch Premier League fixtures for the current season

```bash
node dist/scripts/fetchLeagueFixturesNoServer.js 39
```

#### Fetch Premier League fixtures for a specific season

```bash
node dist/scripts/fetchLeagueFixturesNoServer.js 39 2024
```

#### Fetch fixtures for all Tier 1 competitions (World Cup, Euro, Champions League, etc.)

```bash
node dist/scripts/fetchLeagueFixturesNoServer.js tier 1
```

#### Fetch fixtures for all leagues

```bash
node dist/scripts/fetchLeagueFixturesNoServer.js all
```

#### Fetch fixtures for the top 5 leagues

```bash
node dist/scripts/fetchLeagueFixturesNoServer.js
```
