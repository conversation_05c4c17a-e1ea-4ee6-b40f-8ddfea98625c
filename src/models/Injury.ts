import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interfaces based on API response structure for /injuries

interface InjuryPlayer {
    id: number;
    name: string;
    photo: string | null;
    type: string; // e.g., "Missing Fixture", "Suspended"
    reason: string | null; // e.g., "Knee Injury", "Red Card"
}

interface InjuryTeam {
    id: number;
    name: string;
    logo: string | null;
}

interface InjuryFixture {
    id: number;
    timezone: string;
    date: string; // ISO 8601
    timestamp: number;
}

interface InjuryLeague {
    id: number;
    season: number;
    name: string;
    country: string;
    logo: string | null;
    flag: string | null;
}

// Main Injury interface for MongoDB document
// We store each injury record individually, linked to player/team/fixture/league
export interface Injury {
    _id: string; // Composite key: fixtureId_teamId_playerId (or similar unique combo)
    player: InjuryPlayer;
    team: InjuryTeam;
    fixture: InjuryFixture;
    league: InjuryLeague;
    lastUpdated: Date; // Our internal update timestamp
}

// Function to get the injuries collection
export function getInjuriesCollection(): Collection<Injury> {
    const db = getDb();
    return db.collection<Injury>('injuries');
}

// Helper to create a unique ID for an injury record
// Using fixture, team, and player IDs should guarantee uniqueness for a specific injury report
export function createInjuryId(fixtureId: number, teamId: number, playerId: number): string {
    return `${fixtureId}_${teamId}_${playerId}`;
}

// Optional: Add helper functions for common operations
