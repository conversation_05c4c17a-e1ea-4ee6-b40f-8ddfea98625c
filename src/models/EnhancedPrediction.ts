/**
 * Enhanced Prediction Models
 *
 * Data structures for storing Dixon-Coles enhanced predictions
 * including correct scores, BTTS predictions, and corner predictions
 */

import { CornerPrediction } from './CornerPrediction';
import { CardPrediction } from './CardPrediction';

export interface CorrectScorePrediction {
  mostLikely: {
    home: number;
    away: number;
    probability: number;
  };
  top5Scores: Array<{
    home: number;
    away: number;
    probability: number;
  }>;
  matrix: { [homeGoals: string]: { [awayGoals: string]: number } }; // Flattened for MongoDB storage
  confidence: 'low' | 'medium' | 'high';
  algorithm: 'dixon-coles' | 'hybrid' | 'poisson';
}

export interface BTTSPrediction {
  prediction: boolean; // true = both teams will score, false = at least one team won't score
  probability: number; // probability of BTTS = Yes
  confidence: 'low' | 'medium' | 'high';
  marketAnalysis?: {
    algorithmProbability: number; // Our calculated probability
    marketProbability?: number; // Market implied probability (from odds)
    valueRating?: number; // Percentage difference (positive = value bet)
    recommendedBet?: boolean;
    betDirection?: 'YES' | 'NO';
    confidence: 'low' | 'medium' | 'high';
  };
}

export interface GoalDistributionPrediction {
  under05: number; // 0 total goals
  under15: number; // 0-1 total goals  
  under25: number; // 0-2 total goals
  under35: number; // 0-3 total goals
  over05: number;  // 1+ total goals
  over15: number;  // 2+ total goals
  over25: number;  // 3+ total goals
  over35: number;  // 4+ total goals
}

export interface MatchOutcomePrediction {
  homeWin: number;
  draw: number;
  awayWin: number;
  confidence: 'low' | 'medium' | 'high';
}

export interface ExpectedGoalsPrediction {
  home: number;
  away: number;
  total: number;
  confidence: 'low' | 'medium' | 'high';
}

export interface DixonColesParameters {
  homeAttack: number;
  homeDefense: number;
  awayAttack: number;
  awayDefense: number;
  homeAdvantage: number;
  rho: number;
  leagueId: number;
  calculatedAt: Date;
}

export interface EloMetadata {
  homeEloRating: number;
  awayEloRating: number;
  homeEloRank: number | null;
  awayEloRank: number | null;
  eloInfluence: number; // 0-1 how much ELO influenced the prediction
  eloRatio: number; // homeElo / awayElo
}

export interface PredictionMetadata {
  algorithm: 'dixon-coles' | 'hybrid' | 'poisson';
  dataSource: 'historical' | 'api' | 'hybrid';
  confidence: number; // 1-100 overall confidence score
  processingTime: number; // milliseconds
  lastUpdated: Date;
  modelVersion: string;
  matchesUsedForTraining?: number;
  eloData?: EloMetadata | null; // ELO enhancement data
}

export interface TeamInfo {
  id: number;
  name: string;
  logo?: string;
}

export interface FixtureInfo {
  id: number;
  date: Date;
  status: string; // 'NS', 'LIVE', 'FT', etc.
}

export interface LeagueInfo {
  id: number;
  name: string;
  country: string;
  logo?: string;
  flag?: string;
  season: number;
  round?: string;
  standings?: boolean;
}

/**
 * Main Enhanced Prediction Document
 * This will be stored in the 'enhanced_predictions_v2' collection
 */
export interface EnhancedPrediction {
  _id: number; // fixture ID
  
  // Basic fixture information
  fixture: FixtureInfo;
  league: LeagueInfo;
  teams: {
    home: TeamInfo;
    away: TeamInfo;
  };

  // Enhanced predictions using Dixon-Coles
  predictions: {
    correctScore: CorrectScorePrediction;
    bothTeamsToScore: BTTSPrediction;
    matchOutcome: MatchOutcomePrediction;
    expectedGoals: ExpectedGoalsPrediction;
    goalDistribution: GoalDistributionPrediction;
    corners?: CornerPrediction; // Optional - only for leagues with corner statistics support
    cards?: CardPrediction; // Optional - only for leagues with card statistics support
  };

  // Dixon-Coles model parameters used
  dixonColesParams: DixonColesParameters;

  // Metadata
  metadata: PredictionMetadata;

  // Timestamps
  createdAt: Date;
  lastUpdated: Date;
}

/**
 * Team Strength Document
 * Stored in 'team_strengths' collection
 */
export interface TeamStrengthDocument {
  _id: string; // Format: "{teamId}_{leagueId}"
  teamId: number;
  leagueId: number;
  
  strength: {
    attack: number;
    defense: number;
  };
  
  statistics: {
    matchesPlayed: number;
    goalsScored: number;
    goalsConceded: number;
    homeMatchesPlayed: number;
    awayMatchesPlayed: number;
  };
  
  form: {
    last5Matches: Array<{
      fixtureId: number;
      date: Date;
      opponent: number;
      homeAway: 'home' | 'away';
      goalsFor: number;
      goalsAgainst: number;
      result: 'W' | 'D' | 'L';
    }>;
    recentForm: string; // e.g., "WWDLW"
  };

  lastUpdated: Date;
  calculatedAt: Date;
}

/**
 * League Parameters Document
 * Stored in 'league_parameters' collection
 */
export interface LeagueParametersDocument {
  _id: number; // league ID
  leagueId: number;
  
  parameters: {
    homeAdvantage: number;
    averageGoalsPerGame: number;
    rho: number;
  };
  
  statistics: {
    matchesAnalyzed: number;
    dateRange: {
      from: Date;
      to: Date;
    };
    totalGoals: number;
    homeWins: number;
    draws: number;
    awayWins: number;
  };

  lastUpdated: Date;
  calculatedAt: Date;
}

/**
 * Prediction Accuracy Tracking
 * Stored in 'prediction_accuracy' collection
 */
export interface PredictionAccuracyDocument {
  _id: string; // Format: "{fixtureId}_{algorithm}"
  fixtureId: number;
  algorithm: string;
  
  predictions: {
    correctScore: {
      predicted: { home: number; away: number; probability: number };
      actual: { home: number; away: number };
      correct: boolean;
      probabilityAssigned: number;
    };
    bothTeamsToScore: {
      predicted: boolean;
      actual: boolean;
      correct: boolean;
      probabilityAssigned: number;
    };
    matchOutcome: {
      predicted: 'home' | 'draw' | 'away';
      actual: 'home' | 'draw' | 'away';
      correct: boolean;
      probabilityAssigned: number;
    };
  };

  metadata: {
    predictionDate: Date;
    matchDate: Date;
    confidence: number;
  };

  createdAt: Date;
}

/**
 * Utility functions for confidence calculation
 */
export class PredictionConfidence {
  /**
   * Calculate confidence level based on probability and data quality
   */
  static calculateConfidence(
    probability: number,
    matchesUsed: number,
    dataQuality: number = 1.0
  ): 'low' | 'medium' | 'high' {
    const baseConfidence = probability * matchesUsed * dataQuality;
    
    if (baseConfidence > 50) return 'high';
    if (baseConfidence > 20) return 'medium';
    return 'low';
  }

  /**
   * Calculate overall prediction confidence score (1-100)
   */
  static calculateOverallConfidence(
    correctScoreConfidence: number,
    bttsConfidence: number,
    matchesUsed: number,
    dataAge: number // days since last update
  ): number {
    const avgConfidence = (correctScoreConfidence + bttsConfidence) / 2;
    const dataQualityFactor = Math.min(matchesUsed / 20, 1.0); // Max at 20 matches
    const freshnessFacto = Math.max(1 - (dataAge / 30), 0.5); // Decay over 30 days
    
    return Math.round(avgConfidence * dataQualityFactor * freshnessFacto * 100);
  }

  /**
   * Determine if a prediction represents good betting value
   */
  static calculateValueRating(
    algorithmProbability: number,
    marketProbability: number
  ): number {
    if (marketProbability === 0) return 0;
    return ((algorithmProbability - marketProbability) / marketProbability) * 100;
  }
}
