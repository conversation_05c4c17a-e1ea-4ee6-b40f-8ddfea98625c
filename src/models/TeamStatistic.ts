import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interfaces based on API response structure for /teams/statistics
// These might need refinement based on actual API responses

interface FixtureCounts {
    played: { home: number; away: number; total: number };
    wins: { home: number; away: number; total: number };
    draws: { home: number; away: number; total: number };
    loses: { home: number; away: number; total: number }; // Note API spelling 'loses'
}

interface GoalStats {
    'for': { total: { home: number; away: number; total: number }; average: { home: string; away: string; total: string }; minute: Record<string, { total: number | null; percentage: string | null }> };
    against: { total: { home: number; away: number; total: number }; average: { home: string; away: string; total: string }; minute: Record<string, { total: number | null; percentage: string | null }> };
    // Added based on changelog 3.9.3
    over: Record<string, { home: number; away: number; total: number }>;
    under: Record<string, { home: number; away: number; total: number }>;
}

interface BiggestStats {
    streak: { wins: number; draws: number; loses: number };
    wins: { home: string | null; away: string | null };
    loses: { home: string | null; away: string | null }; // Note API spelling 'loses'
    goals: { 'for': { home: number; away: number }; against: { home: number; away: number } };
}

interface CardStats {
    yellow: Record<string, { total: number | null; percentage: string | null }>;
    red: Record<string, { total: number | null; percentage: string | null }>;
}

interface LineupInfo {
    formation: string;
    played: number;
}

// Main TeamStatistic interface for MongoDB document
export interface TeamStatistic {
    _id: string; // Composite key: teamId_leagueId_seasonYear
    league: { id: number; name: string; country: string; logo: string; flag: string; season: number };
    team: { id: number; name: string; logo: string };
    form: string | null; // e.g., "WLWLD"
    fixtures: FixtureCounts;
    goals: GoalStats;
    biggest: BiggestStats;
    clean_sheet: { home: number; away: number; total: number };
    failed_to_score: { home: number; away: number; total: number };
    penalty: { scored: { total: number; percentage: string }; missed: { total: number; percentage: string }; total: number };
    lineups: LineupInfo[];
    cards: CardStats;
    lastUpdated: Date;
}

// Function to get the teamStatistics collection
export function getTeamStatisticsCollection(): Collection<TeamStatistic> {
    const db = getDb();
    return db.collection<TeamStatistic>('teamStatistics');
}

// Helper to create the composite ID
export function createTeamStatisticId(teamId: number, leagueId: number, season: number): string {
    return `${teamId}_${leagueId}_${season}`;
}
