/**
 * Card Prediction Models
 * 
 * Data structures for storing card predictions using Poisson distribution
 * Focuses on Total Cards Over/Under markets only
 */

export interface CardStatistics {
  fixtureId: number;
  homeYellowCards: number;
  awayYellowCards: number;
  homeRedCards: number;
  awayRedCards: number;
  totalCards: number;
  totalYellowCards: number;
  totalRedCards: number;
  homeTeamId: number;
  awayTeamId: number;
  leagueId: number;
  date: Date;
}

export interface TeamCardDiscipline {
  teamId: number;
  leagueId: number;
  cardAttack: number;        // Average cards received per game (team's indiscipline)
  cardDefense: number;       // Average cards caused to opponents per game
  homeCardPenalty: number;   // Additional card rate when playing at home (multiplier)
  awayCardPenalty: number;   // Additional card rate when playing away (multiplier)
  yellowCardRate: number;    // Yellow cards per game
  redCardRate: number;       // Red cards per game
  matchCount: number;        // Number of matches used for calculation
  confidence: number;        // 0-1 confidence score based on data quality
  lastUpdated: Date;
}

export interface LeagueCardParameters {
  leagueId: number;
  averageCards: number;      // League average total cards per match
  averageYellowCards: number; // League average yellow cards per match
  averageRedCards: number;   // League average red cards per match
  homeAdvantage: number;     // Home team card advantage (typically 0.9-1.1)
  awayPenalty: number;       // Away team card penalty (typically 1.1-1.3)
  varianceAdjustment: number; // League-specific variance adjustment
  matchesAnalyzed: number;   // Number of matches used for calculation
  hasCardSupport: boolean;   // Whether league has card statistics available
  lastUpdated: Date;
}

export interface CardMarketPrediction {
  threshold: number;         // e.g., 2.5, 3.5, 4.5, 5.5
  overProbability: number;   // Probability of over threshold
  underProbability: number;  // Probability of under threshold
  value: number;            // Value rating (-100 to +100, positive = good value)
  confidence: 'low' | 'medium' | 'high';
}

export interface CardPrediction {
  expectedTotalCards: number;     // Expected total cards in the match
  expectedYellowCards: number;    // Expected yellow cards in the match
  expectedRedCards: number;       // Expected red cards in the match
  homeExpectedCards: number;      // Expected cards for home team
  awayExpectedCards: number;      // Expected cards for away team
  confidence: number;             // Overall confidence score (0-100)
  
  // Total Cards Over/Under markets
  markets: {
    over2_5: CardMarketPrediction;
    over3_5: CardMarketPrediction;
    over4_5: CardMarketPrediction;
    over5_5: CardMarketPrediction;
  };
  
  // Card distribution (probability for each total card count)
  distribution: Array<{
    cards: number;
    probability: number;
  }>;
  
  // Metadata
  algorithm: 'poisson';
  dataSource: 'historical' | 'league_average';
  modelVersion: string;
  calculatedAt: Date;
}

/**
 * Team Card Discipline Document
 * Stored in 'team_card_discipline' collection
 */
export interface TeamCardDisciplineDocument {
  _id: string; // Format: "{teamId}_{leagueId}"
  teamId: number;
  leagueId: number;
  
  discipline: {
    cardAttack: number;
    cardDefense: number;
    homeCardPenalty: number;
    awayCardPenalty: number;
    yellowCardRate: number;
    redCardRate: number;
  };
  
  statistics: {
    matchesPlayed: number;
    cardsReceived: number;
    cardsCaused: number;
    homeMatchesPlayed: number;
    awayMatchesPlayed: number;
    averageCardsHome: number;
    averageCardsAway: number;
    yellowCardsReceived: number;
    redCardsReceived: number;
  };
  
  form: {
    last5Matches: Array<{
      fixtureId: number;
      date: Date;
      opponent: number;
      homeAway: 'home' | 'away';
      cardsReceived: number;
      cardsCaused: number;
    }>;
    recentCardForm: number; // Recent card discipline trend
  };

  lastUpdated: Date;
  calculatedAt: Date;
}

/**
 * League Card Parameters Document
 * Stored in 'league_card_parameters' collection
 */
export interface LeagueCardParametersDocument {
  _id: number; // league ID
  leagueId: number;
  
  parameters: {
    averageCards: number;
    averageYellowCards: number;
    averageRedCards: number;
    homeAdvantage: number;
    awayPenalty: number;
    varianceAdjustment: number;
  };
  
  statistics: {
    matchesAnalyzed: number;
    dateRange: {
      from: Date;
      to: Date;
    };
    totalCards: number;
    totalYellowCards: number;
    totalRedCards: number;
    averageHomeCards: number;
    averageAwayCards: number;
    cardDistribution: { [cards: string]: number }; // Distribution of card counts
  };
  
  coverage: {
    hasCardSupport: boolean;
    dataQuality: 'low' | 'medium' | 'high';
    lastDataUpdate: Date;
  };

  lastUpdated: Date;
  calculatedAt: Date;
}

/**
 * Card Prediction Accuracy Tracking
 * Stored in 'card_prediction_accuracy' collection
 */
export interface CardPredictionAccuracyDocument {
  _id: string; // Format: "{fixtureId}_card"
  fixtureId: number;
  
  predictions: {
    expectedTotalCards: number;
    actualTotalCards: number;
    markets: {
      [market: string]: {
        predicted: number;
        actual: boolean;
        correct: boolean;
      };
    };
  };
  
  accuracy: {
    totalCardsError: number; // Absolute error in total cards
    marketAccuracy: number;  // Percentage of correct market predictions
    confidenceCalibration: number; // How well confidence matched actual accuracy
  };

  metadata: {
    predictionDate: Date;
    matchDate: Date;
    confidence: number;
    leagueId: number;
  };

  createdAt: Date;
}

/**
 * Utility functions for card prediction calculations
 */
export class CardPredictionUtils {
  /**
   * Calculate Poisson probability mass function
   */
  static poissonPMF(lambda: number, k: number): number {
    if (k < 0) return 0;
    return (Math.pow(lambda, k) * Math.exp(-lambda)) / this.factorial(k);
  }
  
  /**
   * Calculate Poisson cumulative distribution function
   */
  static poissonCDF(lambda: number, k: number): number {
    let sum = 0;
    for (let i = 0; i <= k; i++) {
      sum += this.poissonPMF(lambda, i);
    }
    return sum;
  }
  
  /**
   * Calculate factorial
   */
  static factorial(n: number): number {
    if (n <= 1) return 1;
    return n * this.factorial(n - 1);
  }
  
  /**
   * Calculate over probability for a given threshold
   */
  static calculateOverProbability(lambda: number, threshold: number): number {
    return 1 - this.poissonCDF(lambda, Math.floor(threshold));
  }
  
  /**
   * Calculate confidence level based on data quality
   */
  static calculateConfidence(
    matchCount: number,
    dataQuality: number = 1.0,
    minMatches: number = 5
  ): 'low' | 'medium' | 'high' {
    const adjustedCount = matchCount * dataQuality;
    
    if (adjustedCount >= minMatches * 2) return 'high';
    if (adjustedCount >= minMatches) return 'medium';
    return 'low';
  }
  
  /**
   * Calculate value rating for a market
   */
  static calculateValueRating(
    algorithmProbability: number,
    marketProbability?: number
  ): number {
    if (!marketProbability || marketProbability === 0) return 0;
    return ((algorithmProbability - marketProbability) / marketProbability) * 100;
  }
  
  /**
   * Convert card counts to booking points (Yellow = 10, Red = 25)
   */
  static calculateBookingPoints(yellowCards: number, redCards: number): number {
    return (yellowCards * 10) + (redCards * 25);
  }
  
  /**
   * Validate card statistics are reasonable
   */
  static validateCardStatistics(stats: CardStatistics): boolean {
    // Check for reasonable card ranges
    if (stats.totalCards < 0 || stats.totalCards > 15) return false;
    if (stats.totalYellowCards < 0 || stats.totalYellowCards > 12) return false;
    if (stats.totalRedCards < 0 || stats.totalRedCards > 3) return false;
    
    // Check consistency
    if (stats.totalCards !== stats.totalYellowCards + stats.totalRedCards) return false;
    if (stats.homeYellowCards + stats.awayYellowCards !== stats.totalYellowCards) return false;
    if (stats.homeRedCards + stats.awayRedCards !== stats.totalRedCards) return false;
    
    return true;
  }
}
