import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Coach profile information
export interface CoachProfile {
    id: number;
    name: string;
    firstname: string | null;
    lastname: string | null;
    age: number | null;
    birth: {
        date: string | null;
        place: string | null;
        country: string | null;
    };
    nationality: string | null;
    height: string | null;
    weight: string | null;
    photo: string | null;
}

// Coach career information
export interface CoachCareer {
    team: {
        id: number;
        name: string;
        logo: string | null;
    };
    start: string | null;
    end: string | null;
}

// Main Coach document for MongoDB
export interface Coach {
    _id: number; // Use coach ID from API as MongoDB _id
    profile: CoachProfile;
    career: CoachCareer[];
    lastUpdated: Date;
}

// Function to get the coaches collection
export function getCoachesCollection(): Collection<Coach> {
    const db = getDb();
    return db.collection<Coach>('coaches');
}
