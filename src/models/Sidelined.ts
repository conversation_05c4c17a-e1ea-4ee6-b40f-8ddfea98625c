import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Player information in sidelined record
export interface SidelinedPlayer {
    id: number;
    name: string;
    photo: string | null;
}

// Team information in sidelined record
export interface SidelinedTeam {
    id: number;
    name: string;
    logo: string | null;
}

// Main Sidelined document for MongoDB
export interface Sidelined {
    _id: string; // Composite key: playerId:type:start
    player: SidelinedPlayer;
    team: SidelinedTeam;
    type: string; // e.g., "Injury", "Suspension"
    reason: string | null;
    start: string; // ISO date string
    end: string | null; // ISO date string, null if indefinite
    lastUpdated: Date;
}

// Function to get the sidelined collection
export function getSidelinedCollection(): Collection<Sidelined> {
    const db = getDb();
    return db.collection<Sidelined>('sidelined');
}

// Helper function to create a unique ID for a sidelined record
export function createSidelinedId(playerId: number, type: string, start: string): string {
    return `${playerId}:${type}:${start}`;
}
