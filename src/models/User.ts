import { Collection, ObjectId } from 'mongodb';
import { getDb } from '../config/database';
import bcrypt from 'bcrypt';

export interface User {
  _id?: ObjectId;
  email: string;
  password: string;
  name?: string;
  profileImage?: string; // Path to profile image
  createdAt: Date;
  updatedAt: Date;
  favorites: {
    teams: number[];
    players: number[];
    leagues: number[];
  };
  deviceTokens: string[]; // Added for push notifications
  preferences: {
    notifications: {
      upcomingFixtures: boolean;
      allEvents: boolean;
      goals: boolean;
      matchStatus: boolean; // Start/HT/End
      redCards: boolean;
    };
    // Add other preference categories here later if needed
  };
}

export interface UserWithoutPassword {
  _id?: ObjectId;
  email: string;
  name?: string;
  profileImage?: string; // Path to profile image
  createdAt: Date;
  updatedAt: Date;
  favorites: {
    teams: number[];
    players: number[];
    leagues: number[];
  };
  deviceTokens: string[]; // Added for push notifications
  preferences: User['preferences']; // Use the same type
}

export function getUsersCollection(): Collection<User> {
  return getDb().collection<User>('users');
}

export async function createUser(userData: Omit<User, '_id' | 'createdAt' | 'updatedAt' | 'favorites' | 'deviceTokens' | 'preferences'>): Promise<User> { // Also omit preferences
  const collection = getUsersCollection();

  // Check if user already exists
  const existingUser = await collection.findOne({ email: userData.email });
  if (existingUser) {
    throw new Error('User already exists');
  }

  // Hash password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(userData.password, salt);

  const now = new Date();
  const newUser: User = {
    email: userData.email,
    password: hashedPassword,
    name: userData.name,
    createdAt: now,
    updatedAt: now,
    favorites: {
      teams: [],
      players: [],
      leagues: []
    },
    deviceTokens: [], // Initialize deviceTokens
    preferences: { // Initialize preferences
      notifications: {
        upcomingFixtures: true, // Default to true
        allEvents: false, // Default to false
        goals: true, // Default to true
        matchStatus: true, // Default to true
        redCards: true // Default to true
      }
    }
  };

  const result = await collection.insertOne(newUser);
  return { ...newUser, _id: result.insertedId };
}

export async function findUserByEmail(email: string): Promise<User | null> {
  const collection = getUsersCollection();
  return collection.findOne({ email });
}

export async function findUserById(id: string): Promise<User | null> {
  const collection = getUsersCollection();
  return collection.findOne({ _id: new ObjectId(id) });
}

export async function validatePassword(user: User, password: string): Promise<boolean> {
  return bcrypt.compare(password, user.password);
}

export function sanitizeUser(user: User): UserWithoutPassword {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...userWithoutPassword } = user; // Keep deviceTokens and preferences
  return userWithoutPassword as UserWithoutPassword; // Cast to ensure type safety
}

// --- Preference Management ---

export async function updateNotificationPreference(
    userId: string,
    preferences: Partial<User['preferences']['notifications']>
): Promise<void> {
    const collection = getUsersCollection();

    // Create update object with only the provided preferences
    const updateFields: Record<string, any> = {};
    for (const [key, value] of Object.entries(preferences)) {
        updateFields[`preferences.notifications.${key}`] = value;
    }

    // Add updatedAt field
    updateFields.updatedAt = new Date();

    await collection.updateOne(
        { _id: new ObjectId(userId) },
        { $set: updateFields }
    );
}

// --- Profile Image Management ---

export async function updateProfileImage(userId: string, imagePath: string): Promise<User | null> {
    const collection = getUsersCollection();
    const result = await collection.findOneAndUpdate(
        { _id: new ObjectId(userId) },
        {
            $set: {
                profileImage: imagePath,
                updatedAt: new Date()
            }
        },
        { returnDocument: 'after' }
    );

    return result;
}


// --- Device Token Management ---

export async function addDeviceToken(userId: string, token: string): Promise<void> {
  const collection = getUsersCollection();
  await collection.updateOne(
    { _id: new ObjectId(userId) },
    {
      $addToSet: { deviceTokens: token }, // Add token if it doesn't exist
      $set: { updatedAt: new Date() }
    }
  );
}

export async function removeDeviceToken(userId: string, token: string): Promise<void> {
  const collection = getUsersCollection();
  await collection.updateOne(
    { _id: new ObjectId(userId) },
    {
      $pull: { deviceTokens: token }, // Remove the specific token
      $set: { updatedAt: new Date() }
    }
  );
}

// --- Favorite Management ---

export async function addFavoriteTeam(userId: string, teamId: number): Promise<void> {
  const collection = getUsersCollection();
  await collection.updateOne(
    { _id: new ObjectId(userId) },
    {
      $addToSet: { 'favorites.teams': teamId },
      $set: { updatedAt: new Date() }
    }
  );
}

export async function removeFavoriteTeam(userId: string, teamId: number): Promise<void> {
  const collection = getUsersCollection();
  await collection.updateOne(
    { _id: new ObjectId(userId) },
    {
      $pull: { 'favorites.teams': teamId },
      $set: { updatedAt: new Date() }
    }
  );
}

export async function addFavoritePlayer(userId: string, playerId: number): Promise<void> {
  const collection = getUsersCollection();
  await collection.updateOne(
    { _id: new ObjectId(userId) },
    {
      $addToSet: { 'favorites.players': playerId },
      $set: { updatedAt: new Date() }
    }
  );
}

export async function removeFavoritePlayer(userId: string, playerId: number): Promise<void> {
  const collection = getUsersCollection();
  await collection.updateOne(
    { _id: new ObjectId(userId) },
    {
      $pull: { 'favorites.players': playerId },
      $set: { updatedAt: new Date() }
    }
  );
}

export async function addFavoriteLeague(userId: string, leagueId: number): Promise<void> {
  const collection = getUsersCollection();
  await collection.updateOne(
    { _id: new ObjectId(userId) },
    {
      $addToSet: { 'favorites.leagues': leagueId },
      $set: { updatedAt: new Date() }
    }
  );
}

export async function removeFavoriteLeague(userId: string, leagueId: number): Promise<void> {
  const collection = getUsersCollection();
  await collection.updateOne(
    { _id: new ObjectId(userId) },
    {
      $pull: { 'favorites.leagues': leagueId },
      $set: { updatedAt: new Date() }
    }
  );
}

export async function getUserFavorites(userId: string): Promise<User['favorites'] | null> {
  const collection = getUsersCollection();
  const user = await collection.findOne(
    { _id: new ObjectId(userId) },
    { projection: { favorites: 1 } }
  );

  return user ? user.favorites : null;
}
