import { Collection, ObjectId } from 'mongodb';
import { getDb } from '../config/database';

export interface AnonymousDeviceToken {
  _id?: ObjectId;
  token: string;
  createdAt: Date;
  updatedAt: Date;
  notificationPreferences: {
    allEvents: boolean;
    goals: boolean;
    matchStatus: boolean; // Start/HT/End
    redCards: boolean;
  };
  subscribedFixtures?: number[]; // Optional array of fixture IDs to receive notifications for
  subscribedTeams?: number[]; // Optional array of team IDs to receive notifications for
  subscribedLeagues?: number[]; // Optional array of league IDs to receive notifications for
}

export function getAnonymousDeviceTokensCollection(): Collection<AnonymousDeviceToken> {
  return getDb().collection<AnonymousDeviceToken>('anonymousDeviceTokens');
}

/**
 * Add a new anonymous device token
 * @param token The device token
 * @returns The created anonymous device token
 */
export async function addAnonymousDeviceToken(token: string): Promise<AnonymousDeviceToken> {
  const collection = getAnonymousDeviceTokensCollection();

  // Check if token already exists
  const existingToken = await collection.findOne({ token });
  if (existingToken) {
    return existingToken;
  }

  const now = new Date();
  const newToken: AnonymousDeviceToken = {
    token,
    createdAt: now,
    updatedAt: now,
    notificationPreferences: {
      allEvents: false,
      goals: true, // Default to true for goals
      matchStatus: true, // Default to true for match status
      redCards: true, // Default to true for red cards
    },
    subscribedFixtures: [],
    subscribedTeams: [],
    subscribedLeagues: []
  };

  const result = await collection.insertOne(newToken);
  return { ...newToken, _id: result.insertedId };
}

/**
 * Remove an anonymous device token
 * @param token The device token to remove
 */
export async function removeAnonymousDeviceToken(token: string): Promise<void> {
  const collection = getAnonymousDeviceTokensCollection();
  await collection.deleteOne({ token });
}

/**
 * Update notification preferences for an anonymous device token
 * @param token The device token
 * @param preferences The notification preferences to update
 */
export async function updateAnonymousNotificationPreferences(
  token: string,
  preferences: Partial<AnonymousDeviceToken['notificationPreferences']>
): Promise<AnonymousDeviceToken | null> {
  const collection = getAnonymousDeviceTokensCollection();

  // Create update object with only the provided preferences
  const updateFields: Record<string, any> = {};
  for (const [key, value] of Object.entries(preferences)) {
    updateFields[`notificationPreferences.${key}`] = value;
  }

  const result = await collection.findOneAndUpdate(
    { token },
    {
      $set: {
        ...updateFields,
        updatedAt: new Date()
      }
    },
    { returnDocument: 'after' }
  );

  return result;
}

/**
 * Subscribe an anonymous device to a fixture
 * @param token The device token
 * @param fixtureId The fixture ID to subscribe to
 */
export async function subscribeAnonymousToFixture(token: string, fixtureId: number): Promise<void> {
  const collection = getAnonymousDeviceTokensCollection();
  await collection.updateOne(
    { token },
    {
      $addToSet: { subscribedFixtures: fixtureId },
      $set: { updatedAt: new Date() }
    }
  );
}

/**
 * Unsubscribe an anonymous device from a fixture
 * @param token The device token
 * @param fixtureId The fixture ID to unsubscribe from
 */
export async function unsubscribeAnonymousFromFixture(token: string, fixtureId: number): Promise<void> {
  const collection = getAnonymousDeviceTokensCollection();
  await collection.updateOne(
    { token },
    {
      $pull: { subscribedFixtures: fixtureId },
      $set: { updatedAt: new Date() }
    }
  );
}

/**
 * Subscribe an anonymous device to a team
 * @param token The device token
 * @param teamId The team ID to subscribe to
 */
export async function subscribeAnonymousToTeam(token: string, teamId: number): Promise<void> {
  const collection = getAnonymousDeviceTokensCollection();
  await collection.updateOne(
    { token },
    {
      $addToSet: { subscribedTeams: teamId },
      $set: { updatedAt: new Date() }
    }
  );
}

/**
 * Unsubscribe an anonymous device from a team
 * @param token The device token
 * @param teamId The team ID to unsubscribe from
 */
export async function unsubscribeAnonymousFromTeam(token: string, teamId: number): Promise<void> {
  const collection = getAnonymousDeviceTokensCollection();
  await collection.updateOne(
    { token },
    {
      $pull: { subscribedTeams: teamId },
      $set: { updatedAt: new Date() }
    }
  );
}

/**
 * Check if an anonymous device is subscribed to a fixture
 * @param token The device token
 * @param fixtureId The fixture ID to check
 * @returns Object with isSubscribed flag and notification preferences
 */
export async function checkAnonymousFixtureSubscription(token: string, fixtureId: number): Promise<{
  isSubscribed: boolean;
  preferences: AnonymousDeviceToken['notificationPreferences'] | null;
}> {
  const collection = getAnonymousDeviceTokensCollection();

  // Find the token document
  const tokenDoc = await collection.findOne({ token });

  if (!tokenDoc) {
    return {
      isSubscribed: false,
      preferences: null
    };
  }

  // Check if the fixture ID is in the subscribedFixtures array
  const isSubscribed = tokenDoc.subscribedFixtures?.includes(fixtureId) || false;

  return {
    isSubscribed,
    preferences: isSubscribed ? tokenDoc.notificationPreferences : null
  };
}
