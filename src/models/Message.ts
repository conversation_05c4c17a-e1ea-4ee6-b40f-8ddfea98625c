import { Collection, ObjectId } from 'mongodb';
import { getDb } from '../config/database';
import { getRedisClient } from '../config/redis';

export interface Message {
  _id?: ObjectId;
  fixtureId: number;
  userId: ObjectId;
  userName: string; // Store the user's name for display
  content: string;
  createdAt: Date;
}

export function getMessagesCollection(): Collection<Message> {
  return getDb().collection<Message>('messages');
}

// Create a new message
export async function createMessage(messageData: Omit<Message, '_id' | 'createdAt'>): Promise<Message> {
  const collection = getMessagesCollection();

  const now = new Date();
  const newMessage: Message = {
    ...messageData,
    createdAt: now
  };

  const result = await collection.insertOne(newMessage);

  // Invalidate cache for this fixture's messages
  const redisClient = getRedisClient();
  await redisClient.del(`fixture:${messageData.fixtureId}:messages`);

  return { ...newMessage, _id: result.insertedId };
}

// Get messages for a fixture with pagination
export async function getFixtureMessages(
  fixtureId: number,
  limit: number = 50,
  before?: Date
): Promise<Message[]> {
  const collection = getMessagesCollection();

  const query: any = { fixtureId };
  if (before) {
    query.createdAt = { $lt: before };
  }

  return collection.find(query)
    .sort({ createdAt: -1 }) // Most recent first
    .limit(limit)
    .toArray();
}

// Delete a message (for moderation purposes)
export async function deleteMessage(messageId: string): Promise<boolean> {
  const collection = getMessagesCollection();

  // First get the message to know which fixture cache to invalidate
  const message = await collection.findOne({ _id: new ObjectId(messageId) });
  if (!message) {
    return false;
  }

  const result = await collection.deleteOne({ _id: new ObjectId(messageId) });

  // Invalidate cache for this fixture's messages
  const redisClient = getRedisClient();
  await redisClient.del(`fixture:${message.fixtureId}:messages`);

  return result.deletedCount === 1;
}

// Check if chat is open for a fixture
export async function isChatOpen(fixtureId: number): Promise<boolean> {
  const redisClient = getRedisClient();
  const closedKey = `fixture:${fixtureId}:chat_closed`;
  const openKey = `fixture:${fixtureId}:chat_open`;

  // Check if chat is explicitly closed
  const isClosed = await redisClient.get(closedKey);
  if (isClosed === '1') {
    return false;
  }

  // Check if chat is explicitly opened
  const isOpen = await redisClient.get(openKey);
  if (isOpen === '1') {
    return true;
  }

  // If not explicitly opened or closed, check fixture time
  const fixtureCollection = getDb().collection('fixtures');
  const fixture = await fixtureCollection.findOne({ _id: fixtureId as any });

  if (!fixture) {
    return false; // Fixture not found
  }

  // Check if fixture is within 10 minutes of starting or has already started
  const fixtureTime = new Date(fixture.fixture.date);
  const now = new Date();
  const timeDiff = fixtureTime.getTime() - now.getTime();
  const minutesBefore = timeDiff / (1000 * 60);

  // Chat is open if fixture is starting in less than 10 minutes or has already started
  return minutesBefore <= 10 && minutesBefore > -120; // Open 10 mins before, close 2 hours after
}

// Close chat for a fixture
export async function closeChat(fixtureId: number): Promise<void> {
  const redisClient = getRedisClient();
  const closedKey = `fixture:${fixtureId}:chat_closed`;
  const openKey = `fixture:${fixtureId}:chat_open`;

  // Set the closed key to '1' (meaning chat is closed) with no expiration
  await redisClient.set(closedKey, '1');

  // Remove the open key if it exists
  await redisClient.del(openKey);
}

// Open chat for a fixture
export async function openChat(fixtureId: number): Promise<void> {
  const redisClient = getRedisClient();
  const closedKey = `fixture:${fixtureId}:chat_closed`;
  const openKey = `fixture:${fixtureId}:chat_open`;

  // Delete the closed key if it exists
  await redisClient.del(closedKey);

  // Set the open key to '1' (meaning chat is explicitly opened)
  await redisClient.set(openKey, '1');
}
