import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interfaces based on API response structure for /odds

// Bookmaker information
export interface Bookmaker {
    id: number;
    name: string;
}

// Bet information
export interface Bet {
    id: number;
    name: string;
}

// Bet value information
export interface BetValue {
    value: string;
    odd: string;
}

// Odds for a specific bet type from a specific bookmaker
export interface BookmakerOdds {
    id: number;
    name: string;
    bets: {
        id: number;
        name: string;
        values: BetValue[];
    }[];
}

// Main Odds interface for MongoDB document
export interface Odds {
    _id: string; // Composite key: fixtureId_bookmaker_bet
    apiId: string; // Duplicate of _id to match existing database index
    fixtureId: number;
    leagueId: number;
    update: string; // ISO date string when odds were last updated by the API
    bookmakers: BookmakerOdds[];
    lastUpdated: Date; // Our internal update timestamp
}

// Live Odds interface for MongoDB document
export interface LiveOdds {
    _id: string; // Composite key: fixtureId_bookmaker_bet
    apiId: string; // Duplicate of _id to match existing database index
    fixtureId: number;
    leagueId: number;
    update: string; // ISO date string when odds were last updated by the API
    bookmakers: BookmakerOdds[];
    lastUpdated: Date; // Our internal update timestamp
}

// Bookmaker information for MongoDB document
export interface BookmakerInfo {
    _id: number; // Bookmaker ID
    apiId: number; // Duplicate of _id to match existing database index
    name: string;
    lastUpdated: Date;
}

// Bet type information for MongoDB document
export interface BetInfo {
    _id: number; // Bet ID
    apiId: number; // Duplicate of _id to match existing database index
    name: string;
    lastUpdated: Date;
}

// Function to get the odds collection
export function getOddsCollection(): Collection<Odds> {
    const db = getDb();
    return db.collection<Odds>('odds');
}

// Function to get the live odds collection
export function getLiveOddsCollection(): Collection<LiveOdds> {
    const db = getDb();
    return db.collection<LiveOdds>('liveOdds');
}

// Function to get the bookmakers collection
export function getBookmakersCollection(): Collection<BookmakerInfo> {
    const db = getDb();
    return db.collection<BookmakerInfo>('bookmakers');
}

// Function to get the bets collection
export function getBetsCollection(): Collection<BetInfo> {
    const db = getDb();
    return db.collection<BetInfo>('bets');
}

// Helper to create the composite ID for odds
export function createOddsId(fixtureId: number, bookmaker: string, bet: string): string {
    return `${fixtureId}_${bookmaker}_${bet}`;
}
