import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interfaces based on API response structure for /fixtures

interface FixtureInfo {
    id: number; // Use API ID as the primary identifier
    referee: string | null;
    timezone: string;
    date: string; // ISO 8601 date string
    timestamp: number;
    periods: {
        first: number | null; // Timestamp
        second: number | null; // Timestamp
    };
    extra?: {
        first: number | null; // Extra time in first half (in minutes)
        second: number | null; // Extra time in second half (in minutes)
    };
    venue: { // Simplified, link to Venue model if needed
        id: number | null;
        name: string | null;
        city: string | null;
    };
    status: {
        long: string; // e.g., "Match Finished", "Not Started"
        short: string; // e.g., "FT", "NS", "HT", "LIVE" etc.
        elapsed: number | null; // Minutes elapsed for live games
    };
}

interface LeagueInfo { // Simplified, link to League model if needed
    id: number;
    name: string;
    country: string;
    logo: string | null;
    flag: string | null;
    season: number;
    round: string; // e.g., "Regular Season - 1"
}

interface TeamInfo { // Simplified, link to Team model if needed
    id: number;
    name: string;
    logo: string | null;
    winner: boolean | null; // Winner status for the fixture
}

interface TeamsInfo {
    home: TeamInfo;
    away: TeamInfo;
}

interface GoalsInfo {
    home: number | null;
    away: number | null;
}

interface ScoreInfo {
    halftime: GoalsInfo;
    fulltime: GoalsInfo;
    extratime: GoalsInfo;
    penalty: GoalsInfo;
}

// --- Detailed Sub-documents (often included when fetching by ID) ---

export interface EventInfo { // Exported
    time: {
        elapsed: number;
        extra: number | null;
    };
    team: { id: number; name: string; logo: string | null };
    player: { id: number | null; name: string | null };
    assist: { id: number | null; name: string | null };
    type: string; // "Goal", "Card", "Subst", "Var"
    detail: string; // e.g., "Normal Goal", "Yellow Card", "Substitution 1"
    comments: string | null;
    _markedForRemoval?: boolean; // Internal flag used for event reconciliation
}

// Not exporting LineupPlayerInfo as it's only used within LineupInfo
interface LineupPlayerInfo {
    player: { id: number; name: string; number: number | null; pos: string | null; grid: string | null }; // pos: G, D, M, F; grid: "1:1"
}

export interface LineupInfo { // Exported
    team: { id: number; name: string; logo: string | null; colors: any | null }; // colors might be { player: { primary: string, number: string, border: string }, goalkeeper: ... }
    coach: { id: number | null; name: string | null; photo: string | null };
    formation: string | null;
    startXI: LineupPlayerInfo[];
    substitutes: LineupPlayerInfo[];
}

// Not exporting StatItem as it's only used within TeamStatsInfo
interface StatItem {
    type: string; // e.g., "Shots on Goal", "Ball Possession"
    value: number | string | null; // Value can be number or string (like "55%")
}

export interface TeamStatsInfo { // Exported
    team: { id: number; name: string; logo: string | null };
    statistics: StatItem[];
}

// Not exporting PlayerStatsPlayerInfo as it's only used within PlayerStatsInfo
interface PlayerStatsPlayerInfo {
    id: number;
    name: string;
    photo: string | null;
}

// Not exporting PlayerStatsItem as it's only used within PlayerStatsInfo
interface PlayerStatsItem {
    offsides: number | null;
    shots: { total: number | null; on: number | null };
    goals: { total: number | null; conceded: number | null; assists: number | null; saves: number | null };
    passes: { total: number | null; key: number | null; accuracy: string | null }; // accuracy as percentage string
    tackles: { total: number | null; blocks: number | null; interceptions: number | null };
    duels: { total: number | null; won: number | null };
    dribbles: { attempts: number | null; success: number | null; past: number | null };
    fouls: { drawn: number | null; committed: number | null };
    cards: { yellow: number | null; red: number | null };
    penalty: { won: number | null; committed: number | null; scored: number | null; missed: number | null; saved: number | null };
    games: { minutes: number | null; number: number | null; position: string | null; rating: string | null; captain: boolean; substitute: boolean };
}

export interface PlayerStatsInfo { // Exported
    team: { id: number; name: string; logo: string | null };
    players: {
        player: PlayerStatsPlayerInfo;
        statistics: PlayerStatsItem[]; // API nests stats inside another array
    }[];
}


// Main Fixture interface for MongoDB document
export interface Fixture {
    _id: number; // Use fixture ID from API as the MongoDB _id
    apiId: number; // Duplicate of _id to match existing database index
    fixture: FixtureInfo;
    league: LeagueInfo;
    teams: TeamsInfo;
    goals: GoalsInfo;
    score: ScoreInfo;
    events?: EventInfo[]; // Optional, fetched separately or via id/ids
    lineups?: LineupInfo[]; // Optional
    statistics?: TeamStatsInfo[]; // Optional
    players?: PlayerStatsInfo[]; // Optional
    date: Date; // Extracted date for easier querying
    lastUpdated: Date; // Our internal update timestamp
    // Chat-related fields
    chatOpeningNotified?: boolean; // Whether users have been notified that chat is now open
    chatClosingNotified?: boolean; // Whether users have been notified that chat will close soon
    chatClosingTime?: Date; // When the chat is scheduled to close (20 minutes after match end)
    chatClosed?: boolean; // Whether the chat has been closed
}

// Function to get the fixtures collection
export function getFixturesCollection(): Collection<Fixture> {
    const db = getDb();
    return db.collection<Fixture>('fixtures');
}

// Optional: Add helper functions for common operations
// e.g., findFixtureById, findFixturesByDate, findLiveFixtures, etc.
