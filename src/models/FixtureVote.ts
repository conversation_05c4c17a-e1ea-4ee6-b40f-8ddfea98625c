import { Collection, ObjectId } from 'mongodb';
import { getDb } from '../config/database';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

export enum VoteOption {
  HOME_WIN = 'home',
  DRAW = 'draw',
  AWAY_WIN = 'away'
}

export interface FixtureVote {
  _id?: ObjectId;
  userId: ObjectId;
  fixtureId: number;
  vote: VoteOption;
  createdAt: Date;
  updatedAt: Date;
}

export function getFixtureVotesCollection(): Collection<FixtureVote> {
  return getDb().collection<FixtureVote>('fixtureVotes');
}

// Create or update a vote
export async function createOrUpdateVote(
  userId: string,
  fixtureId: number,
  vote: VoteOption
): Promise<FixtureVote> {
  const collection = getFixtureVotesCollection();
  const now = new Date();

  // Check if user has already voted for this fixture
  const existingVote = await collection.findOne({
    userId: new ObjectId(userId),
    fixtureId
  });

  if (existingVote) {
    // Update existing vote
    await collection.updateOne(
      { _id: existingVote._id },
      {
        $set: {
          vote,
          updatedAt: now
        }
      }
    );

    return {
      ...existingVote,
      vote,
      updatedAt: now
    };
  } else {
    // Create new vote
    const newVote: FixtureVote = {
      userId: new ObjectId(userId),
      fixtureId,
      vote,
      createdAt: now,
      updatedAt: now
    };

    const result = await collection.insertOne(newVote);
    return { ...newVote, _id: result.insertedId };
  }
}

// Get a user's vote for a fixture
export async function getUserVote(
  userId: string,
  fixtureId: number
): Promise<FixtureVote | null> {
  const collection = getFixtureVotesCollection();
  return collection.findOne({
    userId: new ObjectId(userId),
    fixtureId
  });
}

// Get vote statistics for a fixture
export async function getFixtureVoteStats(fixtureId: number): Promise<{
  homeVotes: number;
  drawVotes: number;
  awayVotes: number;
  totalVotes: number;
}> {
  const collection = getFixtureVotesCollection();

  const stats = await collection.aggregate([
    { $match: { fixtureId } },
    { $group: {
        _id: '$vote',
        count: { $sum: 1 }
      }
    }
  ]).toArray();

  const homeVotes = stats.find(s => s._id === VoteOption.HOME_WIN)?.count || 0;
  const drawVotes = stats.find(s => s._id === VoteOption.DRAW)?.count || 0;
  const awayVotes = stats.find(s => s._id === VoteOption.AWAY_WIN)?.count || 0;
  const totalVotes = homeVotes + drawVotes + awayVotes;

  return {
    homeVotes,
    drawVotes,
    awayVotes,
    totalVotes
  };
}

// Get vote statistics for multiple fixtures
export async function getMultipleFixtureVoteStats(fixtureIds: number[]): Promise<{
  [fixtureId: number]: {
    homeVotes: number;
    drawVotes: number;
    awayVotes: number;
    totalVotes: number;
  }
}> {
  if (!fixtureIds.length) {
    return {};
  }

  const collection = getFixtureVotesCollection();

  const stats = await collection.aggregate([
    { $match: { fixtureId: { $in: fixtureIds } } },
    { $group: {
        _id: { fixtureId: '$fixtureId', vote: '$vote' },
        count: { $sum: 1 }
      }
    }
  ]).toArray();

  const result: {
    [fixtureId: number]: {
      homeVotes: number;
      drawVotes: number;
      awayVotes: number;
      totalVotes: number;
    }
  } = {};

  // Initialize result with zeros for all fixture IDs
  fixtureIds.forEach(fixtureId => {
    result[fixtureId] = {
      homeVotes: 0,
      drawVotes: 0,
      awayVotes: 0,
      totalVotes: 0
    };
  });

  // Fill in the actual vote counts
  stats.forEach(stat => {
    const fixtureId = stat._id.fixtureId;
    const vote = stat._id.vote;
    const count = stat.count;

    if (vote === VoteOption.HOME_WIN) {
      result[fixtureId].homeVotes = count;
    } else if (vote === VoteOption.DRAW) {
      result[fixtureId].drawVotes = count;
    } else if (vote === VoteOption.AWAY_WIN) {
      result[fixtureId].awayVotes = count;
    }

    result[fixtureId].totalVotes += count;
  });

  return result;
}

// Helper function to determine if a vote was correct
function isVoteCorrect(vote: VoteOption, fixture: any): { isCorrect: boolean | null, result: string | null } {
  // If the fixture doesn't have a status or it's not finished, we can't determine if the vote was correct
  if (!fixture || !fixture.fixture || !fixture.fixture.status ||
      !['FT', 'AET', 'PEN'].includes(fixture.fixture.status.short)) {
    return { isCorrect: null, result: null };
  }

  // Get the goals
  const homeGoals = fixture.goals.home;
  const awayGoals = fixture.goals.away;

  // If goals are not available, we can't determine the result
  if (homeGoals === null || awayGoals === null) {
    return { isCorrect: null, result: null };
  }

  // Determine the actual result
  let actualResult: VoteOption;
  if (homeGoals > awayGoals) {
    actualResult = VoteOption.HOME_WIN;
  } else if (homeGoals < awayGoals) {
    actualResult = VoteOption.AWAY_WIN;
  } else {
    actualResult = VoteOption.DRAW;
  }

  // Check if the vote was correct
  const isCorrect = vote === actualResult;

  // Return the result as a string (for display purposes)
  let resultString: string;
  if (actualResult === VoteOption.HOME_WIN) {
    resultString = 'Home Win';
  } else if (actualResult === VoteOption.AWAY_WIN) {
    resultString = 'Away Win';
  } else {
    resultString = 'Draw';
  }

  return { isCorrect, result: resultString };
}

// Get the count of votes for a user
export async function getVoteCount(userId: string): Promise<number> {
  const collection = getFixtureVotesCollection();

  // Count the votes for this user
  const count = await collection.countDocuments({
    userId: new ObjectId(userId)
  });

  return count;
}

// Get all dates for which a user has votes
export async function getVoteDates(userId: string): Promise<string[]> {
  const collection = getFixtureVotesCollection();
  const fixturesCollection = getDb().collection('fixtures');

  // First, get all votes for the user
  const votes = await collection.find({
    userId: new ObjectId(userId)
  }).toArray();

  // If no votes, return empty array
  if (!votes.length) {
    return [];
  }

  // Get all fixture IDs from the votes
  const fixtureIds = votes.map(vote => vote.fixtureId);

  // Fetch fixture dates
  const fixtures = await fixturesCollection.aggregate([
    { $match: { _id: { $in: fixtureIds } } },
    { $project: { "fixture.date": 1 } }
  ]).toArray();

  // Extract dates and format them as YYYY-MM-DD
  const dates = fixtures.map(fixture => {
    const date = new Date(fixture.fixture.date);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  });

  // Remove duplicates and sort
  return [...new Set(dates)].sort((a, b) => b.localeCompare(a));
}

// Get all votes for a user with fixture details, optionally filtered by date
export async function getUserVotes(userId: string, dateFilter?: string, timezone?: string): Promise<any[]> {
  const collection = getFixtureVotesCollection();
  const fixturesCollection = getDb().collection('fixtures');

  // First, get all votes for the user
  const votes = await collection.find({
    userId: new ObjectId(userId)
  }).toArray();

  // If no votes, return empty array
  if (!votes.length) {
    return [];
  }

  // Get all fixture IDs from the votes
  const fixtureIds = votes.map(vote => vote.fixtureId);

  // Prepare the aggregation pipeline
  const pipeline: any[] = [
    { $match: { _id: { $in: fixtureIds } } }
  ];

  // Add projection to only include necessary fields
  pipeline.push({
    $project: {
      "fixture.id": 1,
      "fixture.date": 1,
      "fixture.status": 1,
      "teams.home.name": 1,
      "teams.home.logo": 1,
      "teams.away.name": 1,
      "teams.away.logo": 1,
      "goals": 1
    }
  });

  // Fetch fixture details with minimal data
  const fixtures = await fixturesCollection.aggregate(pipeline).toArray();

  // Create a map of fixture ID to fixture details for quick lookup
  const fixtureMap: { [key: string]: any } = {};
  fixtures.forEach(fixture => {
    // Convert _id to string to avoid type issues
    fixtureMap[fixture._id.toString()] = fixture;
  });

  // Combine votes with fixture details and add correctness information
  let votesWithFixtures = votes.map(vote => {
    const fixture = fixtureMap[vote.fixtureId.toString()] || null;
    const { isCorrect, result } = isVoteCorrect(vote.vote, fixture);

    return {
      ...vote,
      fixture,
      voteResult: {
        isCorrect,
        result
      }
    };
  });

  // Filter by date if dateFilter is provided
  if (dateFilter) {
    votesWithFixtures = votesWithFixtures.filter(vote => {
      if (!vote.fixture || !vote.fixture.fixture || !vote.fixture.fixture.date) {
        return false;
      }

      // Get the fixture timestamp (stored in UTC)
      const fixtureTimestamp = new Date(vote.fixture.fixture.date).getTime() / 1000;

      // Calculate day boundaries in the specified timezone (or UTC if not provided)
      let startOfDay: number;
      let endOfDay: number;

      if (timezone) {
        try {
          startOfDay = dayjs.tz(dateFilter, timezone).startOf('day').unix();
          endOfDay = dayjs.tz(dateFilter, timezone).endOf('day').unix();
        } catch (error) {
          console.warn(`Invalid timezone provided: ${timezone}, falling back to UTC`);
          startOfDay = dayjs(dateFilter).startOf('day').unix();
          endOfDay = dayjs(dateFilter).endOf('day').unix();
        }
      } else {
        startOfDay = dayjs(dateFilter).startOf('day').unix();
        endOfDay = dayjs(dateFilter).endOf('day').unix();
      }

      // Check if fixture timestamp falls within the day boundaries
      return fixtureTimestamp >= startOfDay && fixtureTimestamp <= endOfDay;
    });
  }

  return votesWithFixtures;
}
