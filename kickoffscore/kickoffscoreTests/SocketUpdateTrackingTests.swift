import XCTest
@testable import kickoffscore

/// Tests for socket update tracking functionality to prevent stale data overwrites
class SocketUpdateTrackingTests: XCTestCase {
    
    var socketManager: SocketManager!
    
    override func setUp() {
        super.setUp()
        socketManager = SocketManager.shared
    }
    
    override func tearDown() {
        socketManager = nil
        super.tearDown()
    }
    
    /// Test that socket update tracking works correctly
    func testSocketUpdateTracking() {
        let fixtureId = 12345

        // Initially, fixture should not be tracked as recently updated
        XCTAssertFalse(socketManager.wasRecentlyUpdatedViaSocket(fixtureId: fixtureId))

        // Simulate a socket update
        socketManager.trackSocketUpdate(for: fixtureId)

        // Now the fixture should be tracked as recently updated
        XCTAssertTrue(socketManager.wasRecentlyUpdatedViaSocket(fixtureId: fixtureId))

        // Test with a very short time window
        XCTAssertFalse(socketManager.wasRecentlyUpdatedViaSocket(fixtureId: fixtureId, withinSeconds: 0.001))
    }
    
    /// Test that old socket updates are cleaned up
    func testSocketUpdateCleanup() {
        let fixtureId = 12345
        
        // Test that the cleanup mechanism works (this would need internal access to test properly)
        // For now, we verify the public interface behavior
        XCTAssertFalse(socketManager.wasRecentlyUpdatedViaSocket(fixtureId: fixtureId, withinSeconds: 0.1))
    }
    
    /// Test socket connection status
    func testSocketConnectionStatus() {
        // Test that we can check socket connection status
        let isConnected = socketManager.isConnected
        XCTAssertNotNil(isConnected) // Should be a boolean value
        
        // Test subscription status
        let status = socketManager.subscriptionStatus
        XCTAssertTrue(status.contains("Subscriptions"))
    }
}
