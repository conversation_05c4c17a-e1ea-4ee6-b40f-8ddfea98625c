# Socket Background Implementation for KickoffScore

This document explains the implementation of socket connections that continue to work when the app is in the background.

## Overview

The KickoffScore app uses Socket.IO for real-time updates of football fixtures. We've implemented a comprehensive background handling system using Apple's BGTaskScheduler framework for optimal performance and user experience.

## Implementation Details

### 1. Background Modes

The app is configured with the following background modes:
- `fetch`: Allows the app to fetch data in the background
- `processing`: Allows the app to perform processing tasks in the background

These are configured in the project's build settings via the `UIBackgroundModes` Info.plist key.

**Full Background Task Support**:
The app now implements complete background task support with:
- BGTaskScheduler integration for proper background task scheduling
- Background task entitlements for production use
- Smart scheduling and resource management
- Comprehensive error handling and monitoring

Background task identifiers:
- `com.kickoffscore.refresh` - Background app refresh
- `com.kickoffscore.processing` - Background processing tasks
- `com.kickoffscore.socket-refresh` - Socket connection maintenance

### 2. BackgroundTaskManager

The new `BackgroundTaskManager` class provides:
- Centralized background task registration and scheduling
- Smart task scheduling based on app state and user behavior
- Comprehensive error handling and monitoring
- Integration with SocketManager for seamless operation
- Performance metrics and debugging capabilities

### 3. Enhanced SocketManager

The `SocketManager` class has been enhanced to:
- Monitor app state transitions (foreground/background)
- Manage socket connections based on app state
- Use BackgroundTaskManager for proper background task handling
- Implement reconnection logic with exponential backoff
- Track time spent in background to optimize reconnection strategy
- Provide subscription status and refresh capabilities for background tasks

### 4. Background Task Registration

The app registers for background tasks to:
- Periodically refresh socket connections (every 5 minutes)
- Update live fixture data (every 15 minutes)
- Perform cache cleanup and optimization (every 30 minutes)
- Maintain subscriptions to live fixtures
- Optimize battery usage by intelligently managing connections

### 4. View Model Integration

The view models (`FixtureDetailViewModel` and `LeagueFixturesViewModel`) have been updated to:
- Work with the enhanced socket manager
- Rely on socket manager for background state handling
- Maintain timer-based fallback mechanisms when needed
- Force refresh data when returning from background

## How It Works

1. **When App is Active**:
   - Socket connections are established and maintained
   - Real-time updates are received and processed
   - UI is updated immediately

2. **When App Goes to Background**:
   - The app tracks when it enters background state
   - Socket connections are maintained for short background periods
   - For longer background periods, iOS may suspend the app

3. **When App Returns to Foreground**:
   - The time spent in background is calculated
   - For short background periods (<30 seconds), connections are checked
   - For longer periods, connections are re-established
   - Data is refreshed to ensure UI is up-to-date

4. **Simplified Approach for Personal Development Teams**:
   - We rely on Socket.IO's built-in reconnection mechanism
   - We use app state observers to handle transitions
   - We implement intelligent reconnection logic based on time spent in background
   - We don't use BGTaskScheduler features that require special entitlements

## Testing

To test the background functionality:
1. Open the app and navigate to a live fixture
2. Send the app to the background (press home button or swipe up)
3. Wait for a few minutes
4. Return to the app
5. Verify that the fixture data is up-to-date

## Limitations

- iOS may still terminate the app after extended background time
- Background execution time is limited by the system
- Network connectivity issues may affect reconnection
- Battery usage increases with background activity

## Future Improvements

- Implement push notifications as a fallback for critical updates
- Further optimize battery usage with more selective subscriptions
- Add analytics to track background connection reliability
