# Background Tasks Implementation for KickoffScore

This document explains the comprehensive background task implementation for the KickoffScore iOS app, which enables real-time updates and data synchronization even when the app is in the background.

## Overview

The KickoffScore app implements intelligent background task support with automatic fallback for different development environments:

- **Full BGTaskScheduler Support**: When background task entitlements are available (paid Apple Developer account)
- **Enhanced Fallback Mode**: When entitlements are not available (personal development teams)
- **Smart Cache Management**: Optimizes memory and storage usage in both modes
- **Socket Connection Management**: Maintains real-time connections with intelligent reconnection

## Development Team Considerations

### Personal Development Teams (Free Apple Developer Account)
- Background task entitlements (`com.apple.developer.backgroundtasks`) are not available
- App automatically detects this and uses enhanced fallback mode
- Still provides improved background functionality through:
  - Standard background app refresh (when enabled by user)
  - Enhanced socket reconnection logic
  - Smart cache management
  - Optimized memory usage

### Paid Apple Developer Account
- Full BGTaskScheduler support with custom background task identifiers
- Scheduled background processing tasks
- Advanced background refresh capabilities

## Current Status

**Your app is currently configured for personal development teams** and will automatically use the enhanced fallback mode. This provides:

✅ **Working Features:**
- Enhanced socket connection management
- Smart cache cleanup and optimization
- Improved background app refresh (when user enables it)
- Automatic reconnection when app returns to foreground
- Performance monitoring and debugging

⚠️ **Limitations (Personal Development Team):**
- No custom BGTaskScheduler tasks
- Relies on system background app refresh settings
- Background processing limited to standard iOS timeframes

🚀 **To Enable Full Features:**
- Upgrade to paid Apple Developer account ($99/year)
- Add background task entitlements to provisioning profile
- App will automatically detect and enable full BGTaskScheduler support

## Architecture

### 1. BackgroundTaskManager

The `BackgroundTaskManager` is the central coordinator for all background operations:

```swift
// Singleton instance
BackgroundTaskManager.shared

// Key capabilities:
- registerBackgroundTasks()
- scheduleBackgroundAppRefresh()
- scheduleBackgroundProcessing()
- scheduleSocketRefresh()
```

### 2. Background Task Types

#### Background App Refresh (`com.kickoffscore.refresh`)
- **Purpose**: Update live fixture data and scores
- **Frequency**: Every 15 minutes when app is backgrounded
- **Duration**: Short-lived (30 seconds max)
- **Operations**:
  - Refresh live fixtures via socket connection
  - Update cache with latest data
  - Clean expired cache entries

#### Background Processing (`com.kickoffscore.processing`)
- **Purpose**: Perform maintenance and optimization tasks
- **Frequency**: Every 30 minutes when app is backgrounded
- **Duration**: Longer-lived (up to 1 minute)
- **Operations**:
  - Deep cache cleanup
  - Image cache optimization
  - Database maintenance (future)
  - Memory optimization

#### Socket Refresh (`com.kickoffscore.socket-refresh`)
- **Purpose**: Maintain real-time socket connections
- **Frequency**: Every 5 minutes when app is backgrounded
- **Duration**: Short-lived (15 seconds max)
- **Operations**:
  - Check socket connection status
  - Reconnect if necessary
  - Refresh active subscriptions

### 3. Enhanced SocketManager Integration

The `SocketManager` now integrates seamlessly with background tasks:

```swift
// New capabilities:
- hasActiveSubscriptions: Bool
- refreshAllSubscriptions()
- refreshLiveSubscriptions()
- subscriptionStatus: String
```

## Configuration

### 1. Entitlements (`kickoffscore.entitlements`)

```xml
<key>com.apple.developer.backgroundtasks</key>
<array>
    <string>com.kickoffscore.refresh</string>
    <string>com.kickoffscore.processing</string>
    <string>com.kickoffscore.socket-refresh</string>
</array>
```

### 2. Info.plist Configuration

Background task identifiers are automatically configured in the project settings:

```
BGTaskSchedulerPermittedIdentifiers:
- com.kickoffscore.refresh
- com.kickoffscore.processing
- com.kickoffscore.socket-refresh
```

### 3. Background Modes

The app supports these background modes:
- `fetch`: Background app refresh
- `processing`: Background processing tasks

## Implementation Details

### 1. App Lifecycle Integration

**App Launch**:
```swift
// In AppDelegate.didFinishLaunchingWithOptions
BackgroundTaskManager.shared.registerBackgroundTasks()
```

**App Backgrounding**:
```swift
// Automatically schedules background tasks
- scheduleBackgroundAppRefresh()
- scheduleSocketRefresh()
- scheduleBackgroundProcessing() (less frequently)
```

**App Foregrounding**:
```swift
// Cancels pending background tasks
BGTaskScheduler.shared.cancelAllTaskRequests()
```

### 2. Smart Scheduling

Background tasks are scheduled intelligently:

- **Availability Check**: Only schedules if background refresh is enabled
- **Frequency Control**: Prevents over-scheduling with counters
- **Battery Optimization**: Processing tasks don't require external power
- **Network Awareness**: Some tasks require network connectivity

### 3. Error Handling and Monitoring

Comprehensive logging and error handling:

```swift
// Debug status information
BackgroundTaskManager.shared.debugStatus

// Performance monitoring
- isBackgroundRefreshAvailable
- lastBackgroundRefresh
- backgroundTasksScheduled
```

## Cache Management Enhancements

### 1. CacheManager Improvements

New methods for background optimization:

```swift
- clearExpiredCache()        // Remove only expired entries
- performDeepCleanup()       // Aggressive cleanup for background tasks
```

### 2. OddsCache Enhancements

Enhanced odds cache management:

```swift
- performDeepCleanup()       // Remove old and unused odds data
```

### 3. Smart Cleanup Strategy

Background cleanup follows these rules:

1. **Expired Data**: Always removed
2. **Old Data**: Removed if older than 1 hour
3. **Unused Data**: Removed if rarely accessed and old
4. **Memory Pressure**: Prioritizes frequently accessed data

## User Experience Benefits

### 1. Real-Time Updates
- Live scores update even when app is backgrounded
- Match events and status changes are synchronized
- Reduced loading times when returning to app

### 2. Performance Optimization
- Automatic cache cleanup prevents memory bloat
- Image cache optimization reduces storage usage
- Smart data management improves app responsiveness

### 3. Battery Efficiency
- Intelligent scheduling minimizes battery impact
- Tasks are optimized for quick execution
- Background processing is limited and controlled

## Testing and Debugging

### 1. Debug Methods (Development Only)

```swift
#if DEBUG
BackgroundTaskManager.shared.debugForceBackgroundRefresh()
BackgroundTaskManager.shared.debugForceSocketRefresh()
#endif
```

### 2. Monitoring

Check background task status:

```swift
// View current status
print(BackgroundTaskManager.shared.debugStatus)

// Monitor socket subscriptions
print(SocketManager.shared.subscriptionStatus)
```

### 3. Xcode Testing

Use Xcode's background task simulation:
1. Run app in simulator
2. Debug → Simulate Background App Refresh
3. Check logs for background task execution

## Best Practices

### 1. Task Duration
- Keep background tasks short and focused
- Use expiration handlers to clean up properly
- Prioritize critical operations first

### 2. Resource Management
- Clean up resources in expiration handlers
- Use weak references to prevent retain cycles
- Monitor memory usage during background execution

### 3. User Privacy
- Only perform necessary operations in background
- Respect user's background refresh settings
- Provide clear value for background functionality

## Future Enhancements

Potential improvements for future versions:

1. **Adaptive Scheduling**: Adjust frequency based on user behavior
2. **Machine Learning**: Predict optimal refresh times
3. **Push Notifications**: Complement background tasks with server-side notifications
4. **Database Optimization**: Add background database maintenance
5. **Analytics Integration**: Track background task effectiveness

## Troubleshooting

### Common Issues

1. **Background Refresh Disabled**
   - Check Settings → General → Background App Refresh
   - Verify app-specific background refresh is enabled

2. **Tasks Not Executing**
   - Ensure proper entitlements are configured
   - Check BGTaskSchedulerPermittedIdentifiers in Info.plist
   - Verify background modes are enabled

3. **Performance Issues**
   - Monitor cache cleanup frequency
   - Check for memory leaks in background tasks
   - Review task duration and complexity

### Debugging Steps

1. Enable detailed logging in debug builds
2. Use Xcode's background task simulation
3. Monitor system logs for background task execution
4. Check BackgroundTaskManager debug status
5. Verify socket connection status during background execution

This implementation provides a robust foundation for background functionality while maintaining excellent user experience and system performance.
