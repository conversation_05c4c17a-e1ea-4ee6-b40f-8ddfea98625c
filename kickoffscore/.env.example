# KickoffScore Environment Configuration
# Copy this file to .env and fill in your actual values
# DO NOT commit .env to version control

# API Configuration
KICKOFFSCORE_API_KEY=your_api_key_here

# Environment-specific API URLs (optional)
DEV_API_URL=https://dev-api.kickoffpredictions.com/api
STAGING_API_URL=https://staging-api.kickoffpredictions.com/api
PROD_API_URL=https://api.kickoffpredictions.com/api

# Feature Flags (optional - defaults will be used if not set)
FEATURE_PUSH_NOTIFICATIONS=true
FEATURE_ANALYTICS=false
FEATURE_CRASH_REPORTING=false
FEATURE_DEBUG_MENU=true
FEATURE_BETA_FEATURES=true

# Development Settings
DEBUG_LOG_LEVEL=debug
DEBUG_SHOW_PERFORMANCE_METRICS=true
DEBUG_ENABLE_NETWORK_LOGGING=true
