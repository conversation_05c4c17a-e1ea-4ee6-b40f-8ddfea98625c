import SwiftUI

struct CollapsibleSection<Content: View>: View {
    @Binding var isExpanded: Bool
    let title: String
    let content: Content

    init(isExpanded: Binding<Bool>, title: String, @ViewBuilder content: () -> Content) {
        self._isExpanded = isExpanded
        self.title = title
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Text(title)
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                        .foregroundColor(AppColors.text)
                        .lineLimit(1)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColors.secondaryText)
                }
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.vertical, AppLayout.spacingS)
                .background(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                        .fill(Color(UIColor.secondarySystemBackground))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                        .stroke(isExpanded ? AppColors.Brand.primary : Color.clear, lineWidth: 1)
                )
            }

            // Content
            if isExpanded {
                VStack(spacing: AppLayout.spacingS) {
                    content
                }
                .padding(.top, AppLayout.spacingS)
                .padding(.horizontal, AppLayout.spacingS)
                .padding(.bottom, AppLayout.spacingS)
                .background(Color(UIColor.secondarySystemBackground).opacity(0.5))
                .cornerRadius(AppLayout.cornerRadiusM)
                .padding(.top, AppLayout.spacingXS)
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State private var isExpanded = true

        var body: some View {
            VStack {
                Spacer()

                CollapsibleSection(
                    isExpanded: $isExpanded,
                    title: "Match Winner"
                ) {
                    Text("Content goes here")
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                }
                .padding()

                Spacer()
            }
            .background(AppColors.background)
        }
    }

    return PreviewWrapper()
}
