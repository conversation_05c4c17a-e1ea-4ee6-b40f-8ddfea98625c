import SwiftUI

/// A reusable error view component for consistent error handling
struct EnhancedErrorView: View {
    // The error message to display
    let message: String

    // The action to perform when the retry button is tapped
    let retryAction: () -> Void

    // Optional title for the error (defaults to "Error")
    let title: String

    // Optional icon name (defaults to "exclamationmark.triangle")
    let iconName: String

    // Optional icon color (defaults to warning color)
    let iconColor: Color

    // Initialize with required parameters and optional parameters with defaults
    init(
        message: String,
        retryAction: @escaping () -> Void,
        title: String = "Error",
        iconName: String = "exclamationmark.triangle",
        iconColor: Color = AppColors.warning
    ) {
        self.message = message
        self.retryAction = retryAction
        self.title = title
        self.iconName = iconName
        self.iconColor = iconColor
    }

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            // Error icon
            Image(systemName: iconName)
                .font(.system(size: 40))
                .foregroundColor(iconColor)
                .accessibilityHidden(true)

            // Error title
            Text(title)
                .font(AppTypography.dynamicFont(style: .headline, weight: .semibold))
                .foregroundColor(AppColors.text)
                .multilineTextAlignment(.center)

            // Error message
            Text(message)
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingM)

            // Retry button
            Button(action: retryAction) {
                Text("Try Again")
                    .font(AppTypography.dynamicFont(style: .body, weight: .medium))
                    .foregroundColor(.white) // White text looks good on orange accent color
                    .padding(.horizontal, AppLayout.spacingL)
                    .padding(.vertical, AppLayout.spacingS)
                    .background(AppColors.Brand.accent)
                    .cornerRadius(AppLayout.cornerRadiusM)
            }
            .accessibilityHint("Tap to try loading the data again")
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .padding(.vertical, AppLayout.spacingL)
        .padding(.horizontal, AppLayout.spacingL)
    }
}

/// A reusable empty state view component
struct EnhancedEmptyStateView: View {
    // The message to display
    let message: String

    // Optional title (defaults to "No Data")
    let title: String

    // Optional icon name (defaults to "info.circle")
    let iconName: String

    // Optional icon color (defaults to secondary text color)
    let iconColor: Color

    // Initialize with required parameters and optional parameters with defaults
    init(
        message: String,
        title: String = "No Data",
        iconName: String = "info.circle",
        iconColor: Color = AppColors.secondaryText
    ) {
        self.message = message
        self.title = title
        self.iconName = iconName
        self.iconColor = iconColor
    }

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            // Icon
            Image(systemName: iconName)
                .font(.system(size: 40))
                .foregroundColor(iconColor)
                .accessibilityHidden(true)

            // Title
            Text(title)
                .font(AppTypography.dynamicFont(style: .headline, weight: .semibold))
                .foregroundColor(AppColors.text)
                .multilineTextAlignment(.center)

            // Message
            Text(message)
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingM)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .padding(.vertical, AppLayout.spacingL)
        .padding(.horizontal, AppLayout.spacingL)
    }
}

#Preview {
    VStack(spacing: 20) {
        EnhancedErrorView(
            message: "We couldn't load the data. Please check your internet connection and try again.",
            retryAction: {}
        )

        EnhancedEmptyStateView(
            message: "There are no items to display at this time."
        )
    }
    .padding()
}
