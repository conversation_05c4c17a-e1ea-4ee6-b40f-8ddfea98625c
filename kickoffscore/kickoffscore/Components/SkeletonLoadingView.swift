import SwiftUI

/// A component that displays a skeleton loading animation
struct SkeletonLoadingView: View {
    // Animation state
    @State private var isAnimating = false
    
    // Customization properties
    let height: CGFloat
    let cornerRadius: CGFloat
    let padding: EdgeInsets
    
    init(
        height: CGFloat = 20,
        cornerRadius: CGFloat = 4,
        padding: EdgeInsets = EdgeInsets(top: 4, leading: 0, bottom: 4, trailing: 0)
    ) {
        self.height = height
        self.cornerRadius = cornerRadius
        self.padding = padding
    }
    
    var body: some View {
        Rectangle()
            .fill(
                LinearGradient(
                    gradient: Gradient(
                        colors: [
                            Color(UIColor.systemGray5),
                            Color(UIColor.systemGray6),
                            Color(UIColor.systemGray5)
                        ]
                    ),
                    startPoint: .leading,
                    endPoint: isAnimating ? .trailing : .leading
                )
            )
            .frame(height: height)
            .cornerRadius(cornerRadius)
            .padding(padding)
            .onAppear {
                withAnimation(
                    Animation
                        .linear(duration: 1.5)
                        .repeatForever(autoreverses: false)
                ) {
                    isAnimating = true
                }
            }
    }
}

/// A skeleton loading view for text
struct SkeletonText: View {
    let lines: Int
    let lineHeight: CGFloat
    let cornerRadius: CGFloat
    let spacing: CGFloat
    
    init(
        lines: Int = 1,
        lineHeight: CGFloat = 16,
        cornerRadius: CGFloat = 4,
        spacing: CGFloat = 8
    ) {
        self.lines = lines
        self.lineHeight = lineHeight
        self.cornerRadius = cornerRadius
        self.spacing = spacing
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: spacing) {
            ForEach(0..<lines, id: \.self) { index in
                SkeletonLoadingView(
                    height: lineHeight,
                    cornerRadius: cornerRadius
                )
                // Make the last line shorter if multiple lines
                .frame(width: lines > 1 && index == lines - 1 ? 0.7 : 1, alignment: .leading)
            }
        }
    }
}

/// A skeleton loading view for an image
struct SkeletonImage: View {
    let width: CGFloat
    let height: CGFloat
    let cornerRadius: CGFloat
    
    init(
        width: CGFloat,
        height: CGFloat,
        cornerRadius: CGFloat = 8
    ) {
        self.width = width
        self.height = height
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        SkeletonLoadingView(
            height: height,
            cornerRadius: cornerRadius
        )
        .frame(width: width)
    }
}

/// A skeleton loading view for a circular image (like a profile picture)
struct SkeletonCircle: View {
    let size: CGFloat
    
    init(size: CGFloat = 40) {
        self.size = size
    }
    
    var body: some View {
        SkeletonLoadingView(
            height: size,
            cornerRadius: size / 2
        )
        .frame(width: size)
    }
}

/// A skeleton loading view for a fixture row
struct SkeletonFixtureRow: View {
    var body: some View {
        HStack(spacing: 12) {
            // Team logos and names
            HStack {
                VStack(alignment: .trailing, spacing: 4) {
                    SkeletonCircle(size: 32)
                    SkeletonText(lineHeight: 14)
                        .frame(width: 80)
                }
                
                // Score
                VStack(spacing: 4) {
                    SkeletonText(lineHeight: 18)
                        .frame(width: 40)
                    SkeletonText(lineHeight: 12)
                        .frame(width: 30)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    SkeletonCircle(size: 32)
                    SkeletonText(lineHeight: 14)
                        .frame(width: 80)
                }
            }
        }
        .padding(AppLayout.spacingM)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(AppLayout.cornerRadiusL)
    }
}

/// A skeleton loading view for a fixture detail
struct SkeletonFixtureDetail: View {
    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            // Header with team names and score
            HStack {
                VStack(alignment: .center, spacing: 8) {
                    SkeletonCircle(size: 60)
                    SkeletonText(lineHeight: 16)
                        .frame(width: 100)
                }

                Spacer()

                // Score
                VStack(spacing: 8) {
                    SkeletonText(lineHeight: 24)
                        .frame(width: 60)
                    SkeletonText(lineHeight: 14)
                        .frame(width: 40)
                }

                Spacer()

                VStack(alignment: .center, spacing: 8) {
                    SkeletonCircle(size: 60)
                    SkeletonText(lineHeight: 16)
                        .frame(width: 100)
                }
            }
            .padding(.horizontal)

            // Match info
            VStack(spacing: AppLayout.spacingS) {
                ForEach(0..<3, id: \.self) { _ in
                    HStack {
                        SkeletonText(lineHeight: 16)
                            .frame(width: 100)
                        Spacer()
                        SkeletonText(lineHeight: 16)
                            .frame(width: 150)
                    }
                    .padding(.horizontal)
                }
            }

            // Tab bar
            HStack(spacing: AppLayout.spacingL) {
                ForEach(0..<4, id: \.self) { _ in
                    SkeletonText(lineHeight: 16)
                        .frame(width: 60)
                }
            }
            .padding(.horizontal)

            // Content area
            VStack(spacing: AppLayout.spacingM) {
                ForEach(0..<3, id: \.self) { _ in
                    VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                        SkeletonText(lineHeight: 18)
                            .frame(width: 120)

                        SkeletonText(lines: 3, lineHeight: 16, spacing: 6)
                    }
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(AppLayout.cornerRadiusM)
                }
            }
            .padding(.horizontal)
        }
    }
}

/// A skeleton loading view for a league row
struct SkeletonLeagueRow: View {
    let showSubtitle: Bool

    init(showSubtitle: Bool = false) {
        self.showSubtitle = showSubtitle
    }

    var body: some View {
        HStack(spacing: AppLayout.spacingM) {
            // League Logo (24x24 to match real implementation)
            SkeletonCircle(size: 24)

            // League info
            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                // League name (120px width, 16px height)
                SkeletonLoadingView(height: 16, cornerRadius: 4)
                    .frame(width: 120)

                // Optional subtitle for cup competitions (30px width, 12px height)
                if showSubtitle {
                    HStack(spacing: AppLayout.spacingXS) {
                        SkeletonLoadingView(height: 12, cornerRadius: AppLayout.cornerRadiusS)
                            .frame(width: 30)
                    }
                }
            }

            Spacer()

            // Star icon (20x20 square)
            SkeletonLoadingView(height: 20, cornerRadius: 4)
                .frame(width: 20)
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.vertical, AppLayout.spacingM)
        .frame(minHeight: 56)
    }
}

/// A skeleton loading view for a league group header (Africa section)
struct SkeletonLeagueGroupHeader: View {
    var body: some View {
        HStack(spacing: AppLayout.spacingM) {
            // Africa icon (24x24 circular to match real implementation)
            SkeletonCircle(size: 24)

            // Africa text (50px width, 16px height)
            SkeletonLoadingView(height: 16, cornerRadius: 4)
                .frame(width: 50)

            Spacer()

            // Dropdown icon (16x16 square)
            SkeletonLoadingView(height: 16, cornerRadius: 4)
                .frame(width: 16)
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.vertical, AppLayout.spacingM)
        .frame(minHeight: 56)
        .background(
            RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                .fill(Color(UIColor { traitCollection in
                    return traitCollection.userInterfaceStyle == .dark ?
                        UIColor.tertiarySystemBackground : UIColor.systemBackground
                }))
        )
    }
}

// Preview
struct SkeletonLoadingView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            SkeletonText(lines: 3)
                .frame(width: 300)
            
            SkeletonCircle(size: 50)
            
            SkeletonImage(width: 300, height: 150)
            
            SkeletonFixtureRow()
                .frame(width: 350)
            
            Spacer()
        }
        .padding()
        .previewLayout(.sizeThatFits)
        
        SkeletonFixtureDetail()
            .previewDisplayName("Fixture Detail Skeleton")
    }
}
