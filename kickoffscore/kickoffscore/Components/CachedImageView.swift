import SwiftUI
import King<PERSON>er

/// A unified cached image view that provides consistent image loading and caching across the app
/// This component enables disk caching to persist images between app launches
struct CachedImageView: View {
    // Image URL
    let url: URL?
    
    // Image dimensions
    let width: CGFloat
    let height: CGFloat
    
    // Image content mode
    let contentMode: SwiftUI.ContentMode
    
    // Optional image shape
    let clipShape: AnyShape?
    
    // Optional border
    let borderColor: Color?
    let borderWidth: CGFloat
    
    // Fade duration
    let fadeDuration: Double
    
    // Placeholder view
    let placeholder: AnyView
    
    // Cache options - enable both memory and disk caching by default
    let enableDiskCache: Bool
    let cacheExpiration: TimeInterval
    
    // Initialize with required parameters and sensible defaults
    init(
        url: URL?,
        width: CGFloat,
        height: CGFloat,
        contentMode: SwiftUI.ContentMode = .fit,
        clipShape: AnyShape? = nil,
        borderColor: Color? = nil,
        borderWidth: CGFloat = 0,
        fadeDuration: Double = 0.25,
        enableDiskCache: Bool = true,
        cacheExpiration: TimeInterval = 7 * 24 * 60 * 60, // 7 days
        @ViewBuilder placeholder: () -> some View
    ) {
        self.url = url
        self.width = width
        self.height = height
        self.contentMode = contentMode
        self.clipShape = clipShape
        self.borderColor = borderColor
        self.borderWidth = borderWidth
        self.fadeDuration = fadeDuration
        self.enableDiskCache = enableDiskCache
        self.cacheExpiration = cacheExpiration
        self.placeholder = AnyView(placeholder())
    }
    
    var body: some View {
        if let imageUrl = url {
            KFImage(imageUrl)
                .placeholder { placeholder }
                // Configure caching options
                .cacheMemoryOnly(!enableDiskCache) // Enable disk cache unless explicitly disabled
                .diskCacheExpiration(.seconds(cacheExpiration))
                .memoryCacheExpiration(.seconds(300)) // 5 minutes in memory
                // Performance optimizations
                .setProcessor(DownsamplingImageProcessor(size: CGSize(width: width * 2, height: height * 2))) // 2x for retina
                .fade(duration: fadeDuration)
                .retry(maxCount: 2, interval: .seconds(1))
                // Apply image properties
                .resizable()
                .aspectRatio(contentMode: contentMode)
                .frame(width: width, height: height)
                // Apply clip shape if provided
                .clipShape(clipShape != nil ? clipShape! : AnyShape(Rectangle()))
                // Apply border if provided
                .overlay {
                    if let borderColor = borderColor, borderWidth > 0 {
                        if let clipShape = clipShape {
                            clipShape.stroke(borderColor, lineWidth: borderWidth)
                        } else {
                            Rectangle().stroke(borderColor, lineWidth: borderWidth)
                        }
                    }
                }
        } else {
            // No URL provided, show placeholder
            placeholder
                .frame(width: width, height: height)
        }
    }
}

// MARK: - Convenience Initializers

extension CachedImageView {
    /// Convenience initializer for team logos
    static func teamLogo(
        url: String?,
        size: CGFloat = 24,
        teamName: String = "Team"
    ) -> some View {
        CachedImageView(
            url: URL(string: url ?? ""),
            width: size,
            height: size,
            contentMode: .fit,
            clipShape: AnyShape(RoundedRectangle(cornerRadius: 4)),
            fadeDuration: 0.2
        ) {
            Image(systemName: "soccerball")
                .font(.system(size: size * 0.6))
                .foregroundColor(Color(UIColor.secondaryLabel))
                .frame(width: size, height: size)
                .background(Color(UIColor.tertiarySystemFill))
                .clipShape(RoundedRectangle(cornerRadius: 4))
        }
        .accessibilityLabel("\(teamName) logo")
    }
    
    /// Convenience initializer for league logos
    static func leagueLogo(
        url: String?,
        size: CGFloat = 24,
        leagueName: String = "League"
    ) -> some View {
        CachedImageView(
            url: URL(string: url ?? ""),
            width: size,
            height: size,
            contentMode: .fit,
            clipShape: AnyShape(RoundedRectangle(cornerRadius: 4)),
            fadeDuration: 0.2
        ) {
            RoundedRectangle(cornerRadius: 4)
                .fill(Color(UIColor.tertiarySystemFill))
                .overlay(
                    Image(systemName: "trophy.fill")
                        .font(.system(size: size * 0.6))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                )
        }
        .accessibilityLabel("\(leagueName) logo")
    }
    
    /// Convenience initializer for player photos
    static func playerPhoto(
        url: String?,
        size: CGFloat = 40,
        playerName: String = "Player"
    ) -> some View {
        CachedImageView(
            url: URL(string: url ?? ""),
            width: size,
            height: size,
            contentMode: .fill,
            clipShape: AnyShape(Circle()),
            borderColor: .white,
            borderWidth: 1,
            fadeDuration: 0.2
        ) {
            Circle()
                .fill(Color(UIColor.tertiarySystemFill))
                .overlay(
                    Image(systemName: "person.fill")
                        .font(.system(size: size * 0.5))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                )
        }
        .accessibilityLabel("\(playerName) photo")
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // Team logo example
        CachedImageView.teamLogo(
            url: "https://media.api-sports.io/football/teams/33.png",
            size: 32,
            teamName: "Manchester United"
        )
        
        // League logo example
        CachedImageView.leagueLogo(
            url: "https://media.api-sports.io/football/leagues/39.png",
            size: 32,
            leagueName: "Premier League"
        )
        
        // Player photo example
        CachedImageView.playerPhoto(
            url: "https://media.api-sports.io/football/players/882.png",
            size: 50,
            playerName: "Cristiano Ronaldo"
        )
    }
    .padding()
}
