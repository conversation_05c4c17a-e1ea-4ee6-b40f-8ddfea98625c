import SwiftUI

/// A simple skeleton loading view that displays a list of items with a square on the left and text lines on the right
struct SimpleSkeletonListView: View {
    // Number of items to show
    let itemCount: Int

    // Whether to show a header
    let showHeader: Bool

    // Animation state
    @State private var isAnimating = false

    init(itemCount: Int = 8, showHeader: Bool = false) {
        self.itemCount = itemCount
        self.showHeader = showHeader
    }

    var body: some View {
        VStack(spacing: 0) {
            // Optional header
            if showHeader {
                HStack {
                    SkeletonText(lineHeight: 18)
                        .frame(width: 120)

                    Spacer()

                    SkeletonText(lineHeight: 18)
                        .frame(width: 80)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .padding(.bottom, 8)
            }

            // List items
            VStack(spacing: 0) {
                ForEach(0..<itemCount, id: \.self) { _ in
                    SimpleSkeletonListItem()
                }
            }
        }
        .background(AppColors.tertiaryBackground)
    }
}

/// A single item in the simple skeleton list
struct SimpleSkeletonListItem: View {
    var body: some View {
        HStack(spacing: 16) {
            // Square on the left (logo/avatar placeholder)
            SkeletonLoadingView(
                height: 50,
                cornerRadius: 4
            )
            .frame(width: 50)

            // Text content
            VStack(alignment: .leading, spacing: 8) {
                // Title line
                SkeletonLoadingView(
                    height: 16,
                    cornerRadius: 4
                )
                .frame(width: .random(in: 120...200))

                // Subtitle line
                SkeletonLoadingView(
                    height: 14,
                    cornerRadius: 4
                )
                .frame(width: .random(in: 180...240))
            }

            Spacer()
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(AppColors.tertiaryBackground)
        // Add a subtle divider
        .overlay(
            Divider()
                .background(Color(UIColor.systemGray5))
                .padding(.leading, 66), // Align with the end of the square
            alignment: .bottom
        )
    }
}

/// A skeleton loading view that matches the fixture row structure
struct SkeletonFixtureRowView: View {
    var body: some View {
        HStack(alignment: .center, spacing: AppLayout.spacingM) {
            // Left: Date/Time section (matches LeagueFixtureRowView structure)
            VStack(alignment: .leading, spacing: 2) {
                // Date skeleton (e.g., "Fri Aug 16")
                SkeletonLoadingView(height: 12, cornerRadius: 2)
                    .frame(width: 60)

                // Time skeleton (e.g., "8:00 pm")
                SkeletonLoadingView(height: 12, cornerRadius: 2)
                    .frame(width: 45)
            }
            .frame(width: 80, alignment: .leading)

            // Middle: Teams section
            VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                // Home team row
                HStack(spacing: AppLayout.spacingS) {
                    // Team logo
                    SkeletonCircle(size: AppLayout.iconSizeM)

                    // Team name
                    SkeletonLoadingView(height: 16, cornerRadius: 4)
                        .frame(width: .random(in: 100...140))
                }

                // Away team row
                HStack(spacing: AppLayout.spacingS) {
                    // Team logo
                    SkeletonCircle(size: AppLayout.iconSizeM)

                    // Team name
                    SkeletonLoadingView(height: 16, cornerRadius: 4)
                        .frame(width: .random(in: 100...140))
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // Right: Score/Status section
            VStack(alignment: .center, spacing: AppLayout.spacingS) {
                // Score or status placeholder
                SkeletonLoadingView(height: 16, cornerRadius: 4)
                    .frame(width: 20)

                SkeletonLoadingView(height: 16, cornerRadius: 4)
                    .frame(width: 20)
            }
            .frame(minWidth: 60, alignment: .trailing)
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.vertical, 5)
        .shimmering()
    }
}

/// A skeleton loading view for the league fixtures screen using the simple list style
struct SimpleSkeletonLeagueFixturesView: View {
    var body: some View {
        SimpleSkeletonListView(itemCount: 10, showHeader: true)
    }
}

/// A skeleton loading view specifically for league matches that matches the actual fixture display
struct SkeletonLeagueMatchesView: View {
    let itemCount: Int

    init(itemCount: Int = 8) {
        self.itemCount = itemCount
    }

    var body: some View {
        VStack(spacing: 0) {
            ForEach(0..<itemCount, id: \.self) { index in
                VStack {
                    SkeletonFixtureRowView()

                    // Add divider if not the last item
                    if index < itemCount - 1 {
                        Divider()
                            .background(AppColors.separator)
                    }
                }
            }
        }
        .background(AppColors.tertiaryBackground)
    }
}

// Preview
struct SimpleSkeletonListView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SimpleSkeletonListView(itemCount: 8, showHeader: true)
                .previewDisplayName("With Header")

            SimpleSkeletonListView(itemCount: 5, showHeader: false)
                .previewDisplayName("Without Header")

            SimpleSkeletonListItem()
                .previewLayout(.sizeThatFits)
                .previewDisplayName("Single Item")
        }
    }
}
