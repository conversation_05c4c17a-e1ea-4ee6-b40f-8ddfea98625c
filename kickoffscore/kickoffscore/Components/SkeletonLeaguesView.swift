import SwiftUI

/// A skeleton loading view that mimics the structure of the LeaguesView
struct SkeletonLeaguesView: View {
    var body: some View {
        ScrollView(showsIndicators: false) {
            LazyVStack(spacing: AppLayout.spacingS) {
                // Top Competitions Section
                topCompetitionsSkeleton

                // All Competitions Section
                allCompetitionsSkeleton
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.bottom, AppLayout.spacingL)
        }
    }

    // MARK: - Top Competitions Skeleton
    private var topCompetitionsSkeleton: some View {
        VStack(spacing: AppLayout.spacingS) {
            // Top Competitions Header
            topCompetitionsHeaderSkeleton

            // Top Competition Items (7 items matching real data)
            ForEach(0..<7, id: \.self) { index in
                VStack(spacing: 0) {
                    // Last 2 items (index 5 and 6) have subtitles for cup competitions
                    SkeletonLeagueRow(showSubtitle: index >= 5)
                }
                .padding(.top, AppLayout.spacingXS)
                .padding(.bottom, AppLayout.spacingXS)
                .background(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                        .fill(Color(UIColor { traitCollection in
                            return traitCollection.userInterfaceStyle == .dark ?
                                UIColor.tertiarySystemBackground : UIColor.systemBackground
                        }))
                )
            }
        }
    }

    // MARK: - Top Competitions Header Skeleton
    private var topCompetitionsHeaderSkeleton: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
            HStack {
                Text("Top Competitions")
                    .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                    .foregroundColor(AppColors.text)
                Spacer()
            }

            Rectangle()
                .fill(AppColors.Brand.primary.opacity(0.3))
                .frame(height: 2)
                .frame(maxWidth: 120)
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.bottom, AppLayout.spacingS)
        .padding(.top, AppLayout.spacingXS)
    }
    
    // MARK: - All Competitions Skeleton
    private var allCompetitionsSkeleton: some View {
        VStack(spacing: AppLayout.spacingS) {
            // All Competitions Header
            allCompetitionsHeaderSkeleton

            // Competition Groups (1 group as shown in real data - Africa section)
            SkeletonLeagueGroupHeader()
        }
        .padding(.top, AppLayout.spacingM)
    }

    // MARK: - All Competitions Header Skeleton
    private var allCompetitionsHeaderSkeleton: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
            HStack {
                Text("All Competitions")
                    .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                    .foregroundColor(AppColors.text)
                Spacer()
            }

            Rectangle()
                .fill(AppColors.Brand.secondary.opacity(0.3))
                .frame(height: 2)
                .frame(maxWidth: 140)
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.bottom, AppLayout.spacingS)
        .padding(.top, AppLayout.spacingM)
    }
}

#Preview {
    SkeletonLeaguesView()
        .background(AppColors.background)
}
