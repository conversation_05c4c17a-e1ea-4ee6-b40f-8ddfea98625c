import SwiftUI

struct DropdownSelector<T: Identifiable, Content: View>: View {
    @Binding var isOpen: Bool
    let title: String
    let placeholder: String
    let options: [T]
    let selectedOption: T?
    let onSelect: (T) -> Void
    let optionLabel: (T) -> String
    let optionContent: (T) -> Content

    @State private var contentHeight: CGFloat = 0
    @State private var scrollOffset: CGFloat = 0
    @State private var searchText: String = ""

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
            // Title
            Text(title)
                .font(AppTypography.dynamicFont(style: .caption))
                .foregroundColor(AppColors.secondaryText)

            // Dropdown button
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isOpen.toggle()
                    if !isOpen {
                        searchText = ""
                    }
                }
            }) {
                HStack {
                    Text(selectedOption != nil ? optionLabel(selectedOption!) : placeholder)
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(selectedOption != nil ? AppColors.text : AppColors.secondaryText)

                    Spacer()

                    Image(systemName: isOpen ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColors.secondaryText)
                }
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.vertical, AppLayout.spacingS)
                .background(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                        .fill(Color(UIColor.secondarySystemBackground))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                        .stroke(isOpen ? AppColors.Brand.primary : Color.clear, lineWidth: 1)
                )
            }

            // Dropdown content
            if isOpen {
                VStack(spacing: 0) {
                    // Search field
                    if options.count > 5 {
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(AppColors.secondaryText)

                            TextField("Search", text: $searchText)
                                .font(AppTypography.dynamicFont(style: .subheadline))
                                .foregroundColor(AppColors.text)
                        }
                        .padding(.horizontal, AppLayout.spacingM)
                        .padding(.vertical, AppLayout.spacingS)
                        .background(Color(UIColor.tertiarySystemBackground))
                    }

                    // Options list
                    ScrollView {
                        VStack(spacing: 0) {
                            ForEach(filteredOptions) { option in
                                Button(action: {
                                    onSelect(option)
                                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                        isOpen = false
                                        searchText = ""
                                    }
                                }) {
                                    HStack {
                                        optionContent(option)

                                        Spacer()

                                        if selectedOption?.id == option.id {
                                            Image(systemName: "checkmark")
                                                .font(.system(size: 14, weight: .bold))
                                                .foregroundColor(AppColors.Brand.primary)
                                        }
                                    }
                                    .padding(.horizontal, AppLayout.spacingM)
                                    .padding(.vertical, AppLayout.spacingS)
                                    .background(
                                        selectedOption?.id == option.id ?
                                        AppColors.Brand.primary.opacity(0.1) :
                                        Color(UIColor.secondarySystemBackground)
                                    )
                                }

                                if option.id != filteredOptions.last?.id {
                                    Divider()
                                        .padding(.leading, AppLayout.spacingM)
                                }
                            }
                        }
                    }
                    .frame(maxHeight: min(CGFloat(filteredOptions.count) * 44, 220))
                }
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(AppLayout.cornerRadiusM)
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
    }

    private var filteredOptions: [T] {
        if searchText.isEmpty {
            return options
        } else {
            return options.filter { option in
                optionLabel(option).lowercased().contains(searchText.lowercased())
            }
        }
    }
}

// Extension to make Int conform to Identifiable for bet type IDs
extension Int: @retroactive Identifiable {
    public var id: Int {
        return self
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State private var isOpen = false
        @State private var selectedOption: String? = nil

        let options = ["Option 1", "Option 2", "Option 3", "Option 4", "Option 5"]

        var body: some View {
            VStack {
                Spacer()

                DropdownSelector(
                    isOpen: $isOpen,
                    title: "Select an option",
                    placeholder: "Choose an option",
                    options: options.map { Option(name: $0) },
                    selectedOption: selectedOption.map { Option(name: $0) },
                    onSelect: { option in
                        selectedOption = option.name
                    },
                    optionLabel: { $0.name },
                    optionContent: { option in
                        Text(option.name)
                            .font(AppTypography.dynamicFont(style: .subheadline))
                            .foregroundColor(AppColors.text)
                    }
                )
                .padding()

                Spacer()
            }
            .background(AppColors.background)
        }

        struct Option: Identifiable {
            let id = UUID()
            let name: String
        }
    }

    return PreviewWrapper()
}
