import SwiftUI

struct NonCollapsibleSection<Content: View>: View {
    let title: String
    let content: Content

    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header (non-clickable)
            HStack {
                Text(title)
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                    .foregroundColor(AppColors.text)
                    .lineLimit(1)

                Spacer()
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, AppLayout.spacingS)
            .background(
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                    .fill(Color(UIColor.secondarySystemBackground))
            )
            .overlay(
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                    .stroke(AppColors.Brand.primary, lineWidth: 1)
            )

            // Content (always visible)
            VStack(spacing: AppLayout.spacingS) {
                content
            }
            .padding(.top, AppLayout.spacingS)
            .padding(.horizontal, AppLayout.spacingS)
            .padding(.bottom, AppLayout.spacingS)
            .background(Color(UIColor.secondarySystemBackground).opacity(0.5))
            .cornerRadius(AppLayout.cornerRadiusM)
            .padding(.top, AppLayout.spacingXS)
        }
    }
}

// MARK: - Preview
#Preview {
    NonCollapsibleSection(
        title: "Match Winner"
    ) {
        Text("Content goes here")
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
    }
    .padding()
    .background(AppColors.background)
}
