import SwiftUI

/// A skeleton loading view for the league fixtures screen
struct SkeletonLeagueFixturesView: View {
    // Number of leagues to show
    let leagueCount: Int
    
    // Number of fixtures per league
    let fixturesPerLeague: Int
    
    init(leagueCount: Int = 3, fixturesPerLeague: Int = 3) {
        self.leagueCount = leagueCount
        self.fixturesPerLeague = fixturesPerLeague
    }
    
    var body: some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: AppLayout.spacingM) {
                // Date selector skeleton
                HStack {
                    SkeletonText(lineHeight: 18)
                        .frame(width: 120)

                    Spacer()

                    SkeletonText(lineHeight: 18)
                        .frame(width: 80)
                }
                .padding(.horizontal, AppLayout.spacingL)
                .padding(.vertical, AppLayout.spacingS)
                
                // League fixtures
                ForEach(0..<leagueCount, id: \.self) { leagueIndex in
                    VStack(spacing: AppLayout.spacingS) {
                        // League header
                        HStack {
                            // League logo
                            SkeletonCircle(size: 30)
                            
                            // League name
                            SkeletonText(lineHeight: 18)
                                .frame(width: 150)
                            
                            Spacer()
                            
                            // Expand/collapse icon
                            SkeletonCircle(size: 20)
                        }
                        .padding(.horizontal, AppLayout.spacingL)
                        .padding(.vertical, AppLayout.spacingS)
                        
                        // Fixtures
                        ForEach(0..<fixturesPerLeague, id: \.self) { _ in
                            SkeletonFixtureRow()
                                .padding(.horizontal, AppLayout.spacingL)
                        }
                    }
                    .padding(.bottom, AppLayout.spacingM)
                }
            }
            .padding(.vertical, AppLayout.spacingM)
        }
    }
}

/// A skeleton loading view for multiple leagues with fixtures
struct SkeletonLeaguesList: View {
    let leagueCount: Int
    
    init(leagueCount: Int = 3) {
        self.leagueCount = leagueCount
    }
    
    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            ForEach(0..<leagueCount, id: \.self) { _ in
                SkeletonLeagueItem()
            }
        }
        .padding(.horizontal, AppLayout.spacingL)
    }
}

/// A skeleton loading view for a single league item
struct SkeletonLeagueItem: View {
    var body: some View {
        VStack(spacing: AppLayout.spacingS) {
            // League header
            HStack {
                // League logo
                SkeletonCircle(size: 30)
                
                // League name
                SkeletonText(lineHeight: 18)
                    .frame(width: 150)
                
                Spacer()
                
                // Expand/collapse icon
                SkeletonCircle(size: 20)
            }
            .padding(.vertical, AppLayout.spacingS)
            
            // Fixtures (3 per league)
            ForEach(0..<3, id: \.self) { _ in
                SkeletonFixtureRow()
            }
        }
        .padding(.vertical, AppLayout.spacingS)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(AppLayout.cornerRadiusL)
    }
}

// Preview
struct SkeletonLeagueFixturesView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SkeletonLeagueFixturesView()
                .previewDisplayName("Full Screen")
            
            SkeletonLeagueItem()
                .padding()
                .previewLayout(.sizeThatFits)
                .previewDisplayName("Single League")
            
            SkeletonFixtureRow()
                .padding()
                .previewLayout(.sizeThatFits)
                .previewDisplayName("Single Fixture")
        }
    }
}
