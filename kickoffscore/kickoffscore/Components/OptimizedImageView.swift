import SwiftUI
import Kingfisher

/// A performance-optimized image view component that efficiently loads and caches images
struct OptimizedImageView: View {
    // Image URL
    let url: URL?

    // Image dimensions
    let width: CGFloat
    let height: CGFloat

    // Optional placeholder
    let placeholder: AnyView

    // Image content mode
    let contentMode: SwiftUI.ContentMode

    // Optional image shape
    let clipShape: AnyShape?

    // Optional border
    let borderColor: Color?
    let borderWidth: CGFloat

    // Downsampling size (default to 2x the display size for retina screens)
    let downsampleSize: CGSize

    // Fade duration
    let fadeDuration: Double

    // Memory-only caching
    let memoryOnly: Bool

    // Initialize with required parameters and defaults for optional ones
    init(
        url: URL?,
        width: CGFloat,
        height: CGFloat,
        contentMode: SwiftUI.ContentMode = .fill,
        clipShape: AnyShape? = nil,
        borderColor: Color? = nil,
        borderWidth: CGFloat = 0,
        downsampleSize: CGSize? = nil,
        fadeDuration: Double = 0.1,
        memoryOnly: Bool = true,
        @ViewBuilder placeholder: () -> some View
    ) {
        self.url = url
        self.width = width
        self.height = height
        self.contentMode = contentMode
        self.clipShape = clipShape
        self.borderColor = borderColor
        self.borderWidth = borderWidth
        self.placeholder = AnyView(placeholder())
        self.fadeDuration = fadeDuration
        self.memoryOnly = memoryOnly

        // Default downsampling size is 2x the display size
        self.downsampleSize = downsampleSize ?? CGSize(width: width * 2, height: height * 2)
    }

    var body: some View {
        if let imageUrl = url {
            KFImage(imageUrl)
                .placeholder { placeholder }
                // Performance optimizations
                .setProcessor(DownsamplingImageProcessor(size: downsampleSize))
                .cacheMemoryOnly(memoryOnly)
                .fade(duration: fadeDuration)
                .resizable()
                .aspectRatio(contentMode: contentMode)
                .frame(width: width, height: height)
                // Apply clip shape if provided
                .clipShape(clipShape != nil ? clipShape! : AnyShape(Rectangle()))
                // Apply border if provided
                .overlay {
                    if let borderColor = borderColor, borderWidth > 0 {
                        if let clipShape = clipShape {
                            clipShape.stroke(borderColor, lineWidth: borderWidth)
                        } else {
                            Rectangle().stroke(borderColor, lineWidth: borderWidth)
                        }
                    }
                }
                // Cancel image loading when view disappears
                .onDisappear {
                    KingfisherManager.shared.downloader.cancel(url: imageUrl)
                }
        } else {
            // No URL provided, show placeholder
            placeholder
                .frame(width: width, height: height)
        }
    }
}

// Helper to wrap any Shape in AnyShape for type erasure
struct AnyShape: Shape {
    // Make the closure Sendable to conform to Swift 6 requirements
    private let _path: @Sendable (CGRect) -> Path

    init<S: Shape>(_ shape: S) {
        // Capture the shape's path method in a Sendable closure
        _path = { rect in
            shape.path(in: rect)
        }
    }

    func path(in rect: CGRect) -> Path {
        _path(rect)
    }
}

// Convenience extensions for common use cases
extension OptimizedImageView {
    /// Create a circular profile image
    static func circularProfile(
        url: URL?,
        size: CGFloat,
        borderColor: Color? = nil,
        borderWidth: CGFloat = 0
    ) -> OptimizedImageView {
        OptimizedImageView(
            url: url,
            width: size,
            height: size,
            clipShape: AnyShape(Circle()),
            borderColor: borderColor,
            borderWidth: borderWidth
        ) {
            Circle()
                .fill(Color(UIColor.secondarySystemFill))
                .frame(width: size, height: size)
        }
    }

    /// Create a team logo image
    static func teamLogo(
        url: URL?,
        size: CGFloat
    ) -> OptimizedImageView {
        OptimizedImageView(
            url: url,
            width: size,
            height: size,
            contentMode: .fit
        ) {
            Image(systemName: "shield")
                .resizable()
                .scaledToFit()
                .foregroundColor(Color(UIColor.secondaryLabel))
                .frame(width: size, height: size)
        }
    }
}

// Preview
struct OptimizedImageView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // Circular profile image
            OptimizedImageView.circularProfile(
                url: URL(string: "https://media.api-sports.io/football/players/276.png"),
                size: 80,
                borderColor: .white,
                borderWidth: 2
            )

            // Team logo
            OptimizedImageView.teamLogo(
                url: URL(string: "https://media.api-sports.io/football/teams/33.png"),
                size: 60
            )

            // Custom image
            OptimizedImageView(
                url: URL(string: "https://media.api-sports.io/football/leagues/39.png"),
                width: 100,
                height: 100,
                contentMode: .fit
            ) {
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: 100, height: 100)
            }
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
