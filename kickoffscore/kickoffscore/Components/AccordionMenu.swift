import SwiftUI

struct AccordionMenu<T: Identifiable, Content: View>: View {
    @Binding var isExpanded: Bool
    let title: String
    let placeholder: String
    let options: [T]
    let selectedOption: T?
    let onSelect: (T) -> Void
    let optionLabel: (T) -> String
    let optionContent: (T) -> Content
    
    @State private var searchText: String = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                Text(title)
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(AppColors.secondaryText)
                
                // Accordion header button
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                        if !isExpanded {
                            searchText = ""
                        }
                    }
                }) {
                    HStack {
                        Text(selectedOption != nil ? optionLabel(selectedOption!) : placeholder)
                            .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                            .foregroundColor(selectedOption != nil ? AppColors.text : AppColors.secondaryText)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppColors.secondaryText)
                    }
                    .padding(.horizontal, AppLayout.spacingM)
                    .padding(.vertical, AppLayout.spacingS)
                    .background(
                        RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                            .fill(Color(UIColor.secondarySystemBackground))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                            .stroke(isExpanded ? AppColors.Brand.primary : Color.clear, lineWidth: 1)
                    )
                }
            }
            
            // Accordion content
            if isExpanded {
                VStack(spacing: 0) {
                    // Search field for large option lists
                    if options.count > 5 {
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(AppColors.secondaryText)
                            
                            TextField("Search", text: $searchText)
                                .font(AppTypography.dynamicFont(style: .subheadline))
                                .foregroundColor(AppColors.text)
                        }
                        .padding(.horizontal, AppLayout.spacingM)
                        .padding(.vertical, AppLayout.spacingS)
                        .background(Color(UIColor.tertiarySystemBackground))
                    }
                    
                    // Options list
                    ScrollView {
                        LazyVStack(spacing: 0) {
                            ForEach(filteredOptions) { option in
                                Button(action: {
                                    onSelect(option)
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        isExpanded = false
                                        searchText = ""
                                    }
                                }) {
                                    HStack {
                                        optionContent(option)
                                        
                                        Spacer()
                                        
                                        if selectedOption?.id == option.id {
                                            Image(systemName: "checkmark")
                                                .font(.system(size: 14, weight: .bold))
                                                .foregroundColor(AppColors.Brand.primary)
                                        }
                                    }
                                    .padding(.horizontal, AppLayout.spacingM)
                                    .padding(.vertical, AppLayout.spacingS)
                                    .background(
                                        selectedOption?.id == option.id ?
                                        AppColors.Brand.primary.opacity(0.1) :
                                        Color(UIColor.secondarySystemBackground)
                                    )
                                }
                                
                                if option.id != filteredOptions.last?.id {
                                    Divider()
                                        .padding(.leading, AppLayout.spacingM)
                                }
                            }
                        }
                    }
                    .frame(maxHeight: min(CGFloat(filteredOptions.count) * 44, 220))
                }
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(AppLayout.cornerRadiusM)
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
    }
    
    private var filteredOptions: [T] {
        if searchText.isEmpty {
            return options
        } else {
            return options.filter { option in
                optionLabel(option).lowercased().contains(searchText.lowercased())
            }
        }
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State private var isExpanded = false
        @State private var selectedOption: String? = nil
        
        let options = ["Option 1", "Option 2", "Option 3", "Option 4", "Option 5"]
        
        var body: some View {
            VStack {
                Spacer()
                
                AccordionMenu(
                    isExpanded: $isExpanded,
                    title: "Select an option",
                    placeholder: "Choose an option",
                    options: options.map { Option(name: $0) },
                    selectedOption: selectedOption.map { Option(name: $0) },
                    onSelect: { option in
                        selectedOption = option.name
                    },
                    optionLabel: { $0.name },
                    optionContent: { option in
                        Text(option.name)
                            .font(AppTypography.dynamicFont(style: .subheadline))
                            .foregroundColor(AppColors.text)
                    }
                )
                .padding()
                
                Spacer()
            }
            .background(AppColors.background)
        }
        
        struct Option: Identifiable {
            let id = UUID()
            let name: String
        }
    }
    
    return PreviewWrapper()
}
