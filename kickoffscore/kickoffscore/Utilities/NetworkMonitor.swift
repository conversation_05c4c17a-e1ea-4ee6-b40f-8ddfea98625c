import Foundation
import Network
import Combine

/// A class to monitor network connectivity
class NetworkMonitor: ObservableObject {
    // Singleton instance
    static let shared = NetworkMonitor()
    
    // Published properties for UI updates
    @Published var isConnected = true
    @Published var connectionType: ConnectionType = .unknown
    
    // Network path monitor
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    // Connection type enum
    enum ConnectionType {
        case wifi
        case cellular
        case ethernet
        case unknown
    }
    
    private init() {
        // Start monitoring network changes
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
                self?.connectionType = self?.getConnectionType(path) ?? .unknown
            }
        }
        monitor.start(queue: queue)
    }
    
    deinit {
        // Stop monitoring when deallocated
        monitor.cancel()
    }
    
    /// Get the connection type from the network path
    /// - Parameter path: The network path
    /// - Returns: The connection type
    private func getConnectionType(_ path: NWPath) -> ConnectionType {
        if path.usesInterfaceType(.wifi) {
            return .wifi
        } else if path.usesInterfaceType(.cellular) {
            return .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            return .ethernet
        } else {
            return .unknown
        }
    }
    
    /// Check if the device is connected to the internet
    /// - Returns: True if connected, false otherwise
    func isNetworkAvailable() -> Bool {
        return isConnected
    }
    
    /// Get a publisher that emits when the network status changes
    /// - Returns: A publisher that emits the current network status
    func networkStatusPublisher() -> AnyPublisher<Bool, Never> {
        return $isConnected.eraseToAnyPublisher()
    }
}
