import Foundation
import SwiftUI

/// A utility class for monitoring and managing cache performance
class CacheMonitor: ObservableObject {
    // Singleton instance
    static let shared = CacheMonitor()
    
    // Published properties for UI updates
    @Published var isMonitoring = false
    @Published var currentMetrics = CacheMetrics()
    @Published var cacheInfo = (count: 0, estimatedSize: "0 B")
    
    // Monitoring configuration
    private var monitoringTimer: Timer?
    private let monitoringInterval: TimeInterval = 30 // 30 seconds
    
    // Performance thresholds
    private let lowHitRateThreshold: Double = 0.5 // 50%
    private let highResponseTimeThreshold: TimeInterval = 0.1 // 100ms
    
    private init() {}
    
    // MARK: - Monitoring Control
    
    /// Start monitoring cache performance
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        updateMetrics()
        
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: monitoringInterval, repeats: true) { [weak self] _ in
            self?.updateMetrics()
            self?.checkPerformanceThresholds()
        }
        
        Logger.info("Cache monitoring started", category: .performance)
    }
    
    /// Stop monitoring cache performance
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        Logger.info("Cache monitoring stopped", category: .performance)
    }
    
    /// Update metrics manually
    func updateMetrics() {
        DispatchQueue.main.async { [weak self] in
            self?.currentMetrics = CacheManager.shared.getMetrics()
            self?.cacheInfo = CacheManager.shared.getCacheInfo()
        }
    }
    
    // MARK: - Performance Analysis
    
    /// Check if cache performance meets thresholds and log warnings
    private func checkPerformanceThresholds() {
        let metrics = CacheManager.shared.getMetrics()
        
        // Check hit rate
        if metrics.totalRequests > 10 && metrics.hitRate < lowHitRateThreshold {
            Logger.warning("Low cache hit rate: \(String(format: "%.2f%%", metrics.hitRate * 100)) (threshold: \(String(format: "%.2f%%", lowHitRateThreshold * 100)))", category: .performance)
        }
        
        // Check response time
        if metrics.averageResponseTime > highResponseTimeThreshold {
            Logger.warning("High cache response time: \(String(format: "%.4f", metrics.averageResponseTime))s (threshold: \(String(format: "%.4f", highResponseTimeThreshold))s)", category: .performance)
        }
    }
    
    /// Get cache performance recommendations
    func getPerformanceRecommendations() -> [String] {
        let metrics = CacheManager.shared.getMetrics()
        var recommendations: [String] = []
        
        if metrics.totalRequests > 10 {
            if metrics.hitRate < 0.3 {
                recommendations.append("Very low hit rate (\(String(format: "%.1f%%", metrics.hitRate * 100))). Consider increasing cache duration or reviewing cache keys.")
            } else if metrics.hitRate < 0.5 {
                recommendations.append("Low hit rate (\(String(format: "%.1f%%", metrics.hitRate * 100))). Consider optimizing cache strategy.")
            }
        }
        
        if metrics.averageResponseTime > 0.05 {
            recommendations.append("High cache response time (\(String(format: "%.4f", metrics.averageResponseTime))s). Consider cache optimization.")
        }
        
        let cacheInfo = CacheManager.shared.getCacheInfo()
        if cacheInfo.count > 80 {
            recommendations.append("Cache is near capacity (\(cacheInfo.count) items). Consider increasing cache limits or implementing better eviction.")
        }
        
        if recommendations.isEmpty {
            recommendations.append("Cache performance is optimal.")
        }
        
        return recommendations
    }
    
    // MARK: - Cache Management Actions
    
    /// Clear cache and reset metrics
    func clearCacheAndMetrics() {
        CacheManager.shared.clearCache()
        CacheManager.shared.resetMetrics()
        updateMetrics()
        Logger.info("Cache and metrics cleared manually", category: .performance)
    }
    
    /// Trigger smart invalidation for a content type
    func invalidateContent(type: CacheContentType) {
        CacheManager.shared.invalidateCache(for: type)
        updateMetrics()
        Logger.info("Manually invalidated cache for content type: \(type)", category: .performance)
    }
    
    /// Export cache metrics for analysis
    func exportMetrics() -> String {
        let metrics = CacheManager.shared.getMetrics()
        let cacheInfo = CacheManager.shared.getCacheInfo()
        let recommendations = getPerformanceRecommendations()
        
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .medium, timeStyle: .medium)
        
        return """
        Cache Performance Report - \(timestamp)
        
        Metrics:
        - Total Requests: \(metrics.totalRequests)
        - Cache Hit Rate: \(String(format: "%.2f%%", metrics.hitRate * 100))
        - Cache Hits: \(metrics.cacheHits)
        - Cache Misses: \(metrics.cacheMisses)
        - Average Response Time: \(String(format: "%.4f", metrics.averageResponseTime))s
        - Total Response Time: \(String(format: "%.4f", metrics.totalResponseTime))s
        
        Cache Info:
        - Items Count: \(cacheInfo.count)
        - Estimated Size: \(cacheInfo.estimatedSize)
        
        Recommendations:
        \(recommendations.map { "- \($0)" }.joined(separator: "\n"))
        """
    }
}

// MARK: - SwiftUI Integration

/// A SwiftUI view for displaying cache performance metrics
struct CachePerformanceView: View {
    @StateObject private var monitor = CacheMonitor.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Text("Cache Performance")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button(monitor.isMonitoring ? "Stop" : "Start") {
                    if monitor.isMonitoring {
                        monitor.stopMonitoring()
                    } else {
                        monitor.startMonitoring()
                    }
                }
                .foregroundColor(monitor.isMonitoring ? .red : .green)
            }
            
            // Metrics
            if monitor.currentMetrics.totalRequests > 0 {
                VStack(alignment: .leading, spacing: 8) {
                    MetricRow(title: "Hit Rate", value: "\(String(format: "%.1f%%", monitor.currentMetrics.hitRate * 100))")
                    MetricRow(title: "Total Requests", value: "\(monitor.currentMetrics.totalRequests)")
                    MetricRow(title: "Cache Hits", value: "\(monitor.currentMetrics.cacheHits)")
                    MetricRow(title: "Cache Misses", value: "\(monitor.currentMetrics.cacheMisses)")
                    MetricRow(title: "Avg Response Time", value: "\(String(format: "%.4f", monitor.currentMetrics.averageResponseTime))s")
                    MetricRow(title: "Cache Items", value: "\(monitor.cacheInfo.count)")
                    MetricRow(title: "Estimated Size", value: monitor.cacheInfo.estimatedSize)
                }
            } else {
                Text("No cache metrics available")
                    .foregroundColor(.secondary)
            }
            
            // Actions
            HStack {
                Button("Clear Cache") {
                    monitor.clearCacheAndMetrics()
                }
                .foregroundColor(.red)
                
                Spacer()
                
                Button("Refresh") {
                    monitor.updateMetrics()
                }
            }
        }
        .padding()
        .onAppear {
            monitor.updateMetrics()
        }
    }
}

struct MetricRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .fontWeight(.medium)
        }
    }
}

/// A demo view to test cache performance features
struct CachePerformanceDemoView: View {
    @StateObject private var monitor = CacheMonitor.shared
    @State private var isTestingCache = false

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Cache Performance View
                CachePerformanceView()

                Divider()

                // Demo Actions
                VStack(spacing: 12) {
                    Text("Cache Testing")
                        .font(.headline)

                    Button("Test Cache Performance") {
                        testCachePerformance()
                    }
                    .disabled(isTestingCache)

                    Button("Simulate Live Updates") {
                        simulateLiveUpdates()
                    }

                    Button("Export Metrics") {
                        exportMetrics()
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)

                Spacer()
            }
            .padding()
            .navigationTitle("Cache Performance")
            .onAppear {
                monitor.startMonitoring()
            }
            .onDisappear {
                monitor.stopMonitoring()
            }
        }
    }

    private func testCachePerformance() {
        isTestingCache = true

        Task {
            // Simulate multiple cache operations
            for i in 1...20 {
                let testData = "Test data \(i)".data(using: .utf8)!
                let key = "test_key_\(i)"

                // Save to cache
                CacheManager.shared.saveData(testData, forKey: key, contentType: .fixture)

                // Read from cache
                _ = CacheManager.shared.getData(forKey: key)

                // Small delay to simulate real usage
                try? await Task.sleep(nanoseconds: 10_000_000) // 10ms
            }

            await MainActor.run {
                isTestingCache = false
                monitor.updateMetrics()
            }
        }
    }

    private func simulateLiveUpdates() {
        // Simulate fixture updates
        CacheManager.shared.triggerInvalidation(event: "fixture_updated")

        // Simulate live data updates
        CacheManager.shared.triggerInvalidation(event: "live_data_updated")

        monitor.updateMetrics()
    }

    private func exportMetrics() {
        let metricsReport = monitor.exportMetrics()
        print("=== CACHE METRICS REPORT ===")
        print(metricsReport)
        print("=== END REPORT ===")

        // In a real app, you might save this to a file or share it
        Logger.info("Cache metrics exported to console", category: .performance)
    }
}

#if DEBUG
struct CachePerformanceView_Previews: PreviewProvider {
    static var previews: some View {
        CachePerformanceView()
    }
}

struct CachePerformanceDemoView_Previews: PreviewProvider {
    static var previews: some View {
        CachePerformanceDemoView()
    }
}
#endif
