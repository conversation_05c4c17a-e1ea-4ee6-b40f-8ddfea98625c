import SwiftUI

/// A view modifier that adds a blinking effect to any view with battery efficiency
struct BlinkingModifier: ViewModifier {
    // The rate at which the view blinks (in seconds)
    let blinkRate: Double

    // The opacity when the view is in its "dimmed" state
    let minOpacity: Double

    // Animation state
    @State private var isAnimating = false

    init(blinkRate: Double = 1.0, minOpacity: Double = 0.6) {
        self.blinkRate = blinkRate
        self.minOpacity = minOpacity
    }

    func body(content: Content) -> some View {
        content
            .opacity(isAnimating ? minOpacity : 1.0)
            .animation(
                Animation
                    .easeInOut(duration: blinkRate / 2)
                    .repeatForever(autoreverses: true),
                value: isAnimating
            )
            .onAppear {
                // Start animation when view appears
                isAnimating = true
            }
            .onDisappear {
                // Stop animation when view disappears
                isAnimating = false
            }
    }
}

// Extension to make the modifier easier to use
extension View {
    /// Adds a blinking effect to the view with battery efficiency
    /// - Parameters:
    ///   - blinkRate: The rate at which the view blinks (in seconds)
    ///   - minOpacity: The opacity when the view is in its "dimmed" state
    /// - Returns: A view with a blinking effect
    func blinking(blinkRate: Double = 1.0, minOpacity: Double = 0.6) -> some View {
        self.modifier(BlinkingModifier(blinkRate: blinkRate, minOpacity: minOpacity))
    }
}
