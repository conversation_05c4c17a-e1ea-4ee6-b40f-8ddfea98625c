import Foundation

/// Utility for consistent date parsing and formatting across the app
struct DateUtility {
    // MARK: - Date Formatters
    
    /// Standard API date formatter (ISO8601 with fractional seconds)
    static let apiDateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        return formatter
    }()
    
    /// Alternative API date formatter (ISO8601 without fractional seconds)
    static let apiDateFormatterAlt: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        return formatter
    }()
    
    /// Simple date formatter (YYYY-MM-DD)
    static let simpleDateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        return formatter
    }()
    
    /// ISO8601 date formatter with extended options
    static let iso8601Formatter: ISO8601DateFormatter = {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter
    }()
    
    /// Medium date formatter (e.g., Jan 1, 2025)
    static let mediumDateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }()
    
    /// Time formatter (e.g., 3:30 PM)
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter
    }()
    
    // MARK: - Parsing Methods
    
    /// Parse a date string using multiple formatters, returning nil if all fail
    static func parseAPIDate(_ dateString: String) -> Date? {
        // Try primary formatter first
        if let date = apiDateFormatter.date(from: dateString) {
            return date
        }
        
        // Try alternative formatter
        if let date = apiDateFormatterAlt.date(from: dateString) {
            return date
        }
        
        // Try ISO8601 formatter
        if let date = iso8601Formatter.date(from: dateString) {
            return date
        }
        
        // Try simple date formatter as last resort
        return simpleDateFormatter.date(from: dateString)
    }
    
    // MARK: - Formatting Methods
    
    /// Format a date for display in match headers
    static func formatMatchHeaderDate(_ date: Date) -> String {
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            return "Today, " + timeFormatter.string(from: date)
        } else if calendar.isDateInTomorrow(date) {
            return "Tomorrow, " + timeFormatter.string(from: date)
        } else {
            return mediumDateFormatter.string(from: date) + ", " + timeFormatter.string(from: date)
        }
    }
    
    /// Format a date for API requests (YYYY-MM-DD)
    static func formatDateForAPI(_ date: Date) -> String {
        return simpleDateFormatter.string(from: date)
    }
    
    /// Calculate date range (e.g., 5 years ago to today)
    static func dateRangeForPastYears(_ years: Int) -> (from: Date, to: Date)? {
        let calendar = Calendar.current
        let today = Date()
        
        guard let pastDate = calendar.date(byAdding: .year, value: -years, to: today) else {
            return nil
        }
        
        return (from: pastDate, to: today)
    }
}
