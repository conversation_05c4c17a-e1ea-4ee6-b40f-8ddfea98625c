import Foundation
import Security
import CryptoKit

/// Enhanced secure configuration manager for sensitive data
/// Provides secure storage, encryption, and environment-based configuration
struct SecureConfig {
    // MARK: - Constants

    /// Keychain service identifier
    static let keychainService = "com.kickoffscore.secure"

    /// Keychain account for API key
    static let apiKeyAccount = "apiKey"

    /// Keychain account for auth token
    static let authTokenAccount = "authToken"

    /// Keychain account for refresh token
    static let refreshTokenAccount = "refreshToken"

    /// Keychain account for encryption key
    static let encryptionKeyAccount = "encryptionKey"

    // MARK: - Environment Configuration

    /// Environment types
    enum Environment {
        case development
        case staging
        case production

        static var current: Environment {
            #if DEBUG
            return .development
            #else
            return .production
            #endif
        }

        var apiBaseURL: String {
            switch self {
            case .development:
                return "https://api.kickoffpredictions.com/api"
            case .staging:
                return "https://staging-api.kickoffpredictions.com/api"
            case .production:
                return "https://api.kickoffpredictions.com/api"
            }
        }
    }

    // MARK: - Security Configuration

    /// Security access level for keychain items
    private static let keychainAccessLevel = kSecAttrAccessibleWhenUnlockedThisDeviceOnly

    // MARK: - API Key Management

    /// Get the API key from environment or keychain
    static func getAPIKey() -> String? {
        // First try to get from environment variable (for CI/CD and development)
        if let envAPIKey = ProcessInfo.processInfo.environment["KICKOFFSCORE_API_KEY"], !envAPIKey.isEmpty {
            Logger.info("Using API key from environment variable", category: .security)
            return envAPIKey
        }

        // Fallback to keychain
        return getSecureValue(account: apiKeyAccount)
    }

    /// Get a secure value from keychain with enhanced security
    private static func getSecureValue(account: String) -> String? {
        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: keychainService,
            kSecAttrAccount: account,
            kSecAttrAccessible: keychainAccessLevel,
            kSecReturnData: kCFBooleanTrue!,
            kSecMatchLimit: kSecMatchLimitOne
        ] as [String: Any]

        var dataTypeRef: AnyObject?
        let status: OSStatus = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)

        switch status {
        case errSecSuccess:
            guard let retrievedData = dataTypeRef as? Data else {
                Logger.error("Could not convert keychain data", category: .security)
                return nil
            }

            // Decrypt if needed
            if let decryptedData = decryptData(retrievedData),
               let value = String(data: decryptedData, encoding: .utf8) {
                return value
            } else if let value = String(data: retrievedData, encoding: .utf8) {
                // Fallback for non-encrypted data (backward compatibility)
                return value
            }

            Logger.error("Could not decrypt or decode keychain data", category: .security)
            return nil

        case errSecItemNotFound:
            Logger.warning("Keychain item not found for account: \(account)", category: .security)
            return nil

        default:
            Logger.error("Keychain error: \(status) for account: \(account)", category: .security)
            return nil
        }
    }

    /// Save the API key to the keychain with encryption
    @discardableResult
    static func saveAPIKey(_ apiKey: String) -> OSStatus {
        return saveSecureValue(apiKey, account: apiKeyAccount)
    }

    /// Save a secure value to keychain with encryption
    @discardableResult
    private static func saveSecureValue(_ value: String, account: String) -> OSStatus {
        guard let data = value.data(using: .utf8) else {
            Logger.error("Could not convert value to data for account: \(account)", category: .security)
            return errSecParam
        }

        // Encrypt the data before storing
        let encryptedData = encryptData(data) ?? data

        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: keychainService,
            kSecAttrAccount: account,
            kSecAttrAccessible: keychainAccessLevel,
            kSecValueData: encryptedData
        ] as [String: Any]

        // Delete any existing item first to allow updates
        let deleteStatus = SecItemDelete(query as CFDictionary)
        if deleteStatus != errSecSuccess && deleteStatus != errSecItemNotFound {
            Logger.warning("Could not delete existing keychain item: \(deleteStatus)", category: .security)
        }

        // Add the new item
        let status = SecItemAdd(query as CFDictionary, nil)

        if status != errSecSuccess {
            Logger.error("Error saving to Keychain: \(status) for account: \(account)", category: .security)
        } else {
            Logger.info("Successfully saved secure value for account: \(account)", category: .security)
        }

        return status
    }

    /// Delete the API key from the keychain
    @discardableResult
    static func deleteAPIKey() -> OSStatus {
        return deleteSecureValue(account: apiKeyAccount)
    }

    /// Delete a secure value from keychain
    @discardableResult
    private static func deleteSecureValue(account: String) -> OSStatus {
        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: keychainService,
            kSecAttrAccount: account
        ] as [String: Any]

        let status = SecItemDelete(query as CFDictionary)
        if status != errSecSuccess && status != errSecItemNotFound {
            Logger.error("Error deleting from Keychain: \(status) for account: \(account)", category: .security)
        } else if status == errSecSuccess {
            Logger.info("Successfully deleted secure value for account: \(account)", category: .security)
        }
        return status
    }

    // MARK: - Auth Token Management

    /// Get the auth token from the keychain
    static func getAuthToken() -> String? {
        return getSecureValue(account: authTokenAccount)
    }

    /// Save the auth token to the keychain
    @discardableResult
    static func saveAuthToken(_ token: String) -> OSStatus {
        return saveSecureValue(token, account: authTokenAccount)
    }

    /// Delete the auth token from the keychain
    @discardableResult
    static func deleteAuthToken() -> OSStatus {
        return deleteSecureValue(account: authTokenAccount)
    }

    // MARK: - Refresh Token Management

    /// Get the refresh token from the keychain
    static func getRefreshToken() -> String? {
        return getSecureValue(account: refreshTokenAccount)
    }

    /// Save the refresh token to the keychain
    @discardableResult
    static func saveRefreshToken(_ token: String) -> OSStatus {
        return saveSecureValue(token, account: refreshTokenAccount)
    }

    /// Delete the refresh token from the keychain
    @discardableResult
    static func deleteRefreshToken() -> OSStatus {
        return deleteSecureValue(account: refreshTokenAccount)
    }

    // MARK: - App Initialization

    /// Initialize secure configuration on app launch
    static func initialize() {
        Logger.info("Initializing secure configuration", category: .security)

        // Generate or retrieve encryption key
        _ = getOrCreateEncryptionKey()

        // Check if API key exists in environment or keychain
        if getAPIKey() == nil {
            Logger.warning("No API key found in environment or keychain", category: .security)

            // In development, you can set a fallback key
            #if DEBUG
            let fallbackAPIKey = "1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756"
            Logger.warning("Using fallback API key for development", category: .security)
            let saveStatus = saveAPIKey(fallbackAPIKey)

            if saveStatus != errSecSuccess {
                Logger.error("Failed to save fallback API key: \(saveStatus)", category: .security)
            }
            #else
            Logger.error("No API key available in production environment", category: .security)
            #endif
        } else {
            Logger.info("API key successfully loaded", category: .security)
        }

        // Validate security configuration
        validateSecurityConfiguration()
    }

    /// Validate the security configuration
    private static func validateSecurityConfiguration() {
        var issues: [String] = []

        // Check if API key is available
        if getAPIKey() == nil {
            issues.append("API key not available")
        }

        // Check keychain accessibility
        let testKey = "test_\(UUID().uuidString)"
        let testValue = "test_value"
        let saveStatus = saveSecureValue(testValue, account: testKey)

        if saveStatus == errSecSuccess {
            let retrievedValue = getSecureValue(account: testKey)
            if retrievedValue != testValue {
                issues.append("Keychain encryption/decryption failed")
            }
            _ = deleteSecureValue(account: testKey)
        } else {
            issues.append("Keychain not accessible")
        }

        if issues.isEmpty {
            Logger.info("Security configuration validation passed", category: .security)
        } else {
            Logger.error("Security configuration issues: \(issues.joined(separator: ", "))", category: .security)
        }
    }

    // MARK: - Encryption/Decryption

    /// Get or create an encryption key for local data encryption
    private static func getOrCreateEncryptionKey() -> SymmetricKey? {
        // Try to get existing key from keychain WITHOUT encryption (to avoid circular dependency)
        if let existingKeyData = getPlainSecureValue(account: encryptionKeyAccount)?.data(using: .utf8),
           let keyData = Data(base64Encoded: String(data: existingKeyData, encoding: .utf8) ?? "") {
            return SymmetricKey(data: keyData)
        }

        // Generate new key
        let newKey = SymmetricKey(size: .bits256)
        let keyData = newKey.withUnsafeBytes { Data($0) }

        // Save to keychain WITHOUT encryption (to avoid circular dependency)
        let keyString = keyData.base64EncodedString()
        let saveStatus = savePlainSecureValue(keyString, account: encryptionKeyAccount)

        if saveStatus == errSecSuccess {
            Logger.info("Generated and saved new encryption key", category: .security)
            return newKey
        } else {
            Logger.error("Failed to save encryption key: \(saveStatus)", category: .security)
            return nil
        }
    }

    /// Get a secure value from keychain WITHOUT encryption (for encryption key itself)
    private static func getPlainSecureValue(account: String) -> String? {
        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: keychainService,
            kSecAttrAccount: account,
            kSecAttrAccessible: keychainAccessLevel,
            kSecReturnData: kCFBooleanTrue!,
            kSecMatchLimit: kSecMatchLimitOne
        ] as [String: Any]

        var dataTypeRef: AnyObject?
        let status: OSStatus = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)

        switch status {
        case errSecSuccess:
            guard let retrievedData = dataTypeRef as? Data,
                  let value = String(data: retrievedData, encoding: .utf8) else {
                Logger.error("Could not convert keychain data", category: .security)
                return nil
            }
            return value

        case errSecItemNotFound:
            Logger.debug("Keychain item not found for account: \(account)", category: .security)
            return nil

        default:
            Logger.error("Keychain error: \(status) for account: \(account)", category: .security)
            return nil
        }
    }

    /// Save a secure value to keychain WITHOUT encryption (for encryption key itself)
    @discardableResult
    private static func savePlainSecureValue(_ value: String, account: String) -> OSStatus {
        guard let data = value.data(using: .utf8) else {
            Logger.error("Could not convert value to data for account: \(account)", category: .security)
            return errSecParam
        }

        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: keychainService,
            kSecAttrAccount: account,
            kSecAttrAccessible: keychainAccessLevel,
            kSecValueData: data
        ] as [String: Any]

        // Delete any existing item first to allow updates
        let deleteStatus = SecItemDelete(query as CFDictionary)
        if deleteStatus != errSecSuccess && deleteStatus != errSecItemNotFound {
            Logger.warning("Could not delete existing keychain item: \(deleteStatus)", category: .security)
        }

        // Add the new item
        let status = SecItemAdd(query as CFDictionary, nil)

        if status != errSecSuccess {
            Logger.error("Error saving to Keychain: \(status) for account: \(account)", category: .security)
        } else {
            Logger.debug("Successfully saved plain secure value for account: \(account)", category: .security)
        }

        return status
    }

    /// Encrypt data using AES-GCM
    private static func encryptData(_ data: Data) -> Data? {
        guard let key = getOrCreateEncryptionKey() else {
            Logger.error("No encryption key available", category: .security)
            return nil
        }

        do {
            let sealedBox = try AES.GCM.seal(data, using: key)
            return sealedBox.combined
        } catch {
            Logger.error("Encryption failed: \(error)", category: .security)
            return nil
        }
    }

    /// Decrypt data using AES-GCM
    private static func decryptData(_ encryptedData: Data) -> Data? {
        guard let key = getOrCreateEncryptionKey() else {
            Logger.error("No encryption key available for decryption", category: .security)
            return nil
        }

        do {
            let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
            return try AES.GCM.open(sealedBox, using: key)
        } catch {
            Logger.warning("Decryption failed (may be unencrypted data): \(error)", category: .security)
            return nil
        }
    }

    // MARK: - Security Utilities

    /// Clear all secure data (for logout or security reset)
    static func clearAllSecureData() {
        Logger.info("Clearing all secure data", category: .security)

        _ = deleteAuthToken()
        _ = deleteRefreshToken()
        _ = deleteSecureValue(account: encryptionKeyAccount)

        // Note: We don't delete the API key as it's needed for app functionality
        Logger.info("Secure data cleared", category: .security)
    }

    /// Check if the app is running in a secure environment
    static func isSecureEnvironment() -> Bool {
        // Check for jailbreak indicators
        let jailbreakPaths = [
            "/Applications/Cydia.app",
            "/Library/MobileSubstrate/MobileSubstrate.dylib",
            "/bin/bash",
            "/usr/sbin/sshd",
            "/etc/apt"
        ]

        for path in jailbreakPaths {
            if FileManager.default.fileExists(atPath: path) {
                Logger.warning("Potential jailbreak detected: \(path)", category: .security)
                return false
            }
        }

        // Check for debugger
        #if DEBUG
        return true // Allow debugging in debug builds
        #else
        return !isDebuggerAttached()
        #endif
    }

    /// Check if a debugger is attached
    private static func isDebuggerAttached() -> Bool {
        var info = kinfo_proc()
        var mib: [Int32] = [CTL_KERN, KERN_PROC, KERN_PROC_PID, getpid()]
        var size = MemoryLayout<kinfo_proc>.stride

        let result = sysctl(&mib, u_int(mib.count), &info, &size, nil, 0)

        return result == 0 && (info.kp_proc.p_flag & P_TRACED) != 0
    }
}
