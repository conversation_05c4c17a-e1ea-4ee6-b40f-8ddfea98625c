import Foundation

/// A utility for conditional logging throughout the app
struct Logger {
    /// Log levels for different types of messages
    enum Level: String {
        case debug = "DEBUG"
        case info = "INFO"
        case warning = "WARNING"
        case error = "ERROR"

        var emoji: String {
            switch self {
            case .debug: return "🔍"
            case .info: return "ℹ️"
            case .warning: return "⚠️"
            case .error: return "❌"
            }
        }
    }

    /// Categories for organizing logs
    enum Category: String {
        case network = "Network"
        case ui = "UI"
        case data = "Data"
        case performance = "Performance"
        case general = "General"
        case security = "Security"
        case background = "Background"
    }

    /// Check if the debugger is attached
    static var isDebuggerAttached: Bool = {
        var info = kinfo_proc()
        var mib: [Int32] = [CTL_KERN, KERN_PROC, KERN_PROC_PID, getpid()]
        var size = MemoryLayout<kinfo_proc>.stride
        let junk = sysctl(&mib, UInt32(mib.count), &info, &size, nil, 0)
        return (info.kp_proc.p_flag & P_TRACED) != 0
    }()

    /// Whether logging is enabled (can be toggled at runtime)
    static var isEnabled = true

    /// Maximum log level to display (can be changed at runtime)
    static var maxLevel: Level = .debug

    /// Whether to show timestamps in logs
    static var showTimestamps = true

    /// Whether to show the file and line number in logs
    static var showFileInfo = true

    /// Maximum length for truncated logs
    static var maxLogLength = 1000

    /// Whether to log JSON responses (disabled by default to reduce log size)
    static var logJSONResponses = false

    /// Minimum log level when debugger is attached (to reduce log volume)
    static var debuggerAttachedMinLevel: Level = .warning

    /// Log a message with the specified level and category
    /// - Parameters:
    ///   - message: The message to log
    ///   - level: The log level
    ///   - category: The log category
    ///   - file: The file where the log was called from
    ///   - function: The function where the log was called from
    ///   - line: The line number where the log was called from
    static func log(
        _ message: String,
        level: Level = .debug,
        category: Category = .general,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        #if DEBUG
        // Only log if enabled and level is appropriate
        guard isEnabled else { return }

        // When debugger is attached, only log messages at or above the debuggerAttachedMinLevel
        if isDebuggerAttached {
            // Always log errors, but for other levels, check against debuggerAttachedMinLevel
            guard level == .error ||
                  compareLogLevels(level, isGreaterThanOrEqualTo: debuggerAttachedMinLevel) else {
                return
            }
        } else {
            // Normal logging when debugger is not attached
            guard compareLogLevels(level, isGreaterThanOrEqualTo: maxLevel) else { return }
        }

        // Build the log message
        var components: [String] = []

        // Add timestamp if enabled
        if showTimestamps {
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm:ss.SSS"
            components.append("[\(formatter.string(from: Date()))]")
        }

        // Add level and category
        components.append("\(level.emoji) [\(level.rawValue)][\(category.rawValue)]")

        // Add file info if enabled
        if showFileInfo {
            let fileName = URL(fileURLWithPath: file).lastPathComponent
            components.append("[\(fileName):\(line)]")
        }

        // Add the message
        components.append(message)

        // Join components and print
        let fullMessage = components.joined(separator: " ")
        print(fullMessage)
        #endif
    }

    /// Helper method to compare log levels
    private static func compareLogLevels(_ level: Level, isGreaterThanOrEqualTo minLevel: Level) -> Bool {
        switch (level, minLevel) {
        case (.error, _): return true
        case (.warning, .error): return false
        case (.warning, _): return true
        case (.info, .error), (.info, .warning): return false
        case (.info, _): return true
        case (.debug, .debug): return true
        default: return false
        }
    }

    /// Log a debug message
    static func debug(_ message: String, category: Category = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .debug, category: category, file: file, function: function, line: line)
    }

    /// Log an info message
    static func info(_ message: String, category: Category = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .info, category: category, file: file, function: function, line: line)
    }

    /// Log a warning message
    static func warning(_ message: String, category: Category = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .warning, category: category, file: file, function: function, line: line)
    }

    /// Log an error message
    static func error(_ message: String, category: Category = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .error, category: category, file: file, function: function, line: line)
    }

    /// Log network request details
    static func logRequest(_ url: URL, method: String = "GET", file: String = #file, function: String = #function, line: Int = #line) {
        debug("Request: \(method) \(url.absoluteString)", category: .network, file: file, function: function, line: line)
    }

    /// Log network response details
    static func logResponse(statusCode: Int, url: URL, file: String = #file, function: String = #function, line: Int = #line) {
        let level: Level = (200...299).contains(statusCode) ? .info : .error
        log("Response: [\(statusCode)] \(url.absoluteString)", level: level, category: .network, file: file, function: function, line: line)
    }

    /// Log JSON data with truncation for large responses
    static func logJSON(_ json: String, category: Category = .network, file: String = #file, function: String = #function, line: Int = #line) {
        #if DEBUG
        // Skip JSON logging if disabled or if debugger is attached (to improve performance)
        if isDebuggerAttached {
            // When debugger is attached, only log the size of the JSON to minimize overhead
            if compareLogLevels(.debug, isGreaterThanOrEqualTo: debuggerAttachedMinLevel) {
                debug("JSON response received: \(json.count) chars", category: category, file: file, function: function, line: line)
            }
            return
        }

        // Normal JSON logging when debugger is not attached
        guard logJSONResponses else {
            // Just log the size of the JSON instead
            debug("JSON response received: \(json.count) chars", category: category, file: file, function: function, line: line)
            return
        }

        if json.count > maxLogLength {
            let truncated = json.prefix(maxLogLength)
            debug("JSON (truncated): \(truncated)... [\(json.count) chars total]", category: category, file: file, function: function, line: line)
        } else {
            debug("JSON: \(json)", category: category, file: file, function: function, line: line)
        }
        #endif
    }

    /// Log performance metrics
    static func logPerformance(operation: String, duration: TimeInterval, file: String = #file, function: String = #function, line: Int = #line) {
        // Only log performance metrics if they're significant or if the debugger is not attached
        let isSignificant = duration > 0.1 // Only log operations that take more than 100ms

        if !isDebuggerAttached || isSignificant {
            let formattedDuration = String(format: "%.4f", duration)
            info("Performance: '\(operation)' took \(formattedDuration)s", category: .performance, file: file, function: function, line: line)
        }
    }
}
