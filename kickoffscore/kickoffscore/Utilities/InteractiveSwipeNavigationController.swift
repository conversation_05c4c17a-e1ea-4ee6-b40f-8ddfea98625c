import SwiftUI
import UIKit

// MARK: - Enhanced Navigation State Manager

/// Enhanced navigation state manager that tracks view hierarchy for proper swipe back
class EnhancedNavigationStateManager: ObservableObject {
    @Published var dragOffset: CGFloat = 0
    @Published var isDragging: Bool = false

    private let swipeThreshold: CGFloat = 100
    private let edgeThreshold: CGFloat = 20

    func handleDragChanged(_ value: DragGesture.Value, canNavigateBack: Bool) {
        // Only respond to gestures starting from the left edge
        if value.startLocation.x <= edgeThreshold && canNavigateBack {
            if !isDragging {
                isDragging = true
                // Add haptic feedback when starting the gesture
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }

            // Only allow positive (rightward) drag
            dragOffset = max(0, value.translation.width)
        }
    }

    func handleDragEnded(_ value: DragGesture.Value, onNavigateBack: @escaping () -> Void) {
        if isDragging {
            // If dragged far enough, navigate back
            if dragOffset > swipeThreshold {
                // Add success haptic feedback
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()

                // Animate the completion
                withAnimation(.easeOut(duration: 0.25)) {
                    dragOffset = UIScreen.main.bounds.width
                }

                // Navigate back after animation
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    onNavigateBack()
                    self.resetState()
                }
            } else {
                // Snap back to original position
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    dragOffset = 0
                }
                // Reset dragging state after animation
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    self.isDragging = false
                }
            }
        }
    }

    private func resetState() {
        dragOffset = 0
        isDragging = false
    }
}

// MARK: - Enhanced Swipe Back Gesture with Native Navigation

/// A SwiftUI view modifier that adds swipe-to-go-back functionality using native iOS navigation
struct SwipeBackGesture: ViewModifier {
    @EnvironmentObject private var navigationState: NavigationStateManager

    func body(content: Content) -> some View {
        content
            .background(
                // Use the native swipe back enabler instead of custom gesture
                NativeSwipeBackEnabler()
            )
    }
}



// MARK: - Enhanced Interactive Navigation Controller

/// Enhanced UINavigationController that provides better swipe back gesture support
class InteractiveSwipeNavigationController: UINavigationController, UIGestureRecognizerDelegate {

    override func viewDidLoad() {
        super.viewDidLoad()

        // Configure the interactive pop gesture
        setupInteractivePopGesture()

        // Configure navigation bar appearance for better integration
        configureNavigationBarAppearance()
    }

    private func setupInteractivePopGesture() {
        // Enable the swipe back gesture
        interactivePopGestureRecognizer?.delegate = self
        interactivePopGestureRecognizer?.isEnabled = true

        // Create a custom edge pan gesture for better control
        let edgePanGesture = UIScreenEdgePanGestureRecognizer(target: self, action: #selector(handleEdgePan(_:)))
        edgePanGesture.edges = .left
        edgePanGesture.delegate = self
        view.addGestureRecognizer(edgePanGesture)
    }

    private func configureNavigationBarAppearance() {
        // Make navigation bar transparent but functional
        navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationBar.shadowImage = UIImage()
        navigationBar.isTranslucent = true
        navigationBar.backgroundColor = .clear
    }

    @objc private func handleEdgePan(_ gesture: UIScreenEdgePanGestureRecognizer) {
        // Let the system handle the gesture, but ensure it works properly
        if viewControllers.count > 1 {
            switch gesture.state {
            case .began:
                // Add haptic feedback when gesture begins
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            case .ended, .cancelled:
                // Add completion haptic if the gesture completed successfully
                if gesture.velocity(in: view).x > 0 {
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                }
            default:
                break
            }
        }
    }

    // MARK: - UIGestureRecognizerDelegate

    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        // Allow gesture only when we have more than one view controller
        return viewControllers.count > 1
    }

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // Allow simultaneous recognition with other gestures
        return true
    }

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // Give priority to edge pan gestures
        return gestureRecognizer is UIScreenEdgePanGestureRecognizer
    }
}

// MARK: - SwiftUI Wrapper

/// SwiftUI wrapper for the enhanced navigation controller
struct InteractiveSwipeNavigationView<Content: View>: UIViewControllerRepresentable {
    var content: Content

    func makeUIViewController(context: Context) -> UIViewController {
        let hostingController = UIHostingController(rootView: content)
        let navigationController = InteractiveSwipeNavigationController(rootViewController: hostingController)
        return navigationController
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        if let navigationController = uiViewController as? UINavigationController,
           let hostingController = navigationController.viewControllers.first as? UIHostingController<Content> {
            hostingController.rootView = content
        }
    }
}

// MARK: - Native iOS Swipe Back Enabler

/// A UIViewControllerRepresentable that enables native iOS swipe back gesture
struct NativeSwipeBackEnabler: UIViewControllerRepresentable {
    @EnvironmentObject private var navigationState: NavigationStateManager

    func makeUIViewController(context: Context) -> UIViewController {
        let controller = SwipeBackViewController()
        controller.navigationStateManager = navigationState
        return controller
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        // No updates needed
    }
}

/// Helper view controller that enables native swipe back with enhanced features
class SwipeBackViewController: UIViewController {
    weak var navigationStateManager: NavigationStateManager?
    private var edgePanGesture: UIScreenEdgePanGestureRecognizer?
    private let edgeThreshold: CGFloat = 20

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear
        view.isUserInteractionEnabled = false
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        enableNativeSwipeBack()
    }

    private func enableNativeSwipeBack() {
        guard let navigationController = navigationController else { return }

        // Enable the native interactive pop gesture
        navigationController.interactivePopGestureRecognizer?.isEnabled = true
        navigationController.interactivePopGestureRecognizer?.delegate = self

        // Add custom edge pan gesture for enhanced control and haptic feedback
        let edgePan = UIScreenEdgePanGestureRecognizer(target: self, action: #selector(handleEdgePan(_:)))
        edgePan.edges = .left
        edgePan.delegate = self
        navigationController.view.addGestureRecognizer(edgePan)
        self.edgePanGesture = edgePan
    }

    @objc private func handleEdgePan(_ gesture: UIScreenEdgePanGestureRecognizer) {
        guard let navigationController = navigationController,
              navigationController.viewControllers.count > 1 else { return }

        switch gesture.state {
        case .began:
            // Add haptic feedback when gesture begins
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()

        case .ended:
            // Check if the gesture completed successfully
            let translation = gesture.translation(in: gesture.view)
            let velocity = gesture.velocity(in: gesture.view)

            // If the gesture moved far enough or had enough velocity, add success haptic
            if translation.x > 100 || velocity.x > 500 {
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }

        default:
            break
        }
    }
}

extension SwipeBackViewController: UIGestureRecognizerDelegate {
    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        // Allow gesture only when we can navigate back
        guard let canNavigateBack = navigationStateManager?.canNavigateBack else { return false }

        // For edge pan gestures, also check if the gesture starts from the edge
        if let edgePan = gestureRecognizer as? UIScreenEdgePanGestureRecognizer {
            let location = edgePan.location(in: edgePan.view)
            return canNavigateBack && location.x <= edgeThreshold
        }

        return canNavigateBack
    }

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // Only allow simultaneous recognition with the native interactive pop gesture
        // Prevent simultaneous recognition with TabView's internal scroll view gestures
        if otherGestureRecognizer is UIScreenEdgePanGestureRecognizer {
            // Allow with other edge pan gestures (like native interactive pop)
            return true
        }

        // Check if the other gesture is from a UIScrollView (includes both horizontal TabView and vertical content scrolling)
        if let scrollView = otherGestureRecognizer.view as? UIScrollView {
            // If this is a TabView's scroll view (horizontal paging), don't allow simultaneous recognition
            if scrollView.isPagingEnabled {
                return false
            }

            // Also prevent simultaneous recognition with vertical scroll views during edge swipe
            // This prevents content scrolling when performing swipe-to-go-back
            return false
        }

        // Check if the other gesture is a pan gesture from any scroll view
        if otherGestureRecognizer is UIPanGestureRecognizer,
           otherGestureRecognizer.view is UIScrollView {
            // Prioritize edge swipe for navigation over any scroll view interaction
            return false
        }

        // Allow simultaneous recognition with other gestures by default
        return true
    }

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRequireFailureOf otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // Don't require failure of other gestures - we want to take priority
        return false
    }

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // For edge pan gestures, require all scroll view gestures to fail first
        if gestureRecognizer is UIScreenEdgePanGestureRecognizer {
            // If the other gesture is from any scroll view, require it to fail
            if otherGestureRecognizer.view is UIScrollView {
                // This includes both horizontal TabView scrolling and vertical content scrolling
                return true
            }

            // Also handle pan gestures that might be associated with scroll views
            if otherGestureRecognizer is UIPanGestureRecognizer,
               otherGestureRecognizer.view is UIScrollView {
                return true
            }
        }

        return false
    }
}

// MARK: - View Extensions

extension View {
    /// Adds SwiftUI-native swipe-to-go-back functionality
    func swipeToGoBack() -> some View {
        self.modifier(SwipeBackGesture())
    }

    /// Enables native iOS swipe back gesture (recommended)
    func enableNativeSwipeBack() -> some View {
        self.background(NativeSwipeBackEnabler())
    }

    /// Legacy UIKit-based interactive swipe navigation (for compatibility)
    func withInteractiveSwipeNavigation() -> some View {
        InteractiveSwipeNavigationView(content: self)
    }
}
