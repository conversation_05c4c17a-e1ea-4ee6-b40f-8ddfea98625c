import Foundation

/// Extensions to the Result type for better error handling
extension Result {
    /// Get the success value or throw the error
    /// - Returns: The success value
    /// - Throws: The failure error
    func get() throws -> Success {
        switch self {
        case .success(let value):
            return value
        case .failure(let error):
            throw error
        }
    }
    
    /// Map both success and failure
    /// - Parameters:
    ///   - transform: Transform function for success value
    ///   - transformError: Transform function for failure error
    /// - Returns: A new Result with transformed success and failure values
    func bimap<NewSuccess, NewFailure>(
        _ transform: (Success) -> NewSuccess,
        _ transformError: (Failure) -> NewFailure
    ) -> Result<NewSuccess, NewFailure> {
        switch self {
        case .success(let value):
            return .success(transform(value))
        case .failure(let error):
            return .failure(transformError(error))
        }
    }
    
    /// Perform a side effect on success
    /// - Parameter sideEffect: The side effect to perform
    /// - Returns: The original Result
    @discardableResult
    func onSuccess(_ sideEffect: (Success) -> Void) -> Self {
        if case .success(let value) = self {
            sideEffect(value)
        }
        return self
    }
    
    /// Perform a side effect on failure
    /// - Parameter sideEffect: The side effect to perform
    /// - Returns: The original Result
    @discardableResult
    func onFailure(_ sideEffect: (Failure) -> Void) -> Self {
        if case .failure(let error) = self {
            sideEffect(error)
        }
        return self
    }
    
    /// Convert a Result to an optional, returning nil on failure
    /// - Returns: An optional containing the success value, or nil on failure
    func toOptional() -> Success? {
        switch self {
        case .success(let value):
            return value
        case .failure:
            return nil
        }
    }
    
    /// Recover from an error with a default value
    /// - Parameter defaultValue: The default value to use on failure
    /// - Returns: The success value or the default value
    func recover(_ defaultValue: Success) -> Success {
        switch self {
        case .success(let value):
            return value
        case .failure:
            return defaultValue
        }
    }
    
    /// Recover from an error with a value produced by a closure
    /// - Parameter producer: A closure that produces a default value
    /// - Returns: The success value or the produced default value
    func recover(with producer: (Failure) -> Success) -> Success {
        switch self {
        case .success(let value):
            return value
        case .failure(let error):
            return producer(error)
        }
    }
}

/// Extensions to the Optional type for better error handling
extension Optional {
    /// Convert an Optional to a Result
    /// - Parameter error: The error to use if the Optional is nil
    /// - Returns: A Result containing the unwrapped value or the error
    func toResult<E: Error>(_ error: E) -> Result<Wrapped, E> {
        switch self {
        case .some(let value):
            return .success(value)
        case .none:
            return .failure(error)
        }
    }
    
    /// Get the value or throw an error
    /// - Parameter error: The error to throw if the Optional is nil
    /// - Returns: The unwrapped value
    /// - Throws: The error if the Optional is nil
    func orThrow<E: Error>(_ error: E) throws -> Wrapped {
        switch self {
        case .some(let value):
            return value
        case .none:
            throw error
        }
    }
}
