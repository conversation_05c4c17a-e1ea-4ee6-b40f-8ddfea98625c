import Foundation
import Combine

/// Content types for smart cache invalidation
enum CacheContentType {
    case fixture, standings, team, player, league, odds, statistics

    var defaultDuration: TimeInterval {
        switch self {
        case .fixture: return 60 // 1 minute for fixtures (can be live)
        case .standings: return 3600 // 1 hour for standings
        case .team, .league: return 86400 // 24 hours for team/league data
        case .player: return 86400 * 7 // 1 week for player data
        case .odds: return 300 // 5 minutes for odds
        case .statistics: return 300 // 5 minutes for statistics
        }
    }

    var dependencies: [CacheContentType] {
        switch self {
        case .fixture: return [.standings] // Fixture updates might affect standings
        case .standings: return []
        case .team: return [.standings] // Team updates might affect standings
        case .player: return []
        case .league: return [.standings] // League updates might affect standings
        case .odds: return []
        case .statistics: return []
        }
    }
}

/// Cache invalidation strategy
enum CacheInvalidationStrategy {
    case timeOnly // Current behavior - time-based only
    case smart // Smart invalidation with dependencies
    case event // Event-based invalidation
}

/// Performance metrics for cache operations
struct CacheMetrics {
    var totalRequests: Int = 0
    var cacheHits: Int = 0
    var cacheMisses: Int = 0
    var totalResponseTime: TimeInterval = 0
    var averageResponseTime: TimeInterval { totalRequests > 0 ? totalResponseTime / Double(totalRequests) : 0 }
    var hitRate: Double { totalRequests > 0 ? Double(cacheHits) / Double(totalRequests) : 0 }

    mutating func recordHit(responseTime: TimeInterval) {
        totalRequests += 1
        cacheHits += 1
        totalResponseTime += responseTime
    }

    mutating func recordMiss(responseTime: TimeInterval) {
        totalRequests += 1
        cacheMisses += 1
        totalResponseTime += responseTime
    }

    mutating func reset() {
        totalRequests = 0
        cacheHits = 0
        cacheMisses = 0
        totalResponseTime = 0
    }
}

/// A cache manager for storing and retrieving data with smart invalidation and performance metrics
class CacheManager: NSObject {
    // Singleton instance
    static let shared = CacheManager()

    // Cache storage
    private let cache = NSCache<NSString, EnhancedCacheEntry>()

    // Default cache duration
    private let defaultCacheDuration: TimeInterval = 300 // 5 minutes

    // Cache invalidation strategy
    private var invalidationStrategy: CacheInvalidationStrategy = .smart

    // Performance metrics
    private var metrics = CacheMetrics()
    private let metricsQueue = DispatchQueue(label: "cache.metrics", qos: .utility)

    // Cache dependencies tracking
    private var dependencyMap: [String: Set<String>] = [:]
    private let dependencyQueue = DispatchQueue(label: "cache.dependencies", qos: .utility)

    // Event-based invalidation subjects
    private let invalidationSubject = PassthroughSubject<String, Never>()
    private var cancellables = Set<AnyCancellable>()

    // Initialize with default settings
    private override init() {
        // Call super.init() first
        super.init()

        // Set cache limits
        cache.countLimit = 100 // Maximum number of items
        cache.totalCostLimit = 50 * 1024 * 1024 // 50 MB

        // Set up cache delegate for automatic cleanup
        cache.delegate = self

        // Set up periodic metrics logging
        setupMetricsLogging()

        // Set up invalidation event handling
        setupInvalidationHandling()
    }
    
    // MARK: - Public Methods

    /// Save data to the cache with content type for smart invalidation
    /// - Parameters:
    ///   - data: The data to cache
    ///   - key: The cache key
    ///   - contentType: The type of content for smart caching
    ///   - duration: Optional custom cache duration (overrides content type default)
    func saveData(_ data: Data, forKey key: String, contentType: CacheContentType, duration: TimeInterval? = nil) {
        let effectiveDuration = duration ?? contentType.defaultDuration
        let expirationDate = Date().addingTimeInterval(effectiveDuration)
        let cacheEntry = EnhancedCacheEntry(
            data: data,
            expirationDate: expirationDate,
            contentType: contentType,
            createdAt: Date(),
            accessCount: 0
        )

        cache.setObject(cacheEntry, forKey: key as NSString)

        // Track dependencies for smart invalidation
        if invalidationStrategy == .smart {
            trackDependencies(for: key, contentType: contentType)
        }

        Logger.debug("Cached data for key: \(key) (type: \(contentType), duration: \(effectiveDuration)s)", category: .performance)
    }

    /// Save data to the cache (legacy method for backward compatibility)
    /// - Parameters:
    ///   - data: The data to cache
    ///   - key: The cache key
    ///   - duration: The cache duration in seconds (default: 5 minutes)
    func saveData(_ data: Data, forKey key: String, duration: TimeInterval? = nil) {
        // Default to fixture type for backward compatibility
        saveData(data, forKey: key, contentType: .fixture, duration: duration)
    }
    
    /// Get data from the cache with performance tracking
    /// - Parameter key: The cache key
    /// - Returns: The cached data, or nil if not found or expired
    func getData(forKey key: String) -> Data? {
        let startTime = CFAbsoluteTimeGetCurrent()

        guard let cacheEntry = cache.object(forKey: key as NSString) else {
            let responseTime = CFAbsoluteTimeGetCurrent() - startTime
            recordCacheMiss(responseTime: responseTime)
            Logger.debug("Cache MISS for key: \(key)", category: .performance)
            return nil
        }

        // Check if the cache entry has expired
        if cacheEntry.expirationDate < Date() {
            cache.removeObject(forKey: key as NSString)
            let responseTime = CFAbsoluteTimeGetCurrent() - startTime
            recordCacheMiss(responseTime: responseTime)
            Logger.debug("Cache EXPIRED for key: \(key)", category: .performance)
            return nil
        }

        // Update access count and last access time
        cacheEntry.accessCount += 1
        cacheEntry.lastAccessedAt = Date()

        let responseTime = CFAbsoluteTimeGetCurrent() - startTime
        recordCacheHit(responseTime: responseTime)
        Logger.debug("Cache HIT for key: \(key) (accessed \(cacheEntry.accessCount) times)", category: .performance)

        return cacheEntry.data
    }
    
    /// Remove data from the cache
    /// - Parameter key: The cache key
    func removeData(forKey key: String) {
        cache.removeObject(forKey: key as NSString)
        removeDependencyTracking(for: key)
        Logger.debug("Removed cache entry for key: \(key)", category: .performance)
    }

    /// Clear all cached data
    func clearCache() {
        cache.removeAllObjects()
        dependencyQueue.async { [weak self] in
            self?.dependencyMap.removeAll()
        }
        metricsQueue.async { [weak self] in
            self?.metrics.reset()
        }
        Logger.info("Cleared all cache data and metrics", category: .performance)
    }

    /// Clear expired cache entries only
    func clearExpiredCache() {
        let currentTime = Date()
        let allKeys = getAllCacheKeys()
        var expiredKeys: [String] = []

        for key in allKeys {
            if let entry = cache.object(forKey: key as NSString) {
                if entry.expirationDate < currentTime {
                    expiredKeys.append(key)
                }
            }
        }

        for key in expiredKeys {
            removeData(forKey: key)
        }

        if !expiredKeys.isEmpty {
            Logger.info("Cleared \(expiredKeys.count) expired cache entries", category: .performance)
        }
    }

    /// Perform deep cleanup of cache (used by background tasks)
    func performDeepCleanup() {
        let currentTime = Date()
        let allKeys = getAllCacheKeys()
        var keysToRemove: [String] = []
        var totalMemoryFreed = 0

        for key in allKeys {
            if let entry = cache.object(forKey: key as NSString) {
                let age = currentTime.timeIntervalSince(entry.createdAt)

                // Remove entries that are:
                // 1. Expired
                // 2. Very old (more than 1 hour)
                // 3. Rarely accessed (less than 2 times and older than 10 minutes)
                if entry.expirationDate < currentTime ||
                   age > 3600 || // 1 hour
                   (entry.accessCount < 2 && age > 600) { // 10 minutes with low access
                    keysToRemove.append(key)
                    totalMemoryFreed += entry.data.count
                }
            }
        }

        for key in keysToRemove {
            removeData(forKey: key)
        }

        // Also clean up dependency tracking for removed keys
        dependencyQueue.async { [weak self] in
            guard let self = self else { return }
            for key in keysToRemove {
                self.dependencyMap.removeValue(forKey: key)
            }
        }

        Logger.info("Deep cleanup completed: removed \(keysToRemove.count) entries, freed ~\(totalMemoryFreed) bytes", category: .performance)
    }

    /// Invalidate cache entries based on content type and dependencies
    /// - Parameter contentType: The content type that was updated
    func invalidateCache(for contentType: CacheContentType) {
        guard invalidationStrategy == .smart else { return }

        let keysToInvalidate = findKeysToInvalidate(for: contentType)

        for key in keysToInvalidate {
            removeData(forKey: key)
        }

        if !keysToInvalidate.isEmpty {
            Logger.info("Smart invalidation: removed \(keysToInvalidate.count) cache entries for \(contentType)", category: .performance)
        }
    }

    /// Invalidate cache entries by key pattern
    /// - Parameter pattern: The key pattern to match (supports wildcards with *)
    func invalidateCache(keyPattern pattern: String) {
        let allKeys = getAllCacheKeys()
        let keysToInvalidate = allKeys.filter { key in
            matchesPattern(key: key, pattern: pattern)
        }

        for key in keysToInvalidate {
            removeData(forKey: key)
        }

        if !keysToInvalidate.isEmpty {
            Logger.info("Pattern invalidation: removed \(keysToInvalidate.count) cache entries matching '\(pattern)'", category: .performance)
        }
    }

    /// Trigger event-based cache invalidation
    /// - Parameter event: The event identifier
    func triggerInvalidation(event: String) {
        invalidationSubject.send(event)
    }
    
    /// Generate a cache key for an API request
    /// - Parameters:
    ///   - endpoint: The API endpoint
    ///   - parameters: The query parameters
    /// - Returns: A unique cache key
    func generateCacheKey(endpoint: String, parameters: [String: String]? = nil) -> String {
        var key = endpoint

        if let parameters = parameters {
            let sortedParams = parameters.sorted { $0.key < $1.key }
            let paramsString = sortedParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
            key += "?\(paramsString)"
        }

        return key
    }

    // MARK: - Performance Metrics

    /// Get current cache performance metrics
    /// - Returns: Current cache metrics
    func getMetrics() -> CacheMetrics {
        return metricsQueue.sync { metrics }
    }

    /// Reset performance metrics
    func resetMetrics() {
        metricsQueue.async { [weak self] in
            self?.metrics.reset()
        }
        Logger.info("Cache metrics reset", category: .performance)
    }

    /// Log current cache performance metrics
    func logMetrics() {
        let currentMetrics = getMetrics()
        Logger.info("""
            Cache Performance Metrics:
            - Total Requests: \(currentMetrics.totalRequests)
            - Cache Hit Rate: \(String(format: "%.2f%%", currentMetrics.hitRate * 100))
            - Average Response Time: \(String(format: "%.4f", currentMetrics.averageResponseTime))s
            - Cache Hits: \(currentMetrics.cacheHits)
            - Cache Misses: \(currentMetrics.cacheMisses)
            """, category: .performance)
    }

    /// Get cache size information
    func getCacheInfo() -> (count: Int, estimatedSize: String) {
        let count = cache.countLimit > 0 ? min(getAllCacheKeys().count, cache.countLimit) : getAllCacheKeys().count
        let estimatedBytes = count * 1024 // Rough estimate
        let sizeString = ByteCountFormatter.string(fromByteCount: Int64(estimatedBytes), countStyle: .memory)
        return (count: count, estimatedSize: sizeString)
    }

    // MARK: - Private Helper Methods

    private func recordCacheHit(responseTime: TimeInterval) {
        metricsQueue.async { [weak self] in
            self?.metrics.recordHit(responseTime: responseTime)
        }
    }

    private func recordCacheMiss(responseTime: TimeInterval) {
        metricsQueue.async { [weak self] in
            self?.metrics.recordMiss(responseTime: responseTime)
        }
    }

    private func setupMetricsLogging() {
        // Log metrics every 5 minutes
        Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { [weak self] _ in
            self?.logMetrics()
        }
    }

    private func setupInvalidationHandling() {
        invalidationSubject
            .sink { [weak self] event in
                self?.handleInvalidationEvent(event)
            }
            .store(in: &cancellables)
    }

    private func handleInvalidationEvent(_ event: String) {
        // Handle specific invalidation events
        switch event {
        case "fixture_updated":
            invalidateCache(for: .fixture)
            invalidateCache(for: .standings) // Fixtures might affect standings
        case "standings_updated":
            invalidateCache(for: .standings)
        case "live_data_updated":
            invalidateCache(keyPattern: "*live*")
        default:
            Logger.debug("Unhandled invalidation event: \(event)", category: .performance)
        }
    }

    private func trackDependencies(for key: String, contentType: CacheContentType) {
        dependencyQueue.async { [weak self] in
            guard let self = self else { return }

            // Track reverse dependencies
            for dependentType in contentType.dependencies {
                let dependentKeys = self.findKeysForContentType(dependentType)
                for dependentKey in dependentKeys {
                    if self.dependencyMap[dependentKey] == nil {
                        self.dependencyMap[dependentKey] = Set<String>()
                    }
                    self.dependencyMap[dependentKey]?.insert(key)
                }
            }
        }
    }

    private func removeDependencyTracking(for key: String) {
        dependencyQueue.async { [weak self] in
            self?.dependencyMap.removeValue(forKey: key)

            // Remove from other dependencies
            for (dependentKey, _) in self?.dependencyMap ?? [:] {
                self?.dependencyMap[dependentKey]?.remove(key)
            }
        }
    }

    private func findKeysToInvalidate(for contentType: CacheContentType) -> [String] {
        return dependencyQueue.sync {
            var keysToInvalidate: [String] = []

            // Find direct keys of this content type
            let directKeys = findKeysForContentType(contentType)
            keysToInvalidate.append(contentsOf: directKeys)

            // Find dependent keys
            for key in directKeys {
                if let dependencies = dependencyMap[key] {
                    keysToInvalidate.append(contentsOf: dependencies)
                }
            }

            return Array(Set(keysToInvalidate)) // Remove duplicates
        }
    }

    private func findKeysForContentType(_ contentType: CacheContentType) -> [String] {
        let allKeys = getAllCacheKeys()
        return allKeys.filter { key in
            if let entry = cache.object(forKey: key as NSString) {
                return entry.contentType == contentType
            }
            return false
        }
    }

    private func getAllCacheKeys() -> [String] {
        // NSCache doesn't provide a way to enumerate keys directly
        // This is a limitation, but we can work around it by maintaining our own key tracking
        // For now, we'll use a simple approach
        var keys: [String] = []

        // This is a workaround - in a production app, you might want to maintain
        // a separate Set<String> to track all keys
        let mirror = Mirror(reflecting: cache)
        for child in mirror.children {
            if let keyDict = child.value as? NSDictionary {
                for (key, _) in keyDict {
                    if let stringKey = key as? String {
                        keys.append(stringKey)
                    }
                }
            }
        }

        return keys
    }

    private func matchesPattern(key: String, pattern: String) -> Bool {
        if pattern.contains("*") {
            let regexPattern = pattern.replacingOccurrences(of: "*", with: ".*")
            do {
                let regex = try NSRegularExpression(pattern: regexPattern, options: [])
                let range = NSRange(location: 0, length: key.utf16.count)
                return regex.firstMatch(in: key, options: [], range: range) != nil
            } catch {
                return false
            }
        } else {
            return key == pattern
        }
    }
}

// MARK: - NSCacheDelegate

extension CacheManager: NSCacheDelegate {
    private func cache(_ cache: NSCache<AnyObject, AnyObject>, willEvictObject obj: AnyObject) {
        if let entry = obj as? EnhancedCacheEntry {
            Logger.debug("Cache evicting entry (type: \(entry.contentType), accessed: \(entry.accessCount) times)", category: .performance)
        }
    }
}

/// An enhanced cache entry with metadata for smart invalidation and performance tracking
class EnhancedCacheEntry: NSObject {
    let data: Data
    let expirationDate: Date
    let contentType: CacheContentType
    let createdAt: Date
    var accessCount: Int
    var lastAccessedAt: Date?

    init(data: Data, expirationDate: Date, contentType: CacheContentType, createdAt: Date, accessCount: Int) {
        self.data = data
        self.expirationDate = expirationDate
        self.contentType = contentType
        self.createdAt = createdAt
        self.accessCount = accessCount
        self.lastAccessedAt = createdAt
    }
}

/// Legacy cache entry for backward compatibility
class CacheEntry: NSObject {
    let data: Data
    let expirationDate: Date

    init(data: Data, expirationDate: Date) {
        self.data = data
        self.expirationDate = expirationDate
    }
}
