import SwiftUI
import Combine

/// Manages navigation state for implementing tap-to-go-back behavior
class NavigationStateManager: ObservableObject {
    // MARK: - Published Properties

    /// Navigation path for iOS 16+ NavigationStack
    @Published var navigationPath = NavigationPath()

    // MARK: - Private Properties

    /// Prevent rapid navigation attempts
    private var lastNavigationTime: Date = Date.distantPast
    private let navigationCooldown: TimeInterval = 0.5 // 500ms cooldown

    /// Track path changes for debugging
    private var lastPathCount: Int = 0

    /// Track if we're currently in a detail view
    var isInDetailView: Bool {
        return !navigationPath.isEmpty
    }

    /// Track the current navigation depth
    var navigationDepth: Int {
        let currentCount = navigationPath.count

        // Log path changes for debugging
        if currentCount != lastPathCount {
            NavigationDebugger.shared.logPathChanged(
                pathCount: currentCount,
                details: "Path count changed from \(lastPathCount) to \(currentCount)"
            )
            lastPathCount = currentCount
        }

        return currentCount
    }

    // MARK: - Navigation Methods

    /// Navigate to a fixture detail view
    func navigateToFixtureDetail(fixture: Fixture) {
        let now = Date()

        // Log navigation attempt
        NavigationDebugger.shared.logNavigationAttempt(
            fixtureId: fixture.id,
            pathCount: navigationPath.count,
            details: "Teams: \(fixture.teams.home?.name ?? "Unknown") vs \(fixture.teams.away?.name ?? "Unknown")"
        )

        // Prevent rapid navigation attempts
        if now.timeIntervalSince(lastNavigationTime) < navigationCooldown {
            NavigationDebugger.shared.logNavigationBlocked(
                fixtureId: fixture.id,
                pathCount: navigationPath.count,
                details: "Cooldown period active (\(navigationCooldown)s)"
            )
            return
        }

        // Check if we're already navigating to prevent duplicate navigation
        if navigationPath.count > 0 {
            NavigationDebugger.shared.logNavigationAttempt(
                fixtureId: fixture.id,
                pathCount: navigationPath.count,
                details: "Warning - Already in navigation stack"
            )
        }

        // Update last navigation time
        lastNavigationTime = now

        // Ensure we're on the main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.navigationPath.append(fixture)

            NavigationDebugger.shared.logNavigationSuccess(
                fixtureId: fixture.id,
                pathCount: self.navigationPath.count,
                details: "Successfully appended to navigation path"
            )
        }
    }

    /// Navigate to user votes view
    func navigateToUserVotes() {
        navigationPath.append(NavigationDestination.userVotes)
    }

    /// Pop back to the previous view
    func popBack() {
        if !navigationPath.isEmpty {
            navigationPath.removeLast()
        }
    }

    /// Pop back to the root view
    func popToRoot() {
        navigationPath = NavigationPath()
    }

    /// Check if we can navigate back
    var canNavigateBack: Bool {
        return !navigationPath.isEmpty
    }
}

// MARK: - Navigation Destination

/// Enum to define navigation destinations
enum NavigationDestination: Hashable {
    case fixtureDetail(Fixture)
    case userVotes

    // Add more destinations as needed
    // case leagueDetail(League)
    // case playerDetail(Player)
}

// Note: Fixture already conforms to Hashable in its model definition
