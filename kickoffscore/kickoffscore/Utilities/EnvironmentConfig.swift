import Foundation

/// Environment configuration manager for handling different deployment environments
/// and secure configuration loading
struct EnvironmentConfig {

    // MARK: - Environment Detection

    /// Current deployment environment
    static var currentEnvironment: Environment {
        #if DEBUG
        return .development
        #elseif STAGING
        return .staging
        #else
        return .production
        #endif
    }

    /// Environment types
    enum Environment: String, CaseIterable {
        case development = "development"
        case staging = "staging"
        case production = "production"

        var displayName: String {
            switch self {
            case .development: return "Development"
            case .staging: return "Staging"
            case .production: return "Production"
            }
        }

        var apiBaseURL: String {
            switch self {
            case .development:
                return getEnvironmentValue("DEV_API_URL") ?? "https://api.kickoffpredictions.com/api"
            case .staging:
                return getEnvironmentValue("STAGING_API_URL") ?? "https://staging-api.kickoffpredictions.com/api"
            case .production:
                return getEnvironmentValue("PROD_API_URL") ?? "https://api.kickoffpredictions.com/api"
            }
        }

        var logLevel: Logger.Level {
            switch self {
            case .development: return .debug
            case .staging: return .info
            case .production: return .warning
            }
        }

        var enableAnalytics: Bool {
            switch self {
            case .development: return false
            case .staging: return true
            case .production: return true
            }
        }

        var enableCrashReporting: Bool {
            switch self {
            case .development: return false
            case .staging: return true
            case .production: return true
            }
        }
    }

    // MARK: - Configuration Loading

    /// Load configuration for the current environment
    static func loadConfiguration() {
        let env = currentEnvironment
        Logger.info("Loading configuration for environment: \(env.displayName)", category: .general)

        // Set logger configuration based on environment
        Logger.maxLevel = env.logLevel
        Logger.isEnabled = true

        // Environment-specific settings
        switch env {
        case .development:
            Logger.logJSONResponses = true
            Logger.showFileInfo = true
            Logger.showTimestamps = true

        case .staging:
            Logger.logJSONResponses = false
            Logger.showFileInfo = true
            Logger.showTimestamps = true

        case .production:
            Logger.logJSONResponses = false
            Logger.showFileInfo = false
            Logger.showTimestamps = false
        }

        Logger.info("Configuration loaded successfully", category: .general)
    }

    // MARK: - Environment Variables

    /// Get environment variable value
    static func getEnvironmentValue(_ key: String) -> String? {
        return ProcessInfo.processInfo.environment[key]
    }

    /// Get required environment variable (throws if not found)
    static func getRequiredEnvironmentValue(_ key: String) throws -> String {
        guard let value = getEnvironmentValue(key), !value.isEmpty else {
            throw ConfigurationError.missingRequiredEnvironmentVariable(key)
        }
        return value
    }

    /// Get environment variable with default value
    static func getEnvironmentValue(_ key: String, defaultValue: String) -> String {
        return getEnvironmentValue(key) ?? defaultValue
    }

    /// Get boolean environment variable
    static func getBooleanEnvironmentValue(_ key: String, defaultValue: Bool = false) -> Bool {
        guard let value = getEnvironmentValue(key) else { return defaultValue }
        return ["true", "1", "yes", "on"].contains(value.lowercased())
    }

    /// Get integer environment variable
    static func getIntegerEnvironmentValue(_ key: String, defaultValue: Int = 0) -> Int {
        guard let value = getEnvironmentValue(key), let intValue = Int(value) else {
            return defaultValue
        }
        return intValue
    }

    // MARK: - Security Configuration

    /// Get API key from environment or secure storage
    static func getAPIKey() -> String? {
        // Try environment variable first (for CI/CD)
        if let envKey = getEnvironmentValue("KICKOFFSCORE_API_KEY"), !envKey.isEmpty {
            Logger.info("Using API key from environment variable", category: .security)
            return envKey
        }

        // Fallback to secure storage
        return SecureConfig.getAPIKey()
    }

    /// Validate required configuration
    static func validateConfiguration() throws {
        var missingItems: [String] = []

        // Check API key
        if getAPIKey() == nil {
            missingItems.append("API Key")
        }

        // Environment-specific validation
        switch currentEnvironment {
        case .production:
            // Production requires stricter validation
            if !SecureConfig.isSecureEnvironment() {
                throw ConfigurationError.insecureEnvironment
            }

        case .staging:
            // Staging validation
            break

        case .development:
            // Development is more lenient
            break
        }

        if !missingItems.isEmpty {
            throw ConfigurationError.missingConfiguration(missingItems)
        }

        Logger.info("Configuration validation passed", category: .security)
    }

    // MARK: - Feature Flags

    /// Check if a feature is enabled
    static func isFeatureEnabled(_ feature: Feature) -> Bool {
        // Check environment variable override first
        let envKey = "FEATURE_\(feature.rawValue.uppercased())"
        if getEnvironmentValue(envKey) != nil {
            return getBooleanEnvironmentValue(envKey)
        }

        // Default feature state based on environment
        return feature.defaultState(for: currentEnvironment)
    }

    /// Available features
    enum Feature: String, CaseIterable {
        case pushNotifications = "push_notifications"
        case analytics = "analytics"
        case crashReporting = "crash_reporting"
        case debugMenu = "debug_menu"
        case betaFeatures = "beta_features"

        func defaultState(for environment: Environment) -> Bool {
            switch (self, environment) {
            case (.pushNotifications, _): return true
            case (.analytics, .development): return false
            case (.analytics, _): return true
            case (.crashReporting, .development): return false
            case (.crashReporting, _): return true
            case (.debugMenu, .development): return true
            case (.debugMenu, _): return false
            case (.betaFeatures, .production): return false
            case (.betaFeatures, _): return true
            }
        }
    }
}

// MARK: - Configuration Errors

enum ConfigurationError: LocalizedError {
    case missingRequiredEnvironmentVariable(String)
    case missingConfiguration([String])
    case insecureEnvironment

    var errorDescription: String? {
        switch self {
        case .missingRequiredEnvironmentVariable(let key):
            return "Missing required environment variable: \(key)"
        case .missingConfiguration(let items):
            return "Missing configuration items: \(items.joined(separator: ", "))"
        case .insecureEnvironment:
            return "Application is running in an insecure environment"
        }
    }
}

// MARK: - Configuration Extensions

extension EnvironmentConfig {
    /// Print current configuration (for debugging)
    static func printConfiguration() {
        #if DEBUG
        print("=== Environment Configuration ===")
        print("Environment: \(currentEnvironment.displayName)")
        print("API Base URL: \(currentEnvironment.apiBaseURL)")
        print("Log Level: \(currentEnvironment.logLevel)")
        print("Analytics Enabled: \(currentEnvironment.enableAnalytics)")
        print("Crash Reporting Enabled: \(currentEnvironment.enableCrashReporting)")
        print("=== Feature Flags ===")
        for feature in Feature.allCases {
            print("\(feature.rawValue): \(isFeatureEnabled(feature))")
        }
        print("================================")
        #endif
    }
}
