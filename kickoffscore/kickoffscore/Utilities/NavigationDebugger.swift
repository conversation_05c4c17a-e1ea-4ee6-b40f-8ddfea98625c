import SwiftUI
import Foundation

/// A debugging utility to help track navigation issues
class NavigationDebugger: ObservableObject {
    static let shared = NavigationDebugger()
    
    @Published var navigationEvents: [NavigationEvent] = []
    private let maxEvents = 50 // Keep only the last 50 events
    
    private init() {}
    
    struct NavigationEvent: Identifiable {
        let id = UUID()
        let timestamp: Date
        let type: EventType
        let fixtureId: Int?
        let pathCount: Int
        let details: String
        
        enum EventType {
            case navigationAttempt
            case navigationSuccess
            case navigationBlocked
            case pathChanged
            case error
        }
        
        var description: String {
            let formatter = DateFormatter()
            formatter.timeStyle = .medium
            formatter.dateStyle = .none
            
            let timeString = formatter.string(from: timestamp)
            let fixtureString = fixtureId.map { " (Fixture: \($0))" } ?? ""
            
            switch type {
            case .navigationAttempt:
                return "[\(timeString)] 🔄 Navigation Attempt\(fixtureString) - Path: \(pathCount)"
            case .navigationSuccess:
                return "[\(timeString)] ✅ Navigation Success\(fixtureString) - Path: \(pathCount)"
            case .navigationBlocked:
                return "[\(timeString)] ❌ Navigation Blocked\(fixtureString) - Path: \(pathCount)"
            case .pathChanged:
                return "[\(timeString)] 📍 Path Changed - New Count: \(pathCount)"
            case .error:
                return "[\(timeString)] ⚠️ Error\(fixtureString) - \(details)"
            }
        }
    }
    
    func logNavigationAttempt(fixtureId: Int, pathCount: Int, details: String = "") {
        addEvent(.navigationAttempt, fixtureId: fixtureId, pathCount: pathCount, details: details)
    }
    
    func logNavigationSuccess(fixtureId: Int, pathCount: Int, details: String = "") {
        addEvent(.navigationSuccess, fixtureId: fixtureId, pathCount: pathCount, details: details)
    }
    
    func logNavigationBlocked(fixtureId: Int, pathCount: Int, details: String = "") {
        addEvent(.navigationBlocked, fixtureId: fixtureId, pathCount: pathCount, details: details)
    }
    
    func logPathChanged(pathCount: Int, details: String = "") {
        addEvent(.pathChanged, fixtureId: nil, pathCount: pathCount, details: details)
    }
    
    func logError(fixtureId: Int? = nil, pathCount: Int, details: String) {
        addEvent(.error, fixtureId: fixtureId, pathCount: pathCount, details: details)
    }
    
    private func addEvent(_ type: NavigationEvent.EventType, fixtureId: Int?, pathCount: Int, details: String) {
        DispatchQueue.main.async { [weak self] in
            let event = NavigationEvent(
                timestamp: Date(),
                type: type,
                fixtureId: fixtureId,
                pathCount: pathCount,
                details: details
            )
            
            self?.navigationEvents.append(event)
            
            // Keep only the last maxEvents
            if let self = self, self.navigationEvents.count > self.maxEvents {
                self.navigationEvents.removeFirst(self.navigationEvents.count - self.maxEvents)
            }
            
            // Print to console for immediate debugging
            print("NavigationDebugger: \(event.description)")
            if !details.isEmpty {
                print("NavigationDebugger: Details - \(details)")
            }
        }
    }
    
    func clearEvents() {
        DispatchQueue.main.async { [weak self] in
            self?.navigationEvents.removeAll()
        }
    }
}

/// A debug view to display navigation events
struct NavigationDebugView: View {
    @StateObject private var debugger = NavigationDebugger.shared
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("Navigation Events")) {
                    if debugger.navigationEvents.isEmpty {
                        Text("No navigation events recorded")
                            .foregroundColor(.secondary)
                    } else {
                        ForEach(debugger.navigationEvents.reversed()) { event in
                            VStack(alignment: .leading, spacing: 4) {
                                Text(event.description)
                                    .font(.caption)
                                    .foregroundColor(colorForEventType(event.type))
                                
                                if !event.details.isEmpty {
                                    Text(event.details)
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .padding(.vertical, 2)
                        }
                    }
                }
            }
            .navigationTitle("Navigation Debug")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Clear") {
                        debugger.clearEvents()
                    }
                }
            }
        }
    }
    
    private func colorForEventType(_ type: NavigationDebugger.NavigationEvent.EventType) -> Color {
        switch type {
        case .navigationAttempt:
            return .blue
        case .navigationSuccess:
            return .green
        case .navigationBlocked:
            return .red
        case .pathChanged:
            return .orange
        case .error:
            return .red
        }
    }
}

#Preview {
    NavigationDebugView()
}
