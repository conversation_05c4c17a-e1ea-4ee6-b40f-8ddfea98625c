import SwiftUI
import Combine

// MARK: - Color Extensions

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

extension UIColor {
    convenience init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }

        self.init(
            red: CGFloat(r) / 255,
            green: CGFloat(g) / 255,
            blue: CGFloat(b) / 255,
            alpha: CGFloat(a) / 255
        )
    }
}

// MARK: - Theme Manager

/// Theme manager to handle app-wide styling
class ThemeManager: ObservableObject {
    /// Current theme mode (auto uses system setting)
    enum ThemeMode {
        case light
        case dark
        case auto
    }

    /// Published theme mode that views can observe
    @Published var themeMode: ThemeMode = .auto

    /// Current color scheme based on themeMode and system setting
    func effectiveColorScheme(systemScheme: ColorScheme) -> ColorScheme {
        switch themeMode {
        case .light: return .light
        case .dark: return .dark
        case .auto: return systemScheme
        }
    }
}

// MARK: - Typography

/// Typography styles following Apple's Human Interface Guidelines
struct AppTypography {
    // MARK: - Text Styles

    /// Use built-in text styles to leverage Dynamic Type and accessibility features

    /// Large title style - Uses system largeTitle
    static func largeTitle() -> some View {
        return Text("")
            .font(.largeTitle)
    }

    /// Title 1 style - Uses system title
    static func title1() -> some View {
        return Text("")
            .font(.title)
    }

    /// Title 2 style - Uses system title2
    static func title2() -> some View {
        return Text("")
            .font(.title2)
    }

    /// Title 3 style - Uses system title3
    static func title3() -> some View {
        return Text("")
            .font(.title3)
    }

    /// Headline style - Uses system headline
    static func headline() -> some View {
        return Text("")
            .font(.headline)
    }

    /// Body style - Uses system body
    static func body() -> some View {
        return Text("")
            .font(.body)
    }

    /// Callout style - Uses system callout
    static func callout() -> some View {
        return Text("")
            .font(.callout)
    }

    /// Subheadline style - Uses system subheadline
    static func subheadline() -> some View {
        return Text("")
            .font(.subheadline)
    }

    /// Footnote style - Uses system footnote
    static func footnote() -> some View {
        return Text("")
            .font(.footnote)
    }

    /// Caption style - Uses system caption
    static func caption1() -> some View {
        return Text("")
            .font(.caption)
    }

    /// Caption 2 style - Uses system caption2
    static func caption2() -> some View {
        return Text("")
            .font(.caption2)
    }

    // MARK: - Font Modifiers

    /// Apply large title style to a Text view
    static func applyLargeTitle<T: View>(_ content: T) -> some View {
        return content
            .font(.largeTitle)
    }

    /// Apply title 1 style to a Text view
    static func applyTitle1<T: View>(_ content: T) -> some View {
        return content
            .font(.title)
    }

    /// Apply title 2 style to a Text view
    static func applyTitle2<T: View>(_ content: T) -> some View {
        return content
            .font(.title2)
    }

    /// Apply title 3 style to a Text view
    static func applyTitle3<T: View>(_ content: T) -> some View {
        return content
            .font(.title3)
    }

    /// Apply headline style to a Text view
    static func applyHeadline<T: View>(_ content: T) -> some View {
        return content
            .font(.headline)
    }

    /// Apply body style to a Text view
    static func applyBody<T: View>(_ content: T) -> some View {
        return content
            .font(.body)
    }

    /// Apply callout style to a Text view
    static func applyCallout<T: View>(_ content: T) -> some View {
        return content
            .font(.callout)
    }

    /// Apply subheadline style to a Text view
    static func applySubheadline<T: View>(_ content: T) -> some View {
        return content
            .font(.subheadline)
    }

    /// Apply footnote style to a Text view
    static func applyFootnote<T: View>(_ content: T) -> some View {
        return content
            .font(.footnote)
    }

    /// Apply caption 1 style to a Text view
    static func applyCaption1<T: View>(_ content: T) -> some View {
        return content
            .font(.caption)
    }

    /// Apply caption 2 style to a Text view
    static func applyCaption2<T: View>(_ content: T) -> some View {
        return content
            .font(.caption2)
    }

    // MARK: - Dynamic Type Support

    /// Apply a font with Dynamic Type support
    static func dynamicFont(style: Font.TextStyle, weight: Font.Weight = .regular) -> Font {
        return Font.system(style, design: .default).weight(weight)
    }

    /// Apply a font with scaled size for accessibility
    static func scaledFont(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        return Font.system(size: size, weight: weight, design: .default)
    }

    // MARK: - App-Specific Text Styles

    /// Team name text style
    static func teamName() -> some View {
        return Text("")
            .font(.headline)
            .foregroundColor(AppColors.text)
    }

    /// Score text style
    static func scoreText() -> some View {
        return Text("")
            .font(.system(.title, design: .rounded, weight: .bold))
            .foregroundColor(AppColors.text)
    }

    /// Match time text style
    static func matchTimeText() -> some View {
        return Text("")
            .font(.caption)
            .foregroundColor(AppColors.secondaryText)
    }

    /// Player name text style
    static func playerName() -> some View {
        return Text("")
            .font(.subheadline)
            .foregroundColor(AppColors.text)
    }

    /// Stat value text style
    static func statValue() -> some View {
        return Text("")
            .font(.system(.body, design: .rounded, weight: .semibold))
            .foregroundColor(AppColors.text)
    }
}

// MARK: - Colors

/// Color styles following Apple's Human Interface Guidelines
struct AppColors {
    /// App brand colors - custom to KickOffScore app
    struct Brand {
        /// Primary brand color - White in dark mode, black in light mode
        static let primary = Color(UIColor { traitCollection in
            return traitCollection.userInterfaceStyle == .dark ?
                UIColor.white :     // White in dark mode
                UIColor.black       // Black in light mode
        })

        /// Secondary brand color - Vibrant green for sports
        static let secondary = Color(UIColor { traitCollection in
            return traitCollection.userInterfaceStyle == .dark ?
                UIColor(red: 0.2, green: 0.78, blue: 0.35, alpha: 1.0) : // #34C759 - iOS green in dark mode
                UIColor(red: 0.2, green: 0.78, blue: 0.35, alpha: 1.0)   // #34C759 - iOS green in light mode
        })

        /// Accent brand color - Orange for highlights
        static let accent = Color(UIColor { traitCollection in
            return traitCollection.userInterfaceStyle == .dark ?
                UIColor(red: 1.0, green: 0.58, blue: 0.0, alpha: 1.0) : // #FF9500 - iOS orange in dark mode
                UIColor(red: 1.0, green: 0.58, blue: 0.0, alpha: 1.0)   // #FF9500 - iOS orange in light mode
        })

        /// Gradient colors for modern effects - Subtle gradients using primary color
        static let gradientStart = Color(UIColor { traitCollection in
            return traitCollection.userInterfaceStyle == .dark ?
                UIColor.white.withAlphaComponent(0.9) :     // Light white in dark mode
                UIColor.black.withAlphaComponent(0.9)       // Light black in light mode
        })

        static let gradientEnd = Color(UIColor { traitCollection in
            return traitCollection.userInterfaceStyle == .dark ?
                UIColor.white.withAlphaComponent(0.7) :     // Lighter white in dark mode
                UIColor.black.withAlphaComponent(0.7)       // Lighter black in light mode
        })
    }

    /// Semantic colors for sports data
    struct Sports {
        /// Win color
        static let win = Color.green

        /// Loss color
        static let loss = Color.red

        /// Draw color
        static let draw = Color.orange

        /// Goal color
        static let goal = Color.blue

        /// Card yellow color
        static let yellowCard = Color.yellow

        /// Card red color
        static let redCard = Color.red

        /// Football pitch background color - adapts to light/dark mode
        static let pitchBackground = Color(UIColor { traitCollection in
            return traitCollection.userInterfaceStyle == .dark ?
                UIColor(hex: "2C2C2C") : // Dark gray in dark mode
                UIColor(hex: "01935C")   // Green in light mode
        })

        /// Football pitch lines color - adapts to light/dark mode
        static let pitchLines = Color(UIColor { traitCollection in
            return traitCollection.userInterfaceStyle == .dark ?
                UIColor(hex: "343434") : // Slightly lighter gray in dark mode
                UIColor(hex: "0D9F68")   // Darker green in light mode
        })
    }

    // MARK: - System Colors

    /// Primary app tint color - Use for primary interactive elements
    static let primary = Color.accentColor

    /// Secondary color - Use for secondary interactive elements
    static let secondary = Color(UIColor.secondarySystemFill)

    /// Success color - Use for positive outcomes or actions
    static let success = Color.green

    /// Warning color - Use for cautionary messages or states
    static let warning = Color.yellow

    /// Error color - Use for errors or destructive actions
    static let error = Color.red

    /// Info color - Use for informational elements
    static let info = Color.blue

    // MARK: - Semantic Colors (Automatically adapt to light/dark mode)

    /// Home team color - Adapts to color scheme
    static let homeTeam = Color.blue

    /// Away team color - Adapts to color scheme
    static let awayTeam = Color.red

    /// Draw color - Adapts to color scheme
    static let draw = Color.gray

    // MARK: - Background Colors (Automatically adapt to light/dark mode)

    /// Primary background color - Main background
    static let background = Color(UIColor { traitCollection in
        return traitCollection.userInterfaceStyle == .dark ?
            UIColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0) : // #000000 - Pure black in dark mode
            UIColor(red: 0.93, green: 0.95, blue: 0.96, alpha: 1.0) // #EDF1F5 - Light blue-gray in light mode
    })

    /// Secondary background color - For cards, grouped content
    static let secondaryBackground = Color(UIColor { traitCollection in
        return traitCollection.userInterfaceStyle == .dark ?
            UIColor(red: 0.15, green: 0.15, blue: 0.15, alpha: 1.0) : // #262626 - Dark gray in dark mode
            UIColor(red: 0.96, green: 0.96, blue: 0.96, alpha: 1.0) // #F5F5F5 - Light gray in light mode
    })

    /// Tertiary background color - For nested content
    static let tertiaryBackground = Color(UIColor { traitCollection in
        return traitCollection.userInterfaceStyle == .dark ?
            UIColor(red: 0.11, green: 0.11, blue: 0.11, alpha: 1.0) : // #1D1D1D - Dark gray in dark mode
            UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0) // #FFFFFF - Pure white in light mode
    })

    /// Group background color - For grouped tables
    static let groupedBackground = Color(UIColor.systemGroupedBackground)

    /// Secondary grouped background - For nested grouped content
    static let secondaryGroupedBackground = Color(UIColor.secondarySystemGroupedBackground)

    // MARK: - Fill Colors (Automatically adapt to light/dark mode)

    /// System fill color - For larger fills
    static let fill = Color(UIColor.systemFill)

    /// Secondary fill color - For medium-size fills
    static let secondaryFill = Color(UIColor.secondarySystemFill)

    /// Tertiary fill color - For small-size fills
    static let tertiaryFill = Color(UIColor.tertiarySystemFill)

    /// Quaternary fill color - For small fills in large areas
    static let quaternaryFill = Color(UIColor.quaternarySystemFill)

    /// Skeleton color - For loading placeholders
    static let skeleton = Color(UIColor { traitCollection in
        return traitCollection.userInterfaceStyle == .dark ?
            UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0) : // Dark gray in dark mode
            UIColor(red: 0.9, green: 0.9, blue: 0.9, alpha: 1.0)   // Light gray in light mode
    })

    // MARK: - Text Colors (Automatically adapt to light/dark mode)

    /// Primary text color - For primary content
    static let text = Color(UIColor.label)

    /// Secondary text color - For secondary content
    static let secondaryText = Color(UIColor.secondaryLabel)

    /// Tertiary text color - For tertiary content
    static let tertiaryText = Color(UIColor.tertiaryLabel)

    /// Quaternary text color - For placeholders
    static let quaternaryText = Color(UIColor.quaternaryLabel)

    // MARK: - Separator Colors (Automatically adapt to light/dark mode)

    /// Separator color - For borders and divider lines
    static let separator = Color(UIColor { traitCollection in
        return traitCollection.userInterfaceStyle == .dark ?
            UIColor(red: 0.15, green: 0.15, blue: 0.15, alpha: 1.0) : // #262626 in dark mode
            UIColor.separator // Default separator color in light mode
    })

    /// Opaque separator color - For thicker dividers
    static let opaqueSeparator = Color(UIColor.opaqueSeparator)

    // MARK: - Dynamic Color Creation

    /// Create a color that adapts to light and dark mode
    static func adaptiveColor(light: Color, dark: Color) -> Color {
        return Color(UIColor { traitCollection in
            return traitCollection.userInterfaceStyle == .dark ?
                UIColor(dark) : UIColor(light)
        })
    }

    /// Returns a color with guaranteed contrast ratio against background
    static func accessibleColor(color: Color, against background: Color, minContrastRatio: Double = 4.5) -> Color {
        // In a real implementation, this would calculate contrast and adjust if needed
        // Simplified version for now
        return color
    }

    /// Get appropriate text color (black or white) for a given background color
    static func appropriateTextColor(for backgroundColor: Color) -> Color {
        return adaptiveColor(light: .black, dark: .white)
    }

    /// Contrast text color - white in dark mode, black in light mode
    static let contrastText = adaptiveColor(light: .black, dark: .white)

    /// Reverse contrast text color - black in dark mode, white in light mode
    static let reverseContrastText = adaptiveColor(light: .white, dark: .black)

    /// Button text color - Adapts to be readable on brand-colored buttons
    static let buttonText = adaptiveColor(light: .white, dark: .white)

    /// Primary button text color - Contrasts with brand primary color
    static let primaryButtonText = Color(UIColor { traitCollection in
        return traitCollection.userInterfaceStyle == .dark ?
            UIColor.black :     // Black text on white background in dark mode
            UIColor.white       // White text on black background in light mode
    })

    /// League header background - Ensures proper contrast for league headers
    static let leagueHeaderBackground = Color(UIColor { traitCollection in
        return traitCollection.userInterfaceStyle == .dark ?
            UIColor(red: 0.12, green: 0.12, blue: 0.12, alpha: 1.0) : // Dark background in dark mode
            UIColor(red: 0.27, green: 0.27, blue: 0.27, alpha: 1.0)   // Medium dark in light mode for proper contrast
    })
}

// MARK: - Layout

/// Layout constants following Apple's Human Interface Guidelines
struct AppLayout {
    // MARK: - Standard Spacing

    /// Extra small spacing (4pt) - For very tight spacing
    static let spacingXS: CGFloat = 4

    /// Small spacing (8pt) - For tight spacing between related elements
    static let spacingS: CGFloat = 10

    /// Medium spacing (16pt) - Standard spacing between elements
    static let spacingM: CGFloat = 16

    /// Large spacing (24pt) - For separating groups of content
    static let spacingL: CGFloat = 24

    /// Extra large spacing (32pt) - For major sections
    static let spacingXL: CGFloat = 32

    /// Double extra large spacing (48pt) - For very significant separations
    static let spacingXXL: CGFloat = 48

    // MARK: - Dynamic Spacing

    /// Compact spacing - For compact UI layouts
    static var compactSpacing: CGFloat {
        return UIDevice.current.userInterfaceIdiom == .phone ? 12 : 16
    }

    /// Regular spacing - For regular UI layouts
    static var regularSpacing: CGFloat {
        return UIDevice.current.userInterfaceIdiom == .phone ? 16 : 20
    }

    /// Expanded spacing - For expanded UI layouts
    static var expandedSpacing: CGFloat {
        return UIDevice.current.userInterfaceIdiom == .phone ? 20 : 24
    }

    // MARK: - Corner Radius

    /// Small corner radius (6pt) - For small elements
    static let cornerRadiusS: CGFloat = 6

    /// Medium corner radius (8pt) - For medium elements like buttons
    static let cornerRadiusM: CGFloat = 8

    /// Large corner radius (8pt) - For larger elements like cards
    static let cornerRadiusL: CGFloat = 8

    /// Extra large corner radius (18pt) - For very large elements
    static let cornerRadiusXL: CGFloat = 18

    /// Circular corner radius (uses .infinity) - For circular elements
    static let cornerRadiusCircular: CGFloat = .infinity

    // MARK: - Icon Sizes

    /// Small icon size (16pt) - For inline icons
    static let iconSizeS: CGFloat = 16

    /// Medium icon size (24pt) - For standard icons
    static let iconSizeM: CGFloat = 24

    /// Large icon size (32pt) - For prominent icons
    static let iconSizeL: CGFloat = 32

    /// Extra large icon size (48pt) - For featured icons
    static let iconSizeXL: CGFloat = 48

    // MARK: - Safe Areas & Margins

    /// Standard horizontal margin (16pt) - Respects safe areas
    static let horizontalMargin: CGFloat = 16

    /// Standard vertical margin (16pt) - Respects safe areas
    static let verticalMargin: CGFloat = 16

    /// Edge-to-edge horizontal inset that respects safe areas
    static func safeHorizontalInset(in geometry: GeometryProxy) -> CGFloat {
        return max(geometry.safeAreaInsets.leading, horizontalMargin)
    }

    /// Edge-to-edge vertical inset that respects safe areas
    static func safeVerticalInset(in geometry: GeometryProxy) -> CGFloat {
        return max(geometry.safeAreaInsets.top, verticalMargin)
    }

    /// Content width - Adapts to device size
    static var contentWidth: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        if screenWidth > 834 { // iPad Pro and larger
            return 748 // Max content width for large screens
        } else {
            return screenWidth - (horizontalMargin * 2)
        }
    }

    // MARK: - Device Specific

    /// Returns true if the device is in compact width mode
    static var isCompactWidth: Bool {
        return UIScreen.main.traitCollection.horizontalSizeClass == .compact
    }

    /// Returns true if the device is in compact height mode
    static var isCompactHeight: Bool {
        return UIScreen.main.traitCollection.verticalSizeClass == .compact
    }

    /// Returns appropriate spacing based on current device traits
    static func adaptiveSpacing(compact: CGFloat, regular: CGFloat) -> CGFloat {
        return isCompactWidth ? compact : regular
    }
}

// MARK: - View Modifiers

/// Shadow modifier for elevated content following Apple's HIG
struct ElevatedStyle: ViewModifier {
    let elevation: Elevation

    enum Elevation {
        case low
        case medium
        case high
    }

    func body(content: Content) -> some View {
        switch elevation {
        case .low:
            return content
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        case .medium:
            return content
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        case .high:
            return content
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)
        }
    }
}

/// Modern card style modifier following iOS 17+ design principles
struct CardStyle: ViewModifier {
    let padding: CGFloat
    let cornerRadius: CGFloat
    let elevation: ElevatedStyle.Elevation
    let backgroundColor: Color
    let useModernShadow: Bool

    init(
        padding: CGFloat = AppLayout.spacingM,
        cornerRadius: CGFloat = AppLayout.cornerRadiusL,
        elevation: ElevatedStyle.Elevation = .medium,
        backgroundColor: Color = AppColors.secondaryBackground,
        useModernShadow: Bool = true
    ) {
        self.padding = padding
        self.cornerRadius = cornerRadius
        self.elevation = elevation
        self.backgroundColor = backgroundColor
        self.useModernShadow = useModernShadow
    }

    func body(content: Content) -> some View {
        content
            .padding(padding)
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(backgroundColor)
                    .shadow(
                        color: useModernShadow ?
                            Color.black.opacity(0.08) :
                            Color.clear,
                        radius: useModernShadow ? 8 : 0,
                        x: 0,
                        y: useModernShadow ? 2 : 0
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(AppColors.separator.opacity(0.3), lineWidth: 0.5)
            )
            .modifier(ElevatedStyle(elevation: elevation))
    }
}

/// Material background style following Apple's HIG
struct MaterialBackgroundStyle: ViewModifier {
    let material: Material
    let cornerRadius: CGFloat

    init(material: Material = .regularMaterial, cornerRadius: CGFloat = AppLayout.cornerRadiusM) {
        self.material = material
        self.cornerRadius = cornerRadius
    }

    func body(content: Content) -> some View {
        content
            .background(material)
            .cornerRadius(cornerRadius)
    }
}

/// Responsive padding that adapts to device size
struct ResponsivePaddingStyle: ViewModifier {
    func body(content: Content) -> some View {
        content
            .padding(.horizontal, AppLayout.adaptiveSpacing(compact: AppLayout.spacingM, regular: AppLayout.spacingL))
            .padding(.vertical, AppLayout.adaptiveSpacing(compact: AppLayout.spacingS, regular: AppLayout.spacingM))
    }
}

/// Accessibility modifier to improve VoiceOver experience
struct AccessibilityModifier: ViewModifier {
    let label: String
    let hint: String?

    init(label: String, hint: String? = nil) {
        self.label = label
        self.hint = hint
    }

    func body(content: Content) -> some View {
        if let hint = hint {
            return content
                .accessibilityLabel(Text(label))
                .accessibilityHint(Text(hint))
        } else {
            return content
                .accessibilityLabel(Text(label))
        }
    }
}

// MARK: - Match-specific Styles

/// Style modifiers for match-related UI components
struct MatchCardStyle: ViewModifier {
    let isLive: Bool

    init(isLive: Bool = false) {
        self.isLive = isLive
    }

    func body(content: Content) -> some View {
        content
            .padding(AppLayout.spacingM)
            .background(isLive ? AppColors.adaptiveColor(light: Color(UIColor.systemGray6), dark: Color(UIColor.systemGray5)) : AppColors.secondaryBackground)
            .cornerRadius(AppLayout.cornerRadiusL)
            .overlay(
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusL)
                    .stroke(isLive ? AppColors.primary : Color.clear, lineWidth: 2)
            )
            .modifier(ElevatedStyle(elevation: .medium))
    }
}

/// Style for stat bars in match statistics
struct StatBarStyle: ViewModifier {
    let value: CGFloat // Between 0 and 1
    let isHighlighted: Bool

    func body(content: Content) -> some View {
        content
            .frame(width: value * 100, height: 8)
            .background(isHighlighted ? AppColors.success : AppColors.secondary)
            .cornerRadius(4)
    }
}

// MARK: - Modern Button Styles

/// Modern prominent button style following iOS 17+ design
struct ProminentButtonStyle: ButtonStyle {
    let backgroundColor: Color
    let foregroundColor: Color
    let cornerRadius: CGFloat
    let isEnabled: Bool

    init(
        backgroundColor: Color = AppColors.Brand.primary,
        foregroundColor: Color? = nil, // Auto-determine based on background
        cornerRadius: CGFloat = AppLayout.cornerRadiusM,
        isEnabled: Bool = true
    ) {
        self.backgroundColor = backgroundColor
        // Auto-determine foreground color for better contrast
        self.foregroundColor = foregroundColor ?? AppColors.primaryButtonText
        self.cornerRadius = cornerRadius
        self.isEnabled = isEnabled
    }

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
            .foregroundColor(foregroundColor)
            .padding(.horizontal, AppLayout.spacingL)
            .padding(.vertical, AppLayout.spacingM)
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(backgroundColor)
                    .opacity(isEnabled ? (configuration.isPressed ? 0.8 : 1.0) : 0.6)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
            .disabled(!isEnabled)
    }
}

/// Modern secondary button style
struct SecondaryButtonStyle: ButtonStyle {
    let borderColor: Color
    let foregroundColor: Color
    let cornerRadius: CGFloat
    let isEnabled: Bool

    init(
        borderColor: Color = AppColors.Brand.primary,
        foregroundColor: Color = AppColors.Brand.primary,
        cornerRadius: CGFloat = AppLayout.cornerRadiusM,
        isEnabled: Bool = true
    ) {
        self.borderColor = borderColor
        self.foregroundColor = foregroundColor
        self.cornerRadius = cornerRadius
        self.isEnabled = isEnabled
    }

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(AppTypography.dynamicFont(style: .body, weight: .medium))
            .foregroundColor(foregroundColor)
            .padding(.horizontal, AppLayout.spacingL)
            .padding(.vertical, AppLayout.spacingM)
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(borderColor, lineWidth: 1.5)
                    .background(
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(configuration.isPressed ? borderColor.opacity(0.1) : Color.clear)
                    )
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
            .disabled(!isEnabled)
    }
}

/// Compact button style for smaller actions
struct CompactButtonStyle: ButtonStyle {
    let backgroundColor: Color
    let foregroundColor: Color

    init(
        backgroundColor: Color = AppColors.tertiaryFill,
        foregroundColor: Color = AppColors.text
    ) {
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
    }

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
            .foregroundColor(foregroundColor)
            .padding(.horizontal, AppLayout.spacingS)
            .padding(.vertical, AppLayout.spacingXS)
            .background(
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                    .fill(backgroundColor)
                    .opacity(configuration.isPressed ? 0.7 : 1.0)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - View Extensions

extension View {
    /// Apply low elevation shadow
    func elevatedLow() -> some View {
        self.modifier(ElevatedStyle(elevation: .low))
    }

    /// Apply medium elevation shadow
    func elevatedMedium() -> some View {
        self.modifier(ElevatedStyle(elevation: .medium))
    }

    /// Apply high elevation shadow
    func elevatedHigh() -> some View {
        self.modifier(ElevatedStyle(elevation: .high))
    }

    /// Apply card style with customizable parameters
    func cardStyle(
        padding: CGFloat = AppLayout.spacingM,
        cornerRadius: CGFloat = AppLayout.cornerRadiusL,
        elevation: ElevatedStyle.Elevation = .medium,
        backgroundColor: Color = AppColors.secondaryBackground
    ) -> some View {
        self.modifier(CardStyle(
            padding: padding,
            cornerRadius: cornerRadius,
            elevation: elevation,
            backgroundColor: backgroundColor
        ))
    }

    /// Apply material background style
    func materialBackground(material: Material = .regularMaterial, cornerRadius: CGFloat = AppLayout.cornerRadiusM) -> some View {
        self.modifier(MaterialBackgroundStyle(material: material, cornerRadius: cornerRadius))
    }

    /// Apply responsive padding that adapts to device size
    func responsivePadding() -> some View {
        self.modifier(ResponsivePaddingStyle())
    }

    /// Apply accessibility labels and hints
    func accessibilityInfo(label: String, hint: String? = nil) -> some View {
        self.modifier(AccessibilityModifier(label: label, hint: hint))
    }

    /// Apply rounded corners to specific corners only
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }

    /// Apply match card style
    func matchCardStyle(isLive: Bool = false) -> some View {
        self.modifier(MatchCardStyle(isLive: isLive))
    }

    /// Apply stat bar style
    func statBarStyle(value: CGFloat, isHighlighted: Bool = false) -> some View {
        self.modifier(StatBarStyle(value: value, isHighlighted: isHighlighted))
    }

    /// Apply themed background based on colorScheme
    func themedBackground(_ colorScheme: ColorScheme) -> some View {
        self.background(colorScheme == .dark ? Color.black : Color.white)
    }

    /// Apply safe area edge-to-edge layout
    func edgeToEdgeContent() -> some View {
        self.edgesIgnoringSafeArea([.horizontal])
            .padding(.horizontal, AppLayout.horizontalMargin)
    }

    /// Apply modern prominent button style
    func prominentButtonStyle(
        backgroundColor: Color = AppColors.Brand.primary,
        foregroundColor: Color? = nil, // Auto-determine for best contrast
        isEnabled: Bool = true
    ) -> some View {
        self.buttonStyle(ProminentButtonStyle(
            backgroundColor: backgroundColor,
            foregroundColor: foregroundColor,
            isEnabled: isEnabled
        ))
    }

    /// Apply modern secondary button style
    func secondaryButtonStyle(
        borderColor: Color = AppColors.Brand.primary,
        foregroundColor: Color = AppColors.Brand.primary,
        isEnabled: Bool = true
    ) -> some View {
        self.buttonStyle(SecondaryButtonStyle(
            borderColor: borderColor,
            foregroundColor: foregroundColor,
            isEnabled: isEnabled
        ))
    }

    /// Apply compact button style
    func compactButtonStyle(
        backgroundColor: Color = AppColors.tertiaryFill,
        foregroundColor: Color = AppColors.text
    ) -> some View {
        self.buttonStyle(CompactButtonStyle(
            backgroundColor: backgroundColor,
            foregroundColor: foregroundColor
        ))
    }
}

/// Helper shape for applying rounded corners to specific corners
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}
