//
//  BackgroundTaskDebugView.swift
//  kickoffscore
//
//  Created by <PERSON><PERSON><PERSON>hr<PERSON> on 05/07/2025.
//

import SwiftUI
import BackgroundTasks

/// Debug view for monitoring background task status and performance
struct BackgroundTaskDebugView: View {
    @StateObject private var backgroundTaskManager = BackgroundTaskManager.shared
    @State private var refreshTimer: Timer?
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            List {
                // Background Task Status Section
                Section("Background Task Status") {
                    StatusRow(
                        title: "Background Refresh Available",
                        value: backgroundTaskManager.isBackgroundRefreshAvailable ? "✅ Yes" : "❌ No",
                        color: backgroundTaskManager.isBackgroundRefreshAvailable ? .green : .red
                    )

                    StatusRow(
                        title: "BGTaskScheduler Support",
                        value: hasBackgroundTaskEntitlements ? "✅ Full Support" : "⚠️ Fallback Mode",
                        color: hasBackgroundTaskEntitlements ? .green : .orange
                    )
                    
                    StatusRow(
                        title: "Tasks Scheduled",
                        value: "\(backgroundTaskManager.backgroundTasksScheduled)",
                        color: .blue
                    )
                    
                    StatusRow(
                        title: "Last Background Refresh",
                        value: backgroundTaskManager.lastBackgroundRefresh?.formatted(date: .abbreviated, time: .shortened) ?? "Never",
                        color: .secondary
                    )
                    
                    StatusRow(
                        title: "System Background Refresh",
                        value: UIApplication.shared.backgroundRefreshStatus.description,
                        color: backgroundRefreshColor
                    )
                }
                
                // Socket Status Section
                Section("Socket Connection Status") {
                    StatusRow(
                        title: "Socket Connected",
                        value: SocketManager.shared.isConnected ? "✅ Connected" : "❌ Disconnected",
                        color: SocketManager.shared.isConnected ? .green : .red
                    )
                    
                    StatusRow(
                        title: "Active Subscriptions",
                        value: SocketManager.shared.hasActiveSubscriptions ? "✅ Yes" : "❌ No",
                        color: SocketManager.shared.hasActiveSubscriptions ? .green : .orange
                    )
                    
                    StatusRow(
                        title: "Subscription Details",
                        value: SocketManager.shared.subscriptionStatus,
                        color: .secondary
                    )
                }
                
                // Cache Status Section
                Section("Cache Status") {
                    StatusRow(
                        title: "Cache Performance",
                        value: String(format: "%.1f%% hit rate", CacheManager.shared.getMetrics().hitRate * 100),
                        color: cachePerformanceColor
                    )
                    
                    StatusRow(
                        title: "Total Cache Requests",
                        value: "\(CacheManager.shared.getMetrics().totalRequests)",
                        color: .blue
                    )
                    
                    StatusRow(
                        title: "Average Response Time",
                        value: String(format: "%.4fs", CacheManager.shared.getMetrics().averageResponseTime),
                        color: .secondary
                    )
                }
                
                // Debug Actions Section
                #if DEBUG
                Section("Debug Actions") {
                    Button("Force Background Refresh") {
                        backgroundTaskManager.debugForceBackgroundRefresh()
                        showAlert(message: "Background refresh triggered")
                    }
                    .foregroundColor(.blue)
                    
                    Button("Force Socket Refresh") {
                        backgroundTaskManager.debugForceSocketRefresh()
                        showAlert(message: "Socket refresh triggered")
                    }
                    .foregroundColor(.blue)
                    
                    Button("Clear Cache") {
                        CacheManager.shared.clearCache()
                        OddsCache.shared.clearAllCache()
                        showAlert(message: "All caches cleared")
                    }
                    .foregroundColor(.orange)
                    
                    Button("Schedule Background Tasks") {
                        backgroundTaskManager.scheduleBackgroundAppRefresh()
                        backgroundTaskManager.scheduleSocketRefresh()
                        showAlert(message: "Background tasks scheduled")
                    }
                    .foregroundColor(.green)
                }
                #endif
                
                // System Information Section
                Section("System Information") {
                    StatusRow(
                        title: "iOS Version",
                        value: UIDevice.current.systemVersion,
                        color: .secondary
                    )
                    
                    StatusRow(
                        title: "Device Model",
                        value: UIDevice.current.model,
                        color: .secondary
                    )
                    
                    StatusRow(
                        title: "App Version",
                        value: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown",
                        color: .secondary
                    )
                    
                    StatusRow(
                        title: "Build Number",
                        value: Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown",
                        color: .secondary
                    )
                }
            }
            .navigationTitle("Background Tasks Debug")
            .navigationBarTitleDisplayMode(.inline)
            .refreshable {
                // Refresh the view data - the @StateObject will automatically update
            }
            .alert("Debug Action", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
        .onAppear {
            startRefreshTimer()
        }
        .onDisappear {
            stopRefreshTimer()
        }
    }
    
    // MARK: - Helper Views
    
    private struct StatusRow: View {
        let title: String
        let value: String
        let color: Color
        
        var body: some View {
            HStack {
                Text(title)
                    .font(.body)
                Spacer()
                Text(value)
                    .font(.body.monospacedDigit())
                    .foregroundColor(color)
            }
        }
    }
    
    // MARK: - Computed Properties

    private var hasBackgroundTaskEntitlements: Bool {
        // Simple check - for personal development teams, assume no entitlements
        // This matches the logic in BackgroundTaskManager
        return false
    }

    private var backgroundRefreshColor: Color {
        switch UIApplication.shared.backgroundRefreshStatus {
        case .available:
            return .green
        case .denied, .restricted:
            return .red
        @unknown default:
            return .orange
        }
    }
    
    private var cachePerformanceColor: Color {
        let hitRate = CacheManager.shared.getMetrics().hitRate
        if hitRate >= 0.8 {
            return .green
        } else if hitRate >= 0.6 {
            return .orange
        } else {
            return .red
        }
    }
    
    // MARK: - Helper Methods
    
    private func showAlert(message: String) {
        alertMessage = message
        showingAlert = true
    }
    
    private func startRefreshTimer() {
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            // Trigger view refresh by updating the backgroundTaskManager
            // The @StateObject will automatically refresh the view
        }
    }
    
    private func stopRefreshTimer() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
}

// MARK: - Preview

#if DEBUG
struct BackgroundTaskDebugView_Previews: PreviewProvider {
    static var previews: some View {
        BackgroundTaskDebugView()
    }
}
#endif
