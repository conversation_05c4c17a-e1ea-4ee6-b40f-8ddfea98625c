import Foundation
import SwiftUI
import <PERSON><PERSON><PERSON>

struct LeagueFixtureRowView: View {
    let fixture: Fixture
    @EnvironmentObject private var navigationState: NavigationStateManager

    var body: some View {
        Button(action: {
            navigationState.navigateToFixtureDetail(fixture: fixture)
        }) {
            HStack(alignment: .center, spacing: AppLayout.spacingXS) {
                // Left: Date/Time
                VStack(alignment: .leading, spacing: 2) {
                    Text(formattedMatchDate())
                        .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                        .foregroundColor(AppColors.secondaryText)
                        .lineLimit(1)
                        .minimumScaleFactor(0.8)

                    Text(formattedKickoffTime())
                        .font(AppTypography.dynamicFont(style: .caption, weight: .regular))
                        .foregroundColor(AppColors.text)
                        .lineLimit(1)
                }
                .frame(width: 80, alignment: .leading)

                // Middle: Teams (Logo + Name)
                VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                    HStack(spacing: AppLayout.spacingS) {
                        TeamLogo(url: fixture.teams.home?.logo, teamName: fixture.teams.home?.name ?? "Home")
                            .foregroundColor(AppColors.Sports.win)

                        HStack(spacing: 6) {
                            Text(fixture.teams.home?.name ?? "N/A")
                                .font(AppTypography.dynamicFont(style: .subheadline))
                                .foregroundColor(Color(UIColor.label))
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)

                            // Show red card icons based on the count
                            let homeRedCards = countTeamRedCards(teamId: fixture.teams.home?.id)

                            if homeRedCards > 0 {
                                HStack(spacing: 2) {
                                    if homeRedCards >= 5 {
                                        // For 5 or more red cards, show one icon with a multiplier
                                        Image("red-card")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 12, height: 16)
                                            .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)

                                        Text("x\(homeRedCards)")
                                            .font(AppTypography.dynamicFont(style: .caption2, weight: .bold))
                                            .foregroundColor(AppColors.Sports.redCard)
                                    } else {
                                        // Show one red card icon per red card (up to 3)
                                        ForEach(0..<min(homeRedCards, 3), id: \.self) { _ in
                                            Image("red-card")
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: 12, height: 16)
                                                .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                                        }
                                    }
                                }
                                .offset(y: 1)
                            }
                        }
                    }

                    HStack(spacing: AppLayout.spacingS) {
                        TeamLogo(url: fixture.teams.away?.logo, teamName: fixture.teams.away?.name ?? "Away")
                            .foregroundColor(AppColors.Sports.draw)

                        HStack(spacing: 6) {
                            Text(fixture.teams.away?.name ?? "N/A")
                                .font(AppTypography.dynamicFont(style: .subheadline))
                                .foregroundColor(Color(UIColor.label))
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)

                            // Show red card icons based on the count
                            let awayRedCards = countTeamRedCards(teamId: fixture.teams.away?.id)

                            if awayRedCards > 0 {
                                HStack(spacing: 2) {
                                    if awayRedCards >= 5 {
                                        // For 5 or more red cards, show one icon with a multiplier
                                        Image("red-card")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 12, height: 16)
                                            .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)

                                        Text("x\(awayRedCards)")
                                            .font(AppTypography.dynamicFont(style: .caption2, weight: .bold))
                                            .foregroundColor(AppColors.Sports.redCard)
                                    } else {
                                        // Show one red card icon per red card (up to 3)
                                        ForEach(0..<min(awayRedCards, 3), id: \.self) { _ in
                                            Image("red-card")
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: 12, height: 16)
                                                .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                                        }
                                    }
                                }
                                .offset(y: 1)
                            }
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // Right: Scores (only for Live/Finished)
                if shouldShowScore {
                    VStack(alignment: .center, spacing: AppLayout.spacingS) {
                        Text("\(fixture.goals.home ?? 0)")
                            .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                            .foregroundColor(Color(UIColor.label))

                        Text("\(fixture.goals.away ?? 0)")
                            .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                            .foregroundColor(Color(UIColor.label))
                    }
                    .frame(width: 40, alignment: .center)
                    .accessibilityInfo(
                        label: "Score: \(fixture.teams.home?.name ?? "Home") \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0) \(fixture.teams.away?.name ?? "Away")"
                    )
                } else {
                    // Show dash for upcoming matches
                    VStack(alignment: .center, spacing: AppLayout.spacingS) {
                        Text("-")
                            .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                            .foregroundColor(AppColors.secondaryText)

                        Text("-")
                            .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                            .foregroundColor(AppColors.secondaryText)
                    }
                    .frame(width: 40, alignment: .center)
                }
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, 5) // Match FixtureRowView padding
            .contentShape(Rectangle()) // Make the entire row tappable
        }
        .buttonStyle(PlainButtonStyle()) // Make the link area look like plain content
        .accessibilityInfo(
            label: getAccessibilityLabel(),
            hint: getAccessibilityHint()
        )
    }

    // Helper View for Team Logo with KFImage for better caching
    private func TeamLogo(url: String?, teamName: String) -> some View {
        KFImage(URL(string: url ?? ""))
            .placeholder {
                Image(systemName: "soccerball")
                    .foregroundColor(Color(UIColor.secondaryLabel))
            }
            .resizable()
            .scaledToFit()
            .frame(width: 20, height: 20) // Smaller logo size (was AppLayout.iconSizeM which is 24)
            .accessibilityLabel("\(teamName) logo")
    }

    // Helper to determine if scores should be shown
    private var shouldShowScore: Bool {
        let scoreStatusCodes = ["FT", "AET", "PEN", "LIVE", "1H", "HT", "2H", "ET", "P", "Aw. ET"]
        return scoreStatusCodes.contains(fixture.status.short ?? "")
    }

    // Helper to format match date
    private func formattedMatchDate() -> String {
        let calendar = Calendar.current
        let kickoffDate = fixture.kickoffDate

        if calendar.isDateInToday(kickoffDate) {
            return "Today"
        } else if calendar.isDateInTomorrow(kickoffDate) {
            return "Tomorrow"
        } else if calendar.isDateInYesterday(kickoffDate) {
            return "Yesterday"
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "E MMM d" // e.g., "Sun May 5"
            return formatter.string(from: kickoffDate)
        }
    }

    // Helper to format kickoff time
    private func formattedKickoffTime() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm" // e.g., 19:45 (24-hour format, no AM/PM)
        return dateFormatter.string(from: fixture.kickoffDate)
    }

    // Helper to count the number of red cards for a team
    private func countTeamRedCards(teamId: Int?) -> Int {
        guard let teamId = teamId, let events = fixture.events else {
            return 0
        }

        // Get all players who received any type of card
        let playersWithCards = events
            .filter { event in
                event.team.id == teamId &&
                event.type == "Card"
            }
            .compactMap { $0.player.id }

        // Count unique players with red cards by checking each player's card events
        var uniquePlayersWithRedCards = Set<Int>()

        // Get unique player IDs
        let uniquePlayerIds = Set(playersWithCards)

        // For each player, check if they received a red card (either direct or through two yellows)
        for playerId in uniquePlayerIds {
            // Get all card events for this player
            let playerCardEvents = events.filter { event in
                event.player.id == playerId &&
                event.team.id == teamId &&
                event.type == "Card"
            }

            // Check if player received a direct red card
            let hasDirectRedCard = playerCardEvents.contains { event in
                event.detail.lowercased().contains("red") &&
                !event.detail.lowercased().contains("yellow")
            }

            // Count yellow cards for this player
            let yellowCardCount = playerCardEvents.filter { event in
                event.detail.lowercased().contains("yellow")
            }.count

            // If player has a direct red card or 2+ yellow cards, add to the set
            if hasDirectRedCard || yellowCardCount >= 2 {
                uniquePlayersWithRedCards.insert(playerId)
            }
        }

        return uniquePlayersWithRedCards.count
    }

    // Helper to generate accessibility label with red card information
    private func getAccessibilityLabel() -> String {
        let homeTeamName = fixture.teams.home?.name ?? "Home"
        let awayTeamName = fixture.teams.away?.name ?? "Away"

        let homeRedCards = countTeamRedCards(teamId: fixture.teams.home?.id)
        let awayRedCards = countTeamRedCards(teamId: fixture.teams.away?.id)

        let homeRedCardText = homeRedCards > 0 ? " (\(homeRedCards) Red Card\(homeRedCards > 1 ? "s" : ""))" : ""
        let awayRedCardText = awayRedCards > 0 ? " (\(awayRedCards) Red Card\(awayRedCards > 1 ? "s" : ""))" : ""

        return "\(homeTeamName)\(homeRedCardText) versus \(awayTeamName)\(awayRedCardText)"
    }

    // Helper to generate accessibility hint based on match status
    private func getAccessibilityHint() -> String {
        return "Match on \(formattedMatchDate()) at \(formattedKickoffTime()). Tap for match details"
    }
}

#Preview {
    LeagueFixtureRowView(fixture: Fixture.mock)
        .padding()
}
