import SwiftUI
import Combine

struct MainTabView: View {
    @StateObject private var authViewModel = AuthViewModel()
    @EnvironmentObject private var themeManager: ThemeManager

    // Tab selection tracking
    @State private var selectedTab: Int = 0
    @State private var previousSelectedTab: Int = 0

    // Separate navigation state management for each tab
    @StateObject private var matchesNavigationState = NavigationStateManager()
    @StateObject private var leaguesNavigationState = NavigationStateManager()
    @StateObject private var profileNavigationState = NavigationStateManager()

    var body: some View {
        TabView(selection: $selectedTab) {
            LeagueFixturesView()
                .environmentObject(authViewModel)
                .environmentObject(matchesNavigationState)
                .tabItem {
                    Image(systemName: "sportscourt.fill")
                    Text("Matches")
                }
                .tag(0)

            NewsView()
                .environmentObject(authViewModel)
                .tabItem {
                    Image(systemName: "newspaper.fill")
                    Text("News")
                }
                .tag(1)

            LeaguesView()
                .environmentObject(authViewModel)
                .environmentObject(leaguesNavigationState)
                .tabItem {
                    Image(systemName: "trophy.fill")
                    Text("Leagues")
                }
                .tag(2)

            VideosView()
                .environmentObject(authViewModel)
                .tabItem {
                    Image(systemName: "play.rectangle.fill")
                    Text("Videos")
                }
                .tag(3)

            // Profile Tab
            NavigationStack(path: $profileNavigationState.navigationPath) {
                ProfileView(viewModel: authViewModel)
                    .navigationDestination(for: NavigationDestination.self) { destination in
                        switch destination {
                        case .userVotes:
                            UserVotesView()
                        case .fixtureDetail(let fixture):
                            FixtureDetailView(fixture: fixture, navigationSource: "Profile")
                        }
                    }
                    .navigationDestination(for: Fixture.self) { fixture in
                        FixtureDetailView(fixture: fixture, navigationSource: "Profile")
                    }
            }
            .environmentObject(authViewModel)
            .environmentObject(profileNavigationState)
            .tabItem {
                Image(systemName: authViewModel.isAuthenticated ? "person.crop.circle.fill" : "person.crop.circle")
                Text("Profile")
            }
            .tag(4)
        }
        .tint(AppColors.Brand.primary) // Set the active tab icon color to our brand primary
        .accentColor(AppColors.Brand.primary) // For older iOS versions
        .onChange(of: selectedTab) { newTab in
            handleTabSelection(newTab: newTab)
        }
        .onAppear {
            configureTabBarAppearance()
        }
    }

    // MARK: - Tab Selection Handling

    /// Handle tab selection changes to implement tap-to-go-back behavior
    private func handleTabSelection(newTab: Int) {
        // Check if the same tab was tapped (tap-to-go-back behavior)
        if newTab == previousSelectedTab {
            switch newTab {
            case 0: // Matches tab
                // If we're in a detail view, navigate back
                if matchesNavigationState.isInDetailView {
                    matchesNavigationState.popToRoot()
                }
            case 2: // Leagues tab
                // If we're in a detail view, navigate back
                if leaguesNavigationState.isInDetailView {
                    leaguesNavigationState.popToRoot()
                }
            case 4: // Profile tab
                // If we're in a detail view, navigate back
                if profileNavigationState.isInDetailView {
                    profileNavigationState.popToRoot()
                }
            default:
                // For other tabs, implement similar logic if needed in the future
                break
            }
        }

        // Update the previous selected tab
        previousSelectedTab = newTab
    }

    private func configureTabBarAppearance() {
        let tabBarAppearance = UITabBarAppearance()
        tabBarAppearance.configureWithOpaqueBackground()

        // Set background color with blur effect
        tabBarAppearance.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)

        // Configure normal state
        tabBarAppearance.stackedLayoutAppearance.normal.iconColor = UIColor.secondaryLabel
        tabBarAppearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.secondaryLabel,
            .font: UIFont.systemFont(ofSize: 10, weight: .medium)
        ]

        // Configure selected state
        tabBarAppearance.stackedLayoutAppearance.selected.iconColor = UIColor(AppColors.Brand.primary)
        tabBarAppearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor(AppColors.Brand.primary),
            .font: UIFont.systemFont(ofSize: 10, weight: .semibold)
        ]

        // Apply the appearance
        UITabBar.appearance().standardAppearance = tabBarAppearance
        UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
    }
}

// MARK: - Placeholder Views

// Placeholder View for News Tab
struct NewsView: View {
    var body: some View {
        NavigationStack {
            ZStack {
                AppColors.background.ignoresSafeArea()

                VStack(spacing: AppLayout.spacingL) {
                    // Modern icon with brand color background
                    ZStack {
                        Circle()
                            .fill(AppColors.Brand.primary)
                            .frame(width: 80, height: 80)

                        Image(systemName: "newspaper.fill")
                            .font(.system(size: 36, weight: .medium))
                            .foregroundColor(Color(UIColor { traitCollection in
                                return traitCollection.userInterfaceStyle == .dark ?
                                    UIColor.black :     // Black icon on white background in dark mode
                                    UIColor.white       // White icon on black background in light mode
                            }))
                    }

                    VStack(spacing: AppLayout.spacingS) {
                        Text("News Coming Soon!")
                            .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                            .foregroundColor(Color(UIColor.label))

                        Text("Stay tuned for the latest football news and updates")
                            .font(AppTypography.dynamicFont(style: .body))
                            .foregroundColor(AppColors.secondaryText)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, AppLayout.spacingL)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .accessibilityInfo(label: "News section", hint: "News content is coming soon")
            }
            .navigationTitle("News")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

// Placeholder View for Videos Tab
struct VideosView: View {
    var body: some View {
        NavigationStack {
            ZStack {
                AppColors.background.ignoresSafeArea()

                VStack(spacing: AppLayout.spacingL) {
                    // Modern icon with secondary color background
                    ZStack {
                        Circle()
                            .fill(AppColors.Brand.secondary)
                            .frame(width: 80, height: 80)

                        Image(systemName: "play.rectangle.fill")
                            .font(.system(size: 36, weight: .medium))
                            .foregroundColor(.white) // White icon looks good on green background
                    }

                    VStack(spacing: AppLayout.spacingS) {
                        Text("Videos Coming Soon!")
                            .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                            .foregroundColor(Color(UIColor.label))

                        Text("Watch match highlights, player interviews, and exclusive content")
                            .font(AppTypography.dynamicFont(style: .body))
                            .foregroundColor(AppColors.secondaryText)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, AppLayout.spacingL)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .accessibilityInfo(label: "Videos section", hint: "Video content is coming soon")
            }
            .navigationTitle("Videos")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}
