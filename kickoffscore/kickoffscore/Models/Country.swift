import Foundation

// MARK: - Country Response
struct CountryResponse: Codable {
    let response: [Country]
}

// MARK: - Country Model
struct Country: Codable, Identifiable, Hashable {
    let name: String
    let code: String?
    let flag: String?
    
    // Computed property for Identifiable conformance
    var id: String { name }
    
    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(name)
        hasher.combine(code)
    }
    
    static func == (lhs: Country, rhs: Country) -> Bool {
        lhs.name == rhs.name && lhs.code == rhs.code
    }
}
