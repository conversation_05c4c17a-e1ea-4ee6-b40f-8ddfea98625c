import Foundation

// MARK: - Player Squad Response
struct PlayerSquadResponse: Codable {
    let response: [TeamSquad]

    // Custom initializer to handle empty array responses
    init(from decoder: Decoder) throws {
        do {
            // Try to decode normally first
            let container = try decoder.container(keyedBy: CodingKeys.self)
            response = try container.decode([TeamSquad].self, forKey: .response)
        } catch {
            // If that fails, check if it's an empty array
            if let array = try? decoder.singleValueContainer().decode([TeamSquad].self), array.isEmpty {
                response = []
            } else {
                // If it's not an empty array, rethrow the original error
                throw error
            }
        }
    }

    // Custom initializer for creating an empty response manually
    init(emptyResponse: Bool = true) {
        self.response = []
    }

    enum CodingKeys: String, CodingKey {
        case response
    }
}

// MARK: - Team Squad
struct TeamSquad: Codable, Identifiable {
    let team: TeamInfo
    let players: [PlayerSquad]

    // Computed property for Identifiable conformance
    var id: Int { team.id }

    // Team info structure
    struct TeamInfo: Codable {
        let id: Int
        let name: String
        let logo: String?
    }

    // Group players by position
    func groupedByPosition() -> [String: [PlayerSquad]] {
        var grouped = [String: [PlayerSquad]]()

        // First, group by standardized position categories
        for player in players {
            let position = standardizePosition(player.player.position ?? "Unknown")
            if grouped[position] == nil {
                grouped[position] = []
            }
            grouped[position]?.append(player)
        }

        return grouped
    }

    // Standardize position names for grouping
    private func standardizePosition(_ position: String) -> String {
        let positionLower = position.lowercased()

        if positionLower.contains("goalkeeper") || positionLower.contains("keeper") || positionLower == "gk" {
            return "Goalkeepers"
        } else if positionLower.contains("defender") || positionLower.contains("back") || positionLower == "df" {
            return "Defenders"
        } else if positionLower.contains("midfielder") || positionLower.contains("mid") || positionLower == "mf" {
            return "Midfielders"
        } else if positionLower.contains("forward") || positionLower.contains("striker") || positionLower.contains("wing") || positionLower == "fw" || positionLower == "at" {
            return "Forwards"
        } else {
            return "Other"
        }
    }

    // Get ordered position categories for display
    static func positionOrder() -> [String] {
        return ["Goalkeepers", "Defenders", "Midfielders", "Forwards", "Other"]
    }
}

// MARK: - Player Squad
struct PlayerSquad: Codable, Identifiable {
    let player: PlayerInfo

    // Computed property for Identifiable conformance
    var id: Int { player.id }

    // Player info structure
    struct PlayerInfo: Codable {
        let id: Int
        let name: String
        let age: Int?
        let number: Int?
        let position: String?
        let photo: String?
    }

    // Get formatted position and number for display
    var formattedPositionAndNumber: String {
        var result = ""

        if let number = player.number {
            result += "#\(number) · "
        }

        if let position = player.position {
            result += position
        } else {
            result += "Unknown"
        }

        return result
    }

    // Get formatted age for display
    var formattedAge: String {
        if let age = player.age {
            return "\(age) years"
        }
        return "Unknown age"
    }
}

// MARK: - Coach
struct Coach: Codable, Identifiable {
    let id: Int
    let name: String
    let firstname: String?
    let lastname: String?
    let age: Int?
    let nationality: String?
    let photo: String?

    // Computed property for display name
    var displayName: String {
        return name
    }

    // Computed property for display nationality
    var displayNationality: String {
        return nationality ?? "Unknown"
    }
}
