import Foundation

// MARK: - Top Scorer Response
struct TopScorerResponse: Codable {
    let response: [TopScorer]
}

// MARK: - Top Scorer
struct TopScorer: Codable, Identifiable {
    var id: String { "\(player.id)-\(statistics.first?.team.id ?? 0)" }
    
    let player: PlayerInfo
    let statistics: [ScorerStatistics]
    
    // MARK: - Player Info
    struct PlayerInfo: Codable {
        let id: Int
        let name: String
        let firstname: String?
        let lastname: String?
        let age: Int?
        let nationality: String?
        let height: String?
        let weight: String?
        let photo: String?
        
        // Computed property for full name with proper formatting
        var fullName: String {
            if let firstName = firstname, let lastName = lastname {
                return "\(firstName) \(lastName)"
            } else {
                return name
            }
        }
    }
    
    // MARK: - Scorer Statistics
    struct ScorerStatistics: Codable {
        let team: TeamInfo
        let league: LeagueInfo
        let games: Games
        let goals: Goals
        
        // MARK: - Team Info
        struct TeamInfo: Codable {
            let id: Int
            let name: String
            let logo: String?
        }
        
        // MARK: - League Info
        struct LeagueInfo: Codable {
            let id: Int
            let name: String
            let logo: String?
        }
        
        // MARK: - Games
        struct Games: Codable {
            let appearences: Int?
            let minutes: Int?
            let position: String?
            let rating: String?
            
            enum CodingKeys: String, CodingKey {
                case appearences, minutes, position, rating
            }
        }
        
        // MARK: - Goals
        struct Goals: Codable {
            let total: Int?
            let conceded: Int?
            let assists: Int?
            
            enum CodingKeys: String, CodingKey {
                case total, conceded, assists
            }
        }
    }
}
