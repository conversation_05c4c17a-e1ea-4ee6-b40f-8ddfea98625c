import Foundation

// Model for user votes list
struct UserVotesList: Codable {
    let votes: [UserVoteDetail]
    let count: Int

    // Custom coding keys to handle response format
    enum CodingKeys: String, CodingKey {
        case votes = "response"
        case count = "results"
    }
}

// Model for detailed user vote
struct UserVoteDetail: Codable, Identifiable {
    let id: String
    let fixtureId: Int
    let vote: VoteOption
    let createdAt: Date
    let userId: String

    // Fixture details
    var fixture: SimplifiedFixture?

    // Vote result information
    var voteResult: VoteResult?

    // Custom coding keys to handle _id vs id and response format
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case fixtureId
        case vote
        case createdAt
        case userId
        case fixture
        case voteResult
    }

    // Initializer for creating mock votes
    init(id: String, fixtureId: Int, vote: VoteOption, createdAt: Date, userId: String, fixture: SimplifiedFixture?, voteResult: VoteResult? = nil) {
        self.id = id
        self.fixtureId = fixtureId
        self.vote = vote
        self.createdAt = createdAt
        self.userId = userId
        self.fixture = fixture
        self.voteResult = voteResult
    }
}

// Model for vote result
struct VoteResult: Codable {
    let isCorrect: Bool?
    let result: String?
}

// Extension for UserVoteDetail to handle custom decoding
extension UserVoteDetail {
    // Custom init to handle date decoding
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        fixtureId = try container.decode(Int.self, forKey: .fixtureId)
        vote = try container.decode(VoteOption.self, forKey: .vote)
        userId = try container.decode(String.self, forKey: .userId)

        // Handle date decoding
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)

        let createdAtString = try container.decode(String.self, forKey: .createdAt)
        if let date = dateFormatter.date(from: createdAtString) {
            createdAt = date
        } else {
            createdAt = Date()
        }

        // Fixture might be null
        fixture = try container.decodeIfPresent(SimplifiedFixture.self, forKey: .fixture)

        // Vote result might be null
        voteResult = try container.decodeIfPresent(VoteResult.self, forKey: .voteResult)
    }
}
