import Foundation

// Response structure for the league-fixtures endpoint
public struct LeagueFixturesResponse: Decodable {
    public let data: [LeagueWithFixtures]
    public let pagination: LeaguePaginationInfo
}

// Structure representing a league with its fixtures
public struct LeagueWithFixtures: Decodable, Identifiable, Hashable {
    public var id: Int { league.id } // Computed property for Identifiable conformance

    public let league: Fixture.LeagueInfo
    public let fixtures: [Fixture]

    // Implement Hashable conformance
    public func hash(into hasher: inout Hasher) {
        hasher.combine(league.id)
        hasher.combine(league.name)
        hasher.combine(league.country)

        // Hash each fixture to ensure changes to fixtures affect the hash
        for fixture in fixtures {
            hasher.combine(fixture)
        }
    }

    public static func == (lhs: LeagueWithFixtures, rhs: LeagueWithFixtures) -> Bool {
        // First compare basic league properties
        guard lhs.league.id == rhs.league.id &&
              lhs.league.name == rhs.league.name &&
              lhs.league.country == rhs.league.country &&
              lhs.fixtures.count == rhs.fixtures.count else {
            return false
        }

        // Then compare each fixture
        for i in 0..<lhs.fixtures.count {
            if lhs.fixtures[i] != rhs.fixtures[i] {
                return false
            }
        }

        return true
    }
}

// Pagination information for league-based pagination
public struct LeaguePaginationInfo: Decodable {
    public let page: Int
    public let leaguesPerPage: Int
    public let totalPages: Int
    public let totalLeagues: Int
    public let hasNextPage: Bool
    public let hasPrevPage: Bool
}
