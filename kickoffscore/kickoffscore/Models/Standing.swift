import Foundation

// MARK: - Standing Response
struct StandingResponse: Codable {
    let response: [[TeamStanding]]
    
    enum CodingKeys: String, CodingKey {
        case response
    }
}

// MARK: - Team Standing
struct TeamStanding: Codable, Identifiable {
    let rank: Int
    let team: TeamInfo
    let points: Int
    let goalsDiff: Int
    let group: String?
    let form: String?
    let status: String?
    let description: String?
    let all: StandingStats
    let home: StandingStats
    let away: StandingStats
    let update: String?
    
    // Computed property for Identifiable conformance
    var id: Int { team.id }
    
    // Team info structure
    struct TeamInfo: Codable {
        let id: Int
        let name: String
        let logo: String?
    }
}

// MARK: - Standing Stats
struct StandingStats: Codable {
    let played: Int?
    let win: Int?
    let draw: Int?
    let lose: Int?
    let goals: GoalsStats?
    
    // Custom decoding to handle API response format
    enum CodingKeys: String, CodingKey {
        case played, win, draw, lose, goals
    }
    
    // Goals stats structure
    struct GoalsStats: Codable {
        let for_: Int?
        let against: Int?
        
        enum CodingKeys: String, CodingKey {
            case for_ = "for"
            case against
        }
    }
}
