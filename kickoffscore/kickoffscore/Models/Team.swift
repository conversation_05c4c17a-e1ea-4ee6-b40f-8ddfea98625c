import Foundation

// MARK: - Team Response
struct TeamResponse: Codable {
    let response: [TeamData]
}

// MARK: - API Team Response
struct APITeamResponse: Codable {
    let _id: Int
    let apiId: Int
    let team: Team
    let venue: Venue?

    // Convert to TeamData
    func toTeamData() -> TeamData {
        return TeamData(team: team, venue: venue)
    }
}

// MARK: - Team Data
struct TeamData: Codable, Identifiable, Hashable {
    let team: Team
    let venue: Venue?

    // Computed property for Identifiable conformance
    var id: Int { team.id }

    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(team.id)
    }

    static func == (lhs: TeamData, rhs: TeamData) -> Bool {
        lhs.team.id == rhs.team.id
    }
}

// MARK: - Team
struct Team: Codable, Identifiable, Hashable {
    let id: Int
    let name: String
    let code: String?
    let country: String?
    let founded: Int?
    let national: Bool
    let logo: String?

    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: Team, rhs: Team) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - Venue
struct Venue: Codable, Hashable {
    let id: Int?
    let name: String?
    let address: String?
    let city: String?
    let country: String?
    let capacity: Int?
    let surface: String?
    let image: String?
}
