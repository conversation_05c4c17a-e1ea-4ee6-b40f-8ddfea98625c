import Foundation

// MARK: - Transfer Models

// Player information in transfer
struct TransferPlayer: Codable, Identifiable, Hashable {
    let id: Int
    let name: String
    
    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: TransferPlayer, rhs: TransferPlayer) -> Bool {
        lhs.id == rhs.id
    }
}

// Team information in transfer
struct TransferTeam: Codable, Identifiable, Hashable {
    let id: Int
    let name: String
    let logo: String?
    
    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: TransferTeam, rhs: TransferTeam) -> Bool {
        lhs.id == rhs.id
    }
}

// Transfer information
struct TransferInfo: Identifiable, Hashable {
    let date: String
    let type: String? // e.g., "Permanent", "Loan"
    let teams: TransferTeams

    // We need the player ID to create a truly unique identifier
    // This will be set by the parent Transfer when creating TransferInfo instances
    var playerId: Int = 0

    // Computed property for Identifiable conformance
    var id: String {
        let typeString = type?.replacingOccurrences(of: " ", with: "_") ?? "unknown"
        return "\(date)-\(playerId)-\(teams.in.id)-\(teams.out.id)-\(typeString)"
    }

    // Custom Codable implementation to exclude playerId from encoding/decoding
    private enum CodingKeys: String, CodingKey {
        case date, type, teams
    }
}

// MARK: - TransferInfo Codable Conformance
extension TransferInfo: Codable {
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        date = try container.decode(String.self, forKey: .date)
        type = try container.decodeIfPresent(String.self, forKey: .type)
        teams = try container.decode(TransferTeams.self, forKey: .teams)
        playerId = 0 // Will be set by parent Transfer
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(date, forKey: .date)
        try container.encodeIfPresent(type, forKey: .type)
        try container.encode(teams, forKey: .teams)
        // playerId is not encoded as it's not part of the API response
    }
}

// MARK: - TransferInfo Hashable Conformance
extension TransferInfo {
    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(date)
        hasher.combine(playerId)
        hasher.combine(teams.in.id)
        hasher.combine(teams.out.id)
        hasher.combine(type)
    }

    static func == (lhs: TransferInfo, rhs: TransferInfo) -> Bool {
        lhs.date == rhs.date && lhs.playerId == rhs.playerId && lhs.teams.in.id == rhs.teams.in.id && lhs.teams.out.id == rhs.teams.out.id && lhs.type == rhs.type
    }
}

// Teams involved in transfer
struct TransferTeams: Codable, Hashable {
    let `in`: TransferTeam  // Team player joined
    let out: TransferTeam   // Team player left
    
    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(`in`.id)
        hasher.combine(out.id)
    }
    
    static func == (lhs: TransferTeams, rhs: TransferTeams) -> Bool {
        lhs.in.id == rhs.in.id && lhs.out.id == rhs.out.id
    }
}

// Main Transfer model matching backend API response
struct Transfer: Codable, Identifiable, Hashable {
    let player: TransferPlayer
    let update: String // Date when the transfer was last updated
    private let _transfers: [TransferInfo]

    // Computed property for Identifiable conformance
    var id: String { "\(player.id)-\(update)" }

    // Computed property that returns transfers with playerId set
    var transfers: [TransferInfo] {
        return _transfers.map { transferInfo in
            var updatedTransferInfo = transferInfo
            updatedTransferInfo.playerId = player.id
            return updatedTransferInfo
        }
    }

    // Custom initializer for creating Transfer instances
    init(player: TransferPlayer, update: String, _transfers: [TransferInfo]) {
        self.player = player
        self.update = update
        self._transfers = _transfers
    }

    // Custom coding keys to map the private property
    private enum CodingKeys: String, CodingKey {
        case player, update
        case _transfers = "transfers"
    }
    
    // Computed property to get the most recent transfer
    var mostRecentTransfer: TransferInfo? {
        transfers.max { first, second in
            guard let firstDate = ISO8601DateFormatter().date(from: first.date),
                  let secondDate = ISO8601DateFormatter().date(from: second.date) else {
                return false
            }
            return firstDate < secondDate
        }
    }
    
    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(player.id)
        hasher.combine(update)
    }

    static func == (lhs: Transfer, rhs: Transfer) -> Bool {
        lhs.player.id == rhs.player.id && lhs.update == rhs.update
    }
}

// MARK: - Transfer Extensions

extension TransferInfo {
    // Helper to get formatted date
    var formattedDate: Date? {
        // Try ISO8601 format first (with time)
        let iso8601Formatter = ISO8601DateFormatter()
        if let date = iso8601Formatter.date(from: date) {
            return date
        }

        // Try simple date format (YYYY-MM-DD) which is what the API returns
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        dateFormatter.timeZone = TimeZone.current
        return dateFormatter.date(from: date)
    }

    // Helper to get time ago string or formatted date
    var timeAgo: String {
        guard let transferDate = formattedDate else {
            // If we can't parse the date, just return the raw date string
            return date
        }

        let now = Date()
        let timeInterval = now.timeIntervalSince(transferDate)

        let days = Int(timeInterval / 86400)

        // For transfers within the last 7 days, show relative time
        if days >= 0 && days <= 7 {
            if days == 0 {
                let hours = Int(timeInterval / 3600)
                let minutes = Int(timeInterval / 60)

                if hours > 0 {
                    return hours == 1 ? "1 hr ago" : "\(hours) hr ago"
                } else if minutes > 0 {
                    return minutes == 1 ? "1 min ago" : "\(minutes) min ago"
                } else {
                    return "Just now"
                }
            } else {
                return days == 1 ? "1 day ago" : "\(days) days ago"
            }
        }

        // For older transfers, show formatted date
        let dateFormatter = DateFormatter()
        let calendar = Calendar.current
        let currentYear = calendar.component(.year, from: now)
        let transferYear = calendar.component(.year, from: transferDate)

        if transferYear == currentYear {
            // Same year: show "07 July" format
            dateFormatter.dateFormat = "dd MMMM"
        } else {
            // Different year: show "04 Feb 2024" format
            dateFormatter.dateFormat = "dd MMM yyyy"
        }

        return dateFormatter.string(from: transferDate)
    }
    
    // Helper to determine if it's a loan
    var isLoan: Bool {
        return type?.lowercased().contains("loan") == true
    }
    
    // Helper to get transfer type display text
    var displayType: String {
        if isLoan {
            return "LOAN"
        } else {
            return type ?? "TRANSFER"
        }
    }
}

// MARK: - Mock Data for Previews

extension Transfer {
    static let mock: Transfer = {
        let transferInfo = TransferInfo(
            date: "2024-01-15",
            type: "Loan",
            teams: TransferTeams(
                in: TransferTeam(id: 42, name: "Aberdeen", logo: "https://media.api-sports.io/football/teams/42.png"),
                out: TransferTeam(id: 41, name: "Sunderland", logo: "https://media.api-sports.io/football/teams/41.png")
            )
        )

        return Transfer(
            player: TransferPlayer(id: 1, name: "Adil Aouchiche"),
            update: "2024-01-15",
            _transfers: [transferInfo]
        )
    }()

    static let mockPermanent: Transfer = {
        let transferInfo = TransferInfo(
            date: "2024-01-10",
            type: "Permanent",
            teams: TransferTeams(
                in: TransferTeam(id: 42, name: "Arsenal", logo: "https://media.api-sports.io/football/teams/42.png"),
                out: TransferTeam(id: 548, name: "Real Sociedad", logo: "https://media.api-sports.io/football/teams/548.png")
            )
        )

        return Transfer(
            player: TransferPlayer(id: 2, name: "Martin Zubimendi"),
            update: "2024-01-10",
            _transfers: [transferInfo]
        )
    }()
}
