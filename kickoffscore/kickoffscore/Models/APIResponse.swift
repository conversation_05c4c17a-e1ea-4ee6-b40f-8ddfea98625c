import Foundation

// Generic structure for common API responses
struct APIResponse<T: Decodable>: Decodable {
    let get: String
    let parameters: [String: String]? // Parameters might not always be present or could be different types
    // let errors: [String: String]? // Or some error object structure
    let results: Int
    let paging: PagingInfo?
    let response: T // The actual data (e.g., [Fixture], Team, etc.)
}

public struct PagingInfo: Decodable {
    public let current: Int
    public let total: Int
}

// MARK: - Specific Response Structs

// Specific response structure for the fixtures endpoint with pagination.
public struct FixturesApiResponse: Decodable {
    // These properties will be set during initialization
    public private(set) var data: [Fixture] // The actual array of fixtures
    public private(set) var pagination: PaginationInfo

    // For backward compatibility with existing code
    public var response: [Fixture] {
        return data
    }

    public var paging: PagingInfo? {
        return PagingInfo(current: pagination.page, total: pagination.totalPages)
    }

    // Custom initializer to handle both response formats
    public init(from decoder: Decoder) throws {
        // First, try to decode as a paginated response
        do {
            let container = try decoder.container(keyedBy: CodingKeys.self)
            self.data = try container.decode([Fixture].self, forKey: .data)
            self.pagination = try container.decode(PaginationInfo.self, forKey: .pagination)
            return
        } catch {
            // If that fails, try to decode as a simple array
            do {
                let fixtures = try [Fixture].self.init(from: decoder)
                self.data = fixtures
                // Create default pagination info
                self.pagination = PaginationInfo(
                    page: 1,
                    pageSize: fixtures.count,
                    totalPages: 1,
                    totalCount: fixtures.count,
                    hasNextPage: false,
                    hasPrevPage: false
                )
                return
            } catch _ {
                // If both attempts fail, throw the original error
                throw error
            }
        }
    }

    private enum CodingKeys: String, CodingKey {
        case data
        case pagination
    }
}

// Pagination information structure from our backend
public struct PaginationInfo: Decodable {
    public let page: Int
    public let pageSize: Int
    public let totalPages: Int
    public let totalCount: Int
    public let hasNextPage: Bool
    public let hasPrevPage: Bool
}

// You might add other specific response types here later, e.g.:
// struct TeamResponse: Decodable { ... }
// struct LeagueResponse: Decodable { ... }
