import Foundation

// MARK: - Odds Response
// The API returns an array of odds documents directly from MongoDB
typealias OddsResponse = [OddsDocument]

// MARK: - Odds Document
struct OddsDocument: Codable {
    // Use AnyCodable to handle both String and numeric _id values
    private let __id: AnyCodable
    let apiId: String?  // Made optional since it might be missing in the API response
    let fixtureId: Int
    let leagueId: Int
    let update: String
    let bookmakers: [BookmakerData]
    let lastUpdated: String

    // Public computed property to access _id as a string
    var _id: String {
        return __id.stringValue ?? String(describing: __id.value)
    }

    // Coding keys for JSON parsing
    enum CodingKeys: String, CodingKey {
        case __id = "_id"
        case apiId, fixtureId, leagueId, update, bookmakers, lastUpdated
    }
}

// MARK: - Live Odds Document
struct LiveOddsDocument: Codable {
    // Use AnyCodable to handle both String and numeric _id values
    private let __id: AnyCodable
    let apiId: String?  // Made optional since it might be missing in the API response
    let fixtureId: Int
    let leagueId: Int
    let update: String
    let bookmakers: [BookmakerData]
    let lastUpdated: String

    // Public computed property to access _id as a string
    var _id: String {
        return __id.stringValue ?? String(describing: __id.value)
    }

    // Coding keys for JSON parsing
    enum CodingKeys: String, CodingKey {
        case __id = "_id"
        case apiId, fixtureId, leagueId, update, bookmakers, lastUpdated
    }
}

// MARK: - Bookmaker Data (for pre-match odds)
struct BookmakerData: Codable, Identifiable {
    let id: Int
    let name: String
    let bets: [BetData]

    // Computed property for Identifiable conformance
    var uniqueId: String { "\(id)" }
}

// MARK: - Bet Data
struct BetData: Codable, Identifiable {
    let id: Int
    let name: String
    let values: [BetValue]

    // Computed property for Identifiable conformance
    var uniqueId: String { "\(id)" }
}

// MARK: - Bet Value
struct BetValue: Codable, Identifiable {
    // Use a private property to handle both String and numeric values
    private let _value: AnyCodable
    private let _odd: AnyCodable
    private let _handicap: AnyCodable?

    // Public computed properties to access the values as strings
    var value: String {
        return _value.stringValue ?? "Unknown"
    }

    var odd: String {
        return _odd.stringValue ?? "0.0"
    }

    var handicap: String? {
        return _handicap?.stringValue
    }

    // Coding keys for JSON parsing
    enum CodingKeys: String, CodingKey {
        case _value = "value"
        case _odd = "odd"
        case _handicap = "handicap"
    }

    // Computed property for Identifiable conformance
    var id: String { value + (handicap ?? "") }

    // Convert odd string to Double for calculations
    var oddValue: Double {
        return Double(odd) ?? 0.0
    }

    // Format odd for display
    var formattedOdd: String {
        if let oddDouble = Double(odd) {
            return String(format: "%.2f", oddDouble)
        }
        return odd
    }

    // Custom initializer for creating mock data
    init(value: String, odd: String, handicap: String?) {
        self._value = AnyCodable(value: value)
        self._odd = AnyCodable(value: odd)
        self._handicap = handicap != nil ? AnyCodable(value: handicap!) : nil
    }
}

// Helper struct to handle any JSON value type
struct AnyCodable: Codable {
    let value: Any

    var stringValue: String? {
        if let string = value as? String {
            return string
        } else if let number = value as? NSNumber {
            return number.stringValue
        } else if let bool = value as? Bool {
            return bool ? "true" : "false"
        }
        return nil
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        if let string = try? container.decode(String.self) {
            value = string
        } else if let int = try? container.decode(Int.self) {
            value = int
        } else if let double = try? container.decode(Double.self) {
            value = double
        } else if let bool = try? container.decode(Bool.self) {
            value = bool
        } else if let array = try? container.decode([AnyCodable].self) {
            value = array.map { $0.value }
        } else if let dictionary = try? container.decode([String: AnyCodable].self) {
            value = dictionary.mapValues { $0.value }
        } else if container.decodeNil() {
            value = NSNull()
        } else {
            throw DecodingError.dataCorruptedError(in: container, debugDescription: "AnyCodable cannot decode value")
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()

        switch value {
        case let string as String:
            try container.encode(string)
        case let int as Int:
            try container.encode(int)
        case let double as Double:
            try container.encode(double)
        case let bool as Bool:
            try container.encode(bool)
        case let array as [Any]:
            try container.encode(array.map { AnyCodable(value: $0) })
        case let dictionary as [String: Any]:
            try container.encode(dictionary.mapValues { AnyCodable(value: $0) })
        case is NSNull:
            try container.encodeNil()
        default:
            throw EncodingError.invalidValue(value, EncodingError.Context(
                codingPath: container.codingPath,
                debugDescription: "AnyCodable cannot encode value"
            ))
        }
    }

    init(value: Any) {
        self.value = value
    }
}

// MARK: - Live Odds Data
struct LiveOddsData: Codable, Identifiable {
    let id: Int
    let name: String
    let bookmakerName: String
    let bookmakerLogo: String
    let values: [LiveBetValue]

    // Computed property for Identifiable conformance
    var uniqueId: String { "\(id)" }
}

// MARK: - Live Bet Value
struct LiveBetValue: Codable, Identifiable {
    // Use a private property to handle both String and numeric values
    private let _value: AnyCodable
    private let _odd: AnyCodable
    private let _handicap: AnyCodable?
    private let _main: AnyCodable?
    private let _suspended: AnyCodable?

    // Public computed properties to access the values as strings
    var value: String {
        return _value.stringValue ?? "Unknown"
    }

    var odd: String {
        return _odd.stringValue ?? "0.0"
    }

    var handicap: String? {
        return _handicap?.stringValue
    }

    var main: Bool? {
        if let mainString = _main?.stringValue, mainString.lowercased() == "true" {
            return true
        } else if let mainString = _main?.stringValue, mainString.lowercased() == "false" {
            return false
        }
        return _main?.value as? Bool
    }

    var suspended: Bool? {
        if let suspendedString = _suspended?.stringValue, suspendedString.lowercased() == "true" {
            return true
        } else if let suspendedString = _suspended?.stringValue, suspendedString.lowercased() == "false" {
            return false
        }
        return _suspended?.value as? Bool
    }

    // Coding keys for JSON parsing
    enum CodingKeys: String, CodingKey {
        case _value = "value"
        case _odd = "odd"
        case _handicap = "handicap"
        case _main = "main"
        case _suspended = "suspended"
    }

    // Computed property for Identifiable conformance
    var id: String { value + (handicap ?? "") }

    // Convert odd string to Double for calculations
    var oddValue: Double {
        return Double(odd) ?? 0.0
    }

    // Format odd for display
    var formattedOdd: String {
        if let oddDouble = Double(odd) {
            return String(format: "%.2f", oddDouble)
        }
        return odd
    }

    // Custom initializer for creating mock data
    init(value: String, odd: String, handicap: String?, main: Bool?, suspended: Bool?) {
        self._value = AnyCodable(value: value)
        self._odd = AnyCodable(value: odd)
        self._handicap = handicap != nil ? AnyCodable(value: handicap!) : nil
        self._main = main != nil ? AnyCodable(value: main!) : nil
        self._suspended = suspended != nil ? AnyCodable(value: suspended!) : nil
    }
}

// MARK: - Odds Model for UI
struct Odds: Identifiable {
    let id: Int // Fixture ID
    let update: Date
    let isLive: Bool
    let preMatchOdds: [BookmakerData]
    let liveOdds: [LiveOddsData]

    // Helper to get the targeted bet types
    var popularBetTypes: [Int] {
        // Targeted bet types by ID (using API-Football's actual IDs)
        return [
            // Match Outcome
            1, 2, 12, 4, 9,
            // Goals
            5, 8, 10, 14, 16, 17,
            // Half-Time
            13, 6, 7,
            // Combinations
            29, 30, 36, 24, 25,
            // Player Props
            92, 93, 102,
            // Events
            45, 80,
            // Tournament
            61
        ]
    }

    // Helper to get the most popular bet types for quick access
    var mostPopularBetTypes: [Int] {
        // Return the most commonly used bet types
        return [1, 5, 8, 10, 4, 13]
    }

    // Get pre-match odds for a specific bet type
    func getPreMatchOdds(forBetType betId: Int) -> [BetData] {
        let allBets = preMatchOdds.flatMap { bookmaker in
            bookmaker.bets.filter { $0.id == betId }
        }
        return allBets
    }

    // Get live odds for a specific bet type
    func getLiveOdds(forBetType betId: Int) -> [LiveOddsData] {
        return liveOdds.filter { $0.id == betId }
    }
}

// MARK: - Mock Data
extension Odds {
    static var mock: Odds {
        // Create mock pre-match odds
        let mockPreMatchOdds: [BookmakerData] = [
            BookmakerData(
                id: 1,
                name: "Bet365",
                bets: [
                    BetData(
                        id: 1,
                        name: "Match Winner",
                        values: [
                            BetValue(value: "Home", odd: "2.10", handicap: nil),
                            BetValue(value: "Draw", odd: "3.40", handicap: nil),
                            BetValue(value: "Away", odd: "3.75", handicap: nil)
                        ]
                    ),
                    BetData(
                        id: 2,
                        name: "Over/Under",
                        values: [
                            BetValue(value: "Over", odd: "1.90", handicap: "2.5"),
                            BetValue(value: "Under", odd: "1.90", handicap: "2.5")
                        ]
                    )
                ]
            ),
            BookmakerData(
                id: 2,
                name: "Betfair",
                bets: [
                    BetData(
                        id: 1,
                        name: "Match Winner",
                        values: [
                            BetValue(value: "Home", odd: "2.15", handicap: nil),
                            BetValue(value: "Draw", odd: "3.30", handicap: nil),
                            BetValue(value: "Away", odd: "3.80", handicap: nil)
                        ]
                    )
                ]
            )
        ]

        // Create mock live odds
        let mockLiveOdds: [LiveOddsData] = [
            LiveOddsData(
                id: 1,
                name: "Match Winner",
                bookmakerName: "Bet365",
                bookmakerLogo: "",
                values: [
                    LiveBetValue(value: "Home", odd: "1.80", handicap: nil, main: true, suspended: false),
                    LiveBetValue(value: "Draw", odd: "3.60", handicap: nil, main: true, suspended: false),
                    LiveBetValue(value: "Away", odd: "4.20", handicap: nil, main: true, suspended: false)
                ]
            ),
            LiveOddsData(
                id: 2,
                name: "Over/Under",
                bookmakerName: "Bet365",
                bookmakerLogo: "",
                values: [
                    LiveBetValue(value: "Over", odd: "1.75", handicap: "2.5", main: true, suspended: false),
                    LiveBetValue(value: "Under", odd: "2.05", handicap: "2.5", main: true, suspended: false)
                ]
            )
        ]

        return Odds(
            id: 12345,
            update: Date(),
            isLive: false,
            preMatchOdds: mockPreMatchOdds,
            liveOdds: mockLiveOdds
        )
    }

    static var mockLive: Odds {
        // Create mock live odds
        let mockLiveOdds: [LiveOddsData] = [
            LiveOddsData(
                id: 1,
                name: "Match Winner",
                bookmakerName: "Unibet",
                bookmakerLogo: "",
                values: [
                    LiveBetValue(value: "Home", odd: "1.50", handicap: nil, main: true, suspended: false),
                    LiveBetValue(value: "Draw", odd: "3.80", handicap: nil, main: true, suspended: false),
                    LiveBetValue(value: "Away", odd: "5.20", handicap: nil, main: true, suspended: false)
                ]
            ),
            LiveOddsData(
                id: 2,
                name: "Next Goal",
                bookmakerName: "Unibet",
                bookmakerLogo: "",
                values: [
                    LiveBetValue(value: "Home", odd: "1.65", handicap: nil, main: true, suspended: false),
                    LiveBetValue(value: "No Goal", odd: "3.20", handicap: nil, main: true, suspended: false),
                    LiveBetValue(value: "Away", odd: "4.50", handicap: nil, main: true, suspended: false)
                ]
            )
        ]

        return Odds(
            id: 12345,
            update: Date(),
            isLive: true,
            preMatchOdds: [],
            liveOdds: mockLiveOdds
        )
    }
}
