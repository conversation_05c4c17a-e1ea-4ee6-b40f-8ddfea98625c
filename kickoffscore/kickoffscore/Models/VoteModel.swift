import Foundation

// MARK: - Vote Option Enum
public enum VoteOption: String, Codable {
    case homeWin = "home"
    case draw = "draw"
    case awayWin = "away"
    
    var displayName: String {
        switch self {
        case .homeWin: return "Home Win"
        case .draw: return "Draw"
        case .awayWin: return "Away Win"
        }
    }
}

// MARK: - Vote Statistics
public struct VoteStatistics: Codable {
    public let homeVotes: Int
    public let drawVotes: Int
    public let awayVotes: Int
    public let totalVotes: Int
    
    // Calculate percentages
    public var homePercentage: Double {
        return totalVotes > 0 ? Double(homeVotes) / Double(totalVotes) * 100 : 0
    }
    
    public var drawPercentage: Double {
        return totalVotes > 0 ? Double(drawVotes) / Double(totalVotes) * 100 : 0
    }
    
    public var awayPercentage: Double {
        return totalVotes > 0 ? Double(awayVotes) / Double(totalVotes) * 100 : 0
    }
    
    // Get the leading vote option
    public var leadingVote: VoteOption? {
        if homeVotes == 0 && drawVotes == 0 && awayVotes == 0 {
            return nil
        }
        
        if homeVotes >= drawVotes && homeVotes >= awayVotes {
            return .homeWin
        } else if drawVotes >= homeVotes && drawVotes >= awayVotes {
            return .draw
        } else {
            return .awayWin
        }
    }
}

// MARK: - User Vote
public struct UserVote: Codable {
    public let vote: VoteOption
}

// MARK: - Vote Response
public struct VoteResponse: Codable {
    public let message: String
    public let stats: VoteStatistics
}

// MARK: - Vote Request
public struct VoteRequest: Codable {
    public let vote: VoteOption
}
