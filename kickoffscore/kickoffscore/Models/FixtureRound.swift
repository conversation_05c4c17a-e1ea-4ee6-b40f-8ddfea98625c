import Foundation

// MARK: - Fixture Day Model

/// Represents fixtures grouped by a specific day
struct FixtureDay: Identifiable, Hashable {
    let id: String
    let date: Date
    let fixtures: [Fixture]

    init(date: Date, fixtures: [Fixture]) {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        self.id = formatter.string(from: date)
        self.date = date
        self.fixtures = fixtures.sorted { fixture1, fixture2 in
            guard let timestamp1 = fixture1.timestamp,
                  let timestamp2 = fixture2.timestamp else {
                return false
            }
            return timestamp1 < timestamp2
        }
    }

    /// Get the day name (e.g., "Friday", "Saturday")
    var dayName: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        return formatter.string(from: date)
    }

    /// Get the formatted date (e.g., "15 August")
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d MMMM"
        return formatter.string(from: date)
    }
}

// MARK: - Fixture Round Models

/// Represents a round with its associated fixtures
struct FixtureRound: Identifiable, Hashable {
    let id: String
    let name: String
    let fixtures: [Fixture]
    
    init(name: String, fixtures: [Fixture]) {
        self.id = name
        self.name = name
        self.fixtures = fixtures
    }
    
    /// Check if this round has any live fixtures
    var hasLiveFixtures: Bool {
        fixtures.contains { fixture in
            guard let status = fixture.status.short else { return false }
            return ["LIVE", "1H", "HT", "2H", "ET", "P", "Aw. ET", "SUSP", "INT"].contains(status)
        }
    }

    /// Check if this round has any upcoming fixtures
    var hasUpcomingFixtures: Bool {
        fixtures.contains { fixture in
            guard let status = fixture.status.short else { return false }
            return ["NS", "TBD", "PST"].contains(status)
        }
    }

    /// Check if this round has any finished fixtures
    var hasFinishedFixtures: Bool {
        fixtures.contains { fixture in
            guard let status = fixture.status.short else { return false }
            return ["FT", "AET", "PEN", "AWD", "WO", "ABD", "CANC"].contains(status)
        }
    }
    
    /// Get fixtures sorted by date
    var sortedFixtures: [Fixture] {
        fixtures.sorted { fixture1, fixture2 in
            guard let timestamp1 = fixture1.timestamp,
                  let timestamp2 = fixture2.timestamp else {
                return false
            }
            return timestamp1 < timestamp2
        }
    }

    /// Get fixtures grouped by day
    var fixturesByDay: [FixtureDay] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: sortedFixtures) { fixture in
            let date = fixture.kickoffDate
            return calendar.startOfDay(for: date)
        }

        return grouped.map { dayStart, fixtures in
            FixtureDay(date: dayStart, fixtures: fixtures)
        }.sorted { $0.date < $1.date }
    }
}

/// Represents the organization of fixtures by rounds for a league
struct LeagueFixturesByRounds: Identifiable {
    let id: Int
    let leagueId: Int
    let season: Int
    let rounds: [FixtureRound]
    
    init(leagueId: Int, season: Int, rounds: [FixtureRound]) {
        self.id = leagueId
        self.leagueId = leagueId
        self.season = season
        self.rounds = rounds
    }
    
    /// Get all fixtures across all rounds
    var allFixtures: [Fixture] {
        rounds.flatMap { $0.fixtures }
    }
    
    /// Get rounds that have finished fixtures
    var finishedRounds: [FixtureRound] {
        rounds.filter { $0.hasFinishedFixtures }
    }
    
    /// Get rounds that have upcoming fixtures
    var upcomingRounds: [FixtureRound] {
        rounds.filter { $0.hasUpcomingFixtures }
    }
    
    /// Get rounds that have live fixtures
    var liveRounds: [FixtureRound] {
        rounds.filter { $0.hasLiveFixtures }
    }
    
    /// Get rounds sorted by their order (assuming round names contain numbers or follow a pattern)
    var sortedRounds: [FixtureRound] {
        rounds.sorted { round1, round2 in
            // Try to extract numbers from round names for proper sorting
            let number1 = extractRoundNumber(from: round1.name)
            let number2 = extractRoundNumber(from: round2.name)
            
            if let num1 = number1, let num2 = number2 {
                return num1 < num2
            }
            
            // Fallback to alphabetical sorting
            return round1.name < round2.name
        }
    }
    
    /// Extract round number from round name (e.g., "Regular Season - 1" -> 1)
    private func extractRoundNumber(from roundName: String) -> Int? {
        let pattern = #"(\d+)"#
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: roundName.utf16.count)
        
        if let match = regex?.firstMatch(in: roundName, range: range) {
            let numberRange = Range(match.range(at: 1), in: roundName)
            if let numberRange = numberRange {
                return Int(String(roundName[numberRange]))
            }
        }
        
        return nil
    }
}

/// Helper extension for organizing fixtures by rounds
extension Array where Element == Fixture {
    /// Group fixtures by their round names
    func groupedByRounds() -> [FixtureRound] {
        let grouped = Dictionary(grouping: self) { fixture in
            fixture.league.round ?? "Unknown Round"
        }
        
        return grouped.map { roundName, fixtures in
            FixtureRound(name: roundName, fixtures: fixtures)
        }
    }
}
