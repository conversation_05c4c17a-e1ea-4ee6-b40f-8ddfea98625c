import Foundation

// Represents a single football fixture
public struct Fixture: Codable, Identifiable, Hashable {
    public let id: Int // Use the fixture ID from the API as the identifiable ID
    public let referee: String?
    public let timezone: String?
    public let date: String // Consider decoding this into a Date object later
    public let timestamp: Int? // Unix timestamp - made optional to handle missing field

    public struct Venue: Codable, Identifiable, Hashable {
        public let id: Int?
        public let name: String?
        public let city: String?
    }
    public let venue: Venue?

    public struct Status: Codable, Hashable {
        public let long: String?
        public let short: String?
        public let elapsed: Int?
        public let extra: Int?
    }
    public let status: Status

    public struct LeagueInfo: Codable, Hashable {
        public let id: Int
        public let name: String?
        public let country: String?
        public let logo: String? // URL string
        public let flag: String? // URL string
        public let season: Int?
        public let round: String?
        public let coverage: Coverage?

        // Coverage information for the league
        public struct Coverage: Codable, Hashable {
            public let standings: Bool
            public let players: Bool
            public let topScorers: Bool
            public let topAssists: Bool
            public let topCards: Bool
            public let injuries: Bool
            public let predictions: Bool
            public let odds: Bool

            enum CodingKeys: String, CodingKey {
                case standings, players, predictions, odds, injuries
                case topScorers = "top_scorers"
                case topAssists = "top_assists"
                case topCards = "top_cards"
            }
        }
    }
    public let league: LeagueInfo

    public struct TeamInfo: Codable, Identifiable, Hashable {
        public let id: Int
        public let name: String?
        public let logo: String? // URL string
        public let winner: Bool?
    }

    public struct Teams: Codable, Hashable {
        public let home: TeamInfo?
        public let away: TeamInfo?
    }
    public let teams: Teams

    public struct Goals: Codable, Hashable {
        public let home: Int?
        public let away: Int?
    }
    public let goals: Goals

    public struct Score: Codable, Hashable {
        public struct Halftime: Codable, Hashable {
            public let home: Int?
            public let away: Int?
        }
        public struct Fulltime: Codable, Hashable {
            public let home: Int?
            public let away: Int?
        }
        public struct Extratime: Codable, Hashable {
            public let home: Int?
            public let away: Int?
        }
        public struct Penalty: Codable, Hashable {
            public let home: Int?
            public let away: Int?
        }

        public let halftime: Halftime?
        public let fulltime: Fulltime?
        public let extratime: Extratime?
        public let penalty: Penalty?
    }
    public let score: Score

    public var lineups: [TeamLineup]? // Changed to var
    public var statistics: [TeamStatistics]? // Changed to var to allow updating
    public let playerStatistics: [PlayerStatisticsInfo]? // Added player statistics
    public let events: [EventInfo]? // Added events property
    public let teamRatings: TeamRatings? // Added team ratings

    // Use fixture.fixture.id for Codable conformance if the API nests it
    public enum CodingKeys: String, CodingKey {
        case fixtureData = "fixture"
        case league, teams, goals, score, lineups, statistics, events // Added events
        case playerStatistics = "players" // Map to the 'players' key from API response
        case teamRatings // Map to the 'teamRatings' key from API response
    }

    // Custom Decoder to handle nested 'fixture' key
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // Decode nested fixture data
        let fixtureContainer = try container.nestedContainer(keyedBy: FixtureKeys.self, forKey: .fixtureData)
        id = try fixtureContainer.decode(Int.self, forKey: .id)
        referee = try fixtureContainer.decodeIfPresent(String.self, forKey: .referee)
        timezone = try fixtureContainer.decodeIfPresent(String.self, forKey: .timezone)
        date = try fixtureContainer.decode(String.self, forKey: .date)
        timestamp = try fixtureContainer.decodeIfPresent(Int.self, forKey: .timestamp)
        venue = try fixtureContainer.decodeIfPresent(Venue.self, forKey: .venue)
        status = try fixtureContainer.decode(Status.self, forKey: .status)

        // Decode top-level keys
        league = try container.decode(LeagueInfo.self, forKey: .league)
        teams = try container.decode(Teams.self, forKey: .teams)
        goals = try container.decode(Goals.self, forKey: .goals)
        score = try container.decode(Score.self, forKey: .score)
        lineups = try container.decodeIfPresent([TeamLineup].self, forKey: .lineups)
        statistics = try container.decodeIfPresent([TeamStatistics].self, forKey: .statistics) // Decode statistics
        playerStatistics = try container.decodeIfPresent([PlayerStatisticsInfo].self, forKey: .playerStatistics) // Decode player stats
        events = try container.decodeIfPresent([EventInfo].self, forKey: .events) // Decode events
        teamRatings = try container.decodeIfPresent(TeamRatings.self, forKey: .teamRatings) // Decode team ratings
    }

    // Custom Encoder to handle nested 'fixture' key
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        // Encode top-level keys
        try container.encode(league, forKey: .league)
        try container.encode(teams, forKey: .teams)
        try container.encode(goals, forKey: .goals)
        try container.encode(score, forKey: .score)
        try container.encodeIfPresent(lineups, forKey: .lineups)
        try container.encodeIfPresent(statistics, forKey: .statistics) // Encode statistics
        try container.encodeIfPresent(playerStatistics, forKey: .playerStatistics) // Encode player stats
        try container.encodeIfPresent(events, forKey: .events) // Encode events
        try container.encodeIfPresent(teamRatings, forKey: .teamRatings) // Encode team ratings

        // Encode nested fixture data
        var fixtureContainer = container.nestedContainer(keyedBy: FixtureKeys.self, forKey: .fixtureData)
        try fixtureContainer.encode(id, forKey: .id)
        try fixtureContainer.encodeIfPresent(referee, forKey: .referee)
        try fixtureContainer.encodeIfPresent(timezone, forKey: .timezone)
        try fixtureContainer.encode(date, forKey: .date)
        try fixtureContainer.encodeIfPresent(timestamp, forKey: .timestamp)
        try fixtureContainer.encodeIfPresent(venue, forKey: .venue)
        try fixtureContainer.encode(status, forKey: .status)
    }

    // Define keys inside the nested 'fixture' object
    public enum FixtureKeys: String, CodingKey {
        case id, referee, timezone, date, timestamp, venue, status
    }

    // Add convenience computed properties if needed
    public var kickoffDate: Date {
        if let timestamp = timestamp {
            return Date(timeIntervalSince1970: TimeInterval(timestamp))
        } else {
            // If timestamp is nil, try to parse the date string
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
            dateFormatter.locale = Locale(identifier: "en_US_POSIX")
            dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)

            if let parsedDate = dateFormatter.date(from: date) {
                return parsedDate
            } else {
                // Return current date as fallback
                return Date()
            }
        }
    }

    public var homeTeam: TeamInfo? { teams.home }
    public var awayTeam: TeamInfo? { teams.away }

    public var homeScore: Int? { goals.home }
    public var awayScore: Int? { goals.away }

    // Implement Hashable conformance - simplified for better navigation stability
    public func hash(into hasher: inout Hasher) {
        // Use only the fixture ID for hashing to ensure stability
        // This prevents navigation issues when fixture data updates
        hasher.combine(id)
    }

    public static func == (lhs: Fixture, rhs: Fixture) -> Bool {
        // For navigation purposes, fixtures are equal if they have the same ID
        // This prevents NavigationPath issues when fixture data updates
        return lhs.id == rhs.id
    }

    // Public Memberwise Initializer
    public init(id: Int, referee: String?, timezone: String?, date: String, timestamp: Int?, venue: Venue?, status: Status, league: LeagueInfo, teams: Teams, goals: Goals, score: Score, lineups: [TeamLineup]? = nil, statistics: [TeamStatistics]? = nil, playerStatistics: [PlayerStatisticsInfo]? = nil, events: [EventInfo]? = nil, teamRatings: TeamRatings? = nil) {
        self.id = id
        self.referee = referee
        self.timezone = timezone
        self.date = date
        self.timestamp = timestamp
        self.venue = venue
        self.status = status
        self.league = league
        self.teams = teams
        self.goals = goals
        self.score = score
        self.lineups = lineups // Allow initializing lineups
        self.statistics = statistics // Allow initializing statistics
        self.playerStatistics = playerStatistics // Allow initializing player stats
        self.events = events // Allow initializing events
        self.teamRatings = teamRatings // Allow initializing team ratings
    }
 }

 // MARK: - Statistics Specific Structures

// Represents statistics for one team in a fixture
public struct TeamStatistics: Codable, Identifiable {
    public var id: Int { team.id }
    public let team: Fixture.TeamInfo // Reuse TeamInfo
    public let statistics: [StatisticItem]? // Full match statistics
    public let statistics_1h: [StatisticItem]? // First half statistics
    public let statistics_2h: [StatisticItem]? // Second half statistics

    enum CodingKeys: String, CodingKey {
        case team
        case statistics
        case statistics_1h
        case statistics_2h
    }
}

// Represents a single statistic item
public struct StatisticItem: Codable {
    public let type: String // e.g., "Shots on Goal", "Ball Possession"
    public let value: String? // Using String? for flexibility (handles null, percentages like "55%")

    // Need a designated initializer for direct creation (e.g., mock data)
    // now that we have a custom Codable init(from:)
    init(type: String, value: String?) {
        self.type = type
        self.value = value
    }

    // Custom decoding to handle String, Int, or Double for 'value'
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        type = try container.decode(String.self, forKey: .type)

        // Try decoding 'value' as different types
        if let stringValue = try? container.decode(String.self, forKey: .value) {
            value = stringValue
        } else if let intValue = try? container.decode(Int.self, forKey: .value) {
            value = String(intValue)
        } else if let doubleValue = try? container.decode(Double.self, forKey: .value) {
            // Format Double to avoid excessive decimal places if needed, e.g., String(format: "%.1f", doubleValue)
            // For now, simple string conversion
            value = String(doubleValue)
        } else {
            // Could also try Bool or just default to nil/"-"
            value = nil // Or assign a default like "-"
        }
    }

    // If you need to encode this back, ensure 'value' is encoded as its original type or consistently as String
    // For simplicity, we might only need decoding from the API
    // Add encode(to:) if saving/sending this struct is required
}

 // MARK: - Player Statistics Specific Structures

// Mirrors PlayerStatsInfo from backend
public struct PlayerStatisticsInfo: Codable, Identifiable {
    public var id: Int { team.id } // Use team ID for Identifiable conformance
    public let team: Fixture.TeamInfo // Reuse TeamInfo
    public let players: [PlayerDetail]?
}

// Mirrors the 'players' array element structure
public struct PlayerDetail: Codable, Identifiable {
     public var id: Int { player.id } // Use player ID for Identifiable conformance
     public let player: PlayerStatsPlayerInfo
     public let statistics: [PlayerStatsContainer]? // Array as per backend model
}

// Mirrors PlayerStatsPlayerInfo from backend
public struct PlayerStatsPlayerInfo: Codable, Identifiable {
    public let id: Int
    public let name: String?
    public let photo: String? // URL String
}

// Mirrors the 'statistics' array element structure
public struct PlayerStatsContainer: Codable {
    // This seems to be the actual container for detailed stats based on the PlayerStatsItem interface
    // Assuming the backend sends something like: statistics: [ { games: {...}, shots: {...}, ... } ]
    // We might need to adjust if the API nests it differently, but PlayerStatsItem seems to define the fields directly.
    // Let's map PlayerStatsItem directly here.

    public let games: PlayerGameStats?
    public let offsides: Int?
    public let shots: PlayerShotStats?
    public let goals: PlayerGoalStats?
    public let passes: PlayerPassStats?
    public let tackles: PlayerTackleStats?
    public let duels: PlayerDuelStats?
    public let dribbles: PlayerDribbleStats?
    public let fouls: PlayerFoulStats?
    public let cards: PlayerCardStats?
    public let penalty: PlayerPenaltyStats?

     // If the API *really* sends statistics: [ { statistics: { games: ..., shots: ... } } ]
     // then we'd need another struct here. But the TS interface suggests PlayerStatsItem fields are direct.
     // Let's assume the direct mapping for now.
}


// --- Detailed Stat Breakdown Structs (mirroring PlayerStatsItem fields) ---

public struct PlayerGameStats: Codable {
    public let minutes: Int?
    public let number: Int? // Jersey number
    public let position: String? // e.g., "G", "D", "M", "F"
    public let rating: String? // Often a string like "7.5"
    public let captain: Bool?
    public let substitute: Bool?
}

public struct PlayerShotStats: Codable {
    public let total: Int?
    public let on: Int? // Shots on goal
}

public struct PlayerGoalStats: Codable {
    public let total: Int?
    public let conceded: Int? // For goalkeepers
    public let assists: Int?
    public let saves: Int? // For goalkeepers
}

public struct PlayerPassStats: Codable {
    public let total: Int?
    public let key: Int? // Key passes
    public let accuracy: String? // Often a percentage string like "85%"
}

public struct PlayerTackleStats: Codable {
    public let total: Int?
    public let blocks: Int? // Blocked shots
    public let interceptions: Int?
}

public struct PlayerDuelStats: Codable {
    public let total: Int?
    public let won: Int?
}

public struct PlayerDribbleStats: Codable {
    public let attempts: Int?
    public let success: Int?
    public let past: Int? // Number of times dribbled past (defender) - check API docs if this is correct
}

public struct PlayerFoulStats: Codable {
    public let drawn: Int? // Fouls drawn
    public let committed: Int?
}

public struct PlayerCardStats: Codable {
    public let yellow: Int?
    public let red: Int?
}

public struct PlayerPenaltyStats: Codable {
    public let won: Int?
    public let committed: Int?
    public let scored: Int?
    public let missed: Int?
    public let saved: Int? // Goalkeeper penalty saves
}


// MARK: - Lineup Specific Structures

public struct TeamLineup: Codable, Identifiable {
    public var id: Int { team.id } // Use team ID for Identifiable conformance
    public let team: TeamInfo // Team info with colors
    public let formation: String?
    public let startXI: [PlayerLineupInfo]?
    public let substitutes: [PlayerLineupInfo]?

    // Team info with colors
    public struct TeamInfo: Codable {
        public let id: Int
        public let name: String?
        public let logo: String?
        public let colors: TeamColors?
    }

    // Team colors structure
    public struct TeamColors: Codable {
        public let player: ColorInfo?
        public let goalkeeper: ColorInfo?

        public struct ColorInfo: Codable {
            public let primary: String?
            public let number: String?
            public let border: String?
        }
    }
}

public struct PlayerLineupInfo: Codable, Identifiable {
    // Create a unique ID that combines player ID, name, and number to avoid duplicates
    // This ensures each player has a unique identifier even if API returns null IDs
    public var id: String {
        let playerId = player.id ?? -1
        let playerName = player.name ?? "Unknown"
        let playerNumber = player.number ?? -1
        return "\(playerId)_\(playerName)_\(playerNumber)"
    }
    public let player: PlayerInfo
}

// Nested PlayerInfo within PlayerLineupInfo for clarity
public extension PlayerLineupInfo {
    struct PlayerInfo: Codable {
        let id: Int? // Make optional to handle potential null from API
        let name: String?
        let number: Int?
        let pos: String? // Position (e.g., 'G', 'D', 'M', 'F')
        let grid: String? // Grid position (e.g., '1:1') - Optional, may need parsing
    }
}

 // Wrapper struct if the API returns an array under a "response" key
public struct FixtureResponse: Codable {
    public let response: [Fixture]
    // Add paging info if present in API response
    // let paging: PagingInfo?
}

 // MARK: - Event Structure

// Represents a single match event (Goal, Card, Substitution, VAR)
public struct EventInfo: Codable, Identifiable {
    public var id = UUID() // Use UUID for Identifiable conformance as API lacks event ID

    public struct Time: Codable {
        public let elapsed: Int
        public let extra: Int?
    }
    public let time: Time

    public struct TeamRef: Codable {
        public let id: Int
        public let name: String?
        public let logo: String?
    }
    public let team: TeamRef

    public struct PlayerRef: Codable {
        public let id: Int?
        public let name: String?
    }
    public let player: PlayerRef
    public let assist: PlayerRef

    public let type: String // e.g., "Goal", "Card", "Subst", "Var"
    public let detail: String // e.g., "Normal Goal", "Yellow Card", "Substitution 1"
    public let comments: String?

    // Adjust CodingKeys if API keys differ (e.g., assist might be assistPlayer)
    enum CodingKeys: String, CodingKey {
        case time, team, player, assist, type, detail, comments
    }

    // Add a public initializer for creating mock data
    public init(time: Time, team: TeamRef, player: PlayerRef, assist: PlayerRef, type: String, detail: String, comments: String?) {
        self.time = time
        self.team = team
        self.player = player
        self.assist = assist
        self.type = type
        self.detail = detail
        self.comments = comments
    }
}

 // MARK: - Team Ratings Structures

// Team ratings calculated from player ratings
public struct TeamRatings: Codable {
    public let homeTeam: TeamRating?
    public let awayTeam: TeamRating?

    public init(homeTeam: TeamRating?, awayTeam: TeamRating?) {
        self.homeTeam = homeTeam
        self.awayTeam = awayTeam
    }
}

// Individual team rating information
public struct TeamRating: Codable, Identifiable {
    public var id: Int { teamId }
    public let teamId: Int
    public let teamName: String
    public let rating: Double
    public let playersCount: Int
    public let playersWithRatings: Int

    public init(teamId: Int, teamName: String, rating: Double, playersCount: Int, playersWithRatings: Int) {
        self.teamId = teamId
        self.teamName = teamName
        self.rating = rating
        self.playersCount = playersCount
        self.playersWithRatings = playersWithRatings
    }

    // Formatted rating for display
    public var formattedRating: String {
        return String(format: "%.2f", rating)
    }

    // Color classification for UI styling
    public var colorClass: String {
        if rating >= 8.0 { return "excellent" }
        if rating >= 7.0 { return "good" }
        if rating >= 6.0 { return "average" }
        if rating >= 5.0 { return "below" }
        return "poor"
    }
}

 // MARK: - Mock Data Extension

public extension Fixture {
    // Mock fixture for a completed match
    static var mock: Fixture {
        // Add mock lineups with team colors
        let mockLineups = [
            TeamLineup(
                team: TeamLineup.TeamInfo(
                    id: 40,
                    name: "Mock Home Team",
                    logo: "https://example.com/mht.png",
                    colors: TeamLineup.TeamColors(
                        player: TeamLineup.TeamColors.ColorInfo(
                            primary: "0000ff", // Blue
                            number: "ffffff", // White
                            border: "0000ff"
                        ),
                        goalkeeper: TeamLineup.TeamColors.ColorInfo(
                            primary: "ffff00", // Yellow
                            number: "000000", // Black
                            border: "ffff00"
                        )
                    )
                ),
                formation: "4-3-3",
                startXI: [
                    PlayerLineupInfo(player: .init(id: 100, name: "H. Goalie", number: 1, pos: "G", grid: "1:1")),
                    PlayerLineupInfo(player: .init(id: 102, name: "H. Def1", number: 2, pos: "D", grid: "2:4")),
                    PlayerLineupInfo(player: .init(id: 103, name: "H. Def2", number: 3, pos: "D", grid: "2:3")),
                    PlayerLineupInfo(player: .init(id: 104, name: "H. Def3", number: 4, pos: "D", grid: "2:2")),
                    PlayerLineupInfo(player: .init(id: 105, name: "H. Def4", number: 5, pos: "D", grid: "2:1")),
                    PlayerLineupInfo(player: .init(id: 106, name: "H. Mid1", number: 6, pos: "M", grid: "3:3")),
                    PlayerLineupInfo(player: .init(id: 107, name: "H. Mid2", number: 8, pos: "M", grid: "3:2")),
                    PlayerLineupInfo(player: .init(id: 108, name: "H. Mid3", number: 10, pos: "M", grid: "3:1")),
                    PlayerLineupInfo(player: .init(id: 109, name: "H. Fwd1", number: 7, pos: "F", grid: "4:3")),
                    PlayerLineupInfo(player: .init(id: 110, name: "H. Fwd2", number: 9, pos: "F", grid: "4:2")),
                    PlayerLineupInfo(player: .init(id: 101, name: "H. Scorer", number: 11, pos: "F", grid: "4:1")),
                ],
                substitutes: [
                    PlayerLineupInfo(player: .init(id: 112, name: "H. Sub1", number: 12, pos: "M", grid: nil)),
                    PlayerLineupInfo(player: .init(id: 113, name: "H. Sub2", number: 14, pos: "D", grid: nil)),
                ]
            ),
            TeamLineup(
                team: TeamLineup.TeamInfo(
                    id: 41,
                    name: "Mock Away Team",
                    logo: "https://example.com/mat.png",
                    colors: TeamLineup.TeamColors(
                        player: TeamLineup.TeamColors.ColorInfo(
                            primary: "ff0000", // Red
                            number: "ffffff", // White
                            border: "ff0000"
                        ),
                        goalkeeper: TeamLineup.TeamColors.ColorInfo(
                            primary: "00ff00", // Green
                            number: "000000", // Black
                            border: "00ff00"
                        )
                    )
                ),
                formation: "4-4-2",
                startXI: [
                    PlayerLineupInfo(player: .init(id: 201, name: "A. Goalie", number: 1, pos: "G", grid: "1:1")),
                    PlayerLineupInfo(player: .init(id: 202, name: "A. Def1", number: 22, pos: "D", grid: "2:4")),
                    PlayerLineupInfo(player: .init(id: 203, name: "A. Def2", number: 23, pos: "D", grid: "2:3")),
                    PlayerLineupInfo(player: .init(id: 204, name: "A. Def3", number: 24, pos: "D", grid: "2:2")),
                    PlayerLineupInfo(player: .init(id: 205, name: "A. Def4", number: 25, pos: "D", grid: "2:1")),
                    PlayerLineupInfo(player: .init(id: 206, name: "A. Mid1", number: 26, pos: "M", grid: "3:4")),
                    PlayerLineupInfo(player: .init(id: 207, name: "A. Mid2", number: 27, pos: "M", grid: "3:3")),
                    PlayerLineupInfo(player: .init(id: 208, name: "A. Mid3", number: 28, pos: "M", grid: "3:2")),
                    PlayerLineupInfo(player: .init(id: 209, name: "A. Mid4", number: 29, pos: "M", grid: "3:1")),
                    PlayerLineupInfo(player: .init(id: 210, name: "A. Fwd1", number: 30, pos: "F", grid: "4:2")),
                    PlayerLineupInfo(player: .init(id: 211, name: "A. Fwd2", number: 31, pos: "F", grid: "4:1")),
                ],
                substitutes: [
                    PlayerLineupInfo(player: .init(id: 212, name: "A. Sub1", number: 32, pos: "F", grid: nil)),
                    PlayerLineupInfo(player: .init(id: 213, name: "A. Sub2", number: 33, pos: "M", grid: nil)),
                ]
            )
        ]

        // Add mock statistics with halftime and second half data
        let mockStats = [
            TeamStatistics(
                team: TeamInfo(id: 40, name: "Mock Home Team", logo: "https://example.com/mht.png", winner: true),
                statistics: [
                    StatisticItem(type: "Shots on Goal", value: "5"),
                    StatisticItem(type: "Ball Possession", value: "60%"),
                    StatisticItem(type: "Total passes", value: "450"),
                    StatisticItem(type: "Fouls", value: "10"),
                    StatisticItem(type: "Yellow Cards", value: "1"),
                    StatisticItem(type: "Red Cards", value: "0")
                ],
                statistics_1h: [
                    StatisticItem(type: "Shots on Goal", value: "3"),
                    StatisticItem(type: "Ball Possession", value: "55%"),
                    StatisticItem(type: "Total passes", value: "220"),
                    StatisticItem(type: "Fouls", value: "4"),
                    StatisticItem(type: "Yellow Cards", value: "0"),
                    StatisticItem(type: "Red Cards", value: "0")
                ],
                statistics_2h: [
                    StatisticItem(type: "Shots on Goal", value: "2"),
                    StatisticItem(type: "Ball Possession", value: "65%"),
                    StatisticItem(type: "Total passes", value: "230"),
                    StatisticItem(type: "Fouls", value: "6"),
                    StatisticItem(type: "Yellow Cards", value: "1"),
                    StatisticItem(type: "Red Cards", value: "0")
                ]
            ),
            TeamStatistics(
                team: TeamInfo(id: 41, name: "Mock Away Team", logo: "https://example.com/mat.png", winner: false),
                statistics: [
                    StatisticItem(type: "Shots on Goal", value: "3"),
                    StatisticItem(type: "Ball Possession", value: "40%"),
                    StatisticItem(type: "Total passes", value: "300"),
                    StatisticItem(type: "Fouls", value: "12"),
                    StatisticItem(type: "Yellow Cards", value: "2"),
                    StatisticItem(type: "Red Cards", value: "0")
                ],
                statistics_1h: [
                    StatisticItem(type: "Shots on Goal", value: "2"),
                    StatisticItem(type: "Ball Possession", value: "45%"),
                    StatisticItem(type: "Total passes", value: "180"),
                    StatisticItem(type: "Fouls", value: "5"),
                    StatisticItem(type: "Yellow Cards", value: "1"),
                    StatisticItem(type: "Red Cards", value: "0")
                ],
                statistics_2h: [
                    StatisticItem(type: "Shots on Goal", value: "1"),
                    StatisticItem(type: "Ball Possession", value: "35%"),
                    StatisticItem(type: "Total passes", value: "120"),
                    StatisticItem(type: "Fouls", value: "7"),
                    StatisticItem(type: "Yellow Cards", value: "1"),
                    StatisticItem(type: "Red Cards", value: "0")
                ]
            )
        ]

        // Add mock player stats if needed for previewing PlayerStatsView
        let mockPlayerStats: [PlayerStatisticsInfo]? = [
            // Home Team Player Stats
            PlayerStatisticsInfo(
                team: Fixture.TeamInfo(id: 40, name: "Mock Home Team", logo: "https://media.api-sports.io/football/teams/40.png", winner: true),
                players: [
                    PlayerDetail(
                        player: PlayerStatsPlayerInfo(id: 110, name: "H. Fwd2", photo: "https://media.api-sports.io/football/players/101.png"),
                        statistics: [
                            PlayerStatsContainer(
                                games: PlayerGameStats(minutes: 90, number: 9, position: "F", rating: "8.5", captain: false, substitute: false),
                                offsides: 1,
                                shots: PlayerShotStats(total: 3, on: 2),
                                goals: PlayerGoalStats(total: 2, conceded: nil, assists: 0, saves: nil),
                                passes: PlayerPassStats(total: 40, key: 2, accuracy: "85%"),
                                tackles: PlayerTackleStats(total: 1, blocks: 0, interceptions: 0),
                                duels: PlayerDuelStats(total: 10, won: 5),
                                dribbles: PlayerDribbleStats(attempts: 3, success: 2, past: nil),
                                fouls: PlayerFoulStats(drawn: 2, committed: 1),
                                cards: PlayerCardStats(yellow: 0, red: 0),
                                penalty: PlayerPenaltyStats(won: nil, committed: nil, scored: 1, missed: 0, saved: nil)
                            )
                        ]
                    ),
                    // Add H. Scorer with 2 goals (1 normal, 1 penalty)
                    PlayerDetail(
                        player: PlayerStatsPlayerInfo(id: 101, name: "H. Scorer", photo: "https://media.api-sports.io/football/players/101.png"),
                        statistics: [
                            PlayerStatsContainer(
                                games: PlayerGameStats(minutes: 90, number: 11, position: "F", rating: "9.2", captain: false, substitute: false),
                                offsides: 0,
                                shots: PlayerShotStats(total: 4, on: 3),
                                goals: PlayerGoalStats(total: 2, conceded: nil, assists: 0, saves: nil),
                                passes: PlayerPassStats(total: 35, key: 1, accuracy: "80%"),
                                tackles: PlayerTackleStats(total: 0, blocks: 0, interceptions: 0),
                                duels: PlayerDuelStats(total: 8, won: 4),
                                dribbles: PlayerDribbleStats(attempts: 2, success: 1, past: nil),
                                fouls: PlayerFoulStats(drawn: 3, committed: 0),
                                cards: PlayerCardStats(yellow: 0, red: 0),
                                penalty: PlayerPenaltyStats(won: nil, committed: nil, scored: 1, missed: 0, saved: nil)
                            )
                        ]
                    ),
                    // Add H. Fwd1 with 1 penalty goal only
                    PlayerDetail(
                        player: PlayerStatsPlayerInfo(id: 109, name: "H. Fwd1", photo: "https://media.api-sports.io/football/players/109.png"),
                        statistics: [
                            PlayerStatsContainer(
                                games: PlayerGameStats(minutes: 90, number: 7, position: "F", rating: "7.8", captain: false, substitute: false),
                                offsides: 1,
                                shots: PlayerShotStats(total: 2, on: 1),
                                goals: PlayerGoalStats(total: 1, conceded: nil, assists: 0, saves: nil),
                                passes: PlayerPassStats(total: 30, key: 0, accuracy: "75%"),
                                tackles: PlayerTackleStats(total: 0, blocks: 0, interceptions: 0),
                                duels: PlayerDuelStats(total: 5, won: 2),
                                dribbles: PlayerDribbleStats(attempts: 3, success: 1, past: nil),
                                fouls: PlayerFoulStats(drawn: 1, committed: 0),
                                cards: PlayerCardStats(yellow: 0, red: 0),
                                penalty: PlayerPenaltyStats(won: nil, committed: nil, scored: 1, missed: 0, saved: nil)
                            )
                        ]
                    ),
                     PlayerDetail(
                        player: PlayerStatsPlayerInfo(id: 108, name: "H. Mid3", photo: "https://media.api-sports.io/football/players/102.png"),
                        statistics: [
                            PlayerStatsContainer(
                                games: PlayerGameStats(minutes: 90, number: 10, position: "M", rating: "7.8", captain: true, substitute: false),
                                offsides: nil, shots: PlayerShotStats(total: 0, on: 0),
                                goals: PlayerGoalStats(total: 0, conceded: 0, assists: 2, saves: 0),
                                passes: PlayerPassStats(total: 25, key: 0, accuracy: "90%"),
                                tackles: nil, duels: PlayerDuelStats(total: 1, won: 1), dribbles: nil,
                                fouls: PlayerFoulStats(drawn: 0, committed: 1), cards: PlayerCardStats(yellow: 1, red: 0),
                                penalty: PlayerPenaltyStats(won: nil, committed: nil, scored: 0, missed: 0, saved: 0)
                            )
                        ]
                    )
                    // Add more mock players...
                ]
            ),
            // Away Team Player Stats
            PlayerStatisticsInfo(
                team: Fixture.TeamInfo(id: 41, name: "Mock Away Team", logo: "https://media.api-sports.io/football/teams/41.png", winner: false),
                players: [
                    PlayerDetail(
                        player: PlayerStatsPlayerInfo(id: 203, name: "A. Def2", photo: "https://media.api-sports.io/football/players/201.png"),
                        statistics: [
                            PlayerStatsContainer(
                                games: PlayerGameStats(minutes: 85, number: 23, position: "D", rating: "5.2", captain: false, substitute: false),
                                offsides: 0, shots: PlayerShotStats(total: 2, on: 1), goals: PlayerGoalStats(total: 0, conceded: nil, assists: 0, saves: nil),
                                passes: PlayerPassStats(total: 55, key: 1, accuracy: "80%"), tackles: PlayerTackleStats(total: 3, blocks: 1, interceptions: 1),
                                duels: PlayerDuelStats(total: 12, won: 6), dribbles: PlayerDribbleStats(attempts: 5, success: 2, past: 1),
                                fouls: PlayerFoulStats(drawn: 1, committed: 2), cards: PlayerCardStats(yellow: 0, red: 0),
                                penalty: PlayerPenaltyStats(won: nil, committed: 1, scored: 0, missed: 0, saved: nil)
                            )
                        ]
                    ),
                    PlayerDetail(
                        player: PlayerStatsPlayerInfo(id: 204, name: "A. Def3", photo: "https://media.api-sports.io/football/players/204.png"),
                        statistics: [
                            PlayerStatsContainer(
                                games: PlayerGameStats(minutes: 85, number: 4, position: "D", rating: "4.8", captain: false, substitute: false),
                                offsides: 0, shots: PlayerShotStats(total: 1, on: 0), goals: PlayerGoalStats(total: 0, conceded: nil, assists: 0, saves: nil),
                                passes: PlayerPassStats(total: 45, key: 0, accuracy: "75%"), tackles: PlayerTackleStats(total: 2, blocks: 0, interceptions: 1),
                                duels: PlayerDuelStats(total: 8, won: 3), dribbles: PlayerDribbleStats(attempts: 2, success: 1, past: 2),
                                fouls: PlayerFoulStats(drawn: 0, committed: 3), cards: PlayerCardStats(yellow: 0, red: 1),
                                penalty: PlayerPenaltyStats(won: nil, committed: 0, scored: 0, missed: 0, saved: nil)
                            )
                        ]
                    )
                    // Add more mock players...
                ]
            )
        ]

        let mockEvents: [EventInfo]? = [
            // Normal goal for H. Scorer
            EventInfo(time: .init(elapsed: 25, extra: nil),
                      team: .init(id: 40, name: "Mock Home Team", logo: nil),
                      player: .init(id: 101, name: "H. Scorer"),
                      assist: .init(id: 102, name: "A. Ssist"),
                      type: "Goal", detail: "Normal Goal", comments: nil),
            // Penalty goal for H. Scorer
            EventInfo(time: .init(elapsed: 55, extra: nil),
                      team: .init(id: 40, name: "Mock Home Team", logo: nil),
                      player: .init(id: 101, name: "H. Scorer"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Penalty", comments: nil),
            // Penalty goal for H. Fwd1 (only scores penalties)
            EventInfo(time: .init(elapsed: 75, extra: nil),
                      team: .init(id: 40, name: "Mock Home Team", logo: nil),
                      player: .init(id: 109, name: "H. Fwd1"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Penalty", comments: nil),
            // Missed penalty for J. Mateta
            EventInfo(time: .init(elapsed: 54, extra: nil),
                      team: .init(id: 40, name: "Mock Home Team", logo: nil),
                      player: .init(id: 107, name: "H. Mid2"),
                      assist: .init(id: 107, name: "H. Mid2"),
                      type: "Goal", detail: "Missed Penalty", comments: nil),
            // Yellow card for Mid3 (home team)
            EventInfo(time: .init(elapsed: 35, extra: nil),
                      team: .init(id: 40, name: "Mock Home Team", logo: nil),
                      player: .init(id: 108, name: "H. Mid3"),
                      assist: .init(id: nil, name: nil), // No assist for card
                      type: "Card", detail: "Yellow Card", comments: "Foul"),
            // Red card for Def3 (away team)
            EventInfo(time: .init(elapsed: 48, extra: nil),
                      team: .init(id: 41, name: "Mock Away Team", logo: nil),
                      player: .init(id: 204, name: "A. Def3"),
                      assist: .init(id: nil, name: nil), // No assist for card
                      type: "Card", detail: "Red Card", comments: "Serious Foul Play"),
            EventInfo(time: .init(elapsed: 65, extra: nil),
                      team: .init(id: 40, name: "Mock Home Team", logo: nil),
                      player: .init(id: 103, name: "P. Out"),
                      assist: .init(id: 104, name: "P. In"), // Assist used for player coming in for Subs
                      type: "subst", detail: "Substitution 1", comments: nil),
            // Add an own goal for Def2
            EventInfo(time: .init(elapsed: 69, extra: nil),
                      team: .init(id: 41, name: "Mock Away Team", logo: nil),
                      player: .init(id: 203, name: "A. Def2"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Own Goal", comments: nil),
            EventInfo(time: .init(elapsed: 88, extra: nil),
                      team: .init(id: 33, name: "Mock Away Team", logo: nil),
                      player: .init(id: 202, name: "A. Scorer"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Penalty", comments: nil)
        ]

        // Create the final mock Fixture including lineups and statistics
        let fixture = Fixture(
            id: 12345,
            referee: "A. Referee",
            timezone: "UTC",
            date: "2025-04-22T17:00:00+00:00",
            timestamp: 1745370000 as Int?,
            venue: Venue(id: 10, name: "Mock Stadium", city: "Mock City"),
            status: Status(long: "Match Finished", short: "FT", elapsed: 90, extra: 4),
            league: LeagueInfo(
                id: 39,
                name: "Premier League",
                country: "England",
                logo: "https://example.com/pl.png",
                flag: "https://example.com/eng.png",
                season: 2024,
                round: "Regular Season - 38",
                coverage: LeagueInfo.Coverage(
                    standings: true,
                    players: true,
                    topScorers: true,
                    topAssists: true,
                    topCards: true,
                    injuries: true,
                    predictions: true,
                    odds: true
                )
            ),
            teams: Teams(
                home: TeamInfo(id: 40, name: "Mock Home Team", logo: "https://example.com/mht.png", winner: true),
                away: TeamInfo(id: 41, name: "Mock Away Team", logo: "https://example.com/mat.png", winner: false)
            ),
            goals: Goals(home: 2, away: 1),
            score: Score(halftime: Score.Halftime(home: 0, away: 1), fulltime: Score.Fulltime(home: 2, away: 1), extratime: nil, penalty: nil),
            lineups: mockLineups, // Add mock lineups here
            statistics: mockStats, // Add mock team stats
            playerStatistics: mockPlayerStats, // Add mock player stats
            events: mockEvents // Add mock events
        )
        return fixture
    }

    // Mock fixture for an upcoming match
    static var mockUpcoming: Fixture {
        // Create a simple fixture with minimal data for an upcoming match
        let fixture = Fixture(
            id: 54321,
            referee: "B. Referee",
            timezone: "UTC",
            date: Date().addingTimeInterval(86400 * 3).ISO8601Format(), // 3 days in the future
            timestamp: (Int(Date().timeIntervalSince1970) + 86400 * 3) as Int?,
            venue: Venue(id: 11, name: "Future Stadium", city: "Future City"),
            status: Status(long: "Not Started", short: "NS", elapsed: nil, extra: nil),
            league: LeagueInfo(
                id: 39,
                name: "Premier League",
                country: "England",
                logo: "https://example.com/pl.png",
                flag: "https://example.com/eng.png",
                season: 2024,
                round: "Regular Season - 39",
                coverage: LeagueInfo.Coverage(
                    standings: true,
                    players: true,
                    topScorers: true,
                    topAssists: true,
                    topCards: true,
                    injuries: true,
                    predictions: true,
                    odds: true
                )
            ),
            teams: Teams(
                home: TeamInfo(id: 42, name: "Arsenal", logo: "https://media.api-sports.io/football/teams/42.png", winner: nil),
                away: TeamInfo(id: 49, name: "Chelsea", logo: "https://media.api-sports.io/football/teams/49.png", winner: nil)
            ),
            goals: Goals(home: 0, away: 0),
            score: Score(halftime: Score.Halftime(home: 0, away: 0), fulltime: Score.Fulltime(home: 0, away: 0), extratime: nil, penalty: nil),
            lineups: nil,
            statistics: nil,
            playerStatistics: nil,
            events: nil
        )
        return fixture
    }

    // Mock fixture with penalty shootout
    static var mockPenalty: Fixture {
        // Create a fixture with penalty shootout data
        let penaltyEvents: [EventInfo] = [
            // First half events
            EventInfo(time: .init(elapsed: 26, extra: nil),
                      team: .init(id: 40, name: "Stockport County", logo: nil),
                      player: .init(id: 101, name: "I. Olaofe"),
                      assist: .init(id: 102, name: "A. Player"),
                      type: "Goal", detail: "Normal Goal", comments: nil),

            // Second half events
            EventInfo(time: .init(elapsed: 74, extra: nil),
                      team: .init(id: 41, name: "Leyton Orient", logo: nil),
                      player: .init(id: 201, name: "O. O'Neill"),
                      assist: .init(id: 202, name: "E. Galbraith"),
                      type: "Goal", detail: "Normal Goal", comments: nil),

            // Yellow cards
            EventInfo(time: .init(elapsed: 26, extra: nil),
                      team: .init(id: 41, name: "Leyton Orient", logo: nil),
                      player: .init(id: 203, name: "I. Touray"),
                      assist: .init(id: nil, name: nil),
                      type: "Card", detail: "Yellow Card", comments: "Foul"),

            EventInfo(time: .init(elapsed: 19, extra: nil),
                      team: .init(id: 41, name: "Leyton Orient", logo: nil),
                      player: .init(id: 204, name: "J. Donley"),
                      assist: .init(id: nil, name: nil),
                      type: "Card", detail: "Yellow Card", comments: "Foul"),

            // Substitution
            EventInfo(time: .init(elapsed: 46, extra: nil),
                      team: .init(id: 40, name: "Stockport County", logo: nil),
                      player: .init(id: 105, name: "I. Touray"),
                      assist: .init(id: 106, name: "R. Rydel"),
                      type: "subst", detail: "Substitution 1", comments: nil),

            // Penalty shootout events (alternating teams)
            // Round 1
            EventInfo(time: .init(elapsed: 120, extra: 1),
                      team: .init(id: 40, name: "Stockport County", logo: nil),
                      player: .init(id: 107, name: "J. Diamond"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Penalty", comments: "Penalty Shootout"),

            EventInfo(time: .init(elapsed: 120, extra: 1),
                      team: .init(id: 41, name: "Leyton Orient", logo: nil),
                      player: .init(id: 205, name: "T. James"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Penalty", comments: "Penalty Shootout"),

            // Round 2
            EventInfo(time: .init(elapsed: 120, extra: 2),
                      team: .init(id: 40, name: "Stockport County", logo: nil),
                      player: .init(id: 108, name: "R. Rydel"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Missed Penalty", comments: "Penalty Shootout"),

            EventInfo(time: .init(elapsed: 120, extra: 2),
                      team: .init(id: 41, name: "Leyton Orient", logo: nil),
                      player: .init(id: 206, name: "S. Clare"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Penalty", comments: "Penalty Shootout"),

            // Round 3
            EventInfo(time: .init(elapsed: 120, extra: 3),
                      team: .init(id: 40, name: "Stockport County", logo: nil),
                      player: .init(id: 109, name: "I. Touray"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Missed Penalty", comments: "Penalty Shootout"),

            EventInfo(time: .init(elapsed: 120, extra: 3),
                      team: .init(id: 41, name: "Leyton Orient", logo: nil),
                      player: .init(id: 207, name: "A. Abdulai"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Penalty", comments: "Penalty Shootout"),

            // Round 4
            EventInfo(time: .init(elapsed: 120, extra: 4),
                      team: .init(id: 40, name: "Stockport County", logo: nil),
                      player: .init(id: 110, name: "L. Norville"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Missed Penalty", comments: "Penalty Shootout"),

            EventInfo(time: .init(elapsed: 120, extra: 4),
                      team: .init(id: 41, name: "Leyton Orient", logo: nil),
                      player: .init(id: 208, name: "E. Galbraith"),
                      assist: .init(id: nil, name: nil),
                      type: "Goal", detail: "Penalty", comments: "Penalty Shootout")
        ]

        let fixture = Fixture(
            id: 75432,
            referee: "D. Referee",
            timezone: "UTC",
            date: "2025-05-15T19:45:00+00:00",
            timestamp: 1747370000 as Int?,
            venue: Venue(id: 13, name: "Wembley Stadium", city: "London"),
            status: Status(long: "Match Finished After Penalties", short: "PEN", elapsed: 120, extra: nil),
            league: LeagueInfo(
                id: 40,
                name: "EFL Trophy",
                country: "England",
                logo: "https://example.com/efl.png",
                flag: "https://example.com/eng.png",
                season: 2024,
                round: "Final",
                coverage: LeagueInfo.Coverage(
                    standings: true,
                    players: true,
                    topScorers: true,
                    topAssists: true,
                    topCards: true,
                    injuries: true,
                    predictions: true,
                    odds: true
                )
            ),
            teams: Teams(
                home: TeamInfo(id: 40, name: "Stockport County", logo: "https://media.api-sports.io/football/teams/40.png", winner: false),
                away: TeamInfo(id: 41, name: "Leyton Orient", logo: "https://media.api-sports.io/football/teams/41.png", winner: true)
            ),
            goals: Goals(home: 1, away: 1),
            score: Score(
                halftime: Score.Halftime(home: 1, away: 0),
                fulltime: Score.Fulltime(home: 1, away: 1),
                extratime: Score.Extratime(home: 0, away: 0),
                penalty: Score.Penalty(home: 1, away: 4)
            ),
            lineups: nil,
            statistics: nil,
            playerStatistics: nil,
            events: penaltyEvents
        )
        return fixture
    }

    // Mock fixture for a live match
    static var mockLive: Fixture {
        // Create a fixture with live match data
        let fixture = Fixture(
            id: 65432,
            referee: "C. Referee",
            timezone: "UTC",
            date: Date().ISO8601Format(), // Current date
            timestamp: Int(Date().timeIntervalSince1970) as Int?,
            venue: Venue(id: 12, name: "Live Stadium", city: "Live City"),
            status: Status(long: "First Half", short: "1H", elapsed: 35, extra: nil),
            league: LeagueInfo(
                id: 39,
                name: "Premier League",
                country: "England",
                logo: "https://example.com/pl.png",
                flag: "https://example.com/eng.png",
                season: 2024,
                round: "Regular Season - 37",
                coverage: LeagueInfo.Coverage(
                    standings: true,
                    players: true,
                    topScorers: true,
                    topAssists: true,
                    topCards: true,
                    injuries: true,
                    predictions: true,
                    odds: true
                )
            ),
            teams: Teams(
                home: TeamInfo(id: 33, name: "Manchester United", logo: "https://media.api-sports.io/football/teams/33.png", winner: nil),
                away: TeamInfo(id: 40, name: "Liverpool", logo: "https://media.api-sports.io/football/teams/40.png", winner: nil)
            ),
            goals: Goals(home: 1, away: 0),
            score: Score(halftime: nil, fulltime: nil, extratime: nil, penalty: nil),
            lineups: nil,
            statistics: nil,
            playerStatistics: nil,
            events: [
                EventInfo(
                    time: .init(elapsed: 22, extra: nil),
                    team: .init(id: 33, name: "Manchester United", logo: nil),
                    player: .init(id: 123, name: "B. Fernandes"),
                    assist: .init(id: 124, name: "M. Rashford"),
                    type: "Goal", detail: "Normal Goal", comments: nil
                )
            ],
            teamRatings: TeamRatings(
                homeTeam: TeamRating(teamId: 33, teamName: "Manchester United", rating: 7.2, playersCount: 11, playersWithRatings: 11),
                awayTeam: TeamRating(teamId: 40, teamName: "Liverpool", rating: 6.8, playersCount: 11, playersWithRatings: 11)
            )
        )
        return fixture
    }
}
