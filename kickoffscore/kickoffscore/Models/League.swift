import Foundation

// MARK: - League Response
struct LeagueResponse: Codable {
    let response: [LeagueData]
}

// MARK: - League Seasons Response
struct LeagueSeasonsResponse: Codable {
    let response: [Int]
}

// MARK: - Season Option for Dropdown
struct SeasonOption: Identifiable, Hashable {
    let id: Int
    let year: Int // This is the API season year
    let displayText: String

    init(year: Int) {
        self.id = year
        self.year = year
        // Format as "2024/25" for seasons that span two years
        // The API year is the starting year (e.g., 2024 for 2024/25 season)
        if year >= 2000 {
            let nextYear = String(year + 1).suffix(2)
            self.displayText = "\(year)/\(nextYear)"
        } else {
            self.displayText = "\(year)"
        }
    }
}

// MARK: - League Data
struct LeagueData: Codable, Identifiable, Hashable {
    let league: LeagueInfo
    let country: CountryInfo
    let seasons: [Season]

    // Computed property for Identifiable conformance
    var id: Int { league.id }

    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(league.id)
    }

    static func == (lhs: LeagueData, rhs: LeagueData) -> Bool {
        lhs.league.id == rhs.league.id
    }
}

// MARK: - League Info
struct LeagueInfo: Codable, Hashable {
    let id: Int
    let name: String
    let type: String
    let logo: String?
}

// MARK: - Country Info
struct CountryInfo: Codable, Hashable {
    let name: String
    let code: String?
    let flag: String?
}

// MARK: - Season
struct Season: Codable, Hashable {
    let year: Int
    let start: String
    let end: String
    let current: Bool
    let coverage: Coverage
}

// MARK: - Coverage
struct Coverage: Codable, Hashable {
    let fixtures: FixturesCoverage
    let standings: Bool
    let players: Bool
    let topScorers: Bool
    let topAssists: Bool
    let topCards: Bool
    let injuries: Bool
    let predictions: Bool
    let odds: Bool

    enum CodingKeys: String, CodingKey {
        case fixtures, standings, players, injuries, predictions, odds
        case topScorers = "top_scorers"
        case topAssists = "top_assists"
        case topCards = "top_cards"
    }
}

// MARK: - Fixtures Coverage
struct FixturesCoverage: Codable, Hashable {
    let events: Bool
    let lineups: Bool
    let statisticsFixtures: Bool
    let statisticsPlayers: Bool

    enum CodingKeys: String, CodingKey {
        case events, lineups
        case statisticsFixtures = "statistics_fixtures"
        case statisticsPlayers = "statistics_players"
    }
}
