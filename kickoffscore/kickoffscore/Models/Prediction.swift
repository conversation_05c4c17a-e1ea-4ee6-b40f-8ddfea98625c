import Foundation

// MARK: - API Response Structure

// Main prediction response model
struct PredictionResponse: Codable {
    let predictions: PredictionDetails
    let league: PredictionLeagueInfo
    let teams: PredictionTeams
    let comparison: PredictionComparison
    let h2h: [H2HMatch]?

    // For UI purposes, we'll create a simplified Prediction model
    func toPrediction(fixtureId: Int) -> Prediction {
        return Prediction(
            id: fixtureId,
            predictions: predictions,
            league: league,
            teams: teams,
            comparison: comparison
        )
    }
}

// Simplified Prediction model for UI
struct Prediction: Identifiable {
    let id: Int
    let predictions: PredictionDetails
    let league: PredictionLeagueInfo
    let teams: PredictionTeams
    let comparison: PredictionComparison
}

// League info for predictions
struct PredictionLeagueInfo: Codable {
    let id: Int
    let name: String?
    let country: String?
    let logo: String?
    let flag: String?
    let season: Int?
    let round: String?
}

// Prediction details
struct PredictionDetails: Codable {
    let winner: Winner
    let winOrDraw: Bool?
    let underOver: String?
    let goals: Goals
    let advice: String?
    let percent: Percentages

    enum CodingKeys: String, CodingKey {
        case winner, advice, percent, goals
        case winOrDraw = "win_or_draw"
        case underOver = "under_over"
    }
}

struct Winner: Codable {
    let id: Int?
    let name: String?
    let comment: String?
}

struct Goals: Codable {
    let home: String?
    let away: String?
}

struct Percentages: Codable {
    let home: String?
    let draw: String?
    let away: String?
}

// Teams with prediction-specific data
struct PredictionTeams: Codable {
    let home: PredictionTeam
    let away: PredictionTeam
}

struct PredictionTeam: Codable {
    let id: Int
    let name: String
    let logo: String?
    let last5: Last5?

    enum CodingKeys: String, CodingKey {
        case id, name, logo
        case last5 = "last_5"
    }
}

struct Last5: Codable {
    let form: String?
    let att: String?
    let def: String?
    let goals: Last5Goals?

    private enum CodingKeys: String, CodingKey {
        case form, att, def, goals
    }
}

struct Last5Goals: Codable {
    let for_: Last5GoalStats?
    let against: Last5GoalStats?

    private enum CodingKeys: String, CodingKey {
        case for_ = "for"
        case against
    }
}

struct Last5GoalStats: Codable {
    let total: Int?
    let average: String?
}

// Comparison data
struct PredictionComparison: Codable {
    let form: ComparisonValue?
    let att: ComparisonValue?
    let def: ComparisonValue?
    let poissonDistribution: ComparisonValue?
    let h2h: ComparisonValue?
    let goals: ComparisonValue?
    let total: ComparisonValue?

    enum CodingKeys: String, CodingKey {
        case form, att, def, h2h, goals, total
        case poissonDistribution = "poisson_distribution"
    }
}

struct ComparisonValue: Codable {
    let home: String?
    let away: String?
}

// Head-to-head match data
struct H2HMatch: Codable {
    let fixture: H2HFixture
    let league: H2HLeague
    let teams: H2HTeams
    let goals: H2HGoals
    let score: H2HScore
}

struct H2HFixture: Codable {
    let id: Int
    let date: String
    let status: H2HStatus
}

struct H2HStatus: Codable {
    let long: String
    let short: String
    let elapsed: Int?
}

struct H2HLeague: Codable {
    let id: Int
    let name: String
    let season: Int
}

struct H2HTeams: Codable {
    let home: H2HTeam
    let away: H2HTeam
}

struct H2HTeam: Codable {
    let id: Int
    let name: String
    let logo: String?
    let winner: Bool?
}

struct H2HGoals: Codable {
    let home: Int?
    let away: Int?
}

struct H2HScore: Codable {
    let halftime: H2HGoals?
    let fulltime: H2HGoals?
}

// MARK: - Mock Data

extension Prediction {
    static var mock: Prediction {
        Prediction(
            id: 12345,
            predictions: PredictionDetails(
                winner: Winner(id: 40, name: "Mock Home Team", comment: "Strong home form"),
                winOrDraw: true,
                underOver: "****",
                goals: Goals(home: "****", away: "-0.5"),
                advice: "Home team has been in excellent form and is expected to win this match.",
                percent: Percentages(home: "65%", draw: "20%", away: "15%")
            ),
            league: PredictionLeagueInfo(
                id: 39,
                name: "Premier League",
                country: "England",
                logo: "https://example.com/pl.png",
                flag: "https://example.com/eng.png",
                season: 2024,
                round: "Regular Season - 38"
            ),
            teams: PredictionTeams(
                home: PredictionTeam(
                    id: 40,
                    name: "Mock Home Team",
                    logo: "https://example.com/mht.png",
                    last5: Last5(
                        form: "WWDLW",
                        att: "strong",
                        def: "medium",
                        goals: nil
                    )
                ),
                away: PredictionTeam(
                    id: 41,
                    name: "Mock Away Team",
                    logo: "https://example.com/mat.png",
                    last5: Last5(
                        form: "LDLWD",
                        att: "medium",
                        def: "weak",
                        goals: nil
                    )
                )
            ),
            comparison: PredictionComparison(
                form: ComparisonValue(home: "65%", away: "35%"),
                att: ComparisonValue(home: "70%", away: "30%"),
                def: ComparisonValue(home: "55%", away: "45%"),
                poissonDistribution: ComparisonValue(home: "60%", away: "40%"),
                h2h: ComparisonValue(home: "58%", away: "42%"),
                goals: ComparisonValue(home: "62%", away: "38%"),
                total: ComparisonValue(home: "63%", away: "37%")
            )
        )
    }
}
