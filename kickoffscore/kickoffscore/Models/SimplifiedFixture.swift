import Foundation

// A simplified version of the Fixture model for use in user votes
public struct SimplifiedFixture: Codable, Identifiable {
    public struct Status: Codable {
        public let long: String?
        public let short: String?
        public let elapsed: Int?
        public let extra: Int?
    }

    public struct FixtureInfo: Codable {
        public let id: Int
        public let date: String
        public let status: Status
    }

    public struct Teams: Codable {
        public struct TeamInfo: Codable {
            public let name: String
            public let logo: String?
        }

        public let home: TeamInfo
        public let away: TeamInfo
    }

    public struct Goals: Codable {
        public let home: Int?
        public let away: Int?
    }

    public let fixture: FixtureInfo
    public let teams: Teams
    public let goals: Goals

    // Use fixture.id for Identifiable conformance
    public var _id: Int?

    // Computed property to satisfy Identifiable
    public var id: Int {
        return fixture.id
    }

    // Convenience properties
    public var homeTeam: String { teams.home.name }
    public var awayTeam: String { teams.away.name }
    public var homeScore: Int? { goals.home }
    public var awayScore: Int? { goals.away }
    public var status: String { fixture.status.short ?? "NS" }
    public var date: String { fixture.date }

    // Format date for display
    public var formattedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")

        if let date = dateFormatter.date(from: fixture.date) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateStyle = .medium
            displayFormatter.timeStyle = .short
            return displayFormatter.string(from: date)
        }

        return fixture.date
    }
}
