import Foundation

// MARK: - Knockout Bracket Data Models

/// Represents a single match in a knockout tournament (can be aggregate of two legs)
struct KnockoutMatchData: Identifiable, Hashable {
    let id: Int
    let homeTeam: KnockoutTeam
    let awayTeam: KnockoutTeam
    let homeScore: Int?
    let awayScore: Int?
    let homePenaltyScore: Int?
    let awayPenaltyScore: Int?
    let status: String
    let date: String?
    let isFinished: Bool
    let isPenalties: Bool
    let isAggregate: Bool // True if this represents aggregate of two legs
    let fixtureIds: [Int] // Array of fixture IDs that make up this match
    
    /// Winner of the match (nil if not finished)
    var winner: KnockoutTeam? {
        guard isFinished else { return nil }

        // For penalty shootouts, use penalty scores to determine winner
        if isPenalties, let homePenScore = homePenaltyScore, let awayPenScore = awayPenaltyScore {
            if homePenScore > awayPenScore {
                return homeTeam
            } else if awayPenScore > homePenScore {
                return awayTeam
            }
        }

        // For regular matches (including aggregates), use regular scores
        guard let homeScore = homeScore, let awayScore = awayScore else { return nil }

        if homeScore > awayScore {
            return homeTeam
        } else if awayScore > homeScore {
            return awayTeam
        }

        // In knockout matches, draws are rare but can happen in group stages
        // If no clear winner can be determined, return nil
        return nil
    }
    
    /// Display score text
    var scoreText: String {
        guard let homeScore = homeScore, let awayScore = awayScore else {
            return date ?? "TBD"
        }

        if isPenalties, let homePenScore = homePenaltyScore, let awayPenScore = awayPenaltyScore {
            return "\(homeScore):\(awayScore) (\(homePenScore):\(awayPenScore) pen)"
        } else {
            return "\(homeScore):\(awayScore)"
        }
    }
    
    /// Direct initializer for creating match data
    init(id: Int, homeTeam: KnockoutTeam, awayTeam: KnockoutTeam, homeScore: Int?, awayScore: Int?, homePenaltyScore: Int? = nil, awayPenaltyScore: Int? = nil, status: String, date: String?, isFinished: Bool, isPenalties: Bool, isAggregate: Bool = false, fixtureIds: [Int] = []) {
        self.id = id
        self.homeTeam = homeTeam
        self.awayTeam = awayTeam
        self.homeScore = homeScore
        self.awayScore = awayScore
        self.homePenaltyScore = homePenaltyScore
        self.awayPenaltyScore = awayPenaltyScore
        self.status = status
        self.date = date
        self.isFinished = isFinished
        self.isPenalties = isPenalties
        self.isAggregate = isAggregate
        self.fixtureIds = fixtureIds.isEmpty ? [id] : fixtureIds
    }

    /// Initialize from Fixture model
    init(from fixture: Fixture) {
        self.id = fixture.id
        self.homeTeam = KnockoutTeam(
            id: fixture.teams.home?.id ?? 0,
            name: fixture.teams.home?.name ?? "Unknown Team",
            logo: fixture.teams.home?.logo
        )
        self.awayTeam = KnockoutTeam(
            id: fixture.teams.away?.id ?? 0,
            name: fixture.teams.away?.name ?? "Unknown Team",
            logo: fixture.teams.away?.logo
        )
        self.homeScore = fixture.goals.home
        self.awayScore = fixture.goals.away
        self.homePenaltyScore = fixture.score.penalty?.home
        self.awayPenaltyScore = fixture.score.penalty?.away
        self.status = fixture.status.short ?? "NS"
        self.date = fixture.date
        self.isFinished = ["FT", "AET", "PEN", "AWD", "WO"].contains(fixture.status.short ?? "")
        self.isPenalties = fixture.status.short == "PEN"
        self.isAggregate = false
        self.fixtureIds = [fixture.id]
    }
}

/// Represents a team in knockout tournament
struct KnockoutTeam: Identifiable, Hashable {
    let id: Int
    let name: String
    let logo: String?
    
    /// Short name for display in compact spaces
    var shortName: String {
        // Create abbreviation from team name
        let words = name.components(separatedBy: " ")
        if words.count >= 2 {
            return words.prefix(2).compactMap { $0.first }.map(String.init).joined()
        } else if let firstWord = words.first {
            return String(firstWord.prefix(3)).uppercased()
        }
        return "TBD"
    }
}

/// Represents a bracket column (round) in the knockout tournament
struct KnockoutBracket: Identifiable, Hashable {
    let id: String
    let name: String
    let displayName: String
    let matches: [KnockoutMatchData]
    let roundIndex: Int // 0 = earliest round, higher = later rounds
    
    init(name: String, matches: [KnockoutMatchData], roundIndex: Int) {
        self.id = name
        self.name = name
        self.displayName = CompetitionTypeUtils.formatKnockoutRoundName(name)
        self.matches = matches
        self.roundIndex = roundIndex
    }
    
    /// Check if this bracket has any live matches
    var hasLiveMatches: Bool {
        matches.contains { match in
            ["LIVE", "1H", "HT", "2H", "ET", "P"].contains(match.status)
        }
    }
    
    /// Check if this bracket has any upcoming matches
    var hasUpcomingMatches: Bool {
        matches.contains { match in
            ["NS", "TBD", "PST"].contains(match.status)
        }
    }
    
    /// Check if all matches in this bracket are finished
    var isCompleted: Bool {
        !matches.isEmpty && matches.allSatisfy { $0.isFinished }
    }
}

/// Represents the complete knockout tournament structure
struct KnockoutTournament: Identifiable {
    let id: Int
    let leagueId: Int
    let season: Int
    let brackets: [KnockoutBracket]
    
    init(leagueId: Int, season: Int, brackets: [KnockoutBracket]) {
        self.id = leagueId
        self.leagueId = leagueId
        self.season = season
        self.brackets = brackets.sorted { $0.roundIndex < $1.roundIndex }
    }
    
    /// Get all matches across all brackets
    var allMatches: [KnockoutMatchData] {
        brackets.flatMap { $0.matches }
    }
    
    /// Get brackets that have live matches
    var liveBrackets: [KnockoutBracket] {
        brackets.filter { $0.hasLiveMatches }
    }
    
    /// Get brackets that have upcoming matches
    var upcomingBrackets: [KnockoutBracket] {
        brackets.filter { $0.hasUpcomingMatches }
    }
    
    /// Get the current active bracket (live > upcoming > most recent completed)
    var activeBracket: KnockoutBracket? {
        // Priority 1: Bracket with live matches
        if let liveBracket = liveBrackets.first {
            return liveBracket
        }
        
        // Priority 2: Bracket with upcoming matches (earliest round)
        if let upcomingBracket = upcomingBrackets.min(by: { $0.roundIndex < $1.roundIndex }) {
            return upcomingBracket
        }
        
        // Priority 3: Most recent completed bracket
        return brackets.filter { $0.isCompleted }.max(by: { $0.roundIndex < $1.roundIndex })
    }
    
    /// Check if tournament has any knockout stages
    var hasKnockoutStages: Bool {
        !brackets.isEmpty
    }
}

// MARK: - Helper Extensions

extension Array where Element == Fixture {
    /// Convert fixtures to knockout tournament structure
    func toKnockoutTournament(leagueId: Int, season: Int) -> KnockoutTournament? {
        // Group fixtures by round
        let fixturesByRound = Dictionary(grouping: self) { fixture in
            fixture.league.round ?? "Unknown"
        }

        // Filter only knockout rounds
        let knockoutRounds = fixturesByRound.filter { roundName, _ in
            CompetitionTypeUtils.isKnockoutRound(roundName: roundName)
        }

        guard !knockoutRounds.isEmpty else { return nil }

        // Create brackets from knockout rounds
        let brackets = knockoutRounds.compactMap { roundName, fixtures -> KnockoutBracket? in
            let matches = aggregateTwoLegMatches(fixtures)
            let priority = CompetitionTypeUtils.getKnockoutRoundPriority(roundName: roundName)
            return KnockoutBracket(name: roundName, matches: matches, roundIndex: priority)
        }

        return KnockoutTournament(leagueId: leagueId, season: season, brackets: brackets)
    }

    /// Aggregate two-leg matches into single knockout match data
    private func aggregateTwoLegMatches(_ fixtures: [Fixture]) -> [KnockoutMatchData] {
        // Group fixtures by team pairs (regardless of home/away order)
        var teamPairGroups: [String: [Fixture]] = [:]

        for fixture in fixtures {
            guard let homeId = fixture.teams.home?.id,
                  let awayId = fixture.teams.away?.id else { continue }

            // Create a consistent key for team pairs (smaller ID first)
            let teamPairKey = homeId < awayId ? "\(homeId)-\(awayId)" : "\(awayId)-\(homeId)"

            if teamPairGroups[teamPairKey] == nil {
                teamPairGroups[teamPairKey] = []
            }
            teamPairGroups[teamPairKey]?.append(fixture)
        }

        var aggregatedMatches: [KnockoutMatchData] = []

        for (_, fixturesForPair) in teamPairGroups {
            if fixturesForPair.count == 1 {
                // Single leg match
                aggregatedMatches.append(KnockoutMatchData(from: fixturesForPair[0]))
            } else if fixturesForPair.count == 2 {
                // Two-leg match - aggregate scores
                let aggregatedMatch = createAggregateMatch(from: fixturesForPair)
                aggregatedMatches.append(aggregatedMatch)
            } else {
                // More than 2 matches for same teams - treat each separately (shouldn't happen in normal knockout)
                for fixture in fixturesForPair {
                    aggregatedMatches.append(KnockoutMatchData(from: fixture))
                }
            }
        }

        return aggregatedMatches
    }

    /// Create aggregate match from two leg fixtures
    private func createAggregateMatch(from fixtures: [Fixture]) -> KnockoutMatchData {
        guard fixtures.count == 2 else {
            // Fallback to first fixture if not exactly 2
            return KnockoutMatchData(from: fixtures[0])
        }

        let fixture1 = fixtures[0]
        let _ = fixtures[1] // Second fixture used in the loop below

        // Determine which fixture has which team as home
        // We'll use the first fixture's home team as the "home" team for display
        let displayHomeTeam = fixture1.teams.home
        let displayAwayTeam = fixture1.teams.away

        guard let homeTeamId = displayHomeTeam?.id,
              let awayTeamId = displayAwayTeam?.id else {
            return KnockoutMatchData(from: fixture1)
        }

        // Calculate aggregate scores
        var homeAggregateScore = 0
        var awayAggregateScore = 0
        var homePenaltyScore: Int? = nil
        var awayPenaltyScore: Int? = nil
        var isPenalties = false
        var isFinished = true
        var latestStatus = "FT"
        var latestDate: String? = nil

        for fixture in fixtures {
            // Add goals based on which team is home/away in this fixture
            if fixture.teams.home?.id == homeTeamId {
                // Home team is playing at home in this fixture
                homeAggregateScore += fixture.goals.home ?? 0
                awayAggregateScore += fixture.goals.away ?? 0

                // Check for penalties in this leg
                if fixture.status.short == "PEN" {
                    isPenalties = true
                    homePenaltyScore = (homePenaltyScore ?? 0) + (fixture.score.penalty?.home ?? 0)
                    awayPenaltyScore = (awayPenaltyScore ?? 0) + (fixture.score.penalty?.away ?? 0)
                }
            } else {
                // Home team is playing away in this fixture (reverse scores)
                homeAggregateScore += fixture.goals.away ?? 0
                awayAggregateScore += fixture.goals.home ?? 0

                // Check for penalties in this leg
                if fixture.status.short == "PEN" {
                    isPenalties = true
                    homePenaltyScore = (homePenaltyScore ?? 0) + (fixture.score.penalty?.away ?? 0)
                    awayPenaltyScore = (awayPenaltyScore ?? 0) + (fixture.score.penalty?.home ?? 0)
                }
            }

            // Update status and date to latest
            if !["FT", "AET", "PEN", "AWD", "WO"].contains(fixture.status.short ?? "") {
                isFinished = false
            }

            latestStatus = fixture.status.short ?? "NS"
            if latestDate == nil || fixture.date > (latestDate ?? "") {
                latestDate = fixture.date
            }
        }

        // Create aggregate match
        return KnockoutMatchData(
            id: fixture1.id, // Use first fixture's ID as primary
            homeTeam: KnockoutTeam(
                id: homeTeamId,
                name: displayHomeTeam?.name ?? "Unknown Team",
                logo: displayHomeTeam?.logo
            ),
            awayTeam: KnockoutTeam(
                id: awayTeamId,
                name: displayAwayTeam?.name ?? "Unknown Team",
                logo: displayAwayTeam?.logo
            ),
            homeScore: homeAggregateScore,
            awayScore: awayAggregateScore,
            homePenaltyScore: homePenaltyScore,
            awayPenaltyScore: awayPenaltyScore,
            status: latestStatus,
            date: latestDate,
            isFinished: isFinished,
            isPenalties: isPenalties,
            isAggregate: true,
            fixtureIds: fixtures.map { $0.id }
        )
    }
}
