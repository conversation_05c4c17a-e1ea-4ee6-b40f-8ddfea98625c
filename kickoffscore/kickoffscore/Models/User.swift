import Foundation

struct User: Codable, Identifiable {
    var id: String
    var email: String
    var name: String?
    var profileImage: String?
    var createdAt: Date
    var updatedAt: Date
    var favorites: Favorites

    struct Favorites: Codable {
        var teams: [Int]
        var players: [Int]
        var leagues: [Int]
    }

    // Custom coding keys to handle _id vs id
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case email
        case name
        case profileImage
        case createdAt
        case updatedAt
        case favorites
    }

    // Custom init to handle date decoding
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        email = try container.decode(String.self, forKey: .email)
        name = try container.decodeIfPresent(String.self, forKey: .name)
        profileImage = try container.decodeIfPresent(String.self, forKey: .profileImage)
        favorites = try container.decode(Favorites.self, forKey: .favorites)

        // Debug print
        print("User decoded - ID: \(id), Email: \(email), Name: \(name ?? "nil"), ProfileImage: \(profileImage ?? "nil")")

        // Handle date decoding
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)

        let createdAtString = try container.decode(String.self, forKey: .createdAt)
        if let date = dateFormatter.date(from: createdAtString) {
            createdAt = date
        } else {
            createdAt = Date()
        }

        let updatedAtString = try container.decode(String.self, forKey: .updatedAt)
        if let date = dateFormatter.date(from: updatedAtString) {
            updatedAt = date
        } else {
            updatedAt = Date()
        }
    }
}

// Auth response from the server
struct AuthResponse: Codable {
    let token: String
    let user: User
}

// Login request
struct LoginRequest: Codable {
    let email: String
    let password: String
}

// Register request
struct RegisterRequest: Codable {
    let email: String
    let password: String
    let name: String?
}
