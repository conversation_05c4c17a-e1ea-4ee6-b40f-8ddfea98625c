import Foundation

/// A response model for when the API returns a direct array of fixtures instead of a response object
public struct DirectFixtureResponse: Codable {
    public let fixtures: [Fixture]
    
    // Custom decoding to handle direct array response
    public init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        fixtures = try container.decode([Fixture].self)
    }
}
