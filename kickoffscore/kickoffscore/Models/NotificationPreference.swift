import Foundation

/// Represents notification preferences for a fixture
struct NotificationPreference: Codable, Equatable {
    var matchStatus: Bool
    var goals: Bool
    var redCards: Bool
    var allEvents: Bool
    
    /// Default preferences with all options enabled
    static var defaultPreferences: NotificationPreference {
        NotificationPreference(
            matchStatus: true,
            goals: true,
            redCards: true,
            allEvents: false
        )
    }
    
    /// Check if any notification is enabled
    var hasEnabledNotifications: Bool {
        allEvents || matchStatus || goals || redCards
    }
    
    /// Update allEvents based on other preferences
    mutating func updateAllEvents() {
        // If all individual notifications are enabled, enable allEvents
        if matchStatus && goals && redCards {
            allEvents = true
        } else {
            allEvents = false
        }
    }
    
    /// Apply allEvents selection to all other preferences
    mutating func applyAllEvents() {
        if allEvents {
            matchStatus = true
            goals = true
            redCards = true
        }
    }
}

/// Represents the subscription status for a fixture
enum FixtureSubscriptionStatus {
    case notSubscribed
    case subscribed(NotificationPreference)
    case loading
    
    var isSubscribed: Bool {
        if case .subscribed = self {
            return true
        }
        return false
    }
    
    var preference: NotificationPreference? {
        if case .subscribed(let pref) = self {
            return pref
        }
        return nil
    }
}
