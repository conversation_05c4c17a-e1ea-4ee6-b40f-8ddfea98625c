import SwiftUI
import King<PERSON><PERSON> // Needed for RankedPlayerRow's KFImage
import UIKit

struct PlayerStatDetailView: View {
    let category: PlayerStatCategory
    let players: [PlayerStatsItem]
    let homeTeamId: Int? // Passed from parent view

    // Pass the already sorted full list

    // Add a state variable to track if we should dismiss
    @Environment(\.presentationMode) private var presentationMode
    @State private var scrollOffset: CGFloat = 0
    @State private var headerHeight: CGFloat = 60

    // Track visible range for optimization
    @State private var visibleRange: Range<Int> = 0..<20 // Initially show first 20 items

    // Computed properties for animations
    private var headerOpacity: Double {
        let opacity = Double(min(1, max(0, scrollOffset / 50)))
        return opacity
    }

    // Dismiss the view efficiently
    private func dismissView() {
        // Cancel any pending image downloads before dismissing
        KingfisherManager.shared.downloader.cancelAll()

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {
            rootViewController.dismiss(animated: true)
        } else {
            // Fallback to SwiftUI's presentation mode
            presentationMode.wrappedValue.dismiss()
        }
    }

    // Update visible range based on scroll position
    private func updateVisibleRange() {
        // Calculate which items should be visible based on scroll position
        // This is a simplified approach - in a real app, you'd use more sophisticated logic
        let startIndex = max(0, Int(-scrollOffset / 50))
        let endIndex = min(players.count, startIndex + 30) // Show 30 items at a time
        visibleRange = startIndex..<endIndex
    }

    var body: some View {
        ZStack(alignment: .top) {
            // Main content
            ScrollView(showsIndicators: false) {
                // Invisible spacer to track scroll position
                GeometryReader { geometry in
                    Color.clear.preference(key: ScrollOffsetPreferenceKey.self,
                                          value: geometry.frame(in: .named("scrollView")).minY)
                }
                .frame(height: 0)

                // Add space at top for the fixed header and title
                Spacer().frame(height: 60)

                // Player stats list with optimized rendering
                LazyVStack(spacing: 0) {
                    // Use indices to avoid creating unnecessary views
                    ForEach(0..<players.count, id: \.self) { index in
                        if visibleRange.contains(index) {
                            let player = players[index]
                            // Reuse the existing row view with improved styling
                            RankedPlayerRow(rank: index + 1, player: player, category: category, isHomeTeam: player.team.id == homeTeamId)
                                .padding(.vertical, 6) // Reduced vertical padding
                                .background(index % 2 == 0 ? Color(UIColor.systemBackground) : Color(UIColor.secondarySystemBackground))
                                // Add ID for better diffing
                                .id(player.id)
                        } else {
                            // Placeholder with correct height but minimal rendering cost
                            Color.clear
                                .frame(height: 44) // Approximate height of a row
                                .id("placeholder-\(index)")
                        }
                    }
                }
                .background(AppColors.secondaryBackground)
                .padding(.bottom, AppLayout.spacingM)
            }
            .coordinateSpace(name: "scrollView")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = value
                updateVisibleRange()
            }
            .scrollIndicators(.hidden) // Hide scroll indicators
            .background(AppColors.background)
            .edgesIgnoringSafeArea(.horizontal)

            // Fixed header with blur effect
            VStack(spacing: 0) {
                // Top bar with close button only
                HStack {
                    // Modern close button
                    Button(action: dismissView) {
                        Image(systemName: "xmark")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(Color(UIColor.label))
                            .frame(width: 32, height: 32)
                    }
                    .padding(.leading, AppLayout.spacingM)

                    Spacer()

                    // Add title in the center
                    Text("\(category.rawValue) Ranking")
                        .font(AppTypography.dynamicFont(style: .headline))
                        .foregroundColor(Color(UIColor.label))

                    Spacer()
                }
                .frame(height: 60)
                .background(Color(UIColor.systemBackground))

                // Add a separator line
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color.gray.opacity(0.2))
            }
            .frame(height: 60)
        }
        .navigationBarHidden(true) // Hide the default navigation bar
        .navigationTitle("\(category.rawValue) Ranking") // Set title for accessibility
        .onDisappear {
            // Clean up resources when view disappears
            KingfisherManager.shared.downloader.cancelAll()
            KingfisherManager.shared.cache.clearMemoryCache()
        }
    }
}

// Preference key to track scroll position
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// Blur view using UIViewRepresentable
struct BlurView: UIViewRepresentable {
    let style: UIBlurEffect.Style

    func makeUIView(context: Context) -> UIVisualEffectView {
        return UIVisualEffectView(effect: UIBlurEffect(style: style))
    }

    func updateUIView(_ uiView: UIVisualEffectView, context: Context) {
        uiView.effect = UIBlurEffect(style: style)
    }
}

// Note: RankedPlayerRow and PlayerStatsItem definitions are currently in PlayerStatsView.swift.
// For this view to compile independently, ensure they are accessible within the project target.
// Consider moving them to separate files if needed later for better organization.

// TODO: Add a PreviewProvider if needed for independent design/testing
