import SwiftUI

struct JerseyShape: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()

        // Adjust these ratios to tweak the proportions
        let neckWidthRatio: CGFloat = 0.30
        let neckHeightRatio: CGFloat = 0.15
        let shoulderWidthRatio: CGFloat = 1.0 // Full width
        let sleeveLengthRatio: CGFloat = 0.30 // How far down the sleeve goes
        let torsoWidthRatio: CGFloat = 0.70 // Width at the bottom relative to total width

        let neckWidth = rect.width * neckWidthRatio
        let neckHeight = rect.height * neckHeightRatio
        let shoulderWidth = rect.width * shoulderWidthRatio
        let sleeveDrop = rect.height * sleeveLengthRatio
        let torsoWidth = rect.width * torsoWidthRatio

        // Calculate key points (origin is top-left)
        let neckLeft = CGPoint(x: rect.midX - neckWidth / 2, y: rect.minY + neckHeight * 0.5) // Slightly lower than minY
        let neckRight = CGPoint(x: rect.midX + neckWidth / 2, y: rect.minY + neckHeight * 0.5)
        let neckControl = CGPoint(x: rect.midX, y: rect.minY - neckHeight * 0.2) // Control point slightly above for curve

        let shoulderLeft = CGPoint(x: rect.minX + (rect.width - shoulderWidth) / 2, y: rect.minY + neckHeight)
        let shoulderRight = CGPoint(x: rect.maxX - (rect.width - shoulderWidth) / 2, y: rect.minY + neckHeight)

        let sleeveBottomOuterLeft = CGPoint(x: shoulderLeft.x, y: shoulderLeft.y + sleeveDrop)
        let sleeveBottomOuterRight = CGPoint(x: shoulderRight.x, y: shoulderRight.y + sleeveDrop)

        let torsoTopLeft = CGPoint(x: rect.midX - torsoWidth / 2, y: sleeveBottomOuterLeft.y)
        let torsoTopRight = CGPoint(x: rect.midX + torsoWidth / 2, y: sleeveBottomOuterRight.y)

        let torsoBottomLeft = CGPoint(x: torsoTopLeft.x, y: rect.maxY)
        let torsoBottomRight = CGPoint(x: torsoTopRight.x, y: rect.maxY)

        // Draw the path (counter-clockwise for convention)
        path.move(to: neckLeft) // Start at left neck point
        path.addQuadCurve(to: neckRight, control: neckControl) // Curve for neckline

        path.addLine(to: shoulderRight) // Right shoulder top
        path.addLine(to: sleeveBottomOuterRight) // Outer bottom of right sleeve
        path.addLine(to: torsoTopRight) // Inner bottom of right sleeve / top of torso side
        path.addLine(to: torsoBottomRight) // Bottom right corner
        path.addLine(to: torsoBottomLeft)  // Bottom left corner
        path.addLine(to: torsoTopLeft)    // Inner bottom of left sleeve / top of torso side
        path.addLine(to: sleeveBottomOuterLeft) // Outer bottom of left sleeve
        path.addLine(to: shoulderLeft) // Left shoulder top

        path.closeSubpath() // Connect back to neckLeft

        return path
    }
}

// Football jersey with team color
struct FootballJerseyShape: View {
    var color: Color
    var borderColor: Color = Color.white.opacity(0.3)

    var body: some View {
        ZStack {
            // Fill
            JerseyShape()
                .fill(color)

            // Outline with beveled corners
            JerseyShape()
                .stroke(style: StrokeStyle(
                    lineWidth: 1.5,
                    lineCap: .round,
                    lineJoin: .bevel
                ))
                .foregroundColor(borderColor)
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        FootballJerseyShape(color: .blue)
            .frame(width: 60, height: 60)

        FootballJerseyShape(color: .red)
            .frame(width: 40, height: 40)

        FootballJerseyShape(color: .green)
            .frame(width: 80, height: 80)
    }
    .padding()
    .background(Color.black)
}
