import SwiftUI

struct OddsView: View {
    let fixture: Fixture
    @StateObject private var viewModel = OddsViewModel()

    var body: some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: AppLayout.spacingS) { // Reduced spacing between containers to match other tabs
                if viewModel.isLoading {
                    SimpleSkeletonListView(itemCount: 5, showHeader: true)
                } else if let errorMessage = viewModel.errorMessage {
                    OddsErrorView(message: errorMessage)
                } else if viewModel.noOddsAvailable {
                    OddsEmptyView(message: "Check back later for updated odds")
                } else if let odds = viewModel.odds {
                    // Main odds container
                    if odds.isLive {
                        // Live odds view
                        LiveOddsContainer(
                            fixture: fixture,
                            viewModel: viewModel
                        )
                    } else {
                        // Pre-match odds view
                        PreMatchOddsContainer(
                            fixture: fixture,
                            viewModel: viewModel
                        )
                    }

                    // Disclaimer
                    OddsDisclaimerView()
                } else {
                    OddsEmptyView(message: "Check back later for updated odds")
                }
            }
            .padding(.top, AppLayout.spacingS) // Consistent with other tab views
            .padding(.bottom, AppLayout.spacingM)
        }
        .background(AppColors.background)
        .edgesIgnoringSafeArea(.horizontal) // Ignore safe area for edge-to-edge
        .onAppear {
            viewModel.fetchAppropriateOdds(for: fixture)
        }
    }
}

// MARK: - Pre-Match Odds Container
struct PreMatchOddsContainer: View {
    let fixture: Fixture
    @ObservedObject var viewModel: OddsViewModel

    var body: some View {
        VStack(spacing: 0) {
            // Header with fixture info
            VStack(spacing: AppLayout.spacingXS) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Pre-Match Odds")
                            .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                            .foregroundColor(AppColors.text)

                        Text("\(fixture.teams.home?.name ?? "Home") vs \(fixture.teams.away?.name ?? "Away")")
                            .font(AppTypography.dynamicFont(style: .subheadline))
                            .foregroundColor(AppColors.secondaryText)
                    }

                    Spacer()
                }
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.top, AppLayout.spacingM)
                .padding(.bottom, AppLayout.spacingS)
            }
            .background(AppColors.tertiaryBackground)

            Divider()

            // Bookmaker selector
            VStack(spacing: AppLayout.spacingM) {
                BookmakerSelectorView(viewModel: viewModel)
                    .padding(.horizontal, AppLayout.spacingM)
            }
            .padding(.vertical, AppLayout.spacingM)
            .background(AppColors.tertiaryBackground)

            Divider()

            // Bet type categories
            VStack(spacing: AppLayout.spacingS) {
                ForEach(viewModel.availableCategories, id: \.self) { category in
                    if let betTypeIds = viewModel.availableBetTypesByCategory[category] {
                        BetTypeCategoryView(
                            fixture: fixture,
                            category: category,
                            betTypeIds: betTypeIds,
                            viewModel: viewModel,
                            isLive: false
                        )
                    }
                }
            }
            .padding(.horizontal, AppLayout.spacingS)
            .padding(.vertical, AppLayout.spacingXS)

            // Last updated info
            if let odds = viewModel.odds {
                VStack {
                    LastUpdatedView(updateDate: odds.update)
                        .padding(.horizontal, AppLayout.spacingM)
                        .padding(.vertical, AppLayout.spacingS)
                }
                .background(AppColors.tertiaryBackground)
            }
        }
        .background(AppColors.tertiaryBackground)
    }
}

// MARK: - Live Odds Container
struct LiveOddsContainer: View {
    let fixture: Fixture
    @ObservedObject var viewModel: OddsViewModel

    var body: some View {
        VStack(spacing: 0) {
            // Header with fixture info and live indicator
            VStack(spacing: AppLayout.spacingXS) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack(spacing: 8) {
                            Circle()
                                .fill(AppColors.error)
                                .frame(width: 8, height: 8)
                                .blinking(blinkRate: 1.5, minOpacity: 0.7)

                            Text("LIVE ODDS")
                                .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                                .foregroundColor(AppColors.error)
                        }

                        Text("\(fixture.teams.home?.name ?? "Home") vs \(fixture.teams.away?.name ?? "Away")")
                            .font(AppTypography.dynamicFont(style: .subheadline))
                            .foregroundColor(AppColors.secondaryText)
                    }

                    Spacer()

                    // Score display
                    if let homeGoals = fixture.goals.home, let awayGoals = fixture.goals.away {
                        HStack(spacing: 4) {
                            Text("\(homeGoals)")
                                .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                                .foregroundColor(AppColors.text)

                            Text("-")
                                .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                                .foregroundColor(AppColors.text)

                            Text("\(awayGoals)")
                                .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                                .foregroundColor(AppColors.text)
                        }
                    }
                }
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.top, AppLayout.spacingM)
                .padding(.bottom, AppLayout.spacingS)
            }
            .background(AppColors.tertiaryBackground)

            Divider()

            // Bet type categories
            VStack(spacing: AppLayout.spacingS) {
                ForEach(viewModel.availableCategories, id: \.self) { category in
                    if let betTypeIds = viewModel.availableBetTypesByCategory[category] {
                        BetTypeCategoryView(
                            fixture: fixture,
                            category: category,
                            betTypeIds: betTypeIds,
                            viewModel: viewModel,
                            isLive: true
                        )
                    }
                }
            }
            .padding(.horizontal, AppLayout.spacingS)
            .padding(.vertical, AppLayout.spacingXS)

            // Last updated info
            if let odds = viewModel.odds {
                Divider()

                VStack {
                    LastUpdatedView(updateDate: odds.update)
                        .padding(.horizontal, AppLayout.spacingM)
                        .padding(.vertical, AppLayout.spacingS)
                }
                .background(AppColors.tertiaryBackground)
            }
        }
        .background(AppColors.tertiaryBackground)
    }
}

// MARK: - Bookmaker Selector View
struct BookmakerSelectorView: View {
    @ObservedObject var viewModel: OddsViewModel

    var body: some View {
        AccordionMenu(
            isExpanded: $viewModel.isBookmakerDropdownOpen,
            title: "Bookmaker",
            placeholder: "Select a bookmaker",
            options: viewModel.availableBookmakers,
            selectedOption: viewModel.selectedBookmaker,
            onSelect: { bookmaker in
                viewModel.selectBookmaker(bookmaker)
            },
            optionLabel: { $0.name },
            optionContent: { bookmaker in
                Text(bookmaker.name)
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(AppColors.text)
            }
        )
    }
}





// MARK: - Match Winner Odds View
struct MatchWinnerOddsView: View {
    let fixture: Fixture
    let values: [BetValue]

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            ForEach(values) { value in
                OddsButton(
                    label: get1X2Label(for: value.value),
                    teamName: getTeamName(for: value.value),
                    odd: value.formattedOdd
                )
            }
        }
    }

    // Helper to get 1X2 notation
    private func get1X2Label(for value: String) -> String {
        switch value.lowercased() {
        case "home":
            return "1"
        case "draw":
            return "X"
        case "away":
            return "2"
        default:
            return value
        }
    }

    // Helper to get team name based on value
    private func getTeamName(for value: String) -> String {
        switch value.lowercased() {
        case "home":
            return fixture.teams.home?.name ?? "Home"
        case "draw":
            return "Draw"
        case "away":
            return fixture.teams.away?.name ?? "Away"
        default:
            return value
        }
    }
}

// MARK: - Live Match Winner Odds View
struct LiveMatchWinnerOddsView: View {
    let fixture: Fixture
    let values: [LiveBetValue]

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            ForEach(values) { value in
                OddsButton(
                    label: get1X2Label(for: value.value),
                    teamName: getTeamName(for: value.value),
                    odd: value.formattedOdd,
                    isSuspended: value.suspended ?? false
                )
            }
        }
    }

    // Helper to get 1X2 notation
    private func get1X2Label(for value: String) -> String {
        switch value.lowercased() {
        case "home":
            return "1"
        case "draw":
            return "X"
        case "away":
            return "2"
        default:
            return value
        }
    }

    // Helper to get team name based on value
    private func getTeamName(for value: String) -> String {
        switch value.lowercased() {
        case "home":
            return fixture.teams.home?.name ?? "Home"
        case "draw":
            return "Draw"
        case "away":
            return fixture.teams.away?.name ?? "Away"
        default:
            return value
        }
    }
}

// MARK: - Over/Under Odds View
struct OverUnderOddsView: View {
    let values: [BetValue]

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            ForEach(values) { value in
                OddsButton(
                    label: getLabel(for: value),
                    odd: value.formattedOdd
                )
            }
        }
    }

    // Helper to get label with handicap
    private func getLabel(for value: BetValue) -> String {
        if let handicap = value.handicap {
            // Convert "1" to "Over" and "2" to "Under" for better readability
            if value.value == "1" {
                return "Over \(handicap)"
            } else if value.value == "2" {
                return "Under \(handicap)"
            } else {
                return "\(value.value) \(handicap)"
            }
        }
        return value.value
    }
}

// MARK: - Live Over/Under Odds View
struct LiveOverUnderOddsView: View {
    let values: [LiveBetValue]

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            ForEach(values) { value in
                OddsButton(
                    label: getLabel(for: value),
                    odd: value.formattedOdd,
                    isSuspended: value.suspended ?? false
                )
            }
        }
    }

    // Helper to get label with handicap
    private func getLabel(for value: LiveBetValue) -> String {
        if let handicap = value.handicap {
            // Convert "1" to "Over" and "2" to "Under" for better readability
            if value.value == "1" {
                return "Over \(handicap)"
            } else if value.value == "2" {
                return "Under \(handicap)"
            } else {
                return "\(value.value) \(handicap)"
            }
        }
        return value.value
    }
}

// MARK: - Generic Odds View
struct GenericOddsView: View {
    let values: [BetValue]

    var body: some View {
        LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: AppLayout.spacingM) {
            ForEach(values) { value in
                OddsButton(
                    label: getLabel(for: value),
                    odd: value.formattedOdd
                )
            }
        }
    }

    // Helper to get label with handicap
    private func getLabel(for value: BetValue) -> String {
        // Special handling for corners over/under (bet type 45)
        // Convert "1" to "Over" and "2" to "Under"
        if let handicap = value.handicap {
            if value.value == "1" {
                return "Over \(handicap)"
            } else if value.value == "2" {
                return "Under \(handicap)"
            } else {
                return "\(value.value) \(handicap)"
            }
        }
        return value.value
    }
}

// MARK: - Live Generic Odds View
struct LiveGenericOddsView: View {
    let values: [LiveBetValue]

    var body: some View {
        LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: AppLayout.spacingM) {
            ForEach(values) { value in
                OddsButton(
                    label: getLabel(for: value),
                    odd: value.formattedOdd,
                    isSuspended: value.suspended ?? false
                )
            }
        }
    }

    // Helper to get label with handicap
    private func getLabel(for value: LiveBetValue) -> String {
        // Special handling for corners over/under (bet type 45)
        // Convert "1" to "Over" and "2" to "Under"
        if let handicap = value.handicap {
            if value.value == "1" {
                return "Over \(handicap)"
            } else if value.value == "2" {
                return "Under \(handicap)"
            } else {
                return "\(value.value) \(handicap)"
            }
        }
        return value.value
    }
}

// MARK: - Odds Button
struct OddsButton: View {
    let label: String
    var teamName: String? = nil
    let odd: String
    var isSuspended: Bool = false

    var body: some View {
        VStack(spacing: AppLayout.spacingXS) {
            // Main label (1, X, 2 for match winner)
            Text(label)
                .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                .foregroundColor(isSuspended ? AppColors.secondaryText : AppColors.text)
                .lineLimit(1)
                .minimumScaleFactor(0.8)
                .multilineTextAlignment(.center)

            // Team name if provided
            if let teamName = teamName {
                Text(teamName)
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(AppColors.secondaryText)
                    .lineLimit(1)
                    .minimumScaleFactor(0.7)
                    .multilineTextAlignment(.center)
            }

            // Odds value
            Text(isSuspended ? "Suspended" : odd)
                .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                .foregroundColor(isSuspended ? AppColors.secondaryText : AppColors.Brand.primary)
                .padding(.vertical, 4)
                .padding(.horizontal, 12)
                .background(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                        .fill(isSuspended ? Color.clear : AppColors.Brand.primary.opacity(0.1))
                )
                .padding(.top, 4)
        }
        .frame(maxWidth: .infinity)
        .padding(AppLayout.spacingS)
        .background(
            RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                .fill(isSuspended ? Color(UIColor.tertiarySystemFill) : Color(UIColor.secondarySystemBackground))
                .shadow(color: Color.black.opacity(isSuspended ? 0.0 : 0.05), radius: 3, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                .stroke(isSuspended ? Color.clear : Color(UIColor.systemGray5), lineWidth: 0.5)
        )
        .opacity(isSuspended ? 0.7 : 1.0)
    }
}

// MARK: - Last Updated View
struct LastUpdatedView: View {
    let updateDate: Date

    var body: some View {
        HStack {
            Spacer()

            Text("Last updated: \(formattedDate)")
                .font(AppTypography.dynamicFont(style: .caption))
                .foregroundColor(AppColors.secondaryText)
        }
    }

    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter.string(from: updateDate)
    }
}

// MARK: - Disclaimer View
struct OddsDisclaimerView: View {
    var body: some View {
        VStack(spacing: 0) {
            Divider()

            VStack(spacing: AppLayout.spacingS) {
                Text("Disclaimer")
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                    .foregroundColor(AppColors.text)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Text("Odds displayed are for informational purposes only. Please check with bookmakers for the latest odds before placing any bets. Betting may not be legal in your jurisdiction.")
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(AppColors.secondaryText)
                    .fixedSize(horizontal: false, vertical: true)
            }
            .padding(AppLayout.spacingM)
            .background(AppColors.tertiaryBackground)
        }
        .background(AppColors.tertiaryBackground)
    }
}

// MARK: - Loading, Error, and Empty Views
// OddsLoadingView has been replaced by SimpleSkeletonListView

struct OddsErrorView: View {
    let message: String

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "wifi.exclamationmark")
                .font(.system(size: AppLayout.iconSizeL))
                .foregroundColor(AppColors.warning)
                .padding(.bottom, AppLayout.spacingS)

            Text("Connection Issue")
                .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                .foregroundColor(AppColors.text)

            Text("We're having trouble loading the odds data")
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingS)

            Button(action: {
                // This is a placeholder for refresh functionality
                // The actual refresh is handled by the parent view's refreshable modifier
            }) {
                Label("Try Again", systemImage: "arrow.clockwise")
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                    .foregroundColor(AppColors.Brand.primary)
                    .padding(.vertical, 8)
                    .padding(.horizontal, 16)
                    .background(
                        RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                            .fill(AppColors.Brand.primary.opacity(0.1))
                    )
            }
            .padding(.top, AppLayout.spacingS)
        }
        .frame(maxWidth: .infinity, minHeight: 250)
        .padding(AppLayout.spacingL)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusM)
        .padding(.horizontal, AppLayout.spacingM)
    }
}

struct OddsEmptyView: View {
    let message: String

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "ticket")
                .font(.system(size: AppLayout.iconSizeL))
                .foregroundColor(AppColors.Brand.primary.opacity(0.8))
                .padding(.bottom, AppLayout.spacingS)

            Text("Odds Coming Soon")
                .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                .foregroundColor(AppColors.text)

            Text("We're working on getting the latest odds for this match")
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingS)

            VStack(spacing: AppLayout.spacingXS) {
                Text("Odds typically become available:")
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(AppColors.secondaryText)
                    .multilineTextAlignment(.center)

                HStack(spacing: AppLayout.spacingXS) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(AppColors.success)
                        .font(.system(size: 12))

                    Text("24-48 hours before match time for major leagues")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                }

                HStack(spacing: AppLayout.spacingXS) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(AppColors.success)
                        .font(.system(size: 12))

                    Text("A few hours before kickoff for other matches")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                }
            }
            .padding(.top, AppLayout.spacingS)
        }
        .frame(maxWidth: .infinity, minHeight: 250)
        .padding(AppLayout.spacingL)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusM)
        .padding(.horizontal, AppLayout.spacingM)
    }
}

// MARK: - Preview
#Preview {
    OddsView(fixture: Fixture.mock)
}

#Preview("Live Odds") {
    OddsView(fixture: Fixture.mockLive)
}
