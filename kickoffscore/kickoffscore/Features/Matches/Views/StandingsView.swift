import SwiftUI
import King<PERSON>er

struct StandingsView: View {
    let fixture: Fixture
    @StateObject private var viewModel: StandingsViewModel

    init(fixture: Fixture) {
        self.fixture = fixture
        self._viewModel = StateObject(wrappedValue: StandingsViewModel(fixture: fixture))
    }

    var body: some View {
        VStack(spacing: 0) {
            // Controls row with filter and view mode
            ZStack {
                if !viewModel.isLoading && viewModel.errorMessage == nil && !viewModel.standings.isEmpty {
                    VStack(spacing: 0) {
                        // Add smaller padding at the top
                        Spacer()
                            .frame(height: AppLayout.spacingS)

                        HStack {
                            // Filter segmented control
                            Picker("Filter", selection: $viewModel.selectedFilter) {
                                ForEach(StandingsFilter.allCases, id: \.self) { filter in
                                    Text(filter.rawValue).tag(filter)
                                }
                            }
                            .pickerStyle(SegmentedPickerStyle())
                            .onChange(of: viewModel.selectedFilter) { newValue in
                                viewModel.setFilter(newValue)
                            }

                            Spacer()

                            // Native pull-down menu
                            Menu {
                                ForEach(StandingsViewMode.allCases, id: \.self) { mode in
                                    Button(action: {
                                        viewModel.selectedViewMode = mode
                                    }) {
                                        HStack {
                                            Text(mode.rawValue)

                                            if viewModel.selectedViewMode == mode {
                                                Spacer()
                                                Image(systemName: "checkmark")
                                            }
                                        }
                                    }
                                }
                            } label: {
                                HStack {
                                    Text(viewModel.selectedViewMode.rawValue)
                                        .font(AppTypography.dynamicFont(style: .subheadline))
                                        .foregroundColor(Color(UIColor.systemBackground))

                                    Spacer()

                                    Image(systemName: "chevron.down")
                                        .font(.system(size: 10, weight: .semibold))
                                        .foregroundColor(Color(UIColor.systemBackground))
                                }
                                .padding(.horizontal, AppLayout.spacingS)
                                .padding(.vertical, 7)
                                .frame(width: 100, height: 32)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(AppColors.Brand.primary)
                                )
                            }
                        }
                        .padding(.horizontal, AppLayout.spacingM)

                        // Add smaller gap between controls and standings container
                        Spacer()
                            .frame(height: AppLayout.spacingS)
                    }
                }
            }
            .zIndex(100) // Higher z-index to ensure controls are above content

            // Main content
            ScrollView(showsIndicators: false) {
                VStack(spacing: AppLayout.spacingM) {
                    if viewModel.isLoading {
                        SimpleSkeletonListView(itemCount: 12, showHeader: true)
                    } else if let errorMessage = viewModel.errorMessage {
                        StandingsErrorView(message: errorMessage)
                    } else if !viewModel.standings.isEmpty {
                        ForEach(Array(viewModel.filteredStandings.enumerated()), id: \.offset) { index, group in
                            CombinedStandingsView(
                                standings: group,
                                homeTeamId: fixture.teams.home?.id,
                                awayTeamId: fixture.teams.away?.id,
                                viewModel: viewModel
                            )
                        }
                    } else {
                        StandingsEmptyView(message: "No standings available for this league")
                    }

                    // Single legend section at the end of all tables
                    if !viewModel.isLoading && viewModel.errorMessage == nil && !viewModel.standings.isEmpty {
                        StandingsLegendView()
                            .environmentObject(viewModel)
                            .padding(.top, AppLayout.spacingM)
                    }
                }
                .padding(.top, AppLayout.spacingS) // Consistent with other tab views
                .padding(.bottom, AppLayout.spacingM)
                .padding(.horizontal, 0) // Remove horizontal padding for edge-to-edge
            }
        }
        .background(AppColors.background)
        .edgesIgnoringSafeArea(.horizontal) // Ignore safe area for edge-to-edge
        .onAppear {
            viewModel.fetchStandings()
        }
    }
}

// MARK: - Removed StandingsTableView as it's replaced by CombinedStandingsView

// MARK: - Standing Row
struct StandingRow: View {
    let standing: TeamStanding
    let isHomeTeam: Bool
    let isAwayTeam: Bool
    let filter: StandingsFilter
    let viewMode: StandingsViewMode

    var body: some View {
        HStack(spacing: 4) { // Minimal spacing between elements
            // Rank with qualification/relegation indicator and status arrow
            ZStack(alignment: .leading) {
                // Qualification/relegation indicator
                if let indicatorColor = qualificationIndicatorColor {
                    Rectangle()
                        .fill(indicatorColor)
                        .frame(width: 4, height: 24)
                        .padding(.leading, -4) // Align to the left edge
                }

                // Rank number with status indicator
                VStack(spacing: 0) {
                    // Top status indicator
                    topStatusIndicator

                    // Rank number
                    Text("\(standing.rank)")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                        .foregroundColor(textColor)

                    // Bottom status indicator
                    bottomStatusIndicator
                }
                .frame(width: 30, alignment: .center)
            }

            // Team
            HStack(spacing: 6) { // Slightly increased spacing between logo and name
                // Team logo
                if let logoUrl = standing.team.logo {
                    KFImage(URL(string: logoUrl))
                        .placeholder {
                            Image(systemName: "shield")
                                .foregroundColor(AppColors.secondaryText)
                        }
                        .resizable()
                        .scaledToFit()
                        .frame(width: 24, height: 24)
                }

                // Team name
                Text(standing.team.name)
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: isHighlighted ? .semibold : .regular))
                    .foregroundColor(textColor)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // Stats group with consistent spacing
            statsView
        }
    }

    // Determine the color for the qualification/relegation indicator
    private var qualificationIndicatorColor: Color? {
        guard let description = standing.description?.lowercased() else {
            return nil
        }

        // European competitions (highest priority)
        if description.contains("champions league") {
            return Color(hex: "28B13C") // Green for Champions League
        } else if description.contains("europa league") {
            return Color(hex: "004E82") // Blue for Europa League
        } else if description.contains("conference") {
            return Color(hex: "3498db") // Light blue for Conference League
        }

        // South American competitions (CONMEBOL)
        else if description.contains("libertadores") && !description.contains("qualifiers") {
            return Color(hex: "28B13C") // Green for CONMEBOL Libertadores
        } else if description.contains("libertadores") && description.contains("qualifiers") {
            return Color(hex: "004E82") // Blue for CONMEBOL Libertadores Qualifiers
        } else if description.contains("sudamericana") && description.contains("group stage") {
            return Color(hex: "e67e22") // Orange for CONMEBOL Sudamericana Group Stage
        } else if description.contains("sudamericana") {
            return Color(hex: "3498db") // Light blue for CONMEBOL Sudamericana
        }

        // Promotion/relegation (only if not already covered by European/CONMEBOL competitions)
        else if description.contains("relegation") {
            return Color(hex: "E74C3C") // Red for relegation
        } else if description.contains("promotion") &&
                  !description.contains("champions league") &&
                  !description.contains("europa league") &&
                  !description.contains("conference") &&
                  !description.contains("libertadores") &&
                  !description.contains("sudamericana") {
            return Color(hex: "28B13C") // Green for promotion (only if not related to continental competitions)
        } else if description.contains("play-off") {
            return Color(hex: "F39C12") // Orange for promotion play-off
        }

        return nil
    }

    // Helper methods to get stats based on the filter
    private func getMatchesPlayed() -> Int {
        switch filter {
        case .overall:
            return standing.all.played ?? 0
        case .home:
            return standing.home.played ?? 0
        case .away:
            return standing.away.played ?? 0
        }
    }

    private func getWins() -> Int {
        switch filter {
        case .overall:
            return standing.all.win ?? 0
        case .home:
            return standing.home.win ?? 0
        case .away:
            return standing.away.win ?? 0
        }
    }

    private func getDraws() -> Int {
        switch filter {
        case .overall:
            return standing.all.draw ?? 0
        case .home:
            return standing.home.draw ?? 0
        case .away:
            return standing.away.draw ?? 0
        }
    }

    private func getLosses() -> Int {
        switch filter {
        case .overall:
            return standing.all.lose ?? 0
        case .home:
            return standing.home.lose ?? 0
        case .away:
            return standing.away.lose ?? 0
        }
    }

    private func getGoalsFor() -> Int {
        switch filter {
        case .overall:
            return standing.all.goals?.for_ ?? 0
        case .home:
            return standing.home.goals?.for_ ?? 0
        case .away:
            return standing.away.goals?.for_ ?? 0
        }
    }

    private func getGoalsAgainst() -> Int {
        switch filter {
        case .overall:
            return standing.all.goals?.against ?? 0
        case .home:
            return standing.home.goals?.against ?? 0
        case .away:
            return standing.away.goals?.against ?? 0
        }
    }

    private func getGoalDifference() -> Int {
        switch filter {
        case .overall:
            return standing.goalsDiff
        case .home:
            return (standing.home.goals?.for_ ?? 0) - (standing.home.goals?.against ?? 0)
        case .away:
            return (standing.away.goals?.for_ ?? 0) - (standing.away.goals?.against ?? 0)
        }
    }

    private func getPoints() -> Int {
        switch filter {
        case .overall:
            return standing.points
        case .home:
            return (standing.home.win ?? 0) * 3 + (standing.home.draw ?? 0)
        case .away:
            return (standing.away.win ?? 0) * 3 + (standing.away.draw ?? 0)
        }
    }

    // Stats view
    private var statsView: some View {
        Group {
            switch viewMode {
            case .short:
                shortStatsView
            case .full:
                fullStatsView
            case .form:
                formView
            }
        }
    }

    // Short stats view (P, GD, Pts)
    private var shortStatsView: some View {
        HStack(spacing: AppLayout.spacingS) {
            // Played - changes based on filter
            Text("\(getMatchesPlayed())")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)
                .frame(width: 30, alignment: .center)

            // Goal Difference - changes based on filter
            let goalDiff = getGoalDifference()
            let goalDiffPrefix = goalDiff > 0 ? "+" : ""
            Text("\(goalDiffPrefix)\(goalDiff)")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)
                .frame(width: 40, alignment: .center)

            // Points - changes based on filter
            Text("\(getPoints())")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(textColor)
                .frame(width: 40, alignment: .center)
        }
    }

    // Full stats view (P, W-D-L, GD, +/-, Pts)
    private var fullStatsView: some View {
        HStack(spacing: 4) { // Reduced spacing between columns
            // Played
            Text("\(getMatchesPlayed())")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)
                .frame(width: 20, alignment: .center)

            // W-D-L
            HStack(spacing: 2) {
                Text("\(getWins())")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                    .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)
                    .frame(width: 16, alignment: .center)

                Text("-")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                    .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)

                Text("\(getDraws())")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                    .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)
                    .frame(width: 16, alignment: .center)

                Text("-")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                    .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)

                Text("\(getLosses())")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                    .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)
                    .frame(width: 16, alignment: .center)
            }

            // Goal Difference
            let goalDiff = getGoalDifference()
            let goalDiffPrefix = goalDiff > 0 ? "+" : ""
            Text("\(goalDiffPrefix)\(goalDiff)")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)
                .frame(width: 30, alignment: .center)

            // Goals For/Against
            Text("\(getGoalsFor()):\(getGoalsAgainst())")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(isHighlighted ? AppColors.secondaryText : AppColors.secondaryText)
                .frame(width: 45, alignment: .center)

            // Points
            Text("\(getPoints())")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(textColor)
                .frame(width: 25, alignment: .center)
        }
    }

    // Form view (last 5 games)
    private var formView: some View {
        HStack(spacing: 8) { // Increased spacing between form indicators
            if let form = standing.form {
                // Reverse the form to show oldest to newest (left to right)
                let reversedForm = String(form.prefix(5)).reversed()

                ForEach(Array(reversedForm.enumerated()), id: \.offset) { index, result in
                    // Add border to the latest match (rightmost) instead of underline
                    if index == reversedForm.count - 1 {
                        // Latest match with border
                        latestFormIndicator(for: String(result))
                    } else {
                        // Regular form indicator
                        formIndicator(for: String(result))
                    }
                }
            } else {
                Text("No form data")
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(AppColors.secondaryText)
            }
        }
    }

    // Regular form indicator for W/D/L
    private func formIndicator(for result: String) -> some View {
        let (backgroundColor, textColor) = formColors(for: result)

        return Text(result)
            .font(.system(size: 13, weight: .bold)) // Increased font size
            .foregroundColor(textColor)
            .frame(width: 22, height: 22) // Increased size
            .background(backgroundColor)
            .cornerRadius(4) // Square with small radius
    }

    // Latest form indicator with border
    private func latestFormIndicator(for result: String) -> some View {
        let (backgroundColor, textColor) = formColors(for: result)

        return Text(result)
            .font(.system(size: 13, weight: .bold)) // Increased font size
            .foregroundColor(textColor)
            .frame(width: 22, height: 22) // Increased size
            .background(backgroundColor)
            .cornerRadius(4) // Square with small radius
            .padding(2) // Add small gap between indicator and border
            .overlay(
                // Add light gray border with gap
                RoundedRectangle(cornerRadius: 6) // Slightly larger corner radius for border
                    .stroke(Color.gray, lineWidth: 1.5)
            )
    }

    // Colors for form indicators
    private func formColors(for result: String) -> (Color, Color) {
        switch result.uppercased() {
        case "W":
            return (Color.green, Color.white)
        case "D":
            return (Color(UIColor.lightGray), Color.white) // Light gray for draws
        case "L":
            return (Color.red, Color.white)
        default:
            return (Color.gray, Color.white)
        }
    }

    // Status indicator views
    private var topStatusIndicator: some View {
        Group {
            if let status = standing.status?.lowercased(), status == "up" {
                Image(systemName: "chevron.up")
                    .font(.system(size: 8, weight: .bold))
                    .foregroundColor(.green)
                    .frame(height: 8)
            } else {
                // Empty space for same or unknown status
                Color.clear.frame(height: 8)
            }
        }
    }

    private var bottomStatusIndicator: some View {
        Group {
            if let status = standing.status?.lowercased() {
                if status == "down" {
                    Image(systemName: "chevron.down")
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(.red)
                        .frame(height: 8)
                } else if status == "same" {
                    // Small dash for "same" status (only at bottom)
                    Rectangle()
                        .fill(Color(UIColor.secondaryLabel))
                        .frame(width: 8, height: 2)
                        .frame(height: 8)
                } else {
                    // Empty space for other statuses
                    Color.clear.frame(height: 8)
                }
            } else {
                // Empty space if no status
                Color.clear.frame(height: 8)
            }
        }
    }

    // Helper computed properties
    private var isHighlighted: Bool {
        isHomeTeam || isAwayTeam
    }

    private var textColor: Color {
        isHighlighted ? Color.white : Color(UIColor.label)
    }
}

// MARK: - Loading View
// StandingsLoadingView has been replaced by SimpleSkeletonListView

// MARK: - Error View
private struct StandingsErrorView: View {
    let message: String

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: AppLayout.iconSizeL))
                .foregroundColor(Color.red)

            Text("Unable to load standings")
                .font(AppTypography.dynamicFont(style: .headline))
                .foregroundColor(Color(UIColor.label))

            Text(message)
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(Color(UIColor.secondaryLabel))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .padding(AppLayout.spacingL)
    }
}

// MARK: - Empty View
private struct StandingsEmptyView: View {
    let message: String

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "table")
                .font(.system(size: AppLayout.iconSizeL))
                .foregroundColor(AppColors.secondaryText)

            Text("No Standings Available")
                .font(AppTypography.dynamicFont(style: .headline))
                .foregroundColor(Color(UIColor.label))

            Text(message)
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(Color(UIColor.secondaryLabel))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .padding(AppLayout.spacingL)
    }
}

// MARK: - Legend View
private struct StandingsLegendView: View {
    // Get the standings from the parent view
    @EnvironmentObject private var viewModel: StandingsViewModel

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingM) {
            // Abbreviations section
            VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                Text("Abbreviations")
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                    .foregroundColor(Color(UIColor.label))

                HStack(spacing: AppLayout.spacingL) {
                    // First column
                    VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                        LegendItem(key: "P", value: "Matches Played")
                        LegendItem(key: "W", value: "Won")
                        LegendItem(key: "D", value: "Drawn")
                    }

                    // Second column
                    VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                        LegendItem(key: "L", value: "Lost")
                        LegendItem(key: "GD", value: "Goal Difference")
                        LegendItem(key: "Pts", value: "Points")
                    }
                }
            }

            // Only show qualification section if there are any qualification statuses
            if hasQualificationStatuses {
                Divider()
                    .background(AppColors.separator)

                // Qualification indicators section
                VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                    Text("Qualification & Relegation")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                        .foregroundColor(Color(UIColor.label))

                    VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                        // European competitions
                        if hasChampionsLeague {
                            QualificationLegendItem(color: Color(hex: "28B13C"), text: "Champions League")
                        }

                        if hasEuropaLeague {
                            QualificationLegendItem(color: Color(hex: "004E82"), text: "Europa League")
                        }

                        if hasConferenceLeague {
                            QualificationLegendItem(color: Color(hex: "3498db"), text: "Conference League")
                        }

                        // South American competitions (CONMEBOL)
                        if hasLibertadores {
                            QualificationLegendItem(color: Color(hex: "28B13C"), text: "CONMEBOL Libertadores")
                        }

                        if hasLibertadoresQualifiers {
                            QualificationLegendItem(color: Color(hex: "004E82"), text: "CONMEBOL Libertadores Qualifiers")
                        }

                        if hasSudamericana {
                            QualificationLegendItem(color: Color(hex: "3498db"), text: "CONMEBOL Sudamericana")
                        }

                        if hasSudamericanaGroupStage {
                            QualificationLegendItem(color: Color(hex: "e67e22"), text: "CONMEBOL Sudamericana Group Stage")
                        }

                        // Promotion/relegation
                        if hasPromotion {
                            QualificationLegendItem(color: Color(hex: "28B13C"), text: "Promotion")
                        }

                        if hasPlayoff {
                            QualificationLegendItem(color: Color(hex: "F39C12"), text: "Promotion Play-off")
                        }

                        if hasRelegation {
                            QualificationLegendItem(color: Color(hex: "E74C3C"), text: "Relegation")
                        }
                    }
                }
            }

            // Status movement section
            Divider()
                .background(AppColors.separator)

            VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                Text("Position Movement")
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                    .foregroundColor(Color(UIColor.label))

                HStack(spacing: AppLayout.spacingL) {
                    StatusLegendItem(systemImageName: "chevron.up", color: .green, text: "Moving Up")
                    StatusLegendItem(systemImageName: "chevron.down", color: .red, text: "Moving Down")
                    StatusLegendItem(isSmallRectangle: true, color: Color(UIColor.secondaryLabel), text: "No Change")
                }
            }
        }
        .padding(AppLayout.spacingM)
        .background(AppColors.tertiaryBackground)
        .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
    }

    // Helper computed properties to check which statuses are present
    // European competitions
    private var hasChampionsLeague: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            standing.description?.lowercased().contains("champions league") ?? false
        }
    }

    private var hasEuropaLeague: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            standing.description?.lowercased().contains("europa league") ?? false
        }
    }

    private var hasConferenceLeague: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            standing.description?.lowercased().contains("conference") ?? false
        }
    }

    // South American competitions (CONMEBOL)
    private var hasLibertadores: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            if let description = standing.description?.lowercased() {
                return description.contains("libertadores") && !description.contains("qualifiers")
            }
            return false
        }
    }

    private var hasLibertadoresQualifiers: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            if let description = standing.description?.lowercased() {
                return description.contains("libertadores") && description.contains("qualifiers")
            }
            return false
        }
    }

    private var hasSudamericana: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            if let description = standing.description?.lowercased() {
                return description.contains("sudamericana") && !description.contains("group stage")
            }
            return false
        }
    }

    private var hasSudamericanaGroupStage: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            if let description = standing.description?.lowercased() {
                return description.contains("sudamericana") && description.contains("group stage")
            }
            return false
        }
    }

    // Promotion/relegation
    private var hasPromotion: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            if let description = standing.description?.lowercased() {
                // Only show promotion if it's not related to European competitions
                return description.contains("promotion") &&
                       !description.contains("play-off") &&
                       !description.contains("champions league") &&
                       !description.contains("europa league") &&
                       !description.contains("conference") &&
                       !description.contains("libertadores") &&
                       !description.contains("sudamericana")
            }
            return false
        }
    }

    private var hasPlayoff: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            standing.description?.lowercased().contains("play-off") ?? false
        }
    }

    private var hasRelegation: Bool {
        return viewModel.standings.flatMap { $0 }.contains { standing in
            standing.description?.lowercased().contains("relegation") ?? false
        }
    }

    private var hasQualificationStatuses: Bool {
        return hasChampionsLeague || hasEuropaLeague || hasConferenceLeague ||
               hasLibertadores || hasLibertadoresQualifiers ||
               hasSudamericana || hasSudamericanaGroupStage ||
               hasPromotion || hasPlayoff || hasRelegation
    }
}

// Helper view for abbreviation legend items
private struct LegendItem: View {
    let key: String
    let value: String

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            Text(key)
                .font(AppTypography.dynamicFont(style: .caption, weight: .bold))
                .foregroundColor(AppColors.secondaryText)
                .frame(width: 30, alignment: .leading)

            Text(value)
                .font(AppTypography.dynamicFont(style: .caption))
                .foregroundColor(Color(UIColor.label))
        }
    }
}

// Helper view for qualification legend items
private struct QualificationLegendItem: View {
    let color: Color
    let text: String

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            Rectangle()
                .fill(color)
                .frame(width: 12, height: 12)

            Text(text)
                .font(AppTypography.dynamicFont(style: .caption))
                .foregroundColor(Color(UIColor.label))
        }
    }
}

// Helper view for status movement legend items
private struct StatusLegendItem: View {
    var imageName: String? = nil
    var systemImageName: String? = nil
    var isRectangle: Bool = false
    var isSmallRectangle: Bool = false
    var color: Color = .primary
    let text: String

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            if let systemImageName = systemImageName {
                Image(systemName: systemImageName)
                    .font(.system(size: 10, weight: .bold))
                    .foregroundColor(color)
                    .frame(width: 12, height: 12)
            } else if let imageName = imageName {
                Image(imageName)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 12, height: 12)
            } else if isSmallRectangle {
                // Small rectangle for "same" status
                Rectangle()
                    .fill(color)
                    .frame(width: 8, height: 2)
                    .frame(width: 12, height: 12)
            } else if isRectangle {
                Rectangle()
                    .fill(color)
                    .frame(width: 12, height: 2)
            }

            Text(text)
                .font(AppTypography.dynamicFont(style: .caption))
                .foregroundColor(Color(UIColor.label))
        }
    }
}

// MARK: - Combined Standings View
struct CombinedStandingsView: View {
    let standings: [TeamStanding]
    let homeTeamId: Int?
    let awayTeamId: Int?
    @ObservedObject var viewModel: StandingsViewModel

    // Get the group name from the first team in the standings - only for group tables
    private var groupName: String? {
        if let firstTeam = standings.first, let group = firstTeam.group {
            // Only show group name for cup competitions with groups
            if group.contains("Group") {
                if let range = group.range(of: "Group\\s+[A-Z]", options: .regularExpression) {
                    return String(group[range])
                }
            }
        }
        return nil
    }

    var body: some View {
        VStack(spacing: 0) {

            // Standings Table Section
            VStack(spacing: 0) {
                // Add padding at the top of the container
                Spacer()
                    .frame(height: AppLayout.spacingS)

                // Column headers
                HStack(spacing: AppLayout.spacingS) {
                    // Team column with group name if available, starting from the rank position
                    if let groupName = groupName {
                        HStack(spacing: 0) {
                            Text(groupName)
                                .font(AppTypography.scaledFont(size: 15, weight: .semibold))
                                .foregroundColor(.white)
                                .padding(.leading, 4) // Align with ranking numbers
                            Spacer()
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                    } else {
                        Text("")
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }

                    // Stats headers with consistent spacing - changes based on view mode
                    Group {
                        switch viewModel.selectedViewMode {
                        case .short:
                            HStack(spacing: AppLayout.spacingS) {
                                Text("P")
                                    .frame(width: 30, alignment: .center)

                                Text("GD")
                                    .frame(width: 40, alignment: .center)

                                Text("Pts")
                                    .frame(width: 40, alignment: .center)
                            }
                        case .full:
                            HStack(spacing: 4) {
                                Text("P")
                                    .frame(width: 20, alignment: .center)

                                Text("W-D-L")
                                    .frame(width: 70, alignment: .center)

                                Text("GD")
                                    .frame(width: 30, alignment: .center)

                                Text("+/-")
                                    .frame(width: 45, alignment: .center)

                                Text("Pts")
                                    .frame(width: 25, alignment: .center)
                            }
                        case .form:
                            HStack(spacing: AppLayout.spacingS) {
                                Text("Form")
                                    .frame(width: 120, alignment: .center)
                            }
                        }
                    }
                }
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(AppColors.secondaryText)
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.vertical, AppLayout.spacingXS)
                .background(AppColors.tertiaryBackground)

                // Divider below column headers
                Divider()
                    .background(AppColors.separator)
                    .padding(.horizontal, AppLayout.spacingM)

                // Team rows
                ForEach(Array(standings.enumerated()), id: \.element.id) { index, standing in
                    let isHomeTeam = standing.team.id == homeTeamId
                    let isAwayTeam = standing.team.id == awayTeamId
                    let currentFilter = viewModel.selectedFilter

                    VStack(spacing: 0) {
                        StandingRow(
                            standing: standing,
                            isHomeTeam: isHomeTeam,
                            isAwayTeam: isAwayTeam,
                            filter: currentFilter,
                            viewMode: viewModel.selectedViewMode
                        )
                        .padding(.horizontal, AppLayout.spacingM)
                        .padding(.vertical, AppLayout.spacingXS) // Reduced vertical padding for smaller row height
                        .background(rowBackground(for: standing))

                        // Add divider after each row except the last one
                        if index < standings.count - 1 {
                            Divider()
                                .background(AppColors.separator)
                                .padding(.horizontal, AppLayout.spacingM)
                        }
                    }
                }

                // No legend section for individual groups
            }
            // No padding at the bottom
        }
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
    }

    // Background color for the row based on team and promotion/relegation status
    private func rowBackground(for standing: TeamStanding) -> Color {
        if standing.team.id == homeTeamId || standing.team.id == awayTeamId {
            return Color(hex: "333333") // Dark gray background for teams in the fixture
        }

        return Color.clear // No background color for other teams
    }

    // Helper computed properties to check which statuses are present
    // European competitions
    private var hasChampionsLeague: Bool {
        return standings.contains { standing in
            standing.description?.lowercased().contains("champions league") ?? false
        }
    }

    private var hasEuropaLeague: Bool {
        return standings.contains { standing in
            standing.description?.lowercased().contains("europa league") ?? false
        }
    }

    private var hasConferenceLeague: Bool {
        return standings.contains { standing in
            standing.description?.lowercased().contains("conference") ?? false
        }
    }

    // South American competitions (CONMEBOL)
    private var hasLibertadores: Bool {
        return standings.contains { standing in
            if let description = standing.description?.lowercased() {
                return description.contains("libertadores") && !description.contains("qualifiers")
            }
            return false
        }
    }

    private var hasLibertadoresQualifiers: Bool {
        return standings.contains { standing in
            if let description = standing.description?.lowercased() {
                return description.contains("libertadores") && description.contains("qualifiers")
            }
            return false
        }
    }

    private var hasSudamericana: Bool {
        return standings.contains { standing in
            if let description = standing.description?.lowercased() {
                return description.contains("sudamericana") && !description.contains("group stage")
            }
            return false
        }
    }

    private var hasSudamericanaGroupStage: Bool {
        return standings.contains { standing in
            if let description = standing.description?.lowercased() {
                return description.contains("sudamericana") && description.contains("group stage")
            }
            return false
        }
    }

    // Promotion/relegation
    private var hasPromotion: Bool {
        return standings.contains { standing in
            if let description = standing.description?.lowercased() {
                // Only show promotion if it's not related to European competitions
                return description.contains("promotion") &&
                       !description.contains("play-off") &&
                       !description.contains("champions league") &&
                       !description.contains("europa league") &&
                       !description.contains("conference") &&
                       !description.contains("libertadores") &&
                       !description.contains("sudamericana")
            }
            return false
        }
    }

    private var hasPlayoff: Bool {
        return standings.contains { standing in
            standing.description?.lowercased().contains("play-off") ?? false
        }
    }

    private var hasRelegation: Bool {
        return standings.contains { standing in
            standing.description?.lowercased().contains("relegation") ?? false
        }
    }

    private var hasQualificationStatuses: Bool {
        return hasChampionsLeague || hasEuropaLeague || hasConferenceLeague ||
               hasLibertadores || hasLibertadoresQualifiers ||
               hasSudamericana || hasSudamericanaGroupStage ||
               hasPromotion || hasPlayoff || hasRelegation
    }
}

// MARK: - Preview
#Preview {
    StandingsView(fixture: Fixture.mock)
}
