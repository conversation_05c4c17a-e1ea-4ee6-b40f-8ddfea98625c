import SwiftUI

// MARK: - Bet Type Section View for Pre-Match Odds
struct BetTypeSectionView: View {
    let fixture: Fixture
    let betTypeId: Int
    @ObservedObject var viewModel: OddsViewModel

    var body: some View {
        NonCollapsibleSection(
            title: viewModel.getBetTypeName(for: betTypeId),
            content: {
                if let betData = viewModel.getBetData(for: betTypeId) {
                    // Different layouts based on bet type
                    if betTypeId == 1 { // Match Winner
                        MatchWinnerOddsView(fixture: fixture, values: betData.values)
                    } else if betTypeId == 12 { // Double Chance
                        ThreeColumnOddsView(fixture: fixture, values: betData.values)
                    } else if betTypeId == 2 || betTypeId == 45 { // Over/Under or Corners Over/Under
                        OverUnderOddsView(values: betData.values)
                    } else if betData.values.count <= 3 {
                        // Use three column layout for bet types with 3 or fewer options
                        ThreeColumnOddsView(fixture: fixture, values: betData.values)
                    } else {
                        // Generic layout for other bet types
                        GenericOddsView(values: betData.values)
                    }
                } else {
                    Text("No odds available for this bet type")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                        .padding()
                }
            }
        )
    }
}

// MARK: - Live Bet Type Section View
struct LiveBetTypeSectionView: View {
    let fixture: Fixture
    let betTypeId: Int
    @ObservedObject var viewModel: OddsViewModel

    var body: some View {
        NonCollapsibleSection(
            title: viewModel.getBetTypeName(for: betTypeId),
            content: {
                if let liveOddsData = viewModel.getLiveOddsData(for: betTypeId) {
                    // Different layouts based on bet type
                    if betTypeId == 1 { // Match Winner
                        LiveMatchWinnerOddsView(fixture: fixture, values: liveOddsData.values)
                    } else if betTypeId == 12 { // Double Chance
                        LiveThreeColumnOddsView(fixture: fixture, values: liveOddsData.values)
                    } else if betTypeId == 2 || betTypeId == 45 { // Over/Under or Corners Over/Under
                        LiveOverUnderOddsView(values: liveOddsData.values)
                    } else if liveOddsData.values.count <= 3 {
                        // Use three column layout for bet types with 3 or fewer options
                        LiveThreeColumnOddsView(fixture: fixture, values: liveOddsData.values)
                    } else {
                        // Generic layout for other bet types
                        LiveGenericOddsView(values: liveOddsData.values)
                    }
                } else {
                    Text("No live odds available for this bet type")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                        .padding()
                }
            }
        )
    }
}

// MARK: - Bet Type Category Section View
struct BetTypeCategoryView: View {
    let fixture: Fixture
    let category: String
    let betTypeIds: [Int]
    @ObservedObject var viewModel: OddsViewModel
    let isLive: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingM) {
            // Category header with background
            HStack {
                Text(category)
                    .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                    .foregroundColor(AppColors.text)

                Spacer()
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, AppLayout.spacingS)
            .background(
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                    .fill(Color(UIColor.secondarySystemBackground))
            )
            .padding(.horizontal, AppLayout.spacingM)

            // Bet type sections
            VStack(spacing: AppLayout.spacingS) {
                ForEach(betTypeIds, id: \.self) { betTypeId in
                    if isLive {
                        LiveBetTypeSectionView(
                            fixture: fixture,
                            betTypeId: betTypeId,
                            viewModel: viewModel
                        )
                    } else {
                        BetTypeSectionView(
                            fixture: fixture,
                            betTypeId: betTypeId,
                            viewModel: viewModel
                        )
                    }
                }
            }
            .padding(.horizontal, AppLayout.spacingS)
        }
        .padding(.vertical, AppLayout.spacingM)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusM)
    }
}

// MARK: - Preview
#Preview {
    BetTypeSectionView(
        fixture: Fixture.mock,
        betTypeId: 1,
        viewModel: OddsViewModel()
    )
    .padding()
    .background(AppColors.background)
}

#Preview("Live") {
    LiveBetTypeSectionView(
        fixture: Fixture.mockLive,
        betTypeId: 1,
        viewModel: OddsViewModel()
    )
    .padding()
    .background(AppColors.background)
}

#Preview("Category") {
    BetTypeCategoryView(
        fixture: Fixture.mock,
        category: "Match Outcome",
        betTypeIds: [1, 2, 12],
        viewModel: OddsViewModel(),
        isLive: false
    )
    .padding()
    .background(AppColors.background)
}
