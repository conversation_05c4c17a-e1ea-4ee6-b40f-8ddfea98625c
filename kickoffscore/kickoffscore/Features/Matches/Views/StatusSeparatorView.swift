import SwiftUI

/// A view that displays a match status separator in the events timeline
struct StatusSeparatorView: View {
    let status: String // "HT", "FT", "ET", "AET"
    let homeScore: Int?
    let awayScore: Int?

    var body: some View {
        HStack(alignment: .center, spacing: 8) {
            // Left divider line
            Rectangle()
                .fill(Color(UIColor.separator))
                .frame(height: 1)

            // Status indicator with icon and text
            HStack(spacing: 8) {
                // Status icon based on match period
                statusIcon

                // Score text (status is now always in the circle)
                Text(formattedScore)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(Color(UIColor.label))
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)

            // Right divider line
            Rectangle()
                .fill(Color(UIColor.separator))
                .frame(height: 1)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, AppLayout.spacingS)
        .accessibilityInfo(
            label: "\(statusLabel) Score: \(homeScore ?? 0) - \(awayScore ?? 0)",
            hint: "Match status indicator"
        )
    }

    // Format the score as "0-0"
    private var formattedScore: String {
        "\(homeScore ?? 0)-\(awayScore ?? 0)"
    }

    // Status icon based on match period
    @ViewBuilder
    private var statusIcon: some View {
        switch status {
        case "HT":
            // Circle with half light grey border, half normal border
            ZStack {
                // Light grey half of the border
                Circle()
                    .trim(from: 0, to: 0.5)
                    .stroke(Color(UIColor.systemGray3), lineWidth: 1.5)
                    .frame(width: 28, height: 28)
                    .rotationEffect(.degrees(-90))

                // Normal color half of the border
                Circle()
                    .trim(from: 0.5, to: 1.0)
                    .stroke(Color(UIColor.label), lineWidth: 1.5)
                    .frame(width: 28, height: 28)
                    .rotationEffect(.degrees(-90))

                // Status text
                Text(status)
                    .font(.system(size: 11, weight: .bold))
                    .foregroundColor(Color(UIColor.label))
            }
        case "FT", "ET", "AET":
            // Text inside a full circle
            Text(status)
                .font(.system(size: 11, weight: .bold))
                .foregroundColor(Color(UIColor.label))
                .frame(width: 28, height: 28)
                .background(
                    Circle()
                        .stroke(Color(UIColor.label), lineWidth: 1.5)
                )
        default:
            // Default circle for unknown status
            Text(status)
                .font(.system(size: 11, weight: .bold))
                .foregroundColor(Color(UIColor.label))
                .frame(width: 28, height: 28)
                .background(
                    Circle()
                        .stroke(Color(UIColor.label), lineWidth: 1.5)
                )
        }
    }

    // Full status label for accessibility
    private var statusLabel: String {
        switch status {
        case "HT":
            return "Half Time."
        case "FT":
            return "Full Time."
        case "ET":
            return "Extra Time."
        case "AET":
            return "After Extra Time."
        default:
            return status
        }
    }
}

#Preview {
    VStack(spacing: 16) {
        StatusSeparatorView(status: "HT", homeScore: 0, awayScore: 0)
        StatusSeparatorView(status: "FT", homeScore: 1, awayScore: 1)
        StatusSeparatorView(status: "ET", homeScore: 1, awayScore: 1)
        StatusSeparatorView(status: "AET", homeScore: 2, awayScore: 1)
    }
    .padding()
    .background(AppColors.background)
}
