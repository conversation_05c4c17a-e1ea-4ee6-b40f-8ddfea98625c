import SwiftUI
import Combine
import Foundation
import <PERSON><PERSON><PERSON>

// Enum to represent the type of goal icon to show
enum GoalIconType {
    case normal
    case penalty
    case missedPenalty
    case none
}

struct LineupsView: View {
    let lineups: [TeamLineup]?
    let playerStatistics: [PlayerStatisticsInfo]?
    let fixture: Fixture?

    // Default initializer with optional player statistics and fixture
    init(lineups: [TeamLineup]?, playerStatistics: [PlayerStatisticsInfo]? = nil, fixture: Fixture? = nil) {
        self.lineups = lineups
        self.playerStatistics = playerStatistics
        self.fixture = fixture
    }

    // Helper function to get player rating
    private func getPlayerRating(playerId: Int?, teamId: Int) -> String {
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return "" // Return empty string if no rating found
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let rating = stats.games?.rating {
            return rating
        }

        return "" // Return empty string if no rating found
    }

    // Helper function to identify home and away lineups correctly
    private func getHomeAndAwayLineups() -> (home: TeamLineup?, away: TeamLineup?) {
        guard let lineups = lineups, lineups.count == 2, let fixture = fixture else {
            return (nil, nil)
        }

        let homeTeamId = fixture.teams.home?.id
        let awayTeamId = fixture.teams.away?.id

        var homeLineup: TeamLineup?
        var awayLineup: TeamLineup?

        for lineup in lineups {
            if lineup.team.id == homeTeamId {
                homeLineup = lineup
            } else if lineup.team.id == awayTeamId {
                awayLineup = lineup
            }
        }

        return (homeLineup, awayLineup)
    }

    var body: some View {
        ScrollView {
            if let lineups = lineups, lineups.count == 2 {
                let (homeLineup, awayLineup) = getHomeAndAwayLineups()

                if let homeLineup = homeLineup, let awayLineup = awayLineup {
                    VStack(spacing: 0) { // No spacing between lineup elements
                        // Home team header with rating
                        TeamHeaderView(
                            teamName: homeLineup.team.name ?? "Home Team",
                            formation: homeLineup.formation ?? "N/A",
                            teamRating: fixture?.teamRatings?.homeTeam?.formattedRating ?? "",
                            isHomeTeam: true
                        )

                        // Full pitch view with both teams
                        // Visual positioning: homeTeam parameter goes to bottom, awayTeam parameter goes to top
                        // We want home team at top, away team at bottom, so we swap the parameters
                        FullPitchView(
                            homeTeam: awayLineup,  // Away team positioned at bottom of pitch
                            awayTeam: homeLineup,  // Home team positioned at top of pitch
                            playerStatistics: playerStatistics
                        )

                        // Away team header with rating
                        TeamHeaderView(
                            teamName: awayLineup.team.name ?? "Away Team",
                            formation: awayLineup.formation ?? "N/A",
                            teamRating: fixture?.teamRatings?.awayTeam?.formattedRating ?? "",
                            isHomeTeam: false
                        )

                        // Bench players section
                        // Keep the logical home/away team assignment for bench players
                        BenchPlayersView(
                            homeTeamSubstitutes: homeLineup.substitutes ?? [],  // Use correctly identified home team
                            awayTeamSubstitutes: awayLineup.substitutes ?? [],  // Use correctly identified away team
                            homeTeamId: homeLineup.team.id,
                            awayTeamId: awayLineup.team.id,
                            playerStatistics: playerStatistics
                        )
                    }
                    .padding(.top, AppLayout.spacingS) // Only keep gap between tabs and content
                } else {
                    EmptyStateView(
                        icon: "person.3",
                        message: "Unable to match lineup data with teams."
                    )
                    .padding(.top, AppLayout.spacingS)
                }
            } else {
                EmptyStateView(
                    icon: "person.3",
                    message: "Lineups data is not available."
                )
                .padding(.top, AppLayout.spacingS) // Added top padding to match other tabs
            }
        }
        .background(AppColors.background)
        .scrollIndicators(.hidden)
        .edgesIgnoringSafeArea(.horizontal)
    }
}

// MARK: - Team Header View

struct TeamHeaderView: View {
    let teamName: String
    let formation: String
    let teamRating: String
    let isHomeTeam: Bool

    var body: some View {
        HStack(spacing: 8) {
            // Team name and formation
            Text(teamName)
                .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                .foregroundColor(AppColors.text)

            Text(formation)
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(AppColors.secondaryText)

            // Team rating (only show if available) - matches player rating UI
            if !teamRating.isEmpty {
                ZStack {
                    RoundedRectangle(cornerRadius: 6)
                        .fill(ratingColor(for: teamRating))
                        .frame(width: 32, height: 16) // Increased size for better visibility and readability
                        .overlay(
                            RoundedRectangle(cornerRadius: 6)
                                .stroke(Color.white, lineWidth: 0.5)
                        )

                    Text(teamRating)
                        .font(.system(size: 11, weight: .bold)) // Slightly larger font to match increased size
                        .foregroundColor(.white)
                }
            }

            Spacer()
        }
        .padding(.horizontal, AppLayout.spacingM)
        .frame(height: AppLayout.spacingXL + AppLayout.spacingS)
        .background(AppColors.secondaryBackground)
        .accessibilityLabel("\(teamName) lineup")
        .accessibilityHint("Formation: \(formation)" + (teamRating.isEmpty ? "" : ", Rating: \(teamRating)"))

    }

    // MARK: - Helper Functions

    /// Returns a color based on the team rating value - matches player rating colors
    /// Rating scale: 3.0 (worst/red) to 10.0 (best/purple)
    func ratingColor(for ratingString: String) -> Color {
        guard let rating = Double(ratingString) else {
            return Color.gray
        }

        // Use the exact same color scheme as player ratings
        switch rating {
        case 9.0...:
            return Color(hex: "8e44ad") // Purple for exceptional ratings (9.0+)
        case 8.0..<9.0:
            return Color(hex: "2980b9") // Darker blue for excellent ratings (8.0-8.9)
        case 7.0..<8.0:
            return Color(hex: "27ae60") // Darker green for good ratings (7.0-7.9)
        case 6.5..<7.0:
            return Color(hex: "d4ac0d") // Darker yellow for above average ratings (6.5-6.9)
        case 6.0..<6.5:
            return Color(hex: "d35400") // Darker orange for average ratings (6.0-6.4)
        case 3.0..<6.0:
            return Color(hex: "c0392b") // Darker red for poor ratings (3.0-5.9)
        default:
            return Color(hex: "c0392b") // Darker red for very poor ratings (below 3.0)
        }
    }
}

// MARK: - Preview for Team Rating Colors
#if DEBUG
struct TeamRatingPreview: View {
    let sampleRatings = ["9.5", "8.5", "7.5", "6.8", "6.2", "5.5", "4.0"]
    let ratingLabels = ["Exceptional", "Excellent", "Good", "Above Avg", "Average", "Below Avg", "Poor"]

    var body: some View {
        VStack(spacing: 12) {
            Text("Team Rating UI - Matches Player Rating Style")
                .font(.headline)
                .padding()

            ForEach(Array(zip(sampleRatings, ratingLabels)), id: \.0) { rating, label in
                HStack(spacing: 16) {
                    Text(label)
                        .frame(width: 80, alignment: .leading)

                    Text(rating)
                        .font(.subheadline)
                        .frame(width: 40, alignment: .leading)

                    // Show the team rating badge - same as player rating
                    ZStack {
                        RoundedRectangle(cornerRadius: 6)
                            .fill(TeamHeaderView(teamName: "", formation: "", teamRating: rating, isHomeTeam: true).ratingColor(for: rating))
                            .frame(width: 32, height: 16) // Updated to match the new size
                            .overlay(
                                RoundedRectangle(cornerRadius: 6)
                                    .stroke(Color.white, lineWidth: 0.5)
                            )

                        Text(rating)
                            .font(.system(size: 11, weight: .bold)) // Updated to match the new font size
                            .foregroundColor(.white)
                    }

                    Spacer()
                }
            }
        }
        .padding()
    }
}

struct TeamRatingPreview_Previews: PreviewProvider {
    static var previews: some View {
        TeamRatingPreview()
    }
}
#endif

// MARK: - Player Row

struct PlayerRow: View {
    let number: Int
    let name: String
    let position: String
    let teamColor: Color

    var body: some View {
        HStack(spacing: AppLayout.spacingM) {
            // Player number
            Text("\(number)")
                .font(AppTypography.dynamicFont(style: .caption, weight: .bold))
                .foregroundColor(AppColors.buttonText)
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(teamColor)
                )

            // Player name and position
            VStack(alignment: .leading, spacing: 2) {
                Text(name)
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(Color(UIColor.label))
                    .padding(.horizontal, 0)
                    .padding(.bottom, AppLayout.spacingS)

                Text(position)
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color(UIColor.secondaryLabel))
            }

            Spacer()
        }
        .padding(.vertical, AppLayout.spacingXS)
        .accessibilityLabel("\(name), number \(number)")
        .accessibilityHint("Position: \(position)")
    }
}

// MARK: - Full Pitch View

struct FullPitchView: View {
    let homeTeam: TeamLineup
    let awayTeam: TeamLineup
    let playerStatistics: [PlayerStatisticsInfo]?

    // Helper function to get player rating
    private func getPlayerRating(playerId: Int?, teamId: Int) -> String {
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return "" // Return empty string if no rating found
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let rating = stats.games?.rating {
            return rating
        }

        return "" // Return empty string if no rating found
    }

    // Helper to get player goals (including all types: normal, penalty, etc.)
    private func getPlayerGoals(playerId: Int?, teamId: Int) -> Int {
        // First try to get goals from player statistics
        guard let playerId = playerId else {
            return 0
        }

        // Check player statistics first
        if let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
           let players = playerStats.players,
           let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let goals = stats.goals?.total {
            return goals
        }

        // If player statistics are not available or incomplete, check events
        // This ensures we count all types of goals (normal, penalty, etc.)
        guard let events = Fixture.mock.events else {
            return 0
        }

        // Count all goals for this player (excluding own goals)
        let goalCount = events.filter { event in
            event.player.id == playerId &&
            event.team.id == teamId &&
            event.type == "Goal" &&
            !event.detail.lowercased().contains("own")
        }.count

        return max(goalCount, 0)
    }

    // Helper to get the number of penalty goals a player has scored
    private func getPlayerPenaltyGoals(playerId: Int?, teamId: Int) -> Int {
        guard let playerId = playerId else {
            return 0
        }

        // Check player statistics first for penalty goals
        if let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
           let players = playerStats.players,
           let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let penalty = stats.penalty?.scored {
            return penalty
        }

        // If player statistics are not available or incomplete, check events
        guard let events = Fixture.mock.events else {
            return 0
        }

        // Count penalty goals for this player (excluding missed penalties)
        let penaltyGoalCount = events.filter { event in
            event.player.id == playerId &&
            event.team.id == teamId &&
            event.type == "Goal" &&
            event.detail.lowercased().contains("penalty") &&
            !event.detail.lowercased().contains("missed")
        }.count

        return max(penaltyGoalCount, 0)
    }

    // Helper to get the number of normal (non-penalty) goals a player has scored
    private func getPlayerNormalGoals(playerId: Int?, teamId: Int) -> Int {
        let totalGoals = getPlayerGoals(playerId: playerId, teamId: teamId)
        let penaltyGoals = getPlayerPenaltyGoals(playerId: playerId, teamId: teamId)
        return totalGoals - penaltyGoals
    }

    // Helper to check if a player has missed a penalty
    private func hasPlayerMissedPenalty(playerId: Int?, teamId: Int) -> Bool {
        guard let playerId = playerId else {
            return false
        }

        // Check player statistics first for missed penalties
        if let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
           let players = playerStats.players,
           let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let penalty = stats.penalty?.missed,
           penalty > 0 {
            return true
        }

        // If player statistics are not available or incomplete, check events
        guard let events = Fixture.mock.events else {
            return false
        }

        // Check if player has any missed penalties
        return events.contains { event in
            event.player.id == playerId &&
            event.team.id == teamId &&
            event.type == "Goal" &&
            event.detail.lowercased().contains("missed penalty")
        }
    }

    // Helper to determine which goal icon to show
    private func getPlayerGoalIconType(playerId: Int?, teamId: Int) -> GoalIconType {
        let normalGoals = getPlayerNormalGoals(playerId: playerId, teamId: teamId)
        let penaltyGoals = getPlayerPenaltyGoals(playerId: playerId, teamId: teamId)
        let hasMissedPenalty = hasPlayerMissedPenalty(playerId: playerId, teamId: teamId)

        // First check if the player has missed a penalty (and has no other goals)
        if hasMissedPenalty && normalGoals == 0 && penaltyGoals == 0 {
            // Player has only missed a penalty - show missed penalty icon
            return .missedPenalty
        } else if normalGoals > 0 && penaltyGoals > 0 {
            // Player has both normal and penalty goals - show normal goal icon
            return .normal
        } else if normalGoals > 0 {
            // Player has only normal goals
            return .normal
        } else if penaltyGoals > 0 {
            // Player has only penalty goals
            return .penalty
        } else {
            // No goals
            return .none
        }
    }

    // Moving the enum outside of LineupsView to make it accessible to PlayerMarkerWithRating

    // Helper to get player assists
    private func getPlayerAssists(playerId: Int?, teamId: Int) -> Int {
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return 0
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let assists = stats.goals?.assists {
            return assists // Assists is already unwrapped
        }

        return 0
    }

    // Helper to get player own goals
    private func getPlayerOwnGoals(playerId: Int?, teamId: Int) -> Int {
        // Check for own goals in Fixture.mock events data
        guard let events = Fixture.mock.events else {
            return 0
        }

        // Count own goals for this player
        let ownGoalCount = events.filter { event in
            event.player.id == playerId &&
            event.team.id == teamId &&
            event.type == "Goal" &&
            event.detail.lowercased().contains("own")
        }.count

        return max(ownGoalCount, 0)
    }

    // Helper to get player yellow cards
    private func getPlayerYellowCards(playerId: Int?, teamId: Int) -> Int {
        // First check player statistics for yellow cards
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return 0
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let cards = stats.cards,
           let yellowCards = cards.yellow {
            return yellowCards
        }

        // Fallback to events data if player stats not available
        guard let events = Fixture.mock.events else {
            return 0
        }

        // Get all yellow card events for this player
        let yellowCardEvents = events.filter { event in
            event.player.id == playerId &&
            event.team.id == teamId &&
            event.type == "Card" &&
            event.detail.lowercased().contains("yellow") &&
            !event.detail.lowercased().contains("red") // Exclude direct red cards that mention yellow
        }

        // If player has a second yellow, we should only count the first yellow card
        // in the UI display (since the second yellow is shown as a red card)
        let hasSecondYellow = isSecondYellowCard(playerId: playerId, teamId: teamId)

        // If player has a second yellow, return 1 (only show first yellow)
        // Otherwise, return the actual count of yellow cards
        let yellowCardCount = hasSecondYellow ? 1 : yellowCardEvents.count

        return max(yellowCardCount, 0)
    }

    // Helper to get player red cards (including second yellows)
    private func getPlayerRedCards(playerId: Int?, teamId: Int) -> Int {
        // First check player statistics for red cards
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return 0
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let cards = stats.cards,
           let redCards = cards.red {
            return redCards
        }

        // Fallback to events data if player stats not available
        guard let events = Fixture.mock.events else {
            return 0
        }

        // Get all card events for this player
        let cardEvents = events.filter { event in
            event.player.id == playerId &&
            event.team.id == teamId &&
            event.type == "Card"
        }

        // Count direct red cards
        let directRedCards = cardEvents.filter { event in
            event.detail.lowercased().contains("red") &&
            !event.detail.lowercased().contains("yellow")
        }.count

        // Check for second yellow card
        let hasSecondYellow = isSecondYellowCard(playerId: playerId, teamId: teamId)

        // Total red cards = direct red cards + second yellow (if any)
        let redCardCount = directRedCards + (hasSecondYellow ? 1 : 0)

        return max(redCardCount, 0)
    }



    // Helper to check if a yellow card is a second yellow
    private func isSecondYellowCard(playerId: Int?, teamId: Int) -> Bool {
        guard let playerId = playerId, let events = Fixture.mock.events else {
            return false
        }

        // Get all yellow card events for this player in chronological order
        let yellowCardEvents = events
            .filter { event in
                event.player.id == playerId &&
                event.team.id == teamId &&
                event.type == "Card" &&
                event.detail.lowercased().contains("yellow")
            }
            .sorted {
                if $0.time.elapsed != $1.time.elapsed {
                    return $0.time.elapsed < $1.time.elapsed
                }
                // If elapsed time is the same, compare by extra time (if available)
                let extra0 = $0.time.extra ?? 0
                let extra1 = $1.time.extra ?? 0
                return extra0 < extra1
            }

        // If there are 2 or more yellow cards, the player has received a second yellow
        return yellowCardEvents.count > 1
    }

    // Helper to get player name
    private func getPlayerName(playerId: Int?, teamId: Int?) -> String? {
        if teamId == homeTeam.team.id {
            return homeTeam.startXI?.first(where: { $0.player.id == playerId })?.player.name
        } else if teamId == awayTeam.team.id {
            return awayTeam.startXI?.first(where: { $0.player.id == playerId })?.player.name
        }
        return nil
    }

    // Helper to get player rating as Double (for internal use)
    private func getPlayerRatingAsDouble(playerId: Int?, teamId: Int) -> Double {
        let ratingStr = getPlayerRating(playerId: playerId, teamId: teamId)
        return Double(ratingStr) ?? 0
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Pitch Background - Using standardized color
                Rectangle()
                    .fill(AppColors.Sports.pitchBackground)

                // Pitch Markings
                FullPitchMarkings()

                // Home Team Players (bottom half)
                if let startXI = homeTeam.startXI {
                    ForEach(startXI) { playerInfo in
                        if let position = calculatePlayerPosition(
                            player: playerInfo.player,
                            allPlayers: startXI,
                            geometry: geometry,
                            formation: homeTeam.formation ?? "4-4-2",
                            isHomeTeam: true
                        ) {
                            // Check if this player is in a crowded row (5+ players in the same row)
                            let isCrowdedRow = isCrowdedRow(player: playerInfo.player, formation: homeTeam.formation ?? "4-4-2")

                            PlayerMarkerWithRating(
                                player: playerInfo.player,
                                teamLineup: homeTeam,
                                rating: getPlayerRating(playerId: playerInfo.player.id, teamId: homeTeam.team.id),
                                goals: getPlayerGoals(playerId: playerInfo.player.id, teamId: homeTeam.team.id),
                                assists: getPlayerAssists(playerId: playerInfo.player.id, teamId: homeTeam.team.id),
                                ownGoals: getPlayerOwnGoals(playerId: playerInfo.player.id, teamId: homeTeam.team.id),
                                yellowCards: getPlayerYellowCards(playerId: playerInfo.player.id, teamId: homeTeam.team.id),
                                redCards: getPlayerRedCards(playerId: playerInfo.player.id, teamId: homeTeam.team.id),
                                goalIconType: getPlayerGoalIconType(playerId: playerInfo.player.id, teamId: homeTeam.team.id),
                                isInCrowdedRow: isCrowdedRow
                            )
                            .position(x: position.x, y: position.y)
                        }
                    }
                }

                // Away Team Players (top half)
                if let startXI = awayTeam.startXI {
                    ForEach(startXI) { playerInfo in
                        if let position = calculatePlayerPosition(
                            player: playerInfo.player,
                            allPlayers: startXI,
                            geometry: geometry,
                            formation: awayTeam.formation ?? "4-4-2",
                            isHomeTeam: false
                        ) {
                            // Check if this player is in a crowded row (5+ players in the same row)
                            let isCrowdedRow = isCrowdedRow(player: playerInfo.player, formation: awayTeam.formation ?? "4-4-2")

                            PlayerMarkerWithRating(
                                player: playerInfo.player,
                                teamLineup: awayTeam,
                                rating: getPlayerRating(playerId: playerInfo.player.id, teamId: awayTeam.team.id),
                                goals: getPlayerGoals(playerId: playerInfo.player.id, teamId: awayTeam.team.id),
                                assists: getPlayerAssists(playerId: playerInfo.player.id, teamId: awayTeam.team.id),
                                ownGoals: getPlayerOwnGoals(playerId: playerInfo.player.id, teamId: awayTeam.team.id),
                                yellowCards: getPlayerYellowCards(playerId: playerInfo.player.id, teamId: awayTeam.team.id),
                                redCards: getPlayerRedCards(playerId: playerInfo.player.id, teamId: awayTeam.team.id),
                                goalIconType: getPlayerGoalIconType(playerId: playerInfo.player.id, teamId: awayTeam.team.id),
                                isInCrowdedRow: isCrowdedRow
                            )
                            .position(x: position.x, y: position.y)
                        }
                    }
                }

                // No substitution indicators or other icons as requested
            }
            .clipped()
        }
        .frame(height: UIScreen.main.bounds.height * 0.9) // Set pitch height to 90% of screen
        .edgesIgnoringSafeArea(.horizontal)
    }

    // Calculate player position based on formation and player position
    private func calculatePlayerPosition(
        player: PlayerLineupInfo.PlayerInfo,
        allPlayers: [PlayerLineupInfo],
        geometry: GeometryProxy,
        formation: String,
        isHomeTeam: Bool
    ) -> CGPoint? {
        // Parse formation string (e.g., "4-3-3") into an array of integers
        let formationArray = formation.split(separator: "-").compactMap { Int($0) }
        guard !formationArray.isEmpty else { return nil }

        // Get pitch dimensions
        let width = geometry.size.width
        let height = geometry.size.height

        // Default position (fallback)
        var x: CGFloat = width / 2
        var y: CGFloat = height / 2

        // Get player position
        guard let position = player.pos else { return nil }

        // Find this player's index among players with the same position
        let positionPlayers = allPlayers.filter { $0.player.pos == position }
        guard let playerIndex = positionPlayers.firstIndex(where: { $0.player.id == player.id }) else {
            return nil
        }

        // Determine the number of rows in the formation
        let rowCount = getFormationRowCount(formationArray: formationArray)

        // Calculate vertical position (row) based on position type and team
        let halfHeight = height / 2
        // Initialize baseY with a default value
        var baseY: CGFloat = halfHeight

        // Get row positions based on formation row count
        let rowPositions = getRowPositions(rowCount: rowCount, isHomeTeam: isHomeTeam, height: height)

        // Determine which row this player belongs to
        switch position {
        case "G": // Goalkeeper
            baseY = rowPositions[0] // First row (GK)

        case "D": // Defenders
            baseY = rowPositions[1] // Second row (Defenders)

        case "M": // Midfielders
            if rowCount == 5 {
                // For 5-row formations (like 4-2-3-1, 3-4-1-2, 3-4-2-1)
                if formationArray.count >= 3 {
                    // Check if this is a defensive midfielder (first few in the list)
                    let isDM = playerIndex < formationArray[1]

                    if isDM {
                        // Defensive midfielders
                        baseY = rowPositions[2] // Third row (DM)
                    } else {
                        // Attacking midfielders
                        baseY = rowPositions[3] // Fourth row (AM)
                    }
                } else {
                    // Default midfield position
                    baseY = rowPositions[2] // Third row
                }
            } else {
                // For 4-row formations (like 4-4-2, 4-3-3, 5-3-2)
                baseY = rowPositions[2] // Third row (midfielders)
            }

        case "F": // Forwards
            if rowCount == 5 {
                // For 5-row formations with split forward lines
                if formationArray.count >= 4 {
                    // Special handling for 3-4-1-2 or 3-4-2-1 formations
                    if (formationArray[0] == 3 && formationArray[1] == 4 &&
                        ((formationArray[2] == 1 && formationArray[3] == 2) || // 3-4-1-2
                         (formationArray[2] == 2 && formationArray[3] == 1))) { // 3-4-2-1

                        let totalForwards = positionPlayers.count

                        if formationArray[2] == 1 && formationArray[3] == 2 {
                            // 3-4-1-2 formation
                            if totalForwards == 3 && playerIndex == 0 {
                                // This is the CAM (the "1" in 3-4-1-2)
                                baseY = rowPositions[3] // Fourth row (AM/CAM)
                            } else {
                                // These are the strikers (the "2" in 3-4-1-2)
                                baseY = rowPositions[4] // Fifth row (Forwards)
                            }
                        } else if formationArray[2] == 2 && formationArray[3] == 1 {
                            // 3-4-2-1 formation
                            if totalForwards == 3 && playerIndex < 2 {
                                // These are the AMs (the "2" in 3-4-2-1)
                                baseY = rowPositions[3] // Fourth row (AM)
                            } else {
                                // This is the striker (the "1" in 3-4-2-1)
                                baseY = rowPositions[4] // Fifth row (Forwards)
                            }
                        }
                    } else if formationArray[0] == 4 && formationArray[1] == 2 && formationArray[2] == 3 && formationArray[3] == 1 {
                        // 4-2-3-1 formation
                        let totalForwards = positionPlayers.count

                        if totalForwards > 1 && playerIndex < totalForwards - 1 {
                            // These are the AMs (the "3" in 4-2-3-1)
                            baseY = rowPositions[3] // Fourth row (AM)
                        } else {
                            // This is the striker (the "1" in 4-2-3-1)
                            baseY = rowPositions[4] // Fifth row (Forwards)
                        }
                    } else if formationArray[0] == 4 && formationArray[1] == 4 && formationArray[2] == 1 && formationArray[3] == 1 {
                        // 4-4-1-1 formation
                        let totalForwards = positionPlayers.count

                        if totalForwards == 2 && playerIndex == 0 {
                            // This is the CAM/CF (the first "1" in 4-4-1-1)
                            baseY = rowPositions[3] // Fourth row (AM/CAM)
                        } else {
                            // This is the striker (the second "1" in 4-4-1-1)
                            baseY = rowPositions[4] // Fifth row (Forwards)
                        }
                    } else {
                        // Other 5-row formations
                        baseY = rowPositions[4] // Fifth row (Forwards)
                    }
                } else {
                    // Default forward position for 5-row formations
                    baseY = rowPositions[4] // Fifth row (Forwards)
                }
            } else {
                // For 4-row formations (like 4-4-2, 4-3-3, 5-3-2)
                baseY = rowPositions[3] // Fourth row (Forwards)
            }

        default:
            // Default position
            if isHomeTeam {
                baseY = halfHeight + halfHeight * 0.5 // Middle of home half
            } else {
                baseY = halfHeight * 0.5 // Middle of away half
            }
        }

        // Calculate horizontal position (column) based on position and formation
        switch position {
        case "G": // Goalkeeper
            // Always centered
            x = width / 2

        case "D": // Defenders
            if let defenderCount = formationArray.first, defenderCount > 0 {
                // Use 6 columns with first and last empty
                // Previously used a fixed number of columns, now using percentage-based positioning

                if defenderCount == 1 {
                    // Single center back
                    x = width / 2
                } else if defenderCount <= 4 {
                    // Calculate positions using percentage of width

                    // For better spacing, use the full width with padding
                    let usableWidth = width * 0.9 // Use 90% of width
                    let startX = width * 0.05 // 5% padding on each side

                    // Space defenders evenly across the usable width
                    let defenderSpacing = usableWidth / CGFloat(defenderCount)
                    x = startX + defenderSpacing * (CGFloat(playerIndex) + 0.5) // +0.5 to center in each section
                } else {
                    // 5 or more defenders - use wider spacing to spread them out more
                    let usableWidth = width * 0.95 // Use 95% of width
                    let startX = width * 0.025 // 2.5% padding on each side
                    let defenderSpacing = usableWidth / CGFloat(defenderCount)
                    x = startX + defenderSpacing * (CGFloat(playerIndex) + 0.5)
                }
            } else {
                x = width / 2
            }

        case "M": // Midfielders
            // Check which midfielder row this is
            if formationArray.count >= 3 {
                // For formations like 4-2-3-1 or 4-3-3
                if playerIndex < formationArray[1] {
                    // Defensive midfielders (e.g., the "2" in 4-2-3-1)
                    let dmCount = formationArray[1]

                    // Use 5 columns for 2 DMs, 7 columns for 3 DMs
                    // Column calculation replaced with percentage-based positioning

                    if dmCount == 1 {
                        // Single DM
                        x = width / 2
                    } else if dmCount >= 3 {
                        // For 3 or more DMs, use almost the full width to spread them out more
                        let usableWidth = width * 0.95 // Use 95% of width
                        let startX = width * 0.025 // 2.5% padding on each side

                        // Space DMs evenly across the usable width
                        let dmSpacing = usableWidth / CGFloat(dmCount)
                        x = startX + dmSpacing * (CGFloat(playerIndex) + 0.5) // +0.5 to center in each section
                    } else {
                        // For 2 DMs, use standard spacing
                        let usableWidth = width * 0.9 // Use 90% of width
                        let startX = width * 0.05 // 5% padding on each side

                        // Space DMs evenly across the usable width
                        let dmSpacing = usableWidth / CGFloat(dmCount)
                        x = startX + dmSpacing * (CGFloat(playerIndex) + 0.5) // +0.5 to center in each section
                    }
                } else {
                    // Attacking midfielders (e.g., the "3" in 4-2-3-1)
                    let amIndex = playerIndex - formationArray[1]
                    let amCount = formationArray[2]

                    // Use 5 columns with first and last empty
                    // Column calculation replaced with percentage-based positioning

                    if amCount == 1 {
                        // Single AM
                        x = width / 2
                    } else if amCount >= 3 {
                        // For 3 or more AMs, use almost the full width to spread them out more
                        let usableWidth = width * 0.95 // Use 95% of width
                        let startX = width * 0.025 // 2.5% padding on each side

                        // Space AMs evenly across the usable width
                        let amSpacing = usableWidth / CGFloat(amCount)
                        x = startX + amSpacing * (CGFloat(amIndex) + 0.5) // +0.5 to center in each section
                    } else {
                        // For 2 AMs, use standard spacing
                        let usableWidth = width * 0.9 // Use 90% of width
                        let startX = width * 0.05 // 5% padding on each side

                        // Space AMs evenly across the usable width
                        let amSpacing = usableWidth / CGFloat(amCount)
                        x = startX + amSpacing * (CGFloat(amIndex) + 0.5) // +0.5 to center in each section
                    }
                }
            } else {
                // For simpler formations like 4-4-2
                let midfielderCount = formationArray.indices.contains(1) ? formationArray[1] : 4

                // Use 6 columns with first and last empty
                // Column calculation replaced with percentage-based positioning

                if midfielderCount == 1 {
                    // Single midfielder
                    x = width / 2
                } else if midfielderCount >= 4 {
                    // For 4 or 5 midfielders, use almost the full width to spread them out more
                    let usableWidth = width * 0.95 // Use 95% of width
                    let startX = width * 0.025 // 2.5% padding on each side

                    // Space midfielders evenly across the usable width
                    let spacing = usableWidth / CGFloat(midfielderCount)
                    x = startX + spacing * (CGFloat(playerIndex) + 0.5) // +0.5 to center in each section
                } else {
                    // For 2-3 midfielders, use standard spacing
                    let usableWidth = width * 0.9 // Use 90% of width
                    let startX = width * 0.05 // 5% padding on each side

                    // Space midfielders evenly across the usable width
                    let spacing = usableWidth / CGFloat(midfielderCount)
                    x = startX + spacing * (CGFloat(playerIndex) + 0.5) // +0.5 to center in each section
                }
            }

        case "F": // Forwards
            // Special handling for specific formations
            if formationArray.count >= 4 {
                if formationArray[0] == 3 && formationArray[1] == 4 && formationArray[2] == 1 && formationArray[3] == 2 {
                    // 3-4-1-2 formation
                    let totalForwards = positionPlayers.count

                    if totalForwards == 3 {
                        if playerIndex == 0 {
                            // This is the CAM (the "1" in 3-4-1-2) - centered
                            x = width / 2
                        } else {
                            // These are the strikers (the "2" in 3-4-1-2)
                            // Position them side by side with good spacing
                            let strikerIndex = playerIndex - 1 // 0 or 1 for the two strikers
                            let usableWidth = width * 0.6 // Use 60% of width for strikers
                            let startX = width * 0.2 // 20% padding on each side

                            // Space the two strikers evenly
                            let spacing = usableWidth / 2
                            x = startX + spacing * (CGFloat(strikerIndex) + 0.5) // +0.5 to center in each section
                        }
                    } else {
                        // Default positioning if we don't have exactly 3 forwards
                        x = width / 2
                    }
                } else if formationArray[0] == 3 && formationArray[1] == 4 && formationArray[2] == 2 && formationArray[3] == 1 {
                    // 3-4-2-1 formation
                    let totalForwards = positionPlayers.count

                    if totalForwards == 3 {
                        if playerIndex < 2 {
                            // These are the AMs (the "2" in 3-4-2-1)
                            // Position them side by side with good spacing
                            let usableWidth = width * 0.6 // Use 60% of width for AMs
                            let startX = width * 0.2 // 20% padding on each side

                            // Space the two AMs evenly
                            let spacing = usableWidth / 2
                            x = startX + spacing * (CGFloat(playerIndex) + 0.5) // +0.5 to center in each section
                        } else {
                            // This is the striker (the "1" in 3-4-2-1) - centered
                            x = width / 2
                        }
                    } else {
                        // Default positioning if we don't have exactly 3 forwards
                        x = width / 2
                    }
                } else if formationArray[0] == 4 && formationArray[1] == 2 && formationArray[2] == 3 && formationArray[3] == 1 {
                    // 4-2-3-1 formation
                    let totalForwards = positionPlayers.count

                    if totalForwards > 1 {
                        if playerIndex < totalForwards - 1 {
                            // These are the AMs (the "3" in 4-2-3-1)
                            let amCount = min(3, totalForwards - 1) // Should be 3 AMs
                            let amIndex = playerIndex

                            // For better spacing, use almost the full width to spread them out more
                            let usableWidth = width * 0.95 // Use 95% of width
                            let startX = width * 0.025 // 2.5% padding on each side

                            // Space AMs evenly across the usable width
                            let spacing = usableWidth / CGFloat(amCount)
                            x = startX + spacing * (CGFloat(amIndex) + 0.5) // +0.5 to center in each section
                        } else {
                            // This is the striker (the "1" in 4-2-3-1) - centered
                            x = width / 2
                        }
                    } else {
                        // Default positioning if we don't have enough forwards
                        x = width / 2
                    }
                } else if formationArray[0] == 4 && formationArray[1] == 4 && formationArray[2] == 1 && formationArray[3] == 1 {
                    // 4-4-1-1 formation
                    let totalForwards = positionPlayers.count

                    if totalForwards == 2 {
                        if playerIndex == 0 {
                            // This is the CAM/CF (the first "1" in 4-4-1-1) - centered
                            x = width / 2
                        } else {
                            // This is the striker (the second "1" in 4-4-1-1) - centered
                            x = width / 2
                        }
                    } else {
                        // Default positioning if we don't have exactly 2 forwards
                        x = width / 2
                    }
                } else {
                    // Other formations with 4 parts
                    let forwardCount = formationArray[formationArray.count - 1]

                    if forwardCount == 1 {
                        // Single forward
                        x = width / 2
                    } else {
                        // For better spacing, use the full width with padding
                        let usableWidth = width * 0.9 // Use 90% of width
                        let startX = width * 0.05 // 5% padding on each side

                        // Space forwards evenly across the usable width
                        let spacing = usableWidth / CGFloat(forwardCount)
                        x = startX + spacing * (CGFloat(playerIndex) + 0.5) // +0.5 to center in each section
                    }
                }
            } else {
                // Regular forwards for simpler formations
                let forwardCount = formationArray.indices.contains(2) ? formationArray[2] :
                                  (formationArray.indices.contains(formationArray.count - 1) ?
                                   formationArray[formationArray.count - 1] : 2)

                // Use 5 columns with appropriate spacing
                // Column calculation replaced with percentage-based positioning

                if forwardCount == 1 {
                    // Single forward
                    x = width / 2
                } else if forwardCount >= 3 {
                    // For 3 or more forwards, use almost the full width to spread them out more
                    let usableWidth = width * 0.95 // Use 95% of width
                    let startX = width * 0.025 // 2.5% padding on each side

                    // Space forwards evenly across the usable width
                    let spacing = usableWidth / CGFloat(forwardCount)
                    x = startX + spacing * (CGFloat(playerIndex) + 0.5) // +0.5 to center in each section
                } else {
                    // For 2 forwards, use standard spacing
                    let usableWidth = width * 0.9 // Use 90% of width
                    let startX = width * 0.05 // 5% padding on each side

                    // Space forwards evenly across the usable width
                    let spacing = usableWidth / CGFloat(forwardCount)
                    x = startX + spacing * (CGFloat(playerIndex) + 0.5) // +0.5 to center in each section
                }
            }

        default:
            // Default position in the middle of the team's half
            x = width / 2
        }

        // Set the y position to baseY for all cases
        y = baseY

        // Since we swapped teams visually (away team is now at bottom with isHomeTeam: true),
        // we need to flip the horizontal positioning for the bottom team to maintain correct left/right orientation
        if isHomeTeam {
            // This is actually the away team positioned at bottom - flip horizontally
            x = width - x
        }
        // Away team (actually home team at top) keeps normal positioning

        return CGPoint(x: x, y: y)
    }

    // Helper method to determine the number of rows in a formation
    private func getFormationRowCount(formationArray: [Int]) -> Int {
        // Count the goalkeeper row + outfield rows
        if formationArray.isEmpty {
            return 4 // Default to 4 rows (GK + 3 outfield rows) if formation is invalid
        }

        // Special cases for formations with split midfield or forward lines
        if formationArray.count >= 4 {
            // Formations like 3-4-1-2, 3-4-2-1, 4-2-3-1, etc. have 5 rows (GK + 4 outfield rows)
            return 5
        } else if formationArray.count == 3 {
            // Formations like 4-3-3, 3-5-2, etc. have 4 rows (GK + 3 outfield rows)
            return 4
        } else {
            // Simple formations like 4-4-2 have 4 rows (GK + 3 outfield rows)
            return 4
        }
    }

    // Helper function to determine if a player is in a crowded row (5+ players)
    private func isCrowdedRow(player: PlayerLineupInfo.PlayerInfo, formation: String) -> Bool {
        // Parse formation string (e.g., "4-3-3") into an array of integers
        let formationArray = formation.split(separator: "-").compactMap { Int($0) }
        guard !formationArray.isEmpty else { return false }

        // Get player position
        guard let position = player.pos else { return false }

        // Check if this position has 5 or more players in the same row
        switch position {
        case "D": // Defenders
            return formationArray.first ?? 0 >= 5

        case "M": // Midfielders
            if formationArray.count >= 3 {
                // For formations with split midfield (e.g., 4-2-3-1)
                // Check if this is a defensive midfielder or attacking midfielder
                if position == "M" {
                    // For defensive midfielders, check the first midfield number
                    if formationArray.indices.contains(1) {
                        return formationArray[1] >= 5
                    }
                }

                // For attacking midfielders, check the second midfield number
                if formationArray.indices.contains(2) {
                    return formationArray[2] >= 5
                }
            } else if formationArray.indices.contains(1) {
                // For simple formations like 4-5-1
                return formationArray[1] >= 5
            }
            return false

        case "F": // Forwards
            if formationArray.count >= 3 {
                // For formations with multiple forward rows
                let lastIndex = formationArray.count - 1
                return formationArray[lastIndex] >= 5
            }
            return false

        default:
            return false
        }
    }

    // Helper method to get row positions based on formation row count
    private func getRowPositions(rowCount: Int, isHomeTeam: Bool, height: CGFloat) -> [CGFloat] {
        let halfHeight = height / 2

        // Goal area display size is 7% of the pitch height, but we'll keep goalkeeper positioning based on 5%
        // Display depth is used in the pitch markings, not needed here
        let goalKeeperPositioningDepth = height * 0.05 // Original depth for goalkeeper positioning

        if isHomeTeam {
            // Home team (bottom half)
            switch rowCount {
            case 5:
                // 5 rows: GK, defenders, defensive midfielders, attacking midfielders, forwards
                // Calculate the gap to ensure equal spacing between all rows
                // For 5 rows with 4 gaps, we use 19.5% to maintain proportional spacing with 4-row formations
                let gap = halfHeight * 0.195

                // Position goalkeeper closer to the goal line (1.2x the original goal area depth)
                let gkPosition = height - (goalKeeperPositioningDepth * 1.2)

                // Adjust the defender position to maintain equal spacing from goalkeeper
                let defenderPosition = gkPosition - gap

                return [
                    gkPosition, // GK - moved away from goal line
                    defenderPosition, // Defenders
                    defenderPosition - gap, // Defensive midfielders
                    defenderPosition - (2 * gap), // Attacking midfielders
                    defenderPosition - (3 * gap)  // Forwards
                ]
            case 4:
                // 4 rows: GK, defenders, midfielders, forwards
                // Evenly spaced rows in the bottom half with equal gaps (26% of half height)
                let gap = halfHeight * 0.26

                // Position goalkeeper closer to the goal line (1.2x the original goal area depth)
                let gkPosition = height - (goalKeeperPositioningDepth * 1.2)

                // Adjust the defender position to maintain equal spacing from goalkeeper
                let defenderPosition = gkPosition - gap

                return [
                    gkPosition, // GK - moved away from goal line
                    defenderPosition, // Defenders
                    defenderPosition - gap, // Midfielders
                    defenderPosition - (2 * gap)  // Forwards
                ]
            default:
                // Fallback to 4 rows with 26% gap
                let gap = halfHeight * 0.26

                // Position goalkeeper closer to the goal line (1.2x the original goal area depth)
                let gkPosition = height - (goalKeeperPositioningDepth * 1.2)

                // Adjust the defender position to maintain equal spacing from goalkeeper
                let defenderPosition = gkPosition - gap

                return [
                    gkPosition, // GK - moved away from goal line
                    defenderPosition, // Defenders
                    defenderPosition - gap, // Midfielders
                    defenderPosition - (2 * gap)  // Forwards
                ]
            }
        } else {
            // Away team (top half)
            switch rowCount {
            case 5:
                // 5 rows: GK, defenders, defensive midfielders, attacking midfielders, forwards
                // Calculate the gap to ensure equal spacing between all rows
                // For 5 rows with 4 gaps, we use 19.5% to maintain proportional spacing with 4-row formations
                let gap = halfHeight * 0.195

                // Position goalkeeper closer to the goal line (1.2x the original goal area depth)
                let gkPosition = goalKeeperPositioningDepth * 1.2

                // Adjust the defender position to maintain equal spacing from goalkeeper
                let defenderPosition = gkPosition + gap

                return [
                    gkPosition, // GK - moved away from goal line
                    defenderPosition, // Defenders
                    defenderPosition + gap, // Defensive midfielders
                    defenderPosition + (2 * gap), // Attacking midfielders
                    defenderPosition + (3 * gap)  // Forwards
                ]
            case 4:
                // 4 rows: GK, defenders, midfielders, forwards
                // Evenly spaced rows in the top half with equal gaps (26% of half height)
                let gap = halfHeight * 0.26

                // Position goalkeeper closer to the goal line (1.2x the original goal area depth)
                let gkPosition = goalKeeperPositioningDepth * 1.2

                // Adjust the defender position to maintain equal spacing from goalkeeper
                let defenderPosition = gkPosition + gap

                return [
                    gkPosition, // GK - moved away from goal line
                    defenderPosition, // Defenders
                    defenderPosition + gap, // Midfielders
                    defenderPosition + (2 * gap)  // Forwards
                ]
            default:
                // Fallback to 4 rows with 26% gap
                let gap = halfHeight * 0.26

                // Position goalkeeper closer to the goal line (1.2x the original goal area depth)
                let gkPosition = goalKeeperPositioningDepth * 1.2

                // Adjust the defender position to maintain equal spacing from goalkeeper
                let defenderPosition = gkPosition + gap

                return [
                    gkPosition, // GK - moved away from goal line
                    defenderPosition, // Defenders
                    defenderPosition + gap, // Midfielders
                    defenderPosition + (2 * gap)  // Forwards
                ]
            }
        }
    }
}

// Extension to safely access array elements
extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

// MARK: - Full Pitch Markings

struct FullPitchMarkings: View {
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Center Line
                Path { path in
                    path.move(to: CGPoint(x: 0, y: geometry.size.height / 2))
                    path.addLine(to: CGPoint(x: geometry.size.width, y: geometry.size.height / 2))
                }
                .stroke(AppColors.Sports.pitchLines, lineWidth: 3.0) // Increased line thickness

                // Center Circle
                Circle()
                    .stroke(AppColors.Sports.pitchLines, lineWidth: 3.0) // Increased line thickness
                    .frame(width: min(geometry.size.width, geometry.size.height) * 0.2)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)

                // Penalty Areas
                Group {
                    // Top penalty area (away team)
                    Path { path in
                        let width = geometry.size.width * 0.6
                        let height = geometry.size.height * 0.15
                        let x = (geometry.size.width - width) / 2
                        let y: CGFloat = 0

                        path.addRect(CGRect(x: x, y: y, width: width, height: height))
                    }
                    .stroke(AppColors.Sports.pitchLines, lineWidth: 3.0) // Increased line thickness

                    // Bottom penalty area (home team)
                    Path { path in
                        let width = geometry.size.width * 0.6
                        let height = geometry.size.height * 0.15
                        let x = (geometry.size.width - width) / 2
                        let y = geometry.size.height - height

                        path.addRect(CGRect(x: x, y: y, width: width, height: height))
                    }
                    .stroke(AppColors.Sports.pitchLines, lineWidth: 3.0) // Increased line thickness
                }

                // Goal Areas
                Group {
                    // Top goal area (away team)
                    Path { path in
                        let width = geometry.size.width * 0.35 // Increased width
                        let height = geometry.size.height * 0.07 // Increased height
                        let x = (geometry.size.width - width) / 2
                        let y: CGFloat = 0

                        path.addRect(CGRect(x: x, y: y, width: width, height: height))
                    }
                    .stroke(AppColors.Sports.pitchLines, lineWidth: 3.0) // Increased line thickness

                    // Bottom goal area (home team)
                    Path { path in
                        let width = geometry.size.width * 0.35 // Increased width
                        let height = geometry.size.height * 0.07 // Increased height
                        let x = (geometry.size.width - width) / 2
                        let y = geometry.size.height - height

                        path.addRect(CGRect(x: x, y: y, width: width, height: height))
                    }
                    .stroke(AppColors.Sports.pitchLines, lineWidth: 3.0) // Increased line thickness
                }
            }
        }
    }
}

// MARK: - Player Marker With Rating

struct PlayerMarkerWithRating: View {
    let player: PlayerLineupInfo.PlayerInfo
    let teamColor: Color
    let numberColor: Color
    let rating: String // Player rating (e.g., "7.5")
    let showRating: Bool // Whether to show the rating badge
    let goals: Int // Number of goals scored
    let assists: Int // Number of assists
    let ownGoals: Int // Number of own goals
    let yellowCards: Int // Number of yellow cards
    let redCards: Int // Number of red cards (including second yellows)
    let goalIconType: GoalIconType // Type of goal icon to show
    var isInCrowdedRow: Bool = false // Flag to indicate if this player is in a crowded row (5+ players)

    // Default initializer with team and number colors
    init(player: PlayerLineupInfo.PlayerInfo, teamColor: Color, numberColor: Color = .white, rating: String, showRating: Bool = true, goals: Int = 0, assists: Int = 0, ownGoals: Int = 0, yellowCards: Int = 0, redCards: Int = 0, goalIconType: GoalIconType = .none, isInCrowdedRow: Bool = false) {
        self.player = player
        self.teamColor = teamColor
        self.numberColor = numberColor
        self.rating = rating
        self.showRating = showRating
        self.goals = goals
        self.assists = assists
        self.ownGoals = ownGoals
        self.yellowCards = yellowCards
        self.redCards = redCards
        self.goalIconType = goalIconType
        self.isInCrowdedRow = isInCrowdedRow
    }

    // Convenience initializer that takes team colors from the API
    init(player: PlayerLineupInfo.PlayerInfo, teamLineup: TeamLineup, rating: String, showRating: Bool = true, goals: Int = 0, assists: Int = 0, ownGoals: Int = 0, yellowCards: Int = 0, redCards: Int = 0, goalIconType: GoalIconType = .none, isInCrowdedRow: Bool = false) {
        let isGoalkeeper = player.pos == "G"

        // Get the appropriate color info based on player position
        let colorInfo = isGoalkeeper
            ? teamLineup.team.colors?.goalkeeper
            : teamLineup.team.colors?.player

        // Set jersey color from API or fallback to default colors
        var jerseyColor: Color
        if let primaryColor = colorInfo?.primary, !primaryColor.isEmpty {
            jerseyColor = Color(hex: primaryColor)
        } else {
            jerseyColor = isGoalkeeper ? Color.yellow : (teamLineup.id == 40 ? AppColors.homeTeam : AppColors.awayTeam)
        }

        // Set number color from API or ensure good contrast
        var numberColor: Color
        if let numberColorHex = colorInfo?.number, !numberColorHex.isEmpty {
            let apiNumberColor = Color(hex: numberColorHex)

            // Check if we need to override for better contrast
            let jerseyBrightness = PlayerMarkerWithRating.calculateColorBrightness(jerseyColor)
            let numberBrightness = PlayerMarkerWithRating.calculateColorBrightness(apiNumberColor)

            if jerseyBrightness > 0.7 && numberBrightness > 0.7 {
                // Light jersey with light number - use dark number instead
                numberColor = Color.black
            } else if jerseyBrightness < 0.3 && numberBrightness < 0.3 {
                // Dark jersey with dark number - use white number instead
                numberColor = Color.white
            } else {
                // Use the API-provided number color
                numberColor = apiNumberColor
            }
        } else {
            // Default number color based on jersey brightness
            let jerseyBrightness = PlayerMarkerWithRating.calculateColorBrightness(jerseyColor)
            numberColor = jerseyBrightness > 0.6 ? Color.black : Color.white
        }

        self.player = player
        self.teamColor = jerseyColor
        self.numberColor = numberColor
        self.rating = rating
        self.showRating = showRating
        self.goals = goals
        self.assists = assists
        self.ownGoals = ownGoals
        self.yellowCards = yellowCards
        self.redCards = redCards
        self.goalIconType = goalIconType
        self.isInCrowdedRow = isInCrowdedRow
    }

    // Static helper function to determine color brightness (0 = black, 1 = white)
    static func calculateColorBrightness(_ color: Color) -> CGFloat {
        // Convert Color to UIColor to access RGB components
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0

        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

        // Calculate perceived brightness using the formula
        // (0.299*R + 0.587*G + 0.114*B)
        let brightness = (0.299 * red + 0.587 * green + 0.114 * blue)
        return brightness
    }



    // Helper to determine rating background color
    private var ratingBackgroundColor: Color {
        // Parse rating as a Double
        guard let ratingValue = Double(rating) else {
            return Color.gray // Default color if rating can't be parsed
        }

        // Color based on rating value with improved contrast for white text
        switch ratingValue {
        case 9.0...:
            return Color(hex: "8e44ad") // Purple for exceptional ratings (9.0+)
        case 8.0..<9.0:
            return Color(hex: "2980b9") // Darker blue for excellent ratings (8.0-8.9)
        case 7.0..<8.0:
            return Color(hex: "27ae60") // Darker green for good ratings (7.0-7.9)
        case 6.5..<7.0:
            return Color(hex: "d4ac0d") // Darker yellow for above average ratings (6.5-6.9)
        case 6.0..<6.5:
            return Color(hex: "d35400") // Darker orange for average ratings (6.0-6.4)
        case 3.0..<6.0:
            return Color(hex: "c0392b") // Darker red for poor ratings (3.0-5.9)
        default:
            return Color(hex: "c0392b") // Darker red for very poor ratings (below 3.0)
        }
    }

    // Helper to get player photo URL from player statistics
    private func getPlayerPhotoURL() -> String? {
        // In a real implementation, this would fetch the photo URL from the API
        // For now, we'll use a placeholder approach

        // Check if we can find this player in the mock data
        if let playerID = player.id {
            // This is a simplified example - in a real app, you would fetch this from your API
            // For Barcelona players (using mock data)
            if playerID >= 100 && playerID < 200 {
                return "https://media.api-sports.io/football/players/\(playerID).png"
            }
            // For other teams
            return "https://media.api-sports.io/football/players/\(playerID).png"
        }
        return nil
    }

    var body: some View {
        VStack(spacing: 2) {
            // Player jersey or photo with rating badge and goal/assist icons
            ZStack {
                // Check if we have a player photo
                if let photoURL = getPlayerPhotoURL() {
                    // Show player photo if available using cached image view
                    CachedImageView(
                        url: URL(string: photoURL),
                        width: 44,
                        height: 44,
                        contentMode: .fill,
                        clipShape: AnyShape(Circle()),
                        borderColor: .white,
                        borderWidth: 1,
                        fadeDuration: 0.2
                    ) {
                        // Show jersey as placeholder while loading
                        FootballJerseyShape(color: teamColor, borderColor: Color.white.opacity(0.3))
                            .frame(width: 44, height: 44)
                    }
                } else {
                    // Football jersey shape with curved neck and sleeves
                    FootballJerseyShape(color: teamColor, borderColor: Color.white.opacity(0.3))
                        .frame(width: 44, height: 44) // Larger for better visibility

                    // Player number
                    Text("\(player.number ?? 0)")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                        .foregroundColor(numberColor)
                        .offset(y: 2) // Slight offset to center in the jersey (below the neck curve)
                }

                // Rating badge (top-right corner)
                if showRating && !rating.isEmpty {
                    ZStack {
                        RoundedRectangle(cornerRadius: 6)
                            .fill(ratingBackgroundColor)
                            .frame(width: 22, height: 12) // Reduced height
                            .overlay(
                                RoundedRectangle(cornerRadius: 6)
                                    .stroke(Color.white, lineWidth: 0.5)
                            )

                        Text(rating)
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white)
                    }
                    .offset(x: 18, y: -18) // Position in top-right corner (adjusted for larger photo)
                }

                // Card icons (top-left corner)
                if redCards > 0 && yellowCards > 0 {
                    // Player has both yellow and red cards - show double yellow
                    Image("doubleyellow")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 16, height: 20)
                        .shadow(color: Color.black.opacity(0.3), radius: 1, x: 0, y: 1)
                        .offset(x: -18, y: -18) // Position in top-left corner (adjusted for larger photo)
                } else if redCards > 0 {
                    // Direct red card (no yellow)
                    Image("red-card")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 16, height: 20)
                        .shadow(color: Color.black.opacity(0.3), radius: 1, x: 0, y: 1)
                        .offset(x: -18, y: -18) // Position in top-left corner (adjusted for larger photo)
                } else if yellowCards > 0 {
                    // Only yellow card(s)
                    Image("yellow-card")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 16, height: 20)
                        .shadow(color: Color.black.opacity(0.3), radius: 1, x: 0, y: 1)
                        .offset(x: -18, y: -18) // Position in top-left corner (adjusted for larger photo)
                }

                // Goal icon (bottom-right corner) - show appropriate icon based on goal type
                if goals > 0 || goalIconType == .missedPenalty {
                    ZStack {
                        // Use the appropriate goal image asset based on the goal type
                        if goalIconType == .missedPenalty {
                            // Custom icon for missed penalty - ball with red cross
                            ZStack {
                                // Ball icon
                                Image("goal")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 16, height: 16)

                                // Red X overlay
                                Image(systemName: "xmark")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.red)
                            }
                        } else if goalIconType == .penalty {
                            // Use penalty icon for players who only scored penalties (smaller size)
                            Image("penalty")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 14, height: 14) // Smaller size for penalty icon
                        } else {
                            // Use normal goal icon for players who scored normal goals or both
                            Image("goal")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 16, height: 16)
                        }

                        if goals > 1 {
                            // Smaller capsule background with "x2" format
                            Text("x\(goals)")
                                .font(.system(size: 8, weight: .bold))
                                .foregroundColor(.black)
                                .padding(.horizontal, 3)
                                .padding(.vertical, 1)
                                .background(
                                    Capsule()
                                        .fill(Color.white)
                                        .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                                )
                                .offset(x: 8, y: -8)
                        }
                    }
                    .offset(x: 18, y: 18) // Position in bottom-right corner (adjusted for larger photo)
                }

                // Own goal icon (bottom-right corner, separate from regular goal icon)
                if ownGoals > 0 {
                    ZStack {
                        // Use the own-goal image asset exactly like in EventsTimelineView
                        Image("own-goal")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)

                        if ownGoals > 1 {
                            // Smaller capsule background with "x2" format
                            Text("x\(ownGoals)")
                                .font(.system(size: 8, weight: .bold))
                                .foregroundColor(.black)
                                .padding(.horizontal, 3)
                                .padding(.vertical, 1)
                                .background(
                                    Capsule()
                                        .fill(Color.white)
                                        .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                                )
                                .offset(x: 8, y: -8)
                        }
                    }
                    // Position in bottom-right corner, but offset from goal icon if present
                    .offset(x: goals > 0 ? 38 : 18, y: 18)
                }

                // Assist icon (bottom-left corner)
                if assists > 0 {
                    ZStack {
                        // White background with black assist icon
                        ZStack {
                            // White circular background
                            Circle()
                                .fill(Color.white)
                                .frame(width: 16, height: 16)
                                .overlay(
                                    Circle()
                                        .stroke(Color.black.opacity(0.3), lineWidth: 0.5)
                                )

                            // Black assist icon
                            Image("assist")
                                .resizable()
                                .renderingMode(.template) // Enable template mode to apply color
                                .foregroundColor(.black) // Set icon color to black
                                .scaledToFit()
                                .frame(width: 10, height: 10) // Smaller size inside the background
                        }

                        if assists > 1 {
                            // Smaller capsule background with "x2" format
                            Text("x\(assists)")
                                .font(.system(size: 8, weight: .bold))
                                .foregroundColor(.black)
                                .padding(.horizontal, 3)
                                .padding(.vertical, 1)
                                .background(
                                    Capsule()
                                        .fill(Color.white)
                                        .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                                )
                                .offset(x: -8, y: -8) // Position in top-left corner
                        }
                    }
                    .offset(x: -18, y: 18) // Position in bottom-left corner (adjusted for larger photo)
                }
            }

            // Player number and name - stacked format for crowded rows
            if let lastName = player.name?.components(separatedBy: " ").last {
                // Check if we're in a crowded row (5+ players in a row)
                if isInCrowdedRow {
                    // Stacked format (number above name)
                    VStack(spacing: 0) {
                        Text("\(player.number ?? 0).")
                            .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                            .foregroundColor(Color.white)

                        Text(lastName)
                            .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                            .foregroundColor(Color.white)
                    }
                    .lineLimit(1)
                    .fixedSize(horizontal: false, vertical: true)
                    .frame(maxWidth: 70) // Narrower width for stacked format
                } else {
                    // Standard format (e.g., "19. Yamal")
                    Text("\(player.number ?? 0). \(lastName)")
                        .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                        .foregroundColor(Color.white)
                        .lineLimit(1)
                        .fixedSize(horizontal: false, vertical: true)
                        .frame(maxWidth: 90) // Increased max width for larger photo
                }
            }
        }
        .accessibilityLabel("\(player.name ?? "Player") - \(player.number ?? 0) - Rating: \(rating)")
        .accessibilityHint("Position: \(player.pos ?? "Unknown")\(goals > 0 ? ", Goals: \(goals)" : "")\(assists > 0 ? ", Assists: \(assists)" : "")\(ownGoals > 0 ? ", Own Goals: \(ownGoals)" : "")\(yellowCards > 0 ? ", Yellow Cards: \(yellowCards)" : "")\(redCards > 0 ? ", Red Cards: \(redCards)" : "")")
    }
}

// Removed SubstitutionIndicator as it's no longer needed

// MARK: - Bench Players View

struct BenchPlayersView: View {
    let homeTeamSubstitutes: [PlayerLineupInfo]
    let awayTeamSubstitutes: [PlayerLineupInfo]
    let homeTeamId: Int
    let awayTeamId: Int
    let playerStatistics: [PlayerStatisticsInfo]?

    // Helper function to get player rating
    private func getPlayerRating(playerId: Int?, teamId: Int) -> String {
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return "" // Return empty string if no rating found
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let rating = stats.games?.rating {
            return rating
        }

        return "" // Return empty string if no rating found
    }

    // Helper to get player goals
    private func getPlayerGoals(playerId: Int?, teamId: Int) -> Int {
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return 0
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let goals = stats.goals?.total {
            return goals
        }

        return 0
    }

    // Helper to get player assists
    private func getPlayerAssists(playerId: Int?, teamId: Int) -> Int {
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return 0
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let assists = stats.goals?.assists {
            return assists
        }

        return 0
    }

    // Helper to get player yellow cards
    private func getPlayerYellowCards(playerId: Int?, teamId: Int) -> Int {
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return 0
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let cards = stats.cards,
           let yellowCards = cards.yellow {
            return yellowCards
        }

        return 0
    }

    // Helper to get player red cards
    private func getPlayerRedCards(playerId: Int?, teamId: Int) -> Int {
        guard let playerId = playerId,
              let playerStats = playerStatistics?.first(where: { $0.team.id == teamId }),
              let players = playerStats.players else {
            return 0
        }

        // Find the player in the statistics
        if let playerDetail = players.first(where: { $0.player.id == playerId }),
           let stats = playerDetail.statistics?.first,
           let cards = stats.cards,
           let redCards = cards.red {
            return redCards
        }

        return 0
    }

    var body: some View {
        VStack(spacing: 0) {
            // Bench header with divider
            HStack {
                Text("Bench")
                    .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                    .foregroundColor(AppColors.text)

                Spacer()
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, AppLayout.spacingS)

            Divider()
                .background(AppColors.separator)
                .padding(.horizontal, AppLayout.spacingS)

            // Bench players in two columns
            HStack(alignment: .top, spacing: 0) {
                // Home team substitutes (left side)
                VStack(alignment: .leading, spacing: 0) {
                    ForEach(homeTeamSubstitutes) { player in
                        BenchPlayerRow(
                            player: player.player,
                            teamColor: AppColors.homeTeam,
                            rating: getPlayerRating(playerId: player.player.id, teamId: homeTeamId),
                            goals: getPlayerGoals(playerId: player.player.id, teamId: homeTeamId),
                            assists: getPlayerAssists(playerId: player.player.id, teamId: homeTeamId),
                            yellowCards: getPlayerYellowCards(playerId: player.player.id, teamId: homeTeamId),
                            redCards: getPlayerRedCards(playerId: player.player.id, teamId: homeTeamId)
                        )

                        if player.id != homeTeamSubstitutes.last?.id {
                            Divider()
                                .background(AppColors.separator.opacity(0.5))
                                .padding(.horizontal, AppLayout.spacingM)
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // Vertical divider between teams
                Rectangle()
                    .fill(AppColors.separator)
                    .frame(width: 1)
                    .padding(.vertical, AppLayout.spacingS)

                // Away team substitutes (right side)
                VStack(alignment: .leading, spacing: 0) {
                    ForEach(awayTeamSubstitutes) { player in
                        BenchPlayerRow(
                            player: player.player,
                            teamColor: AppColors.awayTeam,
                            rating: getPlayerRating(playerId: player.player.id, teamId: awayTeamId),
                            goals: getPlayerGoals(playerId: player.player.id, teamId: awayTeamId),
                            assists: getPlayerAssists(playerId: player.player.id, teamId: awayTeamId),
                            yellowCards: getPlayerYellowCards(playerId: player.player.id, teamId: awayTeamId),
                            redCards: getPlayerRedCards(playerId: player.player.id, teamId: awayTeamId)
                        )

                        if player.id != awayTeamSubstitutes.last?.id {
                            Divider()
                                .background(AppColors.separator.opacity(0.5))
                                .padding(.horizontal, AppLayout.spacingM)
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.vertical, AppLayout.spacingM)
        }
        .background(AppColors.secondaryBackground)
    }
}

// MARK: - Bench Player Row

struct BenchPlayerRow: View {
    let player: PlayerLineupInfo.PlayerInfo
    let teamColor: Color
    let rating: String
    let goals: Int
    let assists: Int
    let yellowCards: Int
    let redCards: Int
    var alignment: HorizontalAlignment = .leading

    // Helper to get player photo URL
    private func getPlayerPhotoURL() -> String? {
        // In a real implementation, this would fetch the photo URL from the API
        if let playerID = player.id {
            return "https://media.api-sports.io/football/players/\(playerID).png"
        }
        return nil
    }

    // Helper to determine rating background color
    private var ratingBackgroundColor: Color {
        // Parse rating as a Double
        guard let ratingValue = Double(rating) else {
            return Color.gray // Default color if rating can't be parsed
        }

        // Color based on rating value with improved contrast for white text
        switch ratingValue {
        case 9.0...:
            return Color(hex: "8e44ad") // Purple for exceptional ratings (9.0+)
        case 8.0..<9.0:
            return Color(hex: "2980b9") // Darker blue for excellent ratings (8.0-8.9)
        case 7.0..<8.0:
            return Color(hex: "27ae60") // Darker green for good ratings (7.0-7.9)
        case 6.5..<7.0:
            return Color(hex: "d4ac0d") // Darker yellow for above average ratings (6.5-6.9)
        case 6.0..<6.5:
            return Color(hex: "d35400") // Darker orange for average ratings (6.0-6.4)
        case 3.0..<6.0:
            return Color(hex: "c0392b") // Darker red for poor ratings (3.0-5.9)
        default:
            return Color(hex: "c0392b") // Darker red for very poor ratings (below 3.0)
        }
    }

    var body: some View {
        HStack(spacing: 12) {
            // Player photo or jersey with stats overlays
            ZStack {
                // Player photo or team color circle using cached image view
                if let photoURL = getPlayerPhotoURL() {
                    CachedImageView.playerPhoto(
                        url: photoURL,
                        size: 40,
                        playerName: player.name ?? "Player"
                    )
                } else {
                    // Colored circle with player number
                    ZStack {
                        Circle()
                            .fill(teamColor)
                            .frame(width: 40, height: 40)

                        Text("\(player.number ?? 0)")
                            .font(AppTypography.dynamicFont(style: .caption, weight: .bold))
                            .foregroundColor(.white)
                    }
                }

                // Rating badge (top-right corner)
                if !rating.isEmpty {
                    ZStack {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(ratingBackgroundColor)
                            .frame(width: 22, height: 12) // Reduced height
                            .overlay(
                                RoundedRectangle(cornerRadius: 4)
                                    .stroke(Color.white, lineWidth: 0.5)
                            )

                        Text(rating)
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white)
                    }
                    .offset(x: 15, y: -15) // Position in top-right corner
                }

                // Card icons (top-left corner)
                if redCards > 0 && yellowCards > 0 {
                    // Player has both yellow and red cards - show double yellow
                    Image("doubleyellow")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 12, height: 16)
                        .shadow(color: Color.black.opacity(0.3), radius: 1, x: 0, y: 1)
                        .offset(x: -15, y: -15) // Position in top-left corner
                } else if redCards > 0 {
                    // Direct red card (no yellow)
                    Rectangle()
                        .fill(Color.red)
                        .frame(width: 8, height: 12)
                        .cornerRadius(1)
                        .shadow(color: Color.black.opacity(0.3), radius: 1, x: 0, y: 1)
                        .offset(x: -15, y: -15) // Position in top-left corner
                } else if yellowCards > 0 {
                    // Only yellow card(s)
                    Rectangle()
                        .fill(Color.yellow)
                        .frame(width: 8, height: 12)
                        .cornerRadius(1)
                        .shadow(color: Color.black.opacity(0.3), radius: 1, x: 0, y: 1)
                        .offset(x: -15, y: -15) // Position in top-left corner
                }

                // Goal icon (bottom-right corner)
                if goals > 0 {
                    ZStack {
                        Image(systemName: "soccerball")
                            .font(.system(size: 10))
                            .foregroundColor(.white)
                            .padding(2)
                            .background(
                                Circle()
                                    .fill(Color.black.opacity(0.7))
                            )

                        if goals > 1 {
                            // Smaller text with goal count
                            Text("\(goals)")
                                .font(.system(size: 8, weight: .bold))
                                .foregroundColor(.white)
                                .padding(2)
                                .background(
                                    Circle()
                                        .fill(Color.black.opacity(0.7))
                                )
                                .offset(x: 8, y: -8)
                        }
                    }
                    .offset(x: 15, y: 15) // Position in bottom-right corner
                }

                // Assist icon (bottom-left corner)
                if assists > 0 {
                    ZStack {
                        Image(systemName: "arrow.up.forward")
                            .font(.system(size: 10))
                            .foregroundColor(.white)
                            .padding(2)
                            .background(
                                Circle()
                                    .fill(Color.black.opacity(0.7))
                            )

                        if assists > 1 {
                            // Smaller text with assist count
                            Text("\(assists)")
                                .font(.system(size: 8, weight: .bold))
                                .foregroundColor(.white)
                                .padding(2)
                                .background(
                                    Circle()
                                        .fill(Color.black.opacity(0.7))
                                )
                                .offset(x: -8, y: -8)
                        }
                    }
                    .offset(x: -15, y: 15) // Position in bottom-left corner
                }
            }

            VStack(alignment: .leading, spacing: 2) {
                // Player number and name (e.g., "19. Yamal")
                Text("\(player.number ?? 0). \(player.name?.components(separatedBy: " ").last ?? "Unknown")")
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                    .foregroundColor(AppColors.text)
                    .lineLimit(1)

                // Player position
                Text(getPositionName(from: player.pos ?? ""))
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(AppColors.secondaryText)
                    .lineLimit(1)
            }

            Spacer()
        }
        .padding(.vertical, AppLayout.spacingS)
        .padding(.horizontal, AppLayout.spacingM)
    }

    // Helper to convert position code to full name
    private func getPositionName(from posCode: String) -> String {
        switch posCode {
        case "G":
            return "Goalkeeper"
        case "D":
            return "Defender"
        case "M":
            return "Midfielder"
        case "F":
            return "Forward"
        default:
            return posCode
        }
    }
}



// MARK: - Preview

#Preview {
    // Use the mock fixture which now includes lineup data
    LineupsView(lineups: Fixture.mock.lineups, playerStatistics: Fixture.mock.playerStatistics, fixture: Fixture.mock)
}
