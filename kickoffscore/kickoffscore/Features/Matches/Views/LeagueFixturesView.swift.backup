import SwiftUI
import King<PERSON>er

struct LeagueFixturesView: View {
    @StateObject private var viewModel = LeagueFixturesViewModel()
    @StateObject private var authViewModel = AuthViewModel()
    @EnvironmentObject private var navigationState: NavigationStateManager
    @State private var selectedDate: Date = Date()
    @State private var showingDatePicker = false
    @State private var showLiveOnly = false // State to track if we should show only live fixtures

    // Initialize the view and load the expanded leagues state from UserDefaults
    init() {
        // Load the collapsed leagues from UserDefaults
        let defaults = UserDefaults.standard
        let collapsedLeaguesKey = "com.kickoffscore.collapsedLeagues"
        let collapsedLeagues = defaults.array(forKey: collapsedLeaguesKey) as? [Int] ?? []

        // Get all leagues from defaults for pre-initialization
        // This is just a placeholder that will be updated when actual data loads
        // But it prevents the momentary collapse state on app restart
        let allLeaguesKey = "com.kickoffscore.allLeaguesIds"
        let allLeaguesIds = defaults.array(forKey: allLeaguesKey) as? [Int] ?? []

        // Create an initial expanded state with all known leagues except the collapsed ones
        // This prevents the momentary collapsed state when reopening the app
        var initialExpandedState = Set<Int>()
        for leagueId in allLeaguesIds {
            if !collapsedLeagues.contains(leagueId) {
                initialExpandedState.insert(leagueId)
            }
        }

        // We can't directly initialize @State properties in init, so we use _expandedLeagues
        // to set the initial value of the wrapped property
        _expandedLeagues = State(initialValue: initialExpandedState)

        // Mark that we haven't fully initialized the expanded state yet
        // This will trigger complete initialization in onAppear with actual league data
        _hasInitializedExpandedState = State(initialValue: false)

        // Log the initial state
        print("LeagueFixturesView initialized with \(collapsedLeagues.count) collapsed leagues and \(initialExpandedState.count) expanded leagues from UserDefaults")
    }

    // Cached filtered leagues and fixtures
    @State private var cachedFilteredLeagues: [LeagueWithFixtures] = []
    @State private var lastFilterState: Bool = false
    @State private var lastLeaguesHash: Int = 0

    // Track expanded/collapsed state for each league - initialize with saved state from UserDefaults
    @State private var expandedLeagues: Set<Int> = {
        // Load collapsed leagues from UserDefaults
        let defaults = UserDefaults.standard
        let collapsedLeaguesKey = "com.kickoffscore.collapsedLeagues"
        let collapsedLeagues = defaults.array(forKey: collapsedLeaguesKey) as? [Int] ?? []

        // We'll initialize with an empty set, but the actual initialization will happen in init()
        return Set<Int>()
    }()

    // Flag to track if we've initialized the expanded leagues state
    @State private var hasInitializedExpandedState: Bool = false

    // UserDefaults key for saving collapsed leagues
    private let collapsedLeaguesKey = "com.kickoffscore.collapsedLeagues"

    // Timer to force UI refresh when live filter is active
    @State private var refreshTimer: Timer? = nil

    // Store the last observed leagues for onChange comparison in iOS 16
    @State private var lastObservedLeagues: [LeagueWithFixtures] = []

    // Optimized computed property to filter leagues and fixtures based on live filter state
    private var filteredLeaguesWithFixtures: [LeagueWithFixtures] {
        // If live filter is not active, return all leagues and fixtures
        guard showLiveOnly else {
            return viewModel.leaguesWithFixtures
        }

        // Always recalculate when live filter is active to ensure we have the latest data
        // This ensures timer values are always up-to-date

        // Use the liveStatusCodes from the ViewModel for consistency
        let liveStatusCodes = viewModel.liveStatusCodes

        // Filter leagues and their fixtures with explicit type annotation
        let filtered = viewModel.leaguesWithFixtures.compactMap { (league: LeagueWithFixtures) -> LeagueWithFixtures? in
            // Filter fixtures to only include live ones
            let liveFixtures = league.fixtures.filter { fixture in
                guard let status = fixture.status.short else { return false }
                return liveStatusCodes.contains(status)
            }

            // Only include leagues that have at least one live fixture
            if liveFixtures.isEmpty {
                return nil
            }

            // Return a new LeagueWithFixtures with only the live fixtures
            return LeagueWithFixtures(
                league: league.league,
                fixtures: liveFixtures
            )
        }

        // Update the current hash for comparison in other parts of the code
        let currentLeaguesHash = viewModel.leaguesWithFixtures.hashValue
        DispatchQueue.main.async {
            self.updateFilterCache(filtered: filtered, hash: currentLeaguesHash)
        }

        return filtered
    }

    // Helper method to update the cache outside of the view update cycle
    private func updateFilterCache(filtered: [LeagueWithFixtures], hash: Int) {
        self.cachedFilteredLeagues = filtered
        self.lastFilterState = showLiveOnly
        self.lastLeaguesHash = hash
    }

    // Start a timer to force UI refresh when live filter is active
    private func startUIRefreshTimer() {
        // Stop any existing timer first
        stopUIRefreshTimer()

        // Create a new timer that fires every 3 seconds (reduced from 5 seconds for more frequent updates)
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
            // Force view to refresh regardless of live filter state
            // This ensures timer updates are visible even for regular views
            DispatchQueue.main.async {
                // This will cause the filteredLeaguesWithFixtures computed property to recalculate
                self.lastLeaguesHash = 0

                // Force a UI update by creating a small state change
                // This helps ensure SwiftUI recognizes the change and updates the view
                let currentFilter = self.showLiveOnly
                self.showLiveOnly = !currentFilter
                self.showLiveOnly = currentFilter
            }
        }
    }

    // Stop the UI refresh timer
    private func stopUIRefreshTimer() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }

    // Toggle expanded/collapsed state for a league
    private func toggleLeagueExpansion(leagueId: Int) {
        withAnimation(.easeInOut(duration: 0.3)) {
            if expandedLeagues.contains(leagueId) {
                expandedLeagues.remove(leagueId)
                saveCollapsedState(leagueId: leagueId, isCollapsed: true)
            } else {
                expandedLeagues.insert(leagueId)
                saveCollapsedState(leagueId: leagueId, isCollapsed: false)
            }
        }
    }

    // Save collapsed state to UserDefaults
    private func saveCollapsedState(leagueId: Int, isCollapsed: Bool) {
        // Get current collapsed leagues from UserDefaults
        let defaults = UserDefaults.standard
        var collapsedLeagues = defaults.array(forKey: collapsedLeaguesKey) as? [Int] ?? []

        if isCollapsed {
            // Add to collapsed leagues if not already there
            if !collapsedLeagues.contains(leagueId) {
                collapsedLeagues.append(leagueId)
            }
        } else {
            // Remove from collapsed leagues
            collapsedLeagues.removeAll { $0 == leagueId }
        }

        // Save back to UserDefaults
        defaults.set(collapsedLeagues, forKey: collapsedLeaguesKey)
        print("Saved collapsed leagues to UserDefaults: \(collapsedLeagues)")
    }

    // Load collapsed state from UserDefaults
    private func loadCollapsedState() -> Set<Int> {
        let defaults = UserDefaults.standard
        let collapsedLeagues = defaults.array(forKey: collapsedLeaguesKey) as? [Int] ?? []
        print("Loaded collapsed leagues from UserDefaults: \(collapsedLeagues)")
        return Set(collapsedLeagues)
    }

    // Save all league IDs to UserDefaults for proper initialization on app restart
    private func saveAllLeagueIds(leagues: [LeagueWithFixtures]) {
        let defaults = UserDefaults.standard
        let allLeaguesKey = "com.kickoffscore.allLeaguesIds"

        // Extract all league IDs
        let allLeagueIds = leagues.map { $0.league.id }

        // Save to UserDefaults
        defaults.set(allLeagueIds, forKey: allLeaguesKey)
        print("Saved \(allLeagueIds.count) league IDs to UserDefaults for future initialization")
    }

    // Update expanded state when toggling live filter to prevent flicker
    private func updateExpandedStateAfterLiveToggle(previousExpandedState: Set<Int>) {
        // Get collapsed leagues from UserDefaults
        let collapsedLeagues = loadCollapsedState()

        // Create a combined state, starting with the previous expanded state
        var newExpandedState = previousExpandedState

        // Get only the leagues that are currently visible
        let visibleLeagues = showLiveOnly ? filteredLeaguesWithFixtures : viewModel.leaguesWithFixtures

        // For each currently visible league, ensure it's expanded if it should be
        withAnimation(.easeInOut(duration: 0.3)) {
            for league in visibleLeagues {
                let leagueId = league.league.id

                // If this league isn't in the collapsed list, it should be expanded
                if !collapsedLeagues.contains(leagueId) {
                    newExpandedState.insert(leagueId)
                } else {
                    // Otherwise remove it from expanded state
                    newExpandedState.remove(leagueId)
                }
            }

            // Update the expanded leagues set all at once to minimize UI updates
            expandedLeagues = newExpandedState
        }

        // Save all leagues to UserDefaults for future initialization
        saveAllLeagueIds(leagues: viewModel.leaguesWithFixtures)

        print("Updated expanded state after live toggle: \(expandedLeagues.count) leagues expanded")
    }

    // Update expanded state when changing dates to prevent flicker
    private func updateExpandedStateForDateChange(previousExpandedState: Set<Int>) {
        // Get collapsed leagues from UserDefaults
        let collapsedLeagues = loadCollapsedState()

        // Create a new expanded state to minimize UI updates
        var newExpandedState = Set<Int>()

        // First, include all leagues from previous state that aren't explicitly collapsed
        // This ensures continuity in the UI
        for leagueId in previousExpandedState {
            if !collapsedLeagues.contains(leagueId) {
                newExpandedState.insert(leagueId)
            }
        }

        // Then add any new leagues from the current fixture set that should be expanded
        for league in viewModel.leaguesWithFixtures {
            let leagueId = league.league.id

            // If it's not in the collapsed list, it should be expanded
            if !collapsedLeagues.contains(leagueId) {
                newExpandedState.insert(leagueId)
            }
            // Don't need to remove here - we're building a new set from scratch
        }

        // Apply the new state all at once
        expandedLeagues = newExpandedState

        // Save all leagues to UserDefaults for future initialization
        saveAllLeagueIds(leagues: viewModel.leaguesWithFixtures)

        print("Updated expanded state after date change: \(expandedLeagues.count) leagues expanded")
    }

    // Initialize all leagues as expanded when data is loaded, except those saved as collapsed
    private func initializeExpandedLeagues(leagues: [LeagueWithFixtures]) {
        // Get collapsed leagues from UserDefaults
        let collapsedLeagues = loadCollapsedState()

        // Save all league IDs to UserDefaults for future initialization on app restart
        saveAllLeagueIds(leagues: leagues)

        // Only clear and reinitialize if we haven't done so already or if the leagues have changed
        // This prevents leagues from being collapsed when navigating back to this view
        if !hasInitializedExpandedState || expandedLeagues.isEmpty {
            print("Initializing expanded leagues state for the first time or after reset")

            // Clear the current expanded leagues set to start fresh
            expandedLeagues.removeAll()

            // By default, expand all leagues when they're first loaded, except those saved as collapsed
            for league in leagues {
                if !collapsedLeagues.contains(league.league.id) {
                    expandedLeagues.insert(league.league.id)
                }
            }

            // Mark that we've initialized the expanded state
            hasInitializedExpandedState = true

            // Force immediate UI update to prevent flicker
            withAnimation(.easeInOut(duration: 0.1)) {
                // This empty block forces SwiftUI to recognize the state change
            }
        } else {
            print("Expanded state already initialized, updating with any new leagues")

            // Just add any new leagues that aren't in the collapsed list
            var addedNewLeagues = false
            for league in leagues {
                if !collapsedLeagues.contains(league.league.id) && !expandedLeagues.contains(league.league.id) {
                    expandedLeagues.insert(league.league.id)
                    addedNewLeagues = true
                }
            }

            // Only animate if we actually added new leagues
            if addedNewLeagues {
                withAnimation(.easeInOut(duration: 0.3)) {
                    // This empty block forces SwiftUI to recognize the state change
                }
            }
        }

        // Log the state for debugging
        print("Expanded leagues: \(expandedLeagues.count) out of \(leagues.count)")
        print("Collapsed leagues: \(collapsedLeagues.count)")
        print("Expanded league IDs: \(expandedLeagues)")
    }

    // Handle pagination by expanding new leagues that are loaded when scrolling
    private func handlePaginationExpandedState() {
        // Only process if we have leagues and have already initialized the expanded state
        if !viewModel.leaguesWithFixtures.isEmpty && hasInitializedExpandedState {
            // Get the current count of leagues in the expanded state for logging
            let currentExpandedCount = expandedLeagues.count
            let totalLeaguesCount = viewModel.leaguesWithFixtures.count

            print("Handling pagination expanded state - Current leagues: \(totalLeaguesCount), Expanded: \(currentExpandedCount)")

            // Save current expanded state to reference
            let previousExpandedState = expandedLeagues

            // First save all league IDs to ensure no flicker on next app launch
            saveAllLeagueIds(leagues: viewModel.leaguesWithFixtures)

            // Use a more responsive approach with async and animation
            // This ensures smoother user experience when new leagues appear
            DispatchQueue.main.async {
                withAnimation(.easeInOut(duration: 0.25)) {
                    // Create a specialized function for pagination expansion
                    self.updateExpandedStateForPagination(previousExpandedState: previousExpandedState)
                }
            }

            // Check if we need to load more leagues
            let canLoadMore = !viewModel.isLoadingMore && viewModel.hasMorePages
            let hasLastLeague = viewModel.leaguesWithFixtures.last != nil

            if canLoadMore && hasLastLeague {
                // If the last league in the current page is collapsed, we might need to trigger loading more
                let lastLeague = viewModel.leaguesWithFixtures.last!
                let isLastLeagueCollapsed = !expandedLeagues.contains(lastLeague.league.id)

                if isLastLeagueCollapsed {
                    print("Last league is collapsed, checking if we need to load more")
                    viewModel.loadMoreLeagueFixturesIfNeeded()
                }
            }
        }
        else {
            print("Cannot handle pagination expanded state - leagues empty or expanded state not initialized")
            print("Leagues count: \(viewModel.leaguesWithFixtures.count), Initialized: \(hasInitializedExpandedState)")
        }
    }

    // Update expanded state when new leagues are loaded through pagination
    private func updateExpandedStateForPagination(previousExpandedState: Set<Int>) {
        // Get collapsed leagues from UserDefaults
        let collapsedLeagues = loadCollapsedState()

        // Start with the previous expanded state
        var newExpandedState = previousExpandedState

        // Then add any new leagues that should be expanded
        // This will only affect newly loaded leagues
        var newLeaguesAdded = 0

        for league in viewModel.leaguesWithFixtures {
            let leagueId = league.league.id

            // If it's not collapsed and not already expanded, add it
            if !collapsedLeagues.contains(leagueId) && !newExpandedState.contains(leagueId) {
                newExpandedState.insert(leagueId)
                newLeaguesAdded += 1
                print("Expanded new league during pagination: \(league.league.name ?? "Unknown") (ID: \(leagueId))")
            }
        }

        // Only update if we actually added new leagues
        if newLeaguesAdded > 0 {
            expandedLeagues = newExpandedState
            print("Pagination expanded \(newLeaguesAdded) new leagues, total expanded: \(expandedLeagues.count)")
        }
    }

    var body: some View {
        NavigationStack(path: $navigationState.navigationPath) {
            ZStack {
                // Background color
                AppColors.background
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Top Navigation Row
                    HStack {
                        // App name in top left corner
                        Text("KICKOFFSCORE")
                            .font(.system(size: 18, weight: .heavy))
                            .tracking(0.5) // Add slight letter spacing
                            .foregroundColor(Color(UIColor { traitCollection in
                                return traitCollection.userInterfaceStyle == .dark ?
                                    UIColor.white : UIColor.black
                            }))
                            .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1) // Subtle shadow for depth
                            .padding(.leading, AppLayout.spacingM)
                            .padding(.vertical, AppLayout.spacingXS)
                            .accessibilityLabel("KickoffScore")

                        Spacer()

                        // Live Button
                        Button {
                            // First save current expanded state to ensure it's preserved during toggle
                            saveAllLeagueIds(leagues: viewModel.leaguesWithFixtures)

                            // Preserve current expanded state for reference
                            let currentExpandedState = expandedLeagues

                            withAnimation(.easeInOut(duration: 0.3)) {
                                showLiveOnly.toggle()
                                // Force recalculation of filtered leagues on next access
                                DispatchQueue.main.async {
                                    // Reset the filter state to force recalculation
                                    self.lastFilterState = false
                                    self.lastLeaguesHash = 0

                                    // When live filter is toggled, refresh live fixtures with the new state
                                    let calendar = Calendar.current
                                    let isToday = calendar.isDateInToday(selectedDate)

                                    if showLiveOnly {
                                        // When enabling live filter, always switch to today's date
                                        let today = Calendar.current.startOfDay(for: Date())
                                        if !Calendar.current.isDateInToday(selectedDate) {
                                            // Only update if we're not already on today
                                            selectedDate = today
                                        }

                                        // Call our new method to fetch all live fixtures regardless of pagination
                                        viewModel.fetchAllLiveFixtures()

                                        // Ensure expanded state is preserved by setting a flag to prevent reset
                                        // We'll handle this differently to keep expansion states consistent
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                            // Merge current expanded state with new live fixtures
                                            updateExpandedStateAfterLiveToggle(previousExpandedState: currentExpandedState)
                                        }

                                        // Save the current state for restoration when toggling off
                                        self.cachedFilteredLeagues = []
                                    } else {
                                        // If turning off live filter, restore the previous state by fetching all fixtures
                                        // This ensures we go back to showing all fixtures for the selected date
                                        viewModel.fetchLeagueFixtures(date: selectedDate)

                                        // Ensure expanded state is preserved when returning to normal view
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                            // Merge current expanded state with all fixtures
                                            updateExpandedStateAfterLiveToggle(previousExpandedState: currentExpandedState)
                                        }
                                    }

                                    // Always start UI refresh timer for today's fixtures to ensure timers update
                                    if isToday {
                                        startUIRefreshTimer()
                                    } else {
                                        // Only stop the timer if we're not viewing today's fixtures
                                        stopUIRefreshTimer()
                                    }
                                }
                            }
                        } label: {
                            // Clock icon that changes to a live clock when filter is active
                            Image(systemName: showLiveOnly ? "clock.badge.fill" : "clock")
                                .font(.system(size: AppLayout.iconSizeM))
                                .foregroundColor(showLiveOnly ? Color.red : Color(UIColor { traitCollection in
                                    return traitCollection.userInterfaceStyle == .dark ?
                                        UIColor.white : UIColor.black
                                }))
                                .frame(width: 40, height: 40)
                                // Add a subtle animation for the color and icon change
                                .animation(.easeInOut(duration: 0.2), value: showLiveOnly)
                        }
                        .accessibilityInfo(label: "Live matches", hint: showLiveOnly ? "Currently showing only live matches. Tap to show all matches" : "Tap to show only live matches")

                        // Calendar Button
                        Button {
                            showingDatePicker = true
                        } label: {
                            Image(systemName: "calendar")
                                .font(.system(size: AppLayout.iconSizeM))
                                .foregroundColor(Color(UIColor { traitCollection in
                                    return traitCollection.userInterfaceStyle == .dark ?
                                        UIColor.white : UIColor.black
                                }))
                                .frame(width: 40, height: 40)
                                // Background removed for cleaner look
                        }
                        .padding(.trailing, AppLayout.spacingM)
                        .accessibilityInfo(label: "Calendar", hint: "Open date picker to select a specific date")
                    }
                    .background(AppColors.tertiaryBackground)
                    .edgesIgnoringSafeArea(.horizontal)

                    // Date Selector Row
                    LeagueDateSelector(selectedDate: $selectedDate, onDateSelected: { date in
                        // Reset hasInitiallyLoaded flag when date changes to force data refresh
                        UserDefaults.standard.set(false, forKey: "com.kickoffscore.hasInitiallyLoaded")
                        // Check if we're changing to a non-today date while live filter is active
                        let calendar = Calendar.current
                        let isToday = calendar.isDateInToday(date)

                        // Save the current expanded state before changing date
                        print("Changing date - current expanded state: \(expandedLeagues.count) leagues")

                        if isToday && showLiveOnly {
                            // If selecting Today while live filter is active, refresh live fixtures
                            viewModel.fetchAllLiveFixtures()
                        } else if !isToday && showLiveOnly {
                            // If changing to a non-today date and live filter is active, disable it
                            withAnimation {
                                showLiveOnly = false
                                // Fetch fixtures for the selected date
                                viewModel.fetchLeagueFixtures(date: date)

                                // Initialize expanded leagues when data is loaded, but preserve user choices
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    if !viewModel.leaguesWithFixtures.isEmpty {
                                        // Only initialize if we haven't already or if the expanded state was lost
                                        if !hasInitializedExpandedState || expandedLeagues.isEmpty {
                                            print("Initializing expanded leagues after date change")
                                            initializeExpandedLeagues(leagues: viewModel.leaguesWithFixtures)
                                        } else {
                                            print("Preserving expanded state after date change")
                                            // Just add any new leagues that aren't in the collapsed list
                                            let collapsedLeagues = loadCollapsedState()
                                            for league in viewModel.leaguesWithFixtures {
                                                if !collapsedLeagues.contains(league.league.id) && !expandedLeagues.contains(league.league.id) {
                                                    expandedLeagues.insert(league.league.id)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            // Normal case - just fetch fixtures for the selected date
                            viewModel.fetchLeagueFixtures(date: date)

                            // Initialize expanded leagues when data is loaded, but preserve user choices
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                if !viewModel.leaguesWithFixtures.isEmpty {
                                    // Only initialize if we haven't already or if the expanded state was lost
                                    if !hasInitializedExpandedState || expandedLeagues.isEmpty {
                                        print("Initializing expanded leagues after date change")
                                        initializeExpandedLeagues(leagues: viewModel.leaguesWithFixtures)
                                    } else {
                                        print("Preserving expanded state after date change")
                                        // Just add any new leagues that aren't in the collapsed list
                                        let collapsedLeagues = loadCollapsedState()
                                        for league in viewModel.leaguesWithFixtures {
                                            if !collapsedLeagues.contains(league.league.id) && !expandedLeagues.contains(league.league.id) {
                                                expandedLeagues.insert(league.league.id)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    })
                    .zIndex(1) // Ensure date selector stays on top

                    // Main content
                    ZStack {
                        ScrollViewReader { proxy in
                            ScrollView {
                                // Top padding to separate header from first content - using smallest spacing
                                Color.clear.frame(height: AppLayout.spacingXS)
                                    .id("topAnchor") // Add ID for scroll-to-top functionality

                            LazyVStack(spacing: AppLayout.spacingS) { // Balanced spacing between league containers
                                if viewModel.isLoading {
                                    SimpleSkeletonListView(itemCount: 10, showHeader: false)
                                        .padding(.top, AppLayout.spacingS)
                                        .transition(.opacity)
                                        .animation(.easeInOut(duration: 0.3), value: viewModel.isLoading)
                                        .frame(maxWidth: .infinity, alignment: .center)
                                        .accessibilityInfo(label: "Loading fixtures", hint: "Please wait while fixtures are being loaded")
                                } else if let errorMessage = viewModel.errorMessage {
                                    VStack(spacing: AppLayout.spacingM) {
                                        Image(systemName: "exclamationmark.triangle")
                                            .font(.system(size: AppLayout.iconSizeL))
                                            .foregroundColor(.red)

                                        Text("Error: \(errorMessage)")
                                            .font(AppTypography.dynamicFont(style: .body))
                                            .foregroundColor(.red)
                                            .multilineTextAlignment(.center)
                                    }
                                    .padding(AppLayout.spacingL)
                                    .accessibilityInfo(label: "Error loading fixtures", hint: errorMessage)
                                } else if viewModel.leaguesWithFixtures.isEmpty { // Check original list
                                    VStack(spacing: AppLayout.spacingM) {
                                        Image(systemName: "calendar.badge.exclamationmark")
                                            .font(.system(size: AppLayout.iconSizeL))
                                            .foregroundColor(Color(UIColor.secondaryLabel))

                                        Text("No matches found for this date.")
                                            .font(AppTypography.dynamicFont(style: .body))
                                            .foregroundColor(Color(UIColor.secondaryLabel))
                                    }
                                    .padding(AppLayout.spacingL)
                                    .accessibilityInfo(label: "No matches", hint: "There are no matches scheduled for this date")
                                } else if showLiveOnly && filteredLeaguesWithFixtures.isEmpty {
                                    // Show a message when live filter is active but no live matches are found
                                    VStack(spacing: AppLayout.spacingM) {
                                        Image(systemName: "tv")
                                            .font(.system(size: AppLayout.iconSizeL))
                                            .foregroundColor(Color(UIColor.secondaryLabel))

                                        Text("No live matches at the moment.")
                                            .font(AppTypography.dynamicFont(style: .body))
                                            .foregroundColor(Color(UIColor.secondaryLabel))

                                        Button {
                                            withAnimation {
                                                showLiveOnly = false

                                                // Reset the filter state to force recalculation
                                                self.lastFilterState = false
                                                self.lastLeaguesHash = 0

                                                // When turning off live filter, refresh all fixtures for today
                                                let calendar = Calendar.current
                                                let isToday = calendar.isDateInToday(selectedDate)

                                                // Restore the previous state by fetching all fixtures
                                                // This ensures we go back to showing all fixtures for the selected date
                                                viewModel.fetchLeagueFixtures(date: selectedDate)

                                                // Keep UI refresh timer running for today's fixtures if needed
                                                if isToday {
                                                    startUIRefreshTimer()
                                                } else {
                                                    // Only stop the timer if we're not viewing today's fixtures
                                                    stopUIRefreshTimer()
                                                }
                                            }
                                        } label: {
                                            Text("Show all matches")
                                                .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                                .foregroundColor(AppColors.Brand.primary)
                                                .padding(.top, AppLayout.spacingS)
                                        }
                                    }
                                    .padding(AppLayout.spacingL)
                                    .accessibilityInfo(label: "No live matches", hint: "There are no live matches at the moment")
                                } else {
                                    ForEach(filteredLeaguesWithFixtures) { leagueWithFixtures in
                                        LeagueFixtureItemView(
                                            leagueWithFixtures: leagueWithFixtures,
                                            expandedLeagues: $expandedLeagues,
                                            viewModel: viewModel,
                                            toggleLeagueExpansion: toggleLeagueExpansion,
                                            handlePaginationExpandedState: handlePaginationExpandedState
                                        )
                                    }

                                    // Loading indicator for pagination
                                    if viewModel.isLoadingMore {
                                        PaginationLoadingView(
                                            viewModel: viewModel,
                                            handlePaginationExpandedState: handlePaginationExpandedState
                                        )
                                    }

                                    // Add extra space at the bottom to ensure content is visible above tab bar
                                    Color.clear
                                        .frame(height: 100) // Increased height to ensure content is fully visible
                                        .id("bottomSpacer")
                                }
                            }
                            .padding(.bottom, AppLayout.spacingXS) // Small padding above tab bar
                            .scrollIndicators(.hidden) // Keep scroll indicators hidden
                            .edgesIgnoringSafeArea(.bottom) // Edge-to-edge UI
                            .onChange(of: navigationState.shouldScrollToTop) { shouldScroll in
                                if shouldScroll {
                                    withAnimation(.easeInOut(duration: 0.5)) {
                                        proxy.scrollTo("topAnchor", anchor: .top)
                                    }
                                }
                            }
                        }
                        .refreshable {
                            // If we're viewing today's fixtures and there are live fixtures,
                            // just refresh the live fixtures instead of reloading everything
                            let calendar = Calendar.current
                            let isToday = calendar.isDateInToday(selectedDate)

                            // Save the current expanded state before refreshing
                            let currentExpandedState = expandedLeagues
                            print("Saving current expanded state before refresh: \(currentExpandedState.count) leagues")

                            if isToday && (viewModel.hasLiveFixtures || showLiveOnly) {
                                if showLiveOnly {
                                    // Use special fetch for live filter
                                    viewModel.fetchAllLiveFixtures()
                                } else {
                                    // Just refresh live fixtures normally
                                    viewModel.refreshLiveFixtures(liveFilterActive: false)
                                }
                            } else {
                                viewModel.fetchLeagueFixtures(date: selectedDate)

                                // Initialize expanded leagues when data is loaded, preserving current state
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    if !viewModel.leaguesWithFixtures.isEmpty {
                                        // Only initialize if we haven't already or if the expanded state was lost
                                        if !hasInitializedExpandedState || expandedLeagues.isEmpty {
                                            print("Initializing expanded leagues after refresh")
                                            initializeExpandedLeagues(leagues: viewModel.leaguesWithFixtures)
                                        } else {
                                            print("Preserving expanded state after refresh")
                                            // Just add any new leagues that aren't in the collapsed list
                                            let collapsedLeagues = loadCollapsedState()
                                            for league in viewModel.leaguesWithFixtures {
                                                if !collapsedLeagues.contains(league.league.id) && !expandedLeagues.contains(league.league.id) {
                                                    expandedLeagues.insert(league.league.id)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                }
            }
            .navigationBarHidden(true) // Hide default navigation bar
            .sheet(isPresented: $showingDatePicker) {
                LeagueDatePickerView(selectedDate: $selectedDate, onDateSelected: { date in
                    // Reset hasInitiallyLoaded flag when date changes to force data refresh
                    UserDefaults.standard.set(false, forKey: "com.kickoffscore.hasInitiallyLoaded")
                    // Check if we're changing to a non-today date while live filter is active
                    let calendar = Calendar.current
                    let isToday = calendar.isDateInToday(date)

                    // Important: Pre-save expanded state to UserDefaults to ensure no flicker on data reload
                    let defaults = UserDefaults.standard
                    let collapsedLeaguesArray = loadCollapsedState().map { $0 }
                    defaults.set(collapsedLeaguesArray, forKey: collapsedLeaguesKey)

                    // Save the current expanded state before changing date
                    print("Date picker - current expanded state: \(expandedLeagues.count) leagues")

                    if isToday && showLiveOnly {
                        // If selecting Today while live filter is active, refresh live fixtures
                        viewModel.fetchAllLiveFixtures()
                    } else if !isToday && showLiveOnly {
                        // If changing to a non-today date and live filter is active, disable it
                        withAnimation {
                            showLiveOnly = false
                            // Fetch fixtures for the selected date
                            viewModel.fetchLeagueFixtures(date: date)

                            // Initialize expanded leagues when data is loaded, but preserve user choices
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                if !viewModel.leaguesWithFixtures.isEmpty {
                                    // Only initialize if we haven't already or if the expanded state was lost
                                    if !hasInitializedExpandedState || expandedLeagues.isEmpty {
                                        print("Initializing expanded leagues after date picker change")
                                        initializeExpandedLeagues(leagues: viewModel.leaguesWithFixtures)
                                    } else {
                                        print("Preserving expanded state after date picker change")
                                        // Just add any new leagues that aren't in the collapsed list
                                        let collapsedLeagues = loadCollapsedState()
                                        for league in viewModel.leaguesWithFixtures {
                                            if !collapsedLeagues.contains(league.league.id) && !expandedLeagues.contains(league.league.id) {
                                                expandedLeagues.insert(league.league.id)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        // Normal case - just fetch fixtures for the selected date
                        // Save current expanded state before date change to use as reference
                        let previousExpandedState = expandedLeagues

                        // Use a more fluid transition
                        withAnimation(.easeInOut(duration: 0.3)) {
                            // Fetch new fixtures for the selected date
                            viewModel.fetchLeagueFixtures(date: date)
                        }

                        // Use a shorter delay and better animation when updating expanded state
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                            if !viewModel.leaguesWithFixtures.isEmpty {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    // Apply a more comprehensive update to expanded state
                                    updateExpandedStateForDateChange(previousExpandedState: previousExpandedState)
                                }
                            }
                        }
                    }

                    showingDatePicker = false
                })
            }
            .onAppear {
                // Initialize leagues when the view appears
                print("LeagueFixturesView appeared")

                // Pre-load the expanded state from UserDefaults immediately to prevent flicker
                if !hasInitializedExpandedState {
                    // Load collapsed leagues from UserDefaults
                    let collapsedLeagues = loadCollapsedState()
                    print("Preloading expanded state with \(collapsedLeagues.count) collapsed leagues")

                    // If we have leagues data already, initialize the expanded state immediately
                    if !viewModel.leaguesWithFixtures.isEmpty {
                        // Clear the current expanded leagues set to start fresh
                        expandedLeagues.removeAll()

                        // By default, expand all leagues when they're first loaded, except those saved as collapsed
                        for league in viewModel.leaguesWithFixtures {
                            if !collapsedLeagues.contains(league.league.id) {
                                expandedLeagues.insert(league.league.id)
                            }
                        }

                        // Force immediate UI update to prevent flicker
                        withAnimation(.easeInOut(duration: 0.1)) {
                            // This empty block forces SwiftUI to recognize the state change
                        }
                    }

                    // Mark that we've initialized the expanded state
                    hasInitializedExpandedState = true
                }

                // Track if this is the first appearance or a return from navigation
                @AppStorage("com.kickoffscore.hasInitiallyLoaded") var hasInitiallyLoaded: Bool = false

                // Always initialize expanded leagues if we already have data
                // This ensures leagues are expanded when navigating back to this view
                if !viewModel.leaguesWithFixtures.isEmpty {
                    print("Leagues already loaded, initializing expanded state...")
                    initializeExpandedLeagues(leagues: viewModel.leaguesWithFixtures)
                }

                if viewModel.leaguesWithFixtures.isEmpty {
                    print("No leagues loaded yet, fetching fixtures...")
                    viewModel.fetchLeagueFixtures(date: selectedDate)

                    // Initialize expanded leagues after a short delay to ensure data is loaded
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        if !viewModel.leaguesWithFixtures.isEmpty {
                            print("Leagues loaded after fetch, initializing expanded state...")
                            initializeExpandedLeagues(leagues: viewModel.leaguesWithFixtures)
                        }
                    }

                    // Mark that we've initially loaded data
                    hasInitiallyLoaded = true
                } else {
                    // If we already have fixtures, check if we need to refresh them
                    let calendar = Calendar.current
                    let isToday = calendar.isDateInToday(selectedDate)

                    // Only refresh data if this is the first load or if we're returning from background
                    // This prevents refreshing when returning from FixtureDetailView
                    let isFirstLoad = !hasInitiallyLoaded

                    if isToday && (viewModel.hasLiveFixtures || showLiveOnly) {
                        if isFirstLoad {
                            // Only refresh on first load or when returning from background
                            viewModel.refreshLiveFixtures(liveFilterActive: showLiveOnly)
                            viewModel.setupAutoRefreshIfNeeded(liveFilterActive: showLiveOnly)

                            // Mark that we've initially loaded data
                            hasInitiallyLoaded = true
                        }

                        // Always start UI refresh timer for today's fixtures to ensure timers update
                        // This doesn't cause a data refresh, just updates the UI with existing data
                        startUIRefreshTimer()
                    } else {
                        // For non-today dates, check if we need to refresh recently live fixtures
                        // This helps prevent showing outdated statuses for matches that have finished
                        let hasRecentlyLiveFixtures = viewModel.checkForRecentlyLiveFixtures(in: selectedDate)

                        if hasRecentlyLiveFixtures && isFirstLoad {
                            // Force a refresh of fixtures for dates that might have recently live matches
                            Logger.info("View appeared - refreshing fixtures for date with potentially recently live matches", category: .network)
                            viewModel.fetchLeagueFixtures(date: selectedDate)

                            // Re-initialize expanded leagues after refresh
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                if !viewModel.leaguesWithFixtures.isEmpty {
                                    print("Leagues refreshed, re-initializing expanded state...")
                                    initializeExpandedLeagues(leagues: viewModel.leaguesWithFixtures)
                                }
                            }

                            // Mark that we've initially loaded data
                            hasInitiallyLoaded = true
                        }
                    }
                }
            }
            .onDisappear {
                // Stop UI refresh timer when view disappears
                stopUIRefreshTimer()
            }
            // Add observer for app state changes to reset hasInitiallyLoaded flag
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)) { _ in
                // Reset the flag when app goes to background so data refreshes when returning
                UserDefaults.standard.set(false, forKey: "com.kickoffscore.hasInitiallyLoaded")
            }
            // Add onChange modifier to observe changes to leaguesWithFixtures
            // This will catch when new leagues are loaded through pagination
            .onChange(of: viewModel.leaguesWithFixtures) { newValue in
                // Store the old value for comparison
                let oldValue = self.lastObservedLeagues
                self.lastObservedLeagues = newValue

                // Skip the first change when oldValue is empty
                guard !oldValue.isEmpty else { return }

                // Check if this is likely a pagination update (more leagues added)
                let isPaginationUpdate = newValue.count > oldValue.count && viewModel.isLoadingMore == false && !viewModel.isLoading

                if isPaginationUpdate {
                    print("Detected pagination update: \(oldValue.count) -> \(newValue.count) leagues")

                    // Use our pagination handler to expand new leagues
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        print("Running pagination handler after leagues update")
                        handlePaginationExpandedState()
                    }
                } else if newValue.count > 0 && oldValue.count > 0 && newValue.count == oldValue.count {
                    // This might be a case where the content changed but not the count
                    // Check if the last league is collapsed, as this might prevent pagination
                    let hasLastLeague = newValue.last != nil

                    if hasLastLeague {
                        let lastLeague = newValue.last!
                        let isLastLeagueCollapsed = !expandedLeagues.contains(lastLeague.league.id)

                        if isLastLeagueCollapsed {
                            print("Last league is collapsed, checking if we need to load more leagues")

                            // If we have more pages and we're not already loading, trigger loading more
                            let canLoadMore = viewModel.hasMorePages && !viewModel.isLoadingMore && !viewModel.isLoading

                            if canLoadMore {
                                print("Triggering load more from onChange handler")
                                viewModel.loadMoreLeagueFixturesIfNeeded()
                            }
                        }
                    }
                }
            }
            .sheet(isPresented: $authViewModel.showLoginView) {
                LoginView(viewModel: authViewModel)
            }
            .navigationDestination(for: Fixture.self) { fixture in
                FixtureDetailView(fixture: fixture)
                    .onAppear {
                        navigationState.isInDetailView = true
                        navigationState.navigationDepth += 1
                    }
                    .onDisappear {
                        navigationState.isInDetailView = false
                        navigationState.navigationDepth = max(0, navigationState.navigationDepth - 1)
                    }
            }
        }
    }
}

struct LeagueFixturesView_Previews: PreviewProvider {
    static var previews: some View {
        LeagueFixturesView()
    }
}
