import SwiftUI
import King<PERSON>er

struct TwoColumnFormView: View {
    let homeTeamDetailedForm: [DetailedFormResult]?
    let awayTeamDetailedForm: [DetailedFormResult]?
    let homeTeamName: String
    let awayTeamName: String

    var body: some View {
        // Two-column layout
        HStack(alignment: .top, spacing: AppLayout.spacingM) {
            // Home team column
            VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                if let homeTeamDetailedForm = homeTeamDetailedForm, !homeTeamDetailedForm.isEmpty {
                    // Home team form list - reversed to show most recent at top
                    VStack(spacing: AppLayout.spacingXS) {
                        ForEach(homeTeamDetailedForm.reversed()) { match in
                            MatchResultRow(match: match)
                        }
                    }
                } else {
                    Text("No form data available")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                        .padding(.vertical, AppLayout.spacingS)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // Divider
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 1)
                .padding(.vertical, AppLayout.spacingS)

            // Away team column
            VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                if let awayTeamDetailedForm = awayTeamDetailedForm, !awayTeamDetailedForm.isEmpty {
                    // Away team form list - reversed to show most recent at top
                    VStack(spacing: AppLayout.spacingXS) {
                        ForEach(awayTeamDetailedForm.reversed()) { match in
                            MatchResultRow(match: match)
                        }
                    }
                } else {
                    Text("No form data available")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                        .padding(.vertical, AppLayout.spacingS)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
}

private struct MatchResultRow: View {
    let match: DetailedFormResult

    var body: some View {
        HStack {
            // Team logos and score
            HStack(spacing: 4) {
                // Home team logo
                TeamLogoView(logoUrl: match.homeTeam.logo, size: 20)

                // Score
                Text("\(match.score.home)-\(match.score.away)")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.text)

                // Away team logo
                TeamLogoView(logoUrl: match.awayTeam.logo, size: 20)
            }

            Spacer()

            // Result indicator (W/D/L)
            ResultBadge(result: match.result)
        }
        .padding(AppLayout.spacingXS)
        .background(resultBackgroundColor(for: match.result))
        .cornerRadius(AppLayout.cornerRadiusS)
    }

    private func resultBackgroundColor(for result: String) -> Color {
        switch result.uppercased() {
        case "W":
            return AppColors.Sports.win.opacity(0.1)
        case "D":
            return Color.gray.opacity(0.1)
        case "L":
            return Color.red.opacity(0.1)
        default:
            return Color.gray.opacity(0.05)
        }
    }
}

private struct TeamLogoView: View {
    let logoUrl: String
    let size: CGFloat

    var body: some View {
        CachedImageView.teamLogo(
            url: logoUrl,
            size: size,
            teamName: "Team"
        )
    }
}

private struct ResultBadge: View {
    let result: String

    var body: some View {
        Text(result)
            .font(.system(size: 10, weight: .bold))
            .foregroundColor(textColor)
            .frame(width: 16, height: 16)
            .background(backgroundColor)
            .cornerRadius(3)
    }

    private var backgroundColor: Color {
        switch result.uppercased() {
        case "W":
            return AppColors.Sports.win
        case "D":
            return Color.gray.opacity(0.9)
        case "L":
            return Color.red
        default:
            return Color.gray.opacity(0.5)
        }
    }

    private var textColor: Color {
        return .white
    }
}

#Preview {
    let mockHomeTeamForm = [
        DetailedFormResult(
            result: "W",
            fixtureId: 1,
            date: "2023-05-01T15:00:00+00:00",
            homeTeam: DetailedFormResult.TeamInfo(id: 1, name: "Team A", logo: "https://media.api-sports.io/football/teams/1.png"),
            awayTeam: DetailedFormResult.TeamInfo(id: 2, name: "Team B", logo: "https://media.api-sports.io/football/teams/2.png"),
            score: DetailedFormResult.Score(home: 2, away: 1),
            isHomeTeam: true
        ),
        DetailedFormResult(
            result: "D",
            fixtureId: 2,
            date: "2023-05-08T15:00:00+00:00",
            homeTeam: DetailedFormResult.TeamInfo(id: 3, name: "Team C", logo: "https://media.api-sports.io/football/teams/3.png"),
            awayTeam: DetailedFormResult.TeamInfo(id: 1, name: "Team A", logo: "https://media.api-sports.io/football/teams/1.png"),
            score: DetailedFormResult.Score(home: 1, away: 1),
            isHomeTeam: false
        ),
        DetailedFormResult(
            result: "L",
            fixtureId: 3,
            date: "2023-05-15T15:00:00+00:00",
            homeTeam: DetailedFormResult.TeamInfo(id: 1, name: "Team A", logo: "https://media.api-sports.io/football/teams/1.png"),
            awayTeam: DetailedFormResult.TeamInfo(id: 4, name: "Team D", logo: "https://media.api-sports.io/football/teams/4.png"),
            score: DetailedFormResult.Score(home: 0, away: 2),
            isHomeTeam: true
        )
    ]

    let mockAwayTeamForm = [
        DetailedFormResult(
            result: "W",
            fixtureId: 4,
            date: "2023-05-01T15:00:00+00:00",
            homeTeam: DetailedFormResult.TeamInfo(id: 5, name: "Team E", logo: "https://media.api-sports.io/football/teams/5.png"),
            awayTeam: DetailedFormResult.TeamInfo(id: 6, name: "Team F", logo: "https://media.api-sports.io/football/teams/6.png"),
            score: DetailedFormResult.Score(home: 3, away: 0),
            isHomeTeam: true
        ),
        DetailedFormResult(
            result: "W",
            fixtureId: 5,
            date: "2023-05-08T15:00:00+00:00",
            homeTeam: DetailedFormResult.TeamInfo(id: 6, name: "Team F", logo: "https://media.api-sports.io/football/teams/6.png"),
            awayTeam: DetailedFormResult.TeamInfo(id: 7, name: "Team G", logo: "https://media.api-sports.io/football/teams/7.png"),
            score: DetailedFormResult.Score(home: 2, away: 0),
            isHomeTeam: true
        ),
        DetailedFormResult(
            result: "L",
            fixtureId: 6,
            date: "2023-05-15T15:00:00+00:00",
            homeTeam: DetailedFormResult.TeamInfo(id: 8, name: "Team H", logo: "https://media.api-sports.io/football/teams/8.png"),
            awayTeam: DetailedFormResult.TeamInfo(id: 6, name: "Team F", logo: "https://media.api-sports.io/football/teams/6.png"),
            score: DetailedFormResult.Score(home: 1, away: 0),
            isHomeTeam: false
        )
    ]

    return TwoColumnFormView(
        homeTeamDetailedForm: mockHomeTeamForm,
        awayTeamDetailedForm: mockAwayTeamForm,
        homeTeamName: "Team A",
        awayTeamName: "Team F"
    )
    .padding()
    .background(AppColors.tertiaryBackground)
    .cornerRadius(AppLayout.cornerRadiusL)
    .padding()
    .background(AppColors.background)
}
