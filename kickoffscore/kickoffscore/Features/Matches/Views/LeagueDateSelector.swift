import SwiftUI

struct LeagueDateSelector: View {
    @Binding var selectedDate: Date
    var onDateSelected: ((Date) -> Void)
    @State private var scrollViewProxy: ScrollViewProxy? = nil
    @State private var didScrollToToday = false

    // Define the range of days to show
    private let pastDayRange = 7 // 7 days in the past (including today)
    private let futureDayRange = 6 // 6 days in the future

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            ScrollViewReader { proxy in
                HStack(spacing: AppLayout.spacingM) {
                    ForEach(dateRange(), id: \.self) { date in
                        Button {
                            selectedDate = date
                            onDateSelected(date)
                            // Scroll to the selected date
                            scrollViewProxy?.scrollTo(date, anchor: .center)
                        } label: {
                            Text(formatDateButton(date: date))
                                .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                .padding(.vertical, AppLayout.spacingS)
                                .padding(.horizontal, AppLayout.spacingM)
                                .background(
                                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                                        .fill(isSameDay(date, selectedDate) ?
                                              Color(UIColor.systemIndigo) : Color(UIColor.tertiarySystemFill)))
                                // Removed the border overlay for today's date to avoid confusion
                                .foregroundColor(isSameDay(date, selectedDate) ? .white : Color(UIColor.label))
                        }
                        .id(date) // Use date as the ID for ScrollViewReader
                        .accessibilityInfo(
                            label: formatDateButton(date: date),
                            hint: isSameDay(date, selectedDate) ? "Selected date" : "Tap to select this date"
                        )
                    }
                }
                .padding(.horizontal, AppLayout.spacingS)
                .padding(.vertical, AppLayout.spacingXS)
                .onAppear {
                    scrollViewProxy = proxy
                    // We'll scroll to today in onAppear of the parent view
                }
            }
        }
        .frame(height: 50)
        .background(AppColors.tertiaryBackground)
        .edgesIgnoringSafeArea(.horizontal)
        .onAppear {
            // Scroll to the selected date when the view appears
            if !didScrollToToday {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    let targetDate = Calendar.current.startOfDay(for: selectedDate)
                    scrollViewProxy?.scrollTo(targetDate, anchor: .center)
                    didScrollToToday = true
                }
            }
        }
    }

    // Generates the array of dates for the buttons
    private func dateRange() -> [Date] {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        // Create array of dates
        var dates: [Date] = []

        // Add past dates (including today)
        for i in (0..<pastDayRange).reversed() {
            if let date = calendar.date(byAdding: .day, value: -i, to: today) {
                dates.append(date)
            }
        }

        // Add future dates
        for i in 1...futureDayRange {
            if let date = calendar.date(byAdding: .day, value: i, to: today) {
                dates.append(date)
            }
        }

        return dates
    }

    // Formats the date for the button label
    private func formatDateButton(date: Date) -> String {
        let calendar = Calendar.current
        if calendar.isDateInYesterday(date) { return "Yesterday" }
        if calendar.isDateInToday(date) { return "Today" }
        if calendar.isDateInTomorrow(date) { return "Tomorrow" }

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "EEE d MMM" // e.g., Sat 19 Apr
        return dateFormatter.string(from: date)
    }

    // Helper to check if two dates are the same day
    private func isSameDay(_ date1: Date, _ date2: Date) -> Bool {
        Calendar.current.isDate(date1, inSameDayAs: date2)
    }
}

#Preview {
    LeagueDateSelector(selectedDate: .constant(Date()), onDateSelected: { _ in })
}
