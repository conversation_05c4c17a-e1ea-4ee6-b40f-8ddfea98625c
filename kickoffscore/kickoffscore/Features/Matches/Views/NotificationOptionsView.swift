import SwiftUI

struct NotificationOptionsView: View {
    @Binding var isPresented: Bool
    @State private var preferences = NotificationPreference.defaultPreferences
    let onSubscribe: (NotificationPreference) -> Void

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            // Header
            HStack {
                Text("Match Notifications")
                    .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                    .foregroundColor(AppColors.text)

                Spacer()

                Button {
                    isPresented = false
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(AppColors.secondaryText)
                }
                .accessibilityLabel("Close")
            }
            .padding(.bottom, AppLayout.spacingS)

            // Description
            Text("Select which notifications you want to receive for this match:")
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)

            // Options
            VStack(spacing: AppLayout.spacingM) {
                // All Events Toggle
                Toggle(isOn: $preferences.allEvents) {
                    HStack {
                        Image(systemName: "bell.fill")
                            .font(.system(size: AppLayout.iconSizeM))
                            .foregroundColor(AppColors.Brand.primary)

                        Text("All Match Events")
                            .font(AppTypography.dynamicFont(style: .body))
                            .foregroundColor(AppColors.text)
                    }
                }
                .onChange(of: preferences.allEvents) { newValue in
                    if newValue {
                        // If all events is enabled, enable all other options
                        preferences.applyAllEvents()
                    }
                }
                .toggleStyle(SwitchToggleStyle(tint: AppColors.Brand.primary))

                Divider()

                // Match Status Toggle
                Toggle(isOn: $preferences.matchStatus) {
                    HStack {
                        Image(systemName: "timer")
                            .font(.system(size: AppLayout.iconSizeM))
                            .foregroundColor(AppColors.Brand.secondary)

                        Text("Match Status")
                            .font(AppTypography.dynamicFont(style: .body))
                            .foregroundColor(AppColors.text)
                    }
                }
                .onChange(of: preferences.matchStatus) { _ in
                    preferences.updateAllEvents()
                }
                .toggleStyle(SwitchToggleStyle(tint: AppColors.Brand.primary))

                // Goals Toggle
                Toggle(isOn: $preferences.goals) {
                    HStack {
                        Image(systemName: "soccerball")
                            .font(.system(size: AppLayout.iconSizeM))
                            .foregroundColor(AppColors.success)

                        Text("Goals")
                            .font(AppTypography.dynamicFont(style: .body))
                            .foregroundColor(AppColors.text)
                    }
                }
                .onChange(of: preferences.goals) { _ in
                    preferences.updateAllEvents()
                }
                .toggleStyle(SwitchToggleStyle(tint: AppColors.Brand.primary))

                // Red Cards Toggle
                Toggle(isOn: $preferences.redCards) {
                    HStack {
                        Image(systemName: "rectangle.fill")
                            .font(.system(size: AppLayout.iconSizeM))
                            .foregroundColor(AppColors.error)

                        Text("Red Cards")
                            .font(AppTypography.dynamicFont(style: .body))
                            .foregroundColor(AppColors.text)
                    }
                }
                .onChange(of: preferences.redCards) { _ in
                    preferences.updateAllEvents()
                }
                .toggleStyle(SwitchToggleStyle(tint: AppColors.Brand.primary))
            }
            .padding(AppLayout.spacingM)
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(AppLayout.cornerRadiusM)

            // Done Button
            Button {
                onSubscribe(preferences)
                isPresented = false
            } label: {
                Text("Done")
                    .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                    .foregroundColor(AppColors.buttonText)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(preferences.hasEnabledNotifications ? AppColors.Brand.primary : AppColors.Brand.primary.opacity(0.7))
                    .cornerRadius(AppLayout.cornerRadiusM)
            }
            .disabled(!preferences.hasEnabledNotifications)
        }
        .padding(AppLayout.spacingL)
        .background(AppColors.background)
        .cornerRadius(AppLayout.cornerRadiusL)
        .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 5)
        .frame(maxWidth: 400)
        .padding(.horizontal, AppLayout.spacingL)
    }
}

struct NotificationOptionsView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.gray.opacity(0.3).ignoresSafeArea()

            NotificationOptionsView(
                isPresented: .constant(true),
                onSubscribe: { _ in }
            )
        }
    }
}
