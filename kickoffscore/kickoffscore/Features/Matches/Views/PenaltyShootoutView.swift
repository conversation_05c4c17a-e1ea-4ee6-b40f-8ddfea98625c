import SwiftUI
import <PERSON><PERSON><PERSON>

struct PenaltyShootoutView: View {
    let fixture: Fixture
    @State private var isExpanded: Bool = false

    // Determine if the fixture went to penalties or is currently in penalty shootout
    private var hasPenaltyShootout: Bool {
        let matchStatus = fixture.status.short ?? ""

        // Show if match status explicitly indicates penalties (live or finished)
        if matchStatus == "PEN" {
            return true
        }

        // Show if there's a penalty score with non-zero values (for finished matches)
        if let penalty = fixture.score.penalty,
           (penalty.home ?? 0) > 0 || (penalty.away ?? 0) > 0 {
            return true
        }

        // Show if there are penalty shootout events (real-time during shootout)
        if let events = fixture.events, !events.isEmpty {
            let penaltyEvents = events.filter { event in
                event.time.elapsed >= 120 && event.type == "Goal" &&
                (event.detail.lowercased().contains("penalty") ||
                 event.detail.lowercased().contains("missed penalty"))
            }
            return !penaltyEvents.isEmpty
        }

        return false
    }

    // Get penalty shootout events
    private var penaltyEvents: [EventInfo] {
        guard let events = fixture.events else { return [] }

        // Filter for penalty shootout events
        // Penalty shootout events typically have time >= 120 minutes
        return events.filter { event in
            event.time.elapsed >= 120 && event.type == "Goal" &&
            (event.detail.lowercased().contains("penalty") ||
             event.detail.lowercased().contains("missed penalty"))
        }.sorted {
            // Sort by time
            if $0.time.elapsed != $1.time.elapsed {
                return $0.time.elapsed < $1.time.elapsed
            }
            // If elapsed time is the same, sort by extra time
            let extra0 = $0.time.extra ?? 0
            let extra1 = $1.time.extra ?? 0
            return extra0 < extra1
        }
    }

    // Get home team penalty events
    private var homePenaltyEvents: [EventInfo] {
        return penaltyEvents.filter { $0.team.id == fixture.teams.home?.id }
    }

    // Get away team penalty events
    private var awayPenaltyEvents: [EventInfo] {
        return penaltyEvents.filter { $0.team.id == fixture.teams.away?.id }
    }

    // Get penalty shootout score
    private var penaltyScore: (home: Int, away: Int) {
        if let penalty = fixture.score.penalty {
            return (home: penalty.home ?? 0, away: penalty.away ?? 0)
        }

        // Calculate from events if not available in score
        let homeScored = homePenaltyEvents.filter { !$0.detail.lowercased().contains("missed") }.count
        let awayScored = awayPenaltyEvents.filter { !$0.detail.lowercased().contains("missed") }.count

        return (home: homeScored, away: awayScored)
    }

    var body: some View {
        // Only show if the match went to penalties
        if hasPenaltyShootout {
            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                    // Header with expand/collapse button
                    HStack {
                        Text("Penalty Shootout")
                            .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                            .foregroundColor(Color(UIColor.label))

                        Spacer()

                        // Expand/collapse button
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                isExpanded.toggle()
                            }
                        }) {
                            Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(AppColors.secondaryText)
                                .padding(6)
                                .background(Color.gray.opacity(0.1))
                                .clipShape(Circle())
                        }
                    }
                    .padding(.bottom, AppLayout.spacingXS)

                    // Penalty shootout visualization
                    HStack(alignment: .center, spacing: AppLayout.spacingL) {
                        // Home team
                        VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                            // Team logo and score
                            HStack(spacing: AppLayout.spacingXS) {
                                // Team logo
                                KFImage(URL(string: fixture.teams.home?.logo ?? ""))
                                    .placeholder {
                                        Circle()
                                            .fill(Color.gray.opacity(0.3))
                                            .frame(width: 24, height: 24)
                                    }
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 24, height: 24)
                                    .clipShape(Circle())

                                // Penalty score
                                Text("\(penaltyScore.home)")
                                    .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                                    .foregroundColor(AppColors.text)
                            }

                            // Penalty indicators - dynamically show all penalties
                            HStack(spacing: 0) {
                                // Container for dots with leading alignment
                                HStack(spacing: 4) {
                                    ForEach(0..<max(5, homePenaltyEvents.count), id: \.self) { index in
                                        if index < homePenaltyEvents.count {
                                            let event = homePenaltyEvents[index]
                                            let scored = !event.detail.lowercased().contains("missed")

                                            Circle()
                                                .fill(scored ? Color.green : Color.red)
                                                .frame(width: 12, height: 12)
                                        } else if index < 5 {
                                            // Show gray dots for the first 5 positions if not enough penalties
                                            Circle()
                                                .fill(Color.gray.opacity(0.3))
                                                .frame(width: 12, height: 12)
                                        }
                                    }
                                }
                                .frame(maxWidth: .infinity, alignment: .leading)
                            }
                            .frame(height: 16)

                            // Removed last penalty taker display as requested
                        }

                        Spacer()

                        // Away team
                        VStack(alignment: .trailing, spacing: AppLayout.spacingS) {
                            // Team logo and score
                            HStack(spacing: AppLayout.spacingXS) {
                                // Penalty score
                                Text("\(penaltyScore.away)")
                                    .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                                    .foregroundColor(AppColors.text)

                                // Team logo
                                KFImage(URL(string: fixture.teams.away?.logo ?? ""))
                                    .placeholder {
                                        Circle()
                                            .fill(Color.gray.opacity(0.3))
                                            .frame(width: 24, height: 24)
                                    }
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 24, height: 24)
                                    .clipShape(Circle())
                            }

                            // Penalty indicators - dynamically show all penalties (reversed order for away team)
                            HStack(spacing: 0) {
                                // Create an array of indices in reverse order
                                let indices = Array(0..<max(5, awayPenaltyEvents.count)).reversed()

                                // Container for dots with trailing alignment
                                HStack(spacing: 4) {
                                    ForEach(indices, id: \.self) { index in
                                        if index < awayPenaltyEvents.count {
                                            let event = awayPenaltyEvents[index]
                                            let scored = !event.detail.lowercased().contains("missed")

                                            Circle()
                                                .fill(scored ? Color.green : Color.red)
                                                .frame(width: 12, height: 12)
                                        } else if index < 5 {
                                            // Show gray dots for the first 5 positions if not enough penalties
                                            Circle()
                                                .fill(Color.gray.opacity(0.3))
                                                .frame(width: 12, height: 12)
                                        }
                                    }
                                }
                                .frame(maxWidth: .infinity, alignment: .trailing)
                            }
                            .frame(height: 16)

                            // Removed last penalty taker display as requested
                        }
                    }
                    .padding(.vertical, AppLayout.spacingM)

                    // Detailed penalty list - only show when expanded
                    if isExpanded {
                        VStack(spacing: AppLayout.spacingS) {
                            // Display penalties by round, properly matching home and away penalties
                            let maxPenalties = max(homePenaltyEvents.count, awayPenaltyEvents.count)

                            ForEach(0..<maxPenalties, id: \.self) { roundIndex in
                                HStack {
                                    // Home team penalty (if exists for this round)
                                    if roundIndex < homePenaltyEvents.count {
                                        let homeEvent = homePenaltyEvents[roundIndex]
                                        let homeScored = !homeEvent.detail.lowercased().contains("missed")

                                        // Home player name with color only (no emoji)
                                        Text("\(homeEvent.player.name ?? "Unknown")")
                                            .font(AppTypography.dynamicFont(style: .subheadline))
                                            .foregroundColor(homeScored ? .green : .red)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    } else {
                                        // Empty space if no home penalty for this round
                                        Text("")
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    }

                                    Spacer()

                                    // Away team penalty (if exists for this round)
                                    if roundIndex < awayPenaltyEvents.count {
                                        let awayEvent = awayPenaltyEvents[roundIndex]
                                        let awayScored = !awayEvent.detail.lowercased().contains("missed")

                                        // Away player name with color only (no emoji)
                                        Text("\(awayEvent.player.name ?? "Unknown")")
                                            .font(AppTypography.dynamicFont(style: .subheadline))
                                            .foregroundColor(awayScored ? .green : .red)
                                            .frame(maxWidth: .infinity, alignment: .trailing)
                                    } else {
                                        // Empty space if no away penalty for this round
                                        Text("")
                                            .frame(maxWidth: .infinity, alignment: .trailing)
                                    }
                                }
                                .accessibilityElement(children: .combine)
                                .accessibilityLabel(getPenaltyRoundAccessibilityLabel(
                                    roundIndex: roundIndex,
                                    homePenaltyEvents: homePenaltyEvents,
                                    awayPenaltyEvents: awayPenaltyEvents
                                ))

                                if roundIndex < maxPenalties - 1 {
                                    Divider()
                                        .background(AppColors.separator)
                                }
                            }
                        }
                        .transition(.opacity)
                    }
                }
                .padding(AppLayout.spacingM)
                .background(AppColors.tertiaryBackground)
                .cornerRadius(AppLayout.cornerRadiusL)
                .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
            }
            .padding(.horizontal, 0)
        }
    }

    // Helper for accessibility
    private func getPenaltyRoundAccessibilityLabel(
        roundIndex: Int,
        homePenaltyEvents: [EventInfo],
        awayPenaltyEvents: [EventInfo]
    ) -> String {
        var label = "Penalty round \(roundIndex + 1): "

        // Add home team penalty info if exists
        if roundIndex < homePenaltyEvents.count {
            let homeEvent = homePenaltyEvents[roundIndex]
            let homeScored = !homeEvent.detail.lowercased().contains("missed")
            let homeResult = homeScored ? "scored" : "missed"
            label += "\(homeEvent.player.name ?? "Unknown player") from home team \(homeResult)"
        }

        // Add separator if both penalties exist
        if roundIndex < homePenaltyEvents.count && roundIndex < awayPenaltyEvents.count {
            label += ", "
        }

        // Add away team penalty info if exists
        if roundIndex < awayPenaltyEvents.count {
            let awayEvent = awayPenaltyEvents[roundIndex]
            let awayScored = !awayEvent.detail.lowercased().contains("missed")
            let awayResult = awayScored ? "scored" : "missed"
            label += "\(awayEvent.player.name ?? "Unknown player") from away team \(awayResult)"
        }

        return label
    }
}



// MARK: - Preview
struct PenaltyShootoutView_Previews: PreviewProvider {
    static var previews: some View {
        PenaltyShootoutView(fixture: Fixture.mockPenalty)
            .padding()
            .background(AppColors.background)
            .previewLayout(.sizeThatFits)
    }
}
