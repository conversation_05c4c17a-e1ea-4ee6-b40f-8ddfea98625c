import SwiftUI

struct LeagueDatePickerView: View {
    @Binding var selectedDate: Date
    var onDateSelected: ((Date) -> Void)? = nil
    @State private var isPresented: Bool = true

    init(selectedDate: Binding<Date>, isPresented: Binding<Bool>? = nil, onDateSelected: ((Date) -> Void)? = nil) {
        self._selectedDate = selectedDate
        if let isPresented = isPresented {
            self._isPresented = State(initialValue: isPresented.wrappedValue)
        }
        self.onDateSelected = onDateSelected
    }

    var body: some View {
        NavigationView {
            VStack {
                DatePicker(
                    "Select a date",
                    selection: $selectedDate,
                    displayedComponents: .date
                )
                .datePickerStyle(.graphical)
                .padding()
                .tint(AppColors.Brand.primary)
                .accessibilityInfo(label: "Date picker", hint: "Select a date to view fixtures")

                Button(action: {
                    if let onDateSelected = onDateSelected {
                        onDateSelected(selectedDate)
                    } else {
                        isPresented = false
                    }
                }) {
                    Text("Done")
                        .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                        .foregroundColor(AppColors.buttonText)
                        .frame(maxWidth: .infinity)
                        .padding()
                }
                .background(AppColors.Brand.primary)
                .cornerRadius(AppLayout.cornerRadiusM)
                .padding()
                .accessibilityInfo(label: "Done", hint: "Confirm date selection")
            }
            .navigationTitle("Select Date")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        isPresented = false
                    }) {
                        Text("Cancel")
                            .font(AppTypography.dynamicFont(style: .body))
                            .foregroundColor(AppColors.Brand.primary)
                            .padding(.vertical, 8)
                            .padding(.horizontal, 12)
                    }
                    .accessibilityInfo(label: "Cancel", hint: "Cancel date selection")
                }
            }
        }
    }
}
