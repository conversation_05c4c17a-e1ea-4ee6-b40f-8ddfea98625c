import Foundation
import SwiftUI
import Kingfisher
import Combine

struct OverviewView: View {
    let fixture: Fixture
    @Binding var selectedTab: SelectedTab
    @StateObject private var voteViewModel: VoteViewModel
    @StateObject private var oddsViewModel = OddsViewModel()
    @EnvironmentObject private var authViewModel: AuthViewModel

    init(fixture: Fixture, selectedTab: Binding<SelectedTab>) {
        self.fixture = fixture
        self._selectedTab = selectedTab
        // Use _voteViewModel to initialize the StateObject with nil authViewModel
        // We'll set it in onAppear using the environment object
        _voteViewModel = StateObject(wrappedValue: VoteViewModel(fixtureId: fixture.id))
    }

    // This will be called after the view appears and the environment object is available
    private func setupVoteViewModel() {
        // Pass the authViewModel from the environment to the voteViewModel
        voteViewModel.authViewModel = authViewModel

        // Force a check of authentication status
        if authViewModel.isAuthenticated {
            voteViewModel.fetchUserVote()
        }
    }

    // Date formatter for display
    private var formattedFullDateTime: String {
        let kickoff = fixture.kickoffDate
        let displayFormatter = DateFormatter()
        displayFormatter.dateStyle = .medium
        displayFormatter.timeStyle = .short
        return displayFormatter.string(from: kickoff)
    }

    // Determine if the match is considered 'started' (not NS or TBD)
    private var isMatchStarted: Bool {
        let status = fixture.status.short?.uppercased()
        return status != "NS" && status != "TBD"
    }

    var body: some View {
        ScrollView(showsIndicators: false) {
            VStack(alignment: .leading, spacing: AppLayout.spacingS) { // Added consistent spacing between containers

                // Penalty Shootout Section - Only show for matches that went to penalties
                // The PenaltyShootoutView has its own internal check to ensure the match has actually finished
                // and went to penalties, so we can just include it here
                PenaltyShootoutView(fixture: fixture)

                // Events Timeline Section - Only show for matches that have started
                if isMatchStarted {
                    VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                        if let events = fixture.events, !events.isEmpty {
                            EventsTimelineView(events: events,
                                              homeTeamId: fixture.homeTeam?.id,
                                              awayTeamId: fixture.awayTeam?.id,
                                              fixture: fixture,
                                              title: "Match Events")
                        } else {
                            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                                Text("Match Events")
                                    .font(AppTypography.dynamicFont(style: .headline))
                                    .foregroundColor(Color(UIColor.label))
                                    .padding(.horizontal, AppLayout.spacingM)
                                    .padding(.top, AppLayout.spacingM)

                                EmptyStateView(
                                    icon: "exclamationmark.circle",
                                    message: "No match events available yet."
                                )
                                .padding(.horizontal, AppLayout.spacingM)
                                .padding(.bottom, AppLayout.spacingM)
                            }
                            .background(AppColors.tertiaryBackground)
                            .cornerRadius(AppLayout.cornerRadiusL)
                            .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
                        }
                    }
                    .padding(.horizontal, 0)
                }

                // Team Stats Overview Section (only shown for matches that have started)
                if isMatchStarted {
                    TeamStatsOverviewSection(fixture: fixture, selectedTab: $selectedTab)
                }

                // Vote Section (shown for all matches)
                VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                    VoteView(
                        viewModel: voteViewModel,
                        homeTeam: fixture.homeTeam,
                        awayTeam: fixture.awayTeam,
                        title: "Who Will Win?",
                        oddsViewModel: oddsViewModel,
                        fixture: fixture
                    )
                }
                .padding(.horizontal, 0)

                // Team Form Section
                TeamFormView(fixture: fixture)

                // Match Info Section
                VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                    VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                        Text("Match Information")
                            .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                            .foregroundColor(Color(UIColor.label))
                            .padding(.horizontal, 0)
                            .padding(.bottom, AppLayout.spacingS)
                        // League
                        InfoRow(
                            icon: "league",
                            title: fixture.league.name ?? "N/A League",
                            value: fixture.league.round ?? "",
                            imageUrl: fixture.league.logo
                        )

                        Divider()
                            .background(AppColors.separator)

                        // Date & Time
                        InfoRow(
                            icon: "time",
                            title: "Date & Time",
                            value: formattedFullDateTime
                        )

                        Divider()
                            .background(AppColors.separator)

                        // Venue
                        InfoRow(
                            icon: "venue",
                            title: "Venue",
                            value: "\(fixture.venue?.name ?? "N/A Venue"), \(fixture.venue?.city ?? "N/A City")"
                        )

                        // Referee
                        if let referee = fixture.referee, !referee.isEmpty {
                            Divider()
                                .background(AppColors.separator)

                            InfoRow(
                                icon: "referee",
                                title: "Referee",
                                value: referee
                            )
                        }
                    }
                    .padding(AppLayout.spacingM)
                    .background(AppColors.tertiaryBackground)
                    .cornerRadius(AppLayout.cornerRadiusL)
                    .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
                }

                // No spacer at bottom
            }
            .padding(.top, AppLayout.spacingS) // Reduced top padding from spacingM to spacingS
            .padding(.bottom, AppLayout.spacingM)
            .padding(.horizontal, 0)
            .edgesIgnoringSafeArea(.horizontal)
        }
        .background(AppColors.background)
        .onAppear {
            setupVoteViewModel()
            // Fetch odds data for the fixture
            oddsViewModel.fetchAppropriateOdds(for: fixture)
        }
    }
}

// MARK: - Helper Views

struct EmptyStateView: View {
    let icon: String
    let message: String

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: icon)
                .font(.system(size: AppLayout.iconSizeL))
                .foregroundColor(Color(UIColor.secondaryLabel))

            Text(message)
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(Color(UIColor.secondaryLabel))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(AppLayout.spacingL)
        .accessibilityInfo(label: message)
    }
}

struct InfoRow: View {
    let icon: String
    let title: String
    let value: String
    var imageUrl: String? = nil

    // Helper function to determine if we should use a system icon or custom icon
    private func shouldUseSystemIcon(for iconName: String) -> Bool {
        // Only use system icon for league (which was causing the error)
        return iconName == "league"
    }

    // Helper function to convert custom icon names to system icon names when needed
    private func getSystemIconName(for customIcon: String) -> String {
        switch customIcon {
        case "league":
            return "trophy.fill"
        default:
            return "info.circle"
        }
    }

    var body: some View {
        HStack(spacing: AppLayout.spacingM) {
            // Icon or Image
            if let url = imageUrl, !url.isEmpty {
                KFImage(URL(string: url))
                    .placeholder {
                        if shouldUseSystemIcon(for: icon) {
                            Image(systemName: getSystemIconName(for: icon))
                                .foregroundColor(Color(UIColor.systemIndigo))
                        } else {
                            Image(icon)
                                .resizable()
                                .scaledToFit()
                                .foregroundColor(Color(UIColor.systemIndigo))
                        }
                    }
                    .resizable()
                    .scaledToFit()
                    .frame(width: AppLayout.iconSizeM, height: AppLayout.iconSizeM)
            } else if shouldUseSystemIcon(for: icon) {
                // Use system icon for league
                Image(systemName: getSystemIconName(for: icon))
                    .font(.system(size: AppLayout.iconSizeM))
                    .foregroundColor(Color(UIColor.systemIndigo))
                    .frame(width: AppLayout.iconSizeM, height: AppLayout.iconSizeM)
            } else {
                // Use custom icons for venue, time, and referee
                Image(icon)
                    .resizable()
                    .scaledToFit()
                    .foregroundColor(Color(UIColor.systemIndigo))
                    .frame(width: AppLayout.iconSizeM, height: AppLayout.iconSizeM)
            }

            // Content
            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                if title != value {
                    Text(title)
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(Color(UIColor.label))

                    Text(value)
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                } else {
                    Text(title)
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(Color(UIColor.label))
                }
            }

            Spacer()
        }
        .accessibilityInfo(
            label: title,
            hint: value
        )
    }
}

// Add a preview provider if needed
#if DEBUG
struct OverviewView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Preview for a match that has started
            OverviewView(fixture: Fixture.mock, selectedTab: .constant(.overview))
                .previewDisplayName("Match Started")
                .previewLayout(.sizeThatFits)

            // Preview for an upcoming match
            OverviewView(fixture: Fixture.mockUpcoming, selectedTab: .constant(.overview))
                .previewDisplayName("Upcoming Match")
                .previewLayout(.sizeThatFits)
        }
    }
}
#endif
