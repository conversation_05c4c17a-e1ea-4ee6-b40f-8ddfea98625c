import Foundation
import SwiftUI
import <PERSON><PERSON><PERSON>

struct FixtureRowView: View {
    let fixture: Fixture
    @EnvironmentObject private var navigationState: NavigationStateManager
    @State private var refreshTrigger: Int = 0
    @State private var timer: Timer?

    var body: some View {
        Button(action: {
            navigationState.navigateToFixtureDetail(fixture: fixture)
        }) {
            HStack(alignment: .center, spacing: AppLayout.spacingM) {
                // Left: Teams (Logo + Name)
                VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                    HStack(spacing: AppLayout.spacingS) {
                        TeamLogo(url: fixture.teams.home?.logo, teamName: fixture.teams.home?.name ?? "Home")
                            .foregroundColor(AppColors.Sports.win)

                        HStack(spacing: 6) {
                            Text(fixture.teams.home?.name ?? "N/A")
                                .font(AppTypography.dynamicFont(style: .subheadline))
                                .foregroundColor(Color(UIColor.label))
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)

                            // Show red card icons based on the count
                            let homeRedCards = countTeamRedCards(teamId: fixture.teams.home?.id)

                            if homeRedCards > 0 {
                                HStack(spacing: 2) {
                                    if homeRedCards >= 5 {
                                        // For 5 or more red cards, show one icon with a multiplier
                                        Image("red-card")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 12, height: 16)
                                            .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)

                                        Text("x\(homeRedCards)")
                                            .font(AppTypography.dynamicFont(style: .caption2, weight: .bold))
                                            .foregroundColor(AppColors.Sports.redCard)
                                    } else {
                                        // Show one red card icon per red card (up to 3)
                                        ForEach(0..<min(homeRedCards, 3), id: \.self) { _ in
                                            Image("red-card")
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: 12, height: 16)
                                                .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                                        }
                                    }
                                }
                                .offset(y: 1)
                                // Add refresh trigger ID to force red card updates
                                .id("home-red-cards-\(fixture.id)-\(homeRedCards)-\(refreshTrigger)")
                            }
                        }
                    }

                    HStack(spacing: AppLayout.spacingS) {
                        TeamLogo(url: fixture.teams.away?.logo, teamName: fixture.teams.away?.name ?? "Away")
                            .foregroundColor(AppColors.Sports.draw)

                        HStack(spacing: 6) {
                            Text(fixture.teams.away?.name ?? "N/A")
                                .font(AppTypography.dynamicFont(style: .subheadline))
                                .foregroundColor(Color(UIColor.label))
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)

                            // Show red card icons based on the count
                            let awayRedCards = countTeamRedCards(teamId: fixture.teams.away?.id)

                            if awayRedCards > 0 {
                                HStack(spacing: 2) {
                                    if awayRedCards >= 5 {
                                        // For 5 or more red cards, show one icon with a multiplier
                                        Image("red-card")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 12, height: 16)
                                            .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)

                                        Text("x\(awayRedCards)")
                                            .font(AppTypography.dynamicFont(style: .caption2, weight: .bold))
                                            .foregroundColor(AppColors.Sports.redCard)
                                    } else {
                                        // Show one red card icon per red card (up to 3)
                                        ForEach(0..<min(awayRedCards, 3), id: \.self) { _ in
                                            Image("red-card")
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: 12, height: 16)
                                                .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                                        }
                                    }
                                }
                                .offset(y: 1)
                                // Add refresh trigger ID to force red card updates
                                .id("away-red-cards-\(fixture.id)-\(awayRedCards)-\(refreshTrigger)")
                            }
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // Middle: Scores (only for Live/Finished)
                if shouldShowScore {
                    VStack(alignment: .center, spacing: AppLayout.spacingS) {
                        Text("\(fixture.goals.home ?? 0)")
                            .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                            .foregroundColor(Color(UIColor.label)) // Always use label color for better contrast

                        Text("\(fixture.goals.away ?? 0)")
                            .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                            .foregroundColor(Color(UIColor.label)) // Always use label color for better contrast
                    }
                    .padding(.horizontal, AppLayout.spacingS)
                    .accessibilityInfo(
                        label: "Score: \(fixture.teams.home?.name ?? "Home") \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0) \(fixture.teams.away?.name ?? "Away")"
                    )
                    // Add refresh trigger ID to force score updates
                    .id("score-row-\(fixture.id)-\(fixture.goals.home ?? 0)-\(fixture.goals.away ?? 0)-\(refreshTrigger)")
                }

                // Status Text
                statusView()
                    .frame(minWidth: 60, alignment: .trailing)
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, 5) // Changed to 5 points vertical padding
            .contentShape(Rectangle()) // Make the entire row tappable
        }
        .buttonStyle(PlainButtonStyle()) // Make the link area look like plain content
        .accessibilityInfo(
            label: getAccessibilityLabel(),
            hint: getAccessibilityHint()
        )
        .onAppear {
            // Start a timer for live fixtures to ensure UI updates
            // This timer only updates the UI display, not the underlying data
            if isLive {
                timer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
                    // Only continue if the fixture is still live
                    if isLive {
                        // Only increment refresh trigger for UI updates
                        // Do not trigger any data fetches from here to avoid conflicts with socket updates
                        refreshTrigger += 1
                    } else {
                        timer?.invalidate()
                        timer = nil
                    }
                }
            }
        }
        .onDisappear {
            // Clean up timer when view disappears
            timer?.invalidate()
            timer = nil
        }
        .onChange(of: fixture.status.short) { newStatus in
            // React to status changes (likely from socket updates)
            // Stop timer if match is no longer live
            if !isLive && timer != nil {
                timer?.invalidate()
                timer = nil
            }
            // Start timer if match becomes live
            else if isLive && timer == nil {
                timer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
                    if isLive {
                        refreshTrigger += 1
                    } else {
                        timer?.invalidate()
                        timer = nil
                    }
                }
            }
        }
    }

    // Helper View for Team Logo with cached image view for persistence
    private func TeamLogo(url: String?, teamName: String) -> some View {
        CachedImageView.teamLogo(
            url: url,
            size: AppLayout.iconSizeM,
            teamName: teamName
        )
    }

    // Status categorization helpers
    private var isNotStarted: Bool {
        fixture.status.short == "NS"
    }

    private var isLive: Bool {
        let liveCodes = ["LIVE", "1H", "HT", "2H", "ET", "P", "Aw. ET"]
        return liveCodes.contains(fixture.status.short ?? "")
    }

    // Consider any non-NS, non-Live status as Finished/Other for layout purposes
    private var isFinishedOrOther: Bool {
        !isNotStarted && !isLive
    }

    // Helper to determine if scores should be shown
    private var shouldShowScore: Bool {
        let scoreStatusCodes = ["FT", "AET", "PEN", "LIVE", "1H", "HT", "2H", "ET", "P", "Aw. ET"]
        return scoreStatusCodes.contains(fixture.status.short ?? "")
    }

    // Helper ViewBuilder for status/time text
    @ViewBuilder
    private func statusView() -> some View {
        // Display score if FT, otherwise display time/status
        switch fixture.status.short {
        case "FT", "AET", "PEN": // Finished statuses
            Text(fixture.status.short ?? "FT")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(Color(UIColor.secondaryLabel))
                .padding(.vertical, 4) // Reduced vertical padding
                .padding(.horizontal, 8)
                .background(Color(UIColor.tertiarySystemFill).opacity(0.7))
                .cornerRadius(AppLayout.cornerRadiusS)
        case "HT":
            Text("HT")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(AppColors.buttonText)
                .padding(.vertical, 4)
                .padding(.horizontal, 8)
                .background(Color.green.opacity(0.8)) // Darker green for better contrast
                .cornerRadius(AppLayout.cornerRadiusS)
        case "NS":
            Text(formattedKickoffTime()) // Show kickoff time
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(Color(UIColor.secondaryLabel))
        case "LIVE", "1H", "2H", "ET", "P", "Aw. ET": // Various live/in-progress statuses
            if let elapsed = fixture.status.elapsed {
                // Get stoppage time information
                let stoppageInfo = getStoppageTimeInfo(for: elapsed)

                // Display with extra minutes if available
                Text(stoppageInfo.extraMinutes > 0 ? "\(stoppageInfo.baseTime)+\(stoppageInfo.extraMinutes)'" : "\(elapsed)'")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                    .foregroundColor(AppColors.buttonText)
                    .padding(.vertical, 4) // Reduced vertical padding
                    .padding(.horizontal, 8)
                    .background(Color.red.opacity(0.8)) // Darker red for better contrast
                    .cornerRadius(AppLayout.cornerRadiusS)
                    // Add a unique ID that changes when the fixture status changes
                    // Include refresh trigger to force updates
                    .id("timer-\(fixture.id)-\(elapsed)-\(fixture.status.extra ?? 0)-\(fixture.status.short ?? "")-\(refreshTrigger)")
            } else {
                Text(fixture.status.short ?? "Live") // Fallback for live
                    .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                    .foregroundColor(AppColors.buttonText)
                    .padding(.vertical, 4) // Reduced vertical padding
                    .padding(.horizontal, 8)
                    .background(Color.red.opacity(0.8)) // Darker red for better contrast
                    .cornerRadius(AppLayout.cornerRadiusS)
                    // Add ID for live status without elapsed time
                    .id("live-\(fixture.id)-\(fixture.status.short ?? "")-\(refreshTrigger)")
            }
        case "PST", "SUSP", "INT", "CANC", "ABD", "AWD", "WO": // Other statuses
            // Allow wrapping for long statuses like Postponed, Cancelled
            Text(fixture.status.long ?? fixture.status.short ?? "Status")
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(AppColors.secondaryText)
                .fixedSize(horizontal: false, vertical: true) // Allow vertical expansion
                .padding(.vertical, 4) // Reduced vertical padding
                .padding(.horizontal, 8)
                .background(AppColors.tertiaryFill.opacity(0.5))
                .cornerRadius(AppLayout.cornerRadiusS)
        default:
            Text(fixture.status.short ?? "-") // Fallback status
                .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                .foregroundColor(AppColors.secondaryText)
        }
    }

    // Helper to format kickoff time
    private func formattedKickoffTime() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm" // e.g., 19:45
        return dateFormatter.string(from: fixture.kickoffDate)
    }

    // Helper to count the number of red cards for a team
    private func countTeamRedCards(teamId: Int?) -> Int {
        guard let teamId = teamId, let events = fixture.events else {
            return 0
        }

        // Get all players who received any type of card
        let playersWithCards = events
            .filter { event in
                event.team.id == teamId &&
                event.type == "Card"
            }
            .compactMap { $0.player.id }

        // Count unique players with red cards by checking each player's card events
        var uniquePlayersWithRedCards = Set<Int>()

        // Get unique player IDs
        let uniquePlayerIds = Set(playersWithCards)

        // For each player, check if they received a red card (either direct or through two yellows)
        for playerId in uniquePlayerIds {
            // Get all card events for this player
            let playerCardEvents = events.filter { event in
                event.player.id == playerId &&
                event.team.id == teamId &&
                event.type == "Card"
            }

            // Check if player received a direct red card
            let hasDirectRedCard = playerCardEvents.contains { event in
                event.detail.lowercased().contains("red") &&
                !event.detail.lowercased().contains("yellow")
            }

            // Count yellow cards for this player
            let yellowCardCount = playerCardEvents.filter { event in
                event.detail.lowercased().contains("yellow")
            }.count

            // If player has a direct red card or 2+ yellow cards, add to the set
            if hasDirectRedCard || yellowCardCount >= 2 {
                uniquePlayersWithRedCards.insert(playerId)
            }
        }

        return uniquePlayersWithRedCards.count
    }

    // Helper to get players with second yellow cards (for internal use)
    private func getPlayersWithSecondYellows(teamId: Int?) -> [Int] {
        guard let teamId = teamId, let events = fixture.events else {
            return []
        }

        // Get all players who received a second yellow card
        return events
            .filter { event in
                event.team.id == teamId &&
                event.type == "Card" &&
                event.detail.lowercased().contains("yellow")
            }
            .compactMap { $0.player.id }
            .reduce(into: [Int: Int]()) { counts, playerId in
                counts[playerId, default: 0] += 1
            }
            .filter { $0.value > 1 }
            .map { $0.key }
    }

    // Helper to check if a team has received a red card
    private func hasTeamReceivedRedCard(teamId: Int?) -> Bool {
        return countTeamRedCards(teamId: teamId) > 0
    }

    // Helper to check if a player has received a second yellow card
    private func hasPlayerReceivedSecondYellow(playerId: Int?, teamId: Int) -> Bool {
        guard let playerId = playerId, let events = fixture.events else {
            return false
        }

        // Count yellow cards for this player
        let yellowCardEvents = events.filter { event in
            event.player.id == playerId &&
            event.team.id == teamId &&
            event.type == "Card" &&
            event.detail.lowercased().contains("yellow")
        }

        // If there are 2 or more yellow cards, the player has received a second yellow
        return yellowCardEvents.count > 1
    }

    // Helper to generate accessibility label with red card information
    private func getAccessibilityLabel() -> String {
        let homeTeamName = fixture.teams.home?.name ?? "Home"
        let awayTeamName = fixture.teams.away?.name ?? "Away"

        let homeRedCards = countTeamRedCards(teamId: fixture.teams.home?.id)
        let awayRedCards = countTeamRedCards(teamId: fixture.teams.away?.id)

        let homeRedCardText = homeRedCards > 0 ? " (\(homeRedCards) Red Card\(homeRedCards > 1 ? "s" : ""))" : ""
        let awayRedCardText = awayRedCards > 0 ? " (\(awayRedCards) Red Card\(awayRedCards > 1 ? "s" : ""))" : ""

        return "\(homeTeamName)\(homeRedCardText) versus \(awayTeamName)\(awayRedCardText)"
    }

    // Helper to generate accessibility hint based on match status
    private func getAccessibilityHint() -> String {
        if isLive {
            if let elapsed = fixture.status.elapsed {
                let stoppageInfo = getStoppageTimeInfo(for: elapsed)
                let timeDisplay = stoppageInfo.extraMinutes > 0 ? "\(stoppageInfo.baseTime)+\(stoppageInfo.extraMinutes)" : "\(elapsed)"
                return "Live match, \(timeDisplay) minutes played. Score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)"
            } else {
                return "Live match. Score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)"
            }
        } else if isNotStarted {
            return "Match starts at \(formattedKickoffTime())"
        } else if isFinishedOrOther {
            return "Match finished. Final score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)"
        } else {
            return "Tap for match details"
        }
    }

    // Helper to get stoppage time information - simplified to use API data
    private func getStoppageTimeInfo(for currentElapsed: Int) -> (baseTime: Int, extraMinutes: Int) {
        // If the API provides explicit extra time, use it - even if it's 0
        if let statusExtra = fixture.status.extra {
            // Check the match status to determine which half we're in
            let status = fixture.status.short ?? ""
            let baseTime: Int

            // Determine the base time based on match status and elapsed time
            if status == "1H" || status == "HT" {
                // First half or halftime
                baseTime = 45
                return (baseTime: baseTime, extraMinutes: statusExtra)
            } else if status == "2H" {
                // Second half
                baseTime = 90
                return (baseTime: baseTime, extraMinutes: statusExtra)
            } else if status == "ET" && currentElapsed <= 105 {
                // First half of extra time
                baseTime = 105
                return (baseTime: baseTime, extraMinutes: statusExtra)
            } else if status == "ET" {
                // Second half of extra time
                baseTime = 120
                return (baseTime: baseTime, extraMinutes: statusExtra)
            }

            // If we can't determine from status, use elapsed time
            if currentElapsed >= 45 && currentElapsed < 50 {
                // This is likely first half stoppage time
                return (baseTime: 45, extraMinutes: statusExtra)
            } else if currentElapsed >= 90 && currentElapsed < 95 {
                // This is likely second half stoppage time
                return (baseTime: 90, extraMinutes: statusExtra)
            } else if currentElapsed >= 105 && currentElapsed < 110 {
                // This is likely first half of extra time stoppage
                return (baseTime: 105, extraMinutes: statusExtra)
            } else if currentElapsed >= 120 && currentElapsed < 125 {
                // This is likely second half of extra time stoppage
                return (baseTime: 120, extraMinutes: statusExtra)
            } else if currentElapsed <= 45 {
                // Fallback for first half
                return (baseTime: currentElapsed, extraMinutes: 0)
            } else if currentElapsed <= 90 {
                // Fallback for second half
                return (baseTime: currentElapsed, extraMinutes: 0)
            } else if currentElapsed <= 105 {
                // Fallback for first half of extra time
                return (baseTime: currentElapsed, extraMinutes: 0)
            } else {
                // Fallback for second half of extra time
                return (baseTime: currentElapsed, extraMinutes: 0)
            }
        }

        // Special case: Check if this is likely stoppage time based on elapsed time
        // This handles cases where the API doesn't provide extra time but the elapsed time suggests stoppage time
        if currentElapsed > 45 && currentElapsed < 50 {
            // This could be first half stoppage time or early second half
            let status = fixture.status.short ?? ""
            if status == "1H" || status == "HT" {
                // If it's first half or halftime, it's definitely first half stoppage time
                return (baseTime: 45, extraMinutes: currentElapsed - 45)
            } else if status == "2H" && currentElapsed == 46 {
                // If it's second half and minute 46, it's the start of the second half, not stoppage time
                // Show as 46' instead of 45+1'
                return (baseTime: 46, extraMinutes: 0)
            }
        } else if currentElapsed > 90 && currentElapsed < 95 {
            // This could be second half stoppage time or early extra time
            let status = fixture.status.short ?? ""
            if status == "2H" || status == "FT" {
                // If it's second half or full time, it's definitely second half stoppage time
                return (baseTime: 90, extraMinutes: currentElapsed - 90)
            }
        }

        // If no extra time is provided and not in a likely stoppage time range, just return the current elapsed time
        return (baseTime: currentElapsed, extraMinutes: 0)
    }
}

#Preview {
    FixtureRowView(fixture: Fixture.mock)
        .padding()
}
