import SwiftUI

struct TeamStatsOverviewSection: View {
    let fixture: Fixture
    @Binding var selectedTab: SelectedTab

    // Extract home and away stats for easier access - always use full match stats for overview
    private var homeTeamStats: [StatisticItem]? {
        fixture.statistics?.first(where: { $0.team.id == fixture.teams.home?.id })?.statistics
    }

    private var awayTeamStats: [StatisticItem]? {
        fixture.statistics?.first(where: { $0.team.id == fixture.teams.away?.id })?.statistics
    }

    // Helper to get value for a specific stat type and team
    private func getStatValue(type: String, teamStats: [StatisticItem]?) -> String? {
        // Handle Pass Accuracy specifically
        if type.lowercased() == "passes %" {
            // Find 'Total Passes' and 'Accurate Passes' to calculate
            let total = teamStats?.first(where: { $0.type.lowercased() == "total passes" })?.value
            let accurate = teamStats?.first(where: { $0.type.lowercased() == "passes accurate" })?.value
            guard let totalStr = total, let accStr = accurate,
                  let totalVal = Double(totalStr), let accVal = Double(accStr), totalVal > 0 else {
                return "-"
            }
            let percentage = (accVal / totalVal) * 100
            return String(format: "%.1f%%", percentage)
        }
        return teamStats?.first(where: { $0.type.lowercased() == type.lowercased() })?.value
    }

    // Check if we have any stats to display
    private var hasStats: Bool {
        guard let homeStats = homeTeamStats, let awayStats = awayTeamStats else { return false }
        return !homeStats.isEmpty && !awayStats.isEmpty
    }

    // Get the general stats we want to display
    private var generalStats: [String] {
        // We'll show these key stats in the overview
        return ["ball possession", "expected_goals", "fouls"]
    }

    var body: some View {
        if hasStats {
            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                    // Title inside the container
                    Text("Live Stats")
                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                        .foregroundColor(Color(UIColor.label))
                        .padding(.horizontal, AppLayout.spacingM)
                        .padding(.top, AppLayout.spacingM)
                        .padding(.bottom, AppLayout.spacingXS)

                    // Stats content
                    VStack(spacing: AppLayout.spacingS) {
                        ForEach(generalStats, id: \.self) { statType in
                            let homeValue = getStatValue(type: statType, teamStats: homeTeamStats)
                            let awayValue = getStatValue(type: statType, teamStats: awayTeamStats)
                            let displayName = getDisplayName(for: statType)
                            let highlight = compareStatValues(home: homeValue, away: awayValue)

                            if statType == "ball possession" {
                                VStack(alignment: .center, spacing: AppLayout.spacingXS) {
                                    Text(displayName)
                                        .font(AppTypography.dynamicFont(style: .caption))
                                        .foregroundColor(AppColors.text)
                                        .frame(maxWidth: .infinity, alignment: .center)

                                    if let homeVal = homeValue, let awayVal = awayValue {
                                        BallPossessionBar(homeValue: homeVal, awayValue: awayVal)
                                    }
                                }
                                .padding(.horizontal, AppLayout.spacingM)
                            } else {
                                StatRow(
                                    displayStatName: displayName,
                                    homeValue: homeValue ?? "-",
                                    awayValue: awayValue ?? "-",
                                    highlightState: highlight
                                )
                                .padding(.horizontal, AppLayout.spacingM)
                            }

                            if statType != generalStats.last {
                                Divider()
                                    .background(AppColors.separator)
                                    .padding(.horizontal, AppLayout.spacingM)
                            }
                        }
                    }

                    // See all stats button at the bottom center
                    Button {
                        withAnimation {
                            selectedTab = .stats
                        }
                    } label: {
                        Text("See all stats")
                            .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                            .foregroundColor(.white)
                            .padding(.vertical, AppLayout.spacingS)
                    }
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.bottom, AppLayout.spacingS)
                }
                .background(AppColors.tertiaryBackground)
                .cornerRadius(AppLayout.cornerRadiusL)
                .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
            }
            .padding(.horizontal, 0)
        }
    }
}

#Preview {
    TeamStatsOverviewSection(
        fixture: Fixture.mock,
        selectedTab: .constant(.overview)
    )
    .padding()
    .background(AppColors.background)
}
