import SwiftUI
import <PERSON><PERSON><PERSON>

struct TeamFormView: View {
    let fixture: Fixture
    @StateObject private var viewModel = TeamFormViewModel()

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                // Header with expand/collapse button
                HStack {
                    Text("Team Form")
                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                        .foregroundColor(Color(UIColor.label))

                    Spacer()

                    // Only show expand button if we have data
                    if viewModel.canExpand {
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                viewModel.toggleExpansion()
                            }
                        }) {
                            Image(systemName: viewModel.isExpanded ? "chevron.up" : "chevron.down")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(AppColors.secondaryText)
                                .padding(6)
                                .background(Color.gray.opacity(0.1))
                                .clipShape(Circle())
                        }
                    }
                }
                .padding(.bottom, AppLayout.spacingXS)

                if viewModel.isLoading {
                    // Show skeleton loading instead of spinner for better UX
                    VStack(spacing: AppLayout.spacingM) {
                        // Home team skeleton
                        HStack {
                            Circle()
                                .fill(Color(UIColor.systemGray5))
                                .frame(width: 24, height: 24)

                            Rectangle()
                                .fill(Color(UIColor.systemGray5))
                                .frame(width: 100, height: 16)
                                .cornerRadius(4)

                            Spacer()

                            HStack(spacing: 4) {
                                ForEach(0..<5, id: \.self) { _ in
                                    Rectangle()
                                        .fill(Color(UIColor.systemGray5))
                                        .frame(width: 20, height: 20)
                                        .cornerRadius(4)
                                }
                            }
                        }

                        // Away team skeleton
                        HStack {
                            Circle()
                                .fill(Color(UIColor.systemGray5))
                                .frame(width: 24, height: 24)

                            Rectangle()
                                .fill(Color(UIColor.systemGray5))
                                .frame(width: 100, height: 16)
                                .cornerRadius(4)

                            Spacer()

                            HStack(spacing: 4) {
                                ForEach(0..<5, id: \.self) { _ in
                                    Rectangle()
                                        .fill(Color(UIColor.systemGray5))
                                        .frame(width: 20, height: 20)
                                        .cornerRadius(4)
                                }
                            }
                        }
                    }
                    .padding(.vertical, 4)
                } else if viewModel.isExpanded {
                    // Expanded two-column view
                    TwoColumnFormView(
                        homeTeamDetailedForm: viewModel.homeTeamDetailedForm,
                        awayTeamDetailedForm: viewModel.awayTeamDetailedForm,
                        homeTeamName: fixture.teams.home?.name ?? "Home Team",
                        awayTeamName: fixture.teams.away?.name ?? "Away Team"
                    )
                    .transition(.opacity)
                } else {
                    // Collapsed view with simple form indicators
                    VStack(spacing: AppLayout.spacingM) {
                        // Home team form row
                        TeamFormRow(
                            team: fixture.teams.home,
                            form: viewModel.homeTeamForm ?? "WDWLW",
                            isHome: true
                        )

                        // Away team form row
                        TeamFormRow(
                            team: fixture.teams.away,
                            form: viewModel.awayTeamForm ?? "LDWLW",
                            isHome: false
                        )
                    }
                    .transition(.opacity)
                }
            }
            .padding(AppLayout.spacingM)
            .background(AppColors.tertiaryBackground)
            .cornerRadius(AppLayout.cornerRadiusL)
            .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
        }
        .onAppear {
            viewModel.fetchTeamForm(fixture: fixture)
        }
    }
}

struct TeamFormRow: View {
    let team: Fixture.TeamInfo?
    let form: String
    let isHome: Bool

    var body: some View {
        HStack(spacing: AppLayout.spacingM) {
            // Team logo and name
            HStack(spacing: AppLayout.spacingS) {
                KFImage(URL(string: team?.logo ?? ""))
                    .placeholder {
                        Image("goal")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 24, height: 24)
                            .foregroundColor(.white)
                    }
                    .resizable()
                    .scaledToFit()
                    .frame(width: 24, height: 24)

                Text(team?.name ?? "N/A")
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                    .foregroundColor(Color(UIColor.label))
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
            }

            Spacer()

            // Form indicators
            HStack(spacing: 4) {
                if form.isEmpty {
                    Text("No form data")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                } else {
                    // Display last 5 matches form (W/D/L)
                    // The form string should be like "WWDLW" but we need to check if it's in the correct format
                    if form.contains("%") {
                        // If it contains %, it's not in the expected format
                        Text("Form data unavailable")
                            .font(AppTypography.dynamicFont(style: .caption))
                            .foregroundColor(AppColors.secondaryText)
                    } else {
                        // Display form as W/D/L indicators (oldest on the left, most recent on the right)
                        // Take up to 5 characters from the form string
                        let formChars = Array(form.prefix(5))
                        // The backend now returns form in order from oldest to newest
                        ForEach(Array(formChars.enumerated()), id: \.offset) { index, result in
                            // Most recent match is the last one (rightmost)
                            formIndicator(for: String(result), isLatest: index == formChars.count - 1)
                        }
                    }
                }
            }
        }
        .padding(.vertical, 4)
    }

    // Form indicator for W/D/L
    private func formIndicator(for result: String, isLatest: Bool) -> some View {
        let (backgroundColor, textColor) = formColors(for: result)

        let indicator = Text(result)
            .font(.system(size: 12, weight: .bold))
            .foregroundColor(textColor)
            .frame(width: 20, height: 20)
            .background(backgroundColor)
            .cornerRadius(4)

        if isLatest {
            return AnyView(
                indicator
                    .padding(1)
                    .overlay(
                        RoundedRectangle(cornerRadius: 5)
                            .stroke(Color.gray, lineWidth: 1)
                    )
            )
        } else {
            return AnyView(indicator)
        }
    }

    // Get colors for form indicators
    private func formColors(for result: String) -> (Color, Color) {
        switch result.uppercased() {
        case "W":
            return (AppColors.Sports.win, .white)
        case "D":
            return (Color.gray.opacity(0.9), .white)
        case "L":
            return (Color.red, .white)
        default:
            return (Color.gray.opacity(0.5), .white)
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        // Collapsed view
        TeamFormView(fixture: Fixture.mock)
            .padding()

        // Expanded view (two-column layout)
        TwoColumnFormView(
            homeTeamDetailedForm: [
                DetailedFormResult(
                    result: "W",
                    fixtureId: 1,
                    date: "2023-05-01T15:00:00+00:00",
                    homeTeam: DetailedFormResult.TeamInfo(id: 1, name: "Team A", logo: "https://media.api-sports.io/football/teams/1.png"),
                    awayTeam: DetailedFormResult.TeamInfo(id: 2, name: "Team B", logo: "https://media.api-sports.io/football/teams/2.png"),
                    score: DetailedFormResult.Score(home: 2, away: 1),
                    isHomeTeam: true
                ),
                DetailedFormResult(
                    result: "D",
                    fixtureId: 2,
                    date: "2023-05-08T15:00:00+00:00",
                    homeTeam: DetailedFormResult.TeamInfo(id: 3, name: "Team C", logo: "https://media.api-sports.io/football/teams/3.png"),
                    awayTeam: DetailedFormResult.TeamInfo(id: 1, name: "Team A", logo: "https://media.api-sports.io/football/teams/1.png"),
                    score: DetailedFormResult.Score(home: 1, away: 1),
                    isHomeTeam: false
                ),
                DetailedFormResult(
                    result: "L",
                    fixtureId: 3,
                    date: "2023-05-15T15:00:00+00:00",
                    homeTeam: DetailedFormResult.TeamInfo(id: 1, name: "Team A", logo: "https://media.api-sports.io/football/teams/1.png"),
                    awayTeam: DetailedFormResult.TeamInfo(id: 4, name: "Team D", logo: "https://media.api-sports.io/football/teams/4.png"),
                    score: DetailedFormResult.Score(home: 0, away: 2),
                    isHomeTeam: true
                )
            ],
            awayTeamDetailedForm: [
                DetailedFormResult(
                    result: "W",
                    fixtureId: 4,
                    date: "2023-05-01T15:00:00+00:00",
                    homeTeam: DetailedFormResult.TeamInfo(id: 5, name: "Team E", logo: "https://media.api-sports.io/football/teams/5.png"),
                    awayTeam: DetailedFormResult.TeamInfo(id: 6, name: "Team F", logo: "https://media.api-sports.io/football/teams/6.png"),
                    score: DetailedFormResult.Score(home: 3, away: 0),
                    isHomeTeam: true
                ),
                DetailedFormResult(
                    result: "W",
                    fixtureId: 5,
                    date: "2023-05-08T15:00:00+00:00",
                    homeTeam: DetailedFormResult.TeamInfo(id: 6, name: "Team F", logo: "https://media.api-sports.io/football/teams/6.png"),
                    awayTeam: DetailedFormResult.TeamInfo(id: 7, name: "Team G", logo: "https://media.api-sports.io/football/teams/7.png"),
                    score: DetailedFormResult.Score(home: 2, away: 0),
                    isHomeTeam: true
                ),
                DetailedFormResult(
                    result: "L",
                    fixtureId: 6,
                    date: "2023-05-15T15:00:00+00:00",
                    homeTeam: DetailedFormResult.TeamInfo(id: 8, name: "Team H", logo: "https://media.api-sports.io/football/teams/8.png"),
                    awayTeam: DetailedFormResult.TeamInfo(id: 6, name: "Team F", logo: "https://media.api-sports.io/football/teams/6.png"),
                    score: DetailedFormResult.Score(home: 1, away: 0),
                    isHomeTeam: false
                )
            ],
            homeTeamName: "Team A",
            awayTeamName: "Team F"
        )
        .padding()
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .padding()
    }
    .background(AppColors.background)
}
