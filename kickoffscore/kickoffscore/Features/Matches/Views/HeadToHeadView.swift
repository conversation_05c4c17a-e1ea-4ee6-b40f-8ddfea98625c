import SwiftUI
import Kingfisher
import Combine

struct HeadToHeadView: View {
    let fixture: Fixture
    @StateObject private var viewModel = AsyncHeadToHeadViewModel()

    var body: some View {
        ScrollView {
            LazyVStack(spacing: AppLayout.spacingS) { // Reduced spacing between containers to match other tabs
                // Head-to-Head Summary Card
                if viewModel.isLoading {
                    ProgressView()
                        .scaleEffect(1.5)
                        .frame(maxWidth: .infinity, minHeight: 200)
                        .padding(.vertical, AppLayout.spacingL)
                } else if let summary = viewModel.h2hSummary {
                    HeadToHeadSummaryCard(summary: summary, homeTeam: fixture.teams.home, awayTeam: fixture.teams.away)
                        .padding(.top, AppLayout.spacingS) // Reduced top padding from spacingM to spacingS
                } else if let error = viewModel.errorMessage {
                    EnhancedErrorView(
                        message: error,
                        retryAction: {
                            if let homeTeamId = fixture.teams.home?.id, let awayTeamId = fixture.teams.away?.id {
                                Task {
                                    // Force refresh when retrying after an error
                                    await viewModel.loadHeadToHeadData(homeTeamId: homeTeamId, awayTeamId: awayTeamId, forceRefresh: true)
                                }
                            }
                        },
                        title: "Error loading head-to-head data"
                    )
                } else {
                    EnhancedEmptyStateView(
                        message: "These teams haven't played against each other recently",
                        title: "No head-to-head data available"
                    )
                }

                // Previous Matches List
                if !viewModel.h2hMatches.isEmpty {
                    VStack(alignment: .leading, spacing: AppLayout.spacingM) {
                        HStack {
                            Text("Previous Matches")
                                .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                                .foregroundColor(AppColors.text)

                            Spacer()

                            Text("\(viewModel.h2hMatches.count) matches")
                                .font(AppTypography.dynamicFont(style: .subheadline))
                                .foregroundColor(AppColors.secondaryText)
                        }
                        .padding(.horizontal, AppLayout.spacingL)

                        ForEach(viewModel.h2hMatches) { match in
                            HeadToHeadMatchCard(match: match)
                        }
                    }
                    .padding(.bottom, AppLayout.spacingL)
                }
            }
        }
        .background(AppColors.background)
        .onAppear {
            // Load H2H data when the view appears
            if let homeTeamId = fixture.teams.home?.id, let awayTeamId = fixture.teams.away?.id {
                Task {
                    await viewModel.loadHeadToHeadData(homeTeamId: homeTeamId, awayTeamId: awayTeamId)
                }
            }
        }
        .refreshable {
            if let homeTeamId = fixture.teams.home?.id, let awayTeamId = fixture.teams.away?.id {
                // Force refresh when user pulls to refresh
                await viewModel.loadHeadToHeadData(homeTeamId: homeTeamId, awayTeamId: awayTeamId, forceRefresh: true)
            }
        }
    }
}

// MARK: - Head to Head Summary Card
struct HeadToHeadSummaryCard: View {
    let summary: HeadToHeadSummary
    let homeTeam: Fixture.TeamInfo?
    let awayTeam: Fixture.TeamInfo?

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            // Header with title
            HStack {
                Text("Head to Head")
                    .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                    .foregroundColor(AppColors.text)

                Spacer()

                Text("\(summary.totalMatches) matches")
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(AppColors.secondaryText)
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.top, AppLayout.spacingM)

            // Teams and Stats
            HStack(spacing: AppLayout.spacingL) {
                // Home Team
                VStack(spacing: AppLayout.spacingS) {
                    KFImage(URL(string: homeTeam?.logo ?? ""))
                        .placeholder {
                            Image(systemName: "shield")
                                .resizable()
                                .scaledToFit()
                                .foregroundColor(AppColors.secondaryText)
                        }
                        .resizable()
                        .scaledToFit()
                        .frame(width: 50, height: 50)

                    Text(homeTeam?.name ?? "Home")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(AppColors.text)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                        .frame(width: 80)

                    // Home Wins
                    Text("\(summary.homeWins)")
                        .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                        .foregroundColor(AppColors.homeTeam)

                    Text("Wins")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                }
                .frame(width: 80)

                // Draws
                VStack(spacing: AppLayout.spacingS) {
                    Spacer()
                        .frame(height: 50)

                    Text("Draws")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(AppColors.text)

                    Text("\(summary.draws)")
                        .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                        .foregroundColor(AppColors.draw)

                    Text("\(Int(summary.drawPercentage))%")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                }
                .frame(width: 80)

                // Away Team
                VStack(spacing: AppLayout.spacingS) {
                    KFImage(URL(string: awayTeam?.logo ?? ""))
                        .placeholder {
                            Image(systemName: "shield")
                                .resizable()
                                .scaledToFit()
                                .foregroundColor(AppColors.secondaryText)
                        }
                        .resizable()
                        .scaledToFit()
                        .frame(width: 50, height: 50)

                    Text(awayTeam?.name ?? "Away")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(AppColors.text)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                        .frame(width: 80)

                    // Away Wins
                    Text("\(summary.awayWins)")
                        .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                        .foregroundColor(AppColors.awayTeam)

                    Text("Wins")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                }
                .frame(width: 80)
            }
            .padding(.horizontal, AppLayout.spacingM)

            Divider()
                .padding(.horizontal, AppLayout.spacingM)

            // Stats Grid
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: AppLayout.spacingM) {
                // Average Goals
                VStack(spacing: 4) {
                    Text("Avg. Goals")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)

                    Text(String(format: "%.1f", summary.averageGoalsPerMatch))
                        .font(AppTypography.dynamicFont(style: .body, weight: .bold))
                        .foregroundColor(AppColors.text)
                }

                // Both Teams Scored
                VStack(spacing: 4) {
                    Text("Both Scored")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)

                    Text("\(summary.bothTeamsScoredPercentage)%")
                        .font(AppTypography.dynamicFont(style: .body, weight: .bold))
                        .foregroundColor(AppColors.text)
                }

                // Clean Sheets
                VStack(spacing: 4) {
                    Text("Clean Sheets")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)

                    HStack(spacing: 4) {
                        Text("\(summary.homeCleanSheets)")
                            .font(AppTypography.dynamicFont(style: .body, weight: .bold))
                            .foregroundColor(AppColors.homeTeam)

                        Text("-")
                            .font(AppTypography.dynamicFont(style: .body))
                            .foregroundColor(AppColors.text)

                        Text("\(summary.awayCleanSheets)")
                            .font(AppTypography.dynamicFont(style: .body, weight: .bold))
                            .foregroundColor(AppColors.awayTeam)
                    }
                }

                // Total Goals
                VStack(spacing: 4) {
                    Text("Total Goals")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)

                    Text("\(summary.totalGoals)")
                        .font(AppTypography.dynamicFont(style: .body, weight: .bold))
                        .foregroundColor(AppColors.text)
                }

                // Home Goals
                VStack(spacing: 4) {
                    Text(homeTeam?.name ?? "Home")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                        .lineLimit(1)

                    Text("\(summary.homeGoals)")
                        .font(AppTypography.dynamicFont(style: .body, weight: .bold))
                        .foregroundColor(AppColors.homeTeam)
                }

                // Away Goals
                VStack(spacing: 4) {
                    Text(awayTeam?.name ?? "Away")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                        .lineLimit(1)

                    Text("\(summary.awayGoals)")
                        .font(AppTypography.dynamicFont(style: .body, weight: .bold))
                        .foregroundColor(AppColors.awayTeam)
                }
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.bottom, AppLayout.spacingM)
        }
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .elevatedMedium()
    }
}

// MARK: - Head to Head Match Card
struct HeadToHeadMatchCard: View {
    let match: HeadToHeadMatch

    var body: some View {
        VStack(spacing: AppLayout.spacingS) {
            // Match Date and League
            HStack {
                // League Logo and Name
                HStack(spacing: AppLayout.spacingXS) {
                    KFImage(URL(string: match.leagueLogo ?? ""))
                        .placeholder {
                            Image(systemName: "trophy")
                                .resizable()
                                .scaledToFit()
                                .foregroundColor(AppColors.secondaryText)
                        }
                        .resizable()
                        .scaledToFit()
                        .frame(width: 16, height: 16)

                    Text(match.leagueName)
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)
                        .lineLimit(1)
                }

                Spacer()

                // Date
                Text(match.formattedDate)
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(AppColors.secondaryText)
            }

            // Teams and Score
            HStack(spacing: AppLayout.spacingM) {
                // Home Team
                HStack(spacing: AppLayout.spacingS) {
                    KFImage(URL(string: match.homeTeamLogo ?? ""))
                        .placeholder {
                            Image(systemName: "shield")
                                .resizable()
                                .scaledToFit()
                                .foregroundColor(AppColors.secondaryText)
                        }
                        .resizable()
                        .scaledToFit()
                        .frame(width: 28, height: 28)

                    Text(match.homeTeamName)
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: match.homeGoals > match.awayGoals ? .bold : .regular))
                        .foregroundColor(match.homeGoals > match.awayGoals ? AppColors.text : AppColors.secondaryText)
                        .lineLimit(1)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // Score with background
                ZStack {
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                        .fill(AppColors.background)
                        .frame(width: 70, height: 32)
                        .elevatedLow()

                    HStack(spacing: AppLayout.spacingXS) {
                        Text("\(match.homeGoals)")
                            .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                            .foregroundColor(match.homeGoals > match.awayGoals ? AppColors.text : AppColors.secondaryText)

                        Text("-")
                            .font(AppTypography.dynamicFont(style: .title3))
                            .foregroundColor(AppColors.secondaryText)

                        Text("\(match.awayGoals)")
                            .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                            .foregroundColor(match.awayGoals > match.homeGoals ? AppColors.text : AppColors.secondaryText)
                    }
                }
                .frame(width: 70)

                // Away Team
                HStack(spacing: AppLayout.spacingS) {
                    Text(match.awayTeamName)
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: match.awayGoals > match.homeGoals ? .bold : .regular))
                        .foregroundColor(match.awayGoals > match.homeGoals ? AppColors.text : AppColors.secondaryText)
                        .lineLimit(1)
                        .multilineTextAlignment(.trailing)

                    KFImage(URL(string: match.awayTeamLogo ?? ""))
                        .placeholder {
                            Image(systemName: "shield")
                                .resizable()
                                .scaledToFit()
                                .foregroundColor(AppColors.secondaryText)
                        }
                        .resizable()
                        .scaledToFit()
                        .frame(width: 28, height: 28)
                }
                .frame(maxWidth: .infinity, alignment: .trailing)
            }

            // Bottom info row
            HStack {
                // Halftime Score
                HStack(spacing: 4) {
                    Text("HT:")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(AppColors.secondaryText)

                    Text("\(match.halfTimeHomeGoals) - \(match.halfTimeAwayGoals)")
                        .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                        .foregroundColor(AppColors.text)
                }

                Spacer()

                // Venue
                if let venue = match.venue, !venue.isEmpty {
                    HStack(spacing: 4) {
                        Image(systemName: "mappin.circle")
                            .font(.system(size: 12))
                            .foregroundColor(AppColors.secondaryText)

                        Text(venue)
                            .font(AppTypography.dynamicFont(style: .caption))
                            .foregroundColor(AppColors.secondaryText)
                            .lineLimit(1)
                    }
                }
            }
        }
        .padding(AppLayout.spacingM)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .elevatedLow()
    }
}

// MARK: - Preview
struct HeadToHeadView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            HeadToHeadView(fixture: Fixture.mock)
                .preferredColorScheme(.dark)

            HeadToHeadView(fixture: Fixture.mock)
                .preferredColorScheme(.light)
        }
    }
}
