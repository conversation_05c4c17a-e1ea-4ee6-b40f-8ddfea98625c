import SwiftUI

// MARK: - Stat Categories Enum
enum StatCategory: String, CaseIterable, Identifiable {
    case general = "General"
    case attack = "Attack"
    case defence = "Defence"
    case distribution = "Distribution"
    case discipline = "Discipline"

    var id: String { self.rawValue }

    // Helper to get the API stat names (lowercase) for this category
    var apiStatNames: [String] {
        switch self {
        case .general:
            return ["ball possession", "fouls", "expected_goals"]
        case .attack:
            return ["total shots", "shots on goal", "shots off goal", "shots insidebox", "shots outsidebox", "corner kicks", "offsides"]
        case .defence:
            return ["blocked shots", "goalkeeper saves", "goals_prevented"]
        case .distribution:
            // Note: "passes %" is handled separately in the view logic
            return ["passes accurate", "total passes", "passes %"]
        case .discipline:
            return ["yellow cards", "red cards", "fouls"]
        }
    }
}

// MARK: - Stat Mappings & Helpers

// Maps API stat names (lowercase for robust matching) to display names
// First word capitalized as per user preference
let statDisplayMapping: [String: String] = [
    "ball possession": "Ball possession",
    "shots on goal": "Shots on target", // User preferred naming
    "shots off goal": "Shots off target",
    "total shots": "Total shots",
    "blocked shots": "Blocked shots",
    "shots insidebox": "Shots inside box",
    "shots outsidebox": "Shots outside box",
    "fouls": "Fouls",
    "corner kicks": "Corners",
    "offsides": "Offsides",
    "yellow cards": "Yellow cards",
    "red cards": "Red cards",
    "goalkeeper saves": "Goalkeeper saves",
    "total passes": "Total passes",
    "passes accurate": "Accurate passes",
    "passes %": "Pass accuracy",
    "expected_goals": "Expected goals (xG)",
    "goals_prevented": "Goals prevented"
    // Add other stats from API if needed
]

// Helper to get display name, falling back to original if no mapping exists
func getDisplayName(for statType: String) -> String {
    // Handle "Passes %" specifically as its value is the percentage
    if statType.lowercased() == "passes %" {
        return "Pass Accuracy"
    }
    return statDisplayMapping[statType.lowercased()] ?? statType
}

// Helper to compare stat values numerically
enum HighlightState { case none, home, away }

func compareStatValues(home: String?, away: String?) -> HighlightState {
    guard let homeStr = home, let awayStr = away else { return .none }

    // Clean strings (remove %, handle nil/empty)
    let cleanHome = homeStr.trimmingCharacters(in: CharacterSet(charactersIn: "% "))
    let cleanAway = awayStr.trimmingCharacters(in: CharacterSet(charactersIn: "% "))

    guard let homeVal = Double(cleanHome), let awayVal = Double(cleanAway) else { return .none }

    if homeVal > awayVal {
        return .home
    } else if awayVal > homeVal {
        return .away
    } else {
        return .none
    }
}

// Helper to get color for stat highlight based on state
func getHighlightColor(for state: HighlightState) -> Color {
    switch state {
    case .home:
        return Color(red: 0.2, green: 0.6, blue: 0.86) // #3498DB - Blue for home team
    case .away:
        return Color(red: 0.91, green: 0.3, blue: 0.24) // #E74C3C - Red for away team
    case .none:
        return Color(UIColor.secondaryLabel)
    }
}


// MARK: - StatsView
struct StatsView: View {
    let fixture: Fixture

    // Enum for selecting Team or Player stats
    enum StatType: String, CaseIterable, Identifiable {
        case team = "Team"
        case player = "Player"
        var id: String { self.rawValue }
    }

    // Enum for selecting the period of statistics
    enum StatPeriod: String, CaseIterable, Identifiable {
        case all = "All"
        case firstHalf = "1st Half"
        case secondHalf = "2nd Half"
        var id: String { self.rawValue }
    }

    @State private var selectedStatType: StatType = .team
    @State private var selectedPeriod: StatPeriod = .all

    // Get the appropriate statistics based on the selected period
    private func getTeamStats(for teamId: Int, period: StatPeriod) -> [StatisticItem]? {
        guard let teamStats = fixture.statistics?.first(where: { $0.team.id == teamId }) else {
            return nil
        }

        switch period {
        case .all:
            return teamStats.statistics
        case .firstHalf:
            return teamStats.statistics_1h
        case .secondHalf:
            return teamStats.statistics_2h
        }
    }

    // Extract home and away stats for easier access based on selected period
    private var homeTeamStats: [StatisticItem]? {
        guard let homeTeamId = fixture.teams.home?.id else { return nil }
        return getTeamStats(for: homeTeamId, period: selectedPeriod)
    }

    private var awayTeamStats: [StatisticItem]? {
        guard let awayTeamId = fixture.teams.away?.id else { return nil }
        return getTeamStats(for: awayTeamId, period: selectedPeriod)
    }

    // Check if period data is available
    private var isPeriodDataAvailable: Bool {
        guard let homeTeamId = fixture.teams.home?.id,
              let awayTeamId = fixture.teams.away?.id,
              let homeTeamStats = fixture.statistics?.first(where: { $0.team.id == homeTeamId }),
              let awayTeamStats = fixture.statistics?.first(where: { $0.team.id == awayTeamId }) else {
            return false
        }

        switch selectedPeriod {
        case .all:
            return homeTeamStats.statistics != nil && awayTeamStats.statistics != nil
        case .firstHalf:
            return homeTeamStats.statistics_1h != nil && awayTeamStats.statistics_1h != nil
        case .secondHalf:
            return homeTeamStats.statistics_2h != nil && awayTeamStats.statistics_2h != nil
        }
    }

    // Get a unique list of all statistic types present for either team
    private var allStatTypes: [String] {
        guard let homeStats = homeTeamStats, let awayStats = awayTeamStats else { return [] }
        let homeTypes = Set(homeStats.map { $0.type })
        let awayTypes = Set(awayStats.map { $0.type })
        // Combine and potentially sort if needed, but categorization handles order
        return Array(homeTypes.union(awayTypes))
    }

    // Helper to get value for a specific stat type and team
    private func getStatValue(type: String, teamStats: [StatisticItem]?) -> String? {
        // Handle Pass Accuracy specifically
        if type.lowercased() == "passes %" {
            // Find 'Total Passes' and 'Accurate Passes' to calculate
            let total = teamStats?.first(where: { $0.type.lowercased() == "total passes" })?.value
            let accurate = teamStats?.first(where: { $0.type.lowercased() == "passes accurate" })?.value
            guard let totalStr = total, let accStr = accurate,
                  let totalVal = Double(totalStr), let accVal = Double(accStr), totalVal > 0 else {
                return "-"
            }
            let percentage = (accVal / totalVal) * 100
            return String(format: "%.1f%%", percentage)
        }
        return teamStats?.first(where: { $0.type == type })?.value
    }

    var body: some View {
        VStack(spacing: 0) {
            // Use native segmented control for team/player toggle
            Picker("Stat Type", selection: $selectedStatType) {
                ForEach(StatType.allCases) { type in
                    Text(type.rawValue.capitalized)
                        .tag(type)
                        .accessibilityInfo(
                            label: type.rawValue,
                            hint: selectedStatType == type ? "Selected tab" : "Tap to select this tab"
                        )
                }
            }
            .pickerStyle(.segmented)
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, AppLayout.spacingS)

            // Conditional Content based on selected stat type
            if selectedStatType == .team {
                // Period selection segmented control (only show for team stats)
                Picker("Stat Period", selection: $selectedPeriod) {
                    ForEach(StatPeriod.allCases) { period in
                        Text(period.rawValue)
                            .tag(period)
                            .accessibilityInfo(
                                label: period.rawValue,
                                hint: selectedPeriod == period ? "Selected period" : "Tap to select this period"
                            )
                    }
                }
                .pickerStyle(.segmented)
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.bottom, AppLayout.spacingS)

                if isPeriodDataAvailable && !allStatTypes.isEmpty {
                    ScrollView {
                        VStack(alignment: .leading, spacing: AppLayout.spacingXS) { // Further reduced spacing between containers for a more compact layout
                            // Removed team names header entirely as per user preference

                            // Iterate through Categories
                            ForEach(StatCategory.allCases) { category in
                                // Calculate available stats for this category
                                let categoryApiNamesLowercased = Set(category.apiStatNames)
                                let availableStatTypesLowercased = Set(allStatTypes.map { $0.lowercased() })
                                let intersectionLowercased = categoryApiNamesLowercased.intersection(availableStatTypesLowercased)

                                let statsForThisCategory = intersectionLowercased.compactMap { lowercasedName in
                                    allStatTypes.first { $0.lowercased() == lowercasedName }
                                }.sorted { stat1, stat2 in
                                    // Custom sorting for Attack category
                                    if category == .attack {
                                        let attackOrder = ["total shots", "shots on goal", "shots off goal", "shots insidebox", "shots outsidebox", "corner kicks", "offsides"]
                                        let index1 = attackOrder.firstIndex(of: stat1.lowercased()) ?? Int.max
                                        let index2 = attackOrder.firstIndex(of: stat2.lowercased()) ?? Int.max
                                        return index1 < index2
                                    }
                                    // Custom sorting for Distribution category
                                    else if category == .distribution {
                                        let distributionOrder = ["total passes", "passes accurate", "passes %"]
                                        let index1 = distributionOrder.firstIndex(of: stat1.lowercased()) ?? Int.max
                                        let index2 = distributionOrder.firstIndex(of: stat2.lowercased()) ?? Int.max
                                        return index1 < index2
                                    }
                                    // Default alphabetical sorting for other categories
                                    return stat1 < stat2
                                }

                                // Only show section if there are stats for it
                                if !statsForThisCategory.isEmpty {
                                    StatCategorySection(
                                        category: category,
                                        stats: statsForThisCategory,
                                        homeStats: homeTeamStats ?? [],
                                        awayStats: awayTeamStats ?? [],
                                        getStatValue: getStatValue,
                                        isFirstCategory: category == StatCategory.allCases.first
                                    )
                                }
                            }
                        }
                        .padding(.top, 0) // Remove top padding as we already have gap below segmented control
                        .padding(.bottom, AppLayout.spacingM)
                        .padding(.horizontal, 0)
                    }
                    .scrollIndicators(.hidden)
                    .edgesIgnoringSafeArea(.horizontal)
                } else {
                    // Consistent positioning for empty state
                    VStack {
                        // Add proper padding at the top to maintain spacing
                        Spacer()
                            .frame(height: AppLayout.spacingS)

                        // Empty state positioned in the same place as content would be
                        EmptyStateView(
                            icon: "chart.bar.xaxis",
                            message: selectedPeriod == .all ?
                                "Team statistics are not available for this match." :
                                "\(selectedPeriod.rawValue) statistics are not available for this match."
                        )
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                    }
                    .background(AppColors.background)
                }
            } else {
                // Player Stats Content
                PlayerStatsView(fixture: fixture)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: selectedStatType)
        .animation(.easeInOut(duration: 0.3), value: selectedPeriod)
    }
}

// MARK: - Stat Category Section
struct StatCategorySection: View {
    let category: StatCategory
    let stats: [String]
    let homeStats: [StatisticItem]
    let awayStats: [StatisticItem]
    let getStatValue: (String, [StatisticItem]?) -> String?
    var isFirstCategory: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingS) { // Reduced spacing to match other containers
            // Stats container with header inside
            VStack(spacing: AppLayout.spacingS) {
                // Category Header
                Text(category.rawValue)
                    .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                    .foregroundColor(Color(UIColor.label))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, AppLayout.spacingM)
                    .padding(.top, AppLayout.spacingM)
                    .padding(.bottom, AppLayout.spacingXS)
                VStack(spacing: AppLayout.spacingS) {
                    ForEach(stats, id: \.self) { statType in
                        let homeValue = getStatValue(statType, homeStats)
                        let awayValue = getStatValue(statType, awayStats)
                        let displayName = getDisplayName(for: statType)
                        let highlight = compareStatValues(home: homeValue, away: awayValue)

                        StatRow(
                            displayStatName: displayName,
                            homeValue: homeValue ?? "-",
                            awayValue: awayValue ?? "-",
                            highlightState: highlight
                        )

                        if statType != stats.last {
                            Divider()
                                .background(AppColors.separator)
                        }
                    }
                }
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.bottom, AppLayout.spacingM)
            }
            .background(AppColors.tertiaryBackground)
            .cornerRadius(AppLayout.cornerRadiusL)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .padding(.top, isFirstCategory ? 0 : AppLayout.spacingXS)
        .padding(.bottom, AppLayout.spacingXS)
        .accessibilityInfo(
            label: "\(category.rawValue) statistics",
            hint: "Comparison of team statistics in the \(category.rawValue.lowercased()) category"
        )
    }
}

// MARK: - Ball Possession Bar
struct BallPossessionBar: View {
    let homeValue: String
    let awayValue: String

    // Define colors
    private let homeColor = Color(red: 0.2, green: 0.6, blue: 0.86) // #3498DB - Blue for home team
    private let awayColor = Color(red: 0.91, green: 0.3, blue: 0.24) // #E74C3C - Red for away team

    private var homePercentage: Double {
        Double(homeValue.replacingOccurrences(of: "%", with: "")) ?? 0
    }

    private var awayPercentage: Double {
        Double(awayValue.replacingOccurrences(of: "%", with: "")) ?? 0
    }

    var body: some View {
        // Percentage bar
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // Home team section (left side)
                ZStack {
                    Rectangle()
                        .fill(homeColor) // Red for home team
                        .frame(width: geometry.size.width * (homePercentage / 100), height: 24)
                        .clipShape(
                            RoundedCorner(
                                radius: 12,
                                corners: [.topLeft, .bottomLeft]
                            )
                        )

                    // Home percentage label (inside bar)
                    if homePercentage >= 15 { // Only show if there's enough space
                        Text(homeValue)
                            .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                            .foregroundColor(.white)
                            .padding(.leading, 10)
                            .frame(width: geometry.size.width * (homePercentage / 100), alignment: .leading)
                    }
                }

                // Away team section (right side)
                ZStack {
                    Rectangle()
                        .fill(awayColor) // Blue for away team
                        .frame(width: geometry.size.width * (awayPercentage / 100), height: 24)
                        .clipShape(
                            RoundedCorner(
                                radius: 12,
                                corners: [.topRight, .bottomRight]
                            )
                        )
                        .position(x: geometry.size.width - (geometry.size.width * (awayPercentage / 100))/2, y: 12)

                    // Away percentage label (inside bar)
                    if awayPercentage >= 15 { // Only show if there's enough space
                        Text(awayValue)
                            .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                            .foregroundColor(.white)
                            .padding(.trailing, 10)
                            .frame(width: geometry.size.width * (awayPercentage / 100), alignment: .trailing)
                            .position(x: geometry.size.width - (geometry.size.width * (awayPercentage / 100))/2, y: 12)
                    }
                }
            }
        }
        .frame(height: 24)
        .accessibilityInfo(
            label: "Ball possession",
            hint: "Home: \(homeValue), Away: \(awayValue)"
        )
    }
}

// MARK: - StatRow Helper View
struct StatRow: View {
    let displayStatName: String
    let homeValue: String
    let awayValue: String
    let highlightState: HighlightState

    var body: some View {
        Group {
            // Standard stat row layout for all stats
            HStack(spacing: AppLayout.spacingM) {
                // Home value - hide for Ball possession
                if displayStatName != "Ball possession" {
                    Text(homeValue)
                        .font(AppTypography.dynamicFont(
                            style: .subheadline,
                            weight: highlightState == .home ? .bold : .regular
                        ))
                        .foregroundColor(highlightState == .home ? .white : Color(UIColor.secondaryLabel))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                                .fill(highlightState == .home ?
                                      getHighlightColor(for: .home) : Color.clear)
                        )
                } else {
                    Spacer()
                        .frame(width: 30)
                }

                // Stat name
                Text(displayStatName)
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(AppColors.text)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
                    .frame(maxWidth: .infinity)
                    .multilineTextAlignment(.center)

                // Away value - hide for Ball possession
                if displayStatName != "Ball possession" {
                    Text(awayValue)
                        .font(AppTypography.dynamicFont(
                            style: .subheadline,
                            weight: highlightState == .away ? .bold : .regular
                        ))
                        .foregroundColor(highlightState == .away ? .white : Color(UIColor.secondaryLabel))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                                .fill(highlightState == .away ?
                                      getHighlightColor(for: .away) : Color.clear)
                        )
                } else {
                    Spacer()
                        .frame(width: 30)
                }
            }

            // Add possession bar for Ball possession
            if displayStatName == "Ball possession" {
                BallPossessionBar(homeValue: homeValue, awayValue: awayValue)
                    .padding(.top, 4)
            }
        }
        .padding(.vertical, AppLayout.spacingXS)
        .accessibilityInfo(
            label: displayStatName,
            hint: "Home: \(homeValue), Away: \(awayValue)"
        )
    }
}

// MARK: - Preview
/* // << COMMENTED OUT PREVIEW
struct StatsView_Previews: PreviewProvider {
    static var previews: some View {
        // Restore preview using mock data
        StatsView(fixture: Fixture.mock)
    }
}
*/ // << COMMENTED OUT PREVIEW
