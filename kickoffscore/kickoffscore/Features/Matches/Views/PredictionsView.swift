import SwiftUI
import Kingfisher

struct PredictionsView: View {
    let fixture: Fixture
    @StateObject private var viewModel = PredictionViewModel()

    var body: some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: AppLayout.spacingS) { // Reduced spacing between containers to match other tabs
                if viewModel.isLoading {
                    SimpleSkeletonListView(itemCount: 3, showHeader: true)
                } else if let errorMessage = viewModel.errorMessage {
                    ErrorView(message: errorMessage)
                } else if let prediction = viewModel.prediction {
                    // Main prediction container
                    PredictionOverviewCard(
                        fixture: fixture,
                        prediction: prediction
                    )

                    // Team comparison container
                    TeamComparisonCard(
                        fixture: fixture,
                        prediction: prediction
                    )

                    // Additional stats container
                    AdditionalStatsCard(prediction: prediction)
                } else {
                    EmptyView(message: "No prediction available for this match")
                }
            }
            .padding(.top, AppLayout.spacingS) // Reduced top padding from spacingM to spacingS
            .padding(.bottom, AppLayout.spacingM)
            .padding(.horizontal, 0)
            .edgesIgnoringSafeArea(.horizontal)
        }
        .background(AppColors.background)
        .onAppear {
            viewModel.fetchPrediction(fixtureId: fixture.id)
        }
    }
}

// MARK: - Prediction Overview Card
struct PredictionOverviewCard: View {
    let fixture: Fixture
    let prediction: Prediction

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingM) {
            // Card Title
            Text("Prediction Overview")
                .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                .foregroundColor(Color(UIColor.label))
                .padding(.horizontal, 0)
                .padding(.bottom, AppLayout.spacingS)

            // Win probability section
            if let homePercent = prediction.predictions.percent.home,
               let drawPercent = prediction.predictions.percent.draw,
               let awayPercent = prediction.predictions.percent.away {

                VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                    Text("Win Probability")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(Color(UIColor.label))

                    ProbabilityBar(
                        homeTeam: fixture.teams.home?.name ?? "Home",
                        awayTeam: fixture.teams.away?.name ?? "Away",
                        homePercent: homePercent,
                        drawPercent: drawPercent,
                        awayPercent: awayPercent
                    )
                }
            }

            Divider()
                .background(AppColors.separator)

            // Predicted winner section
            if let winnerName = prediction.predictions.winner.name {
                HStack {
                    VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                        Text("Predicted Winner")
                            .font(AppTypography.dynamicFont(style: .caption))
                            .foregroundColor(Color(UIColor.secondaryLabel))

                        Text(winnerName)
                            .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                            .foregroundColor(Color(UIColor.label))
                    }

                    Spacer()

                    Image("league")
                        .resizable()
                        .scaledToFit()
                        .frame(width: AppLayout.iconSizeM, height: AppLayout.iconSizeM)
                }
            }

            // Advice section
            if let advice = prediction.predictions.advice {
                Divider()
                    .background(AppColors.separator)

                VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                    Text("AI Prediction")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(Color(UIColor.secondaryLabel))

                    Text(advice)
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(Color(UIColor.label))
                        .fixedSize(horizontal: false, vertical: true)
                }
            }
        }
        .padding(AppLayout.spacingM)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
    }
}

// MARK: - Team Comparison Card
struct TeamComparisonCard: View {
    let fixture: Fixture
    let prediction: Prediction

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingM) {
            // Card Title
            Text("Team Comparison")
                .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                .foregroundColor(Color(UIColor.label))
                .padding(.horizontal, 0)
                .padding(.bottom, AppLayout.spacingS)
            // Content starts immediately after the header



            // Comparison rows
            VStack(spacing: AppLayout.spacingM) {
                // Current Form
                if let form = prediction.comparison.form {
                    ComparisonRow(
                        title: "Current Form",
                        homeValue: form.home,
                        awayValue: form.away
                    )
                }

                // Attack
                if let att = prediction.comparison.att {
                    ComparisonRow(
                        title: "Attacking",
                        homeValue: att.home,
                        awayValue: att.away
                    )
                }

                // Defense
                if let def = prediction.comparison.def {
                    ComparisonRow(
                        title: "Defensive",
                        homeValue: def.home,
                        awayValue: def.away
                    )
                }

                // Goals
                if let goals = prediction.comparison.goals {
                    ComparisonRow(
                        title: "Goals",
                        homeValue: goals.home,
                        awayValue: goals.away
                    )
                }

                // Poisson Distribution
                if let poisson = prediction.comparison.poissonDistribution {
                    ComparisonRow(
                        title: "Poisson Distribution",
                        homeValue: poisson.home,
                        awayValue: poisson.away
                    )
                }

                // Head to Head
                if let h2h = prediction.comparison.h2h {
                    ComparisonRow(
                        title: "Head to Head",
                        homeValue: h2h.home,
                        awayValue: h2h.away
                    )
                }

                // Total
                if let total = prediction.comparison.total {
                    ComparisonRow(
                        title: "Total",
                        homeValue: total.home,
                        awayValue: total.away
                    )
                }
            }
        }
        .padding(AppLayout.spacingM)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
    }
}

// MARK: - Additional Stats Card
struct AdditionalStatsCard: View {
    let prediction: Prediction

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingM) {
            // Card Title
            Text("Form & Statistics")
                .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                .foregroundColor(Color(UIColor.label))
                .padding(.horizontal, 0)
                .padding(.bottom, AppLayout.spacingS)
            // Content starts immediately after the header

            // Stats grid
            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: AppLayout.spacingM) {
                // Under/Over prediction
                if let underOver = prediction.predictions.underOver {
                    StatItem(
                        title: "Under/Over",
                        value: underOver,
                        icon: "arrow.up.and.down"
                    )
                }

                // Win or Draw prediction
                if prediction.predictions.winOrDraw == true {
                    StatItem(
                        title: "Win or Draw",
                        value: "Yes",
                        icon: "checkmark.circle.fill",
                        valueColor: AppColors.Sports.win
                    )
                }

                // Home Goals prediction
                if let homeGoals = prediction.predictions.goals.home {
                    StatItem(
                        title: "Home Goals",
                        value: homeGoals,
                        icon: "soccerball",
                        valueColor: AppColors.Sports.win
                    )
                }

                // Away Goals prediction
                if let awayGoals = prediction.predictions.goals.away {
                    StatItem(
                        title: "Away Goals",
                        value: awayGoals,
                        icon: "soccerball",
                        valueColor: AppColors.Sports.draw
                    )
                }
            }
        }
        .padding(AppLayout.spacingM)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
    }
}

// MARK: - Helper Components

// Probability Bar Component
struct ProbabilityBar: View {
    let homeTeam: String
    let awayTeam: String
    let homePercent: String
    let drawPercent: String
    let awayPercent: String

    @State private var animatedHomeWidth: CGFloat = 0
    @State private var animatedDrawWidth: CGFloat = 0
    @State private var animatedAwayWidth: CGFloat = 0

    private var homeValue: Double {
        Double(homePercent.replacingOccurrences(of: "%", with: "")) ?? 0
    }

    private var drawValue: Double {
        Double(drawPercent.replacingOccurrences(of: "%", with: "")) ?? 0
    }

    private var awayValue: Double {
        Double(awayPercent.replacingOccurrences(of: "%", with: "")) ?? 0
    }

    private var totalValue: Double {
        homeValue + drawValue + awayValue
    }

    var body: some View {
        VStack(spacing: AppLayout.spacingS) {
            // Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background
                    RoundedRectangle(cornerRadius: 3)
                        .fill(Color(UIColor.tertiarySystemFill))
                        .frame(height: 6)

                    // Home section (blue)
                    RoundedRectangle(cornerRadius: 3)
                        .fill(Color(red: 0.2, green: 0.6, blue: 0.86)) // #3498DB - Blue for home team
                        .frame(width: animatedHomeWidth, height: 6)
                        .cornerRadius(3, corners: [.topLeft, .bottomLeft])

                    // Draw section (improved grey)
                    if drawValue > 0 {
                        Rectangle()
                            .fill(Color(red: 0.6, green: 0.6, blue: 0.6)) // Better grey color
                            .frame(width: animatedDrawWidth, height: 6)
                            .offset(x: animatedHomeWidth)
                    }

                    // Away section (red)
                    if awayValue > 0 {
                        RoundedRectangle(cornerRadius: 3)
                            .fill(Color(red: 0.91, green: 0.3, blue: 0.24)) // #E74C3C - Red for away team
                            .frame(width: animatedAwayWidth, height: 6)
                            .cornerRadius(3, corners: [.topRight, .bottomRight])
                            .offset(x: animatedHomeWidth + animatedDrawWidth)
                    }
                }
                .onAppear {
                    let totalWidth = geometry.size.width

                    withAnimation(.easeOut(duration: 0.8)) {
                        if totalValue > 0 {
                            animatedHomeWidth = totalWidth * (homeValue / totalValue)
                            animatedDrawWidth = totalWidth * (drawValue / totalValue)
                            animatedAwayWidth = totalWidth * (awayValue / totalValue)
                        } else {
                            // Default equal distribution if no values
                            animatedHomeWidth = totalWidth / 3
                            animatedDrawWidth = totalWidth / 3
                            animatedAwayWidth = totalWidth / 3
                        }
                    }
                }
            }
            .frame(height: 6)

            // Labels
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(homePercent)
                        .font(AppTypography.dynamicFont(style: .footnote, weight: .bold))
                        .foregroundColor(Color(UIColor.label))
                }

                Spacer()

                VStack(alignment: .center, spacing: 2) {
                    Text(drawPercent)
                        .font(AppTypography.dynamicFont(style: .footnote, weight: .bold))
                        .foregroundColor(Color(UIColor.label))
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    Text(awayPercent)
                        .font(AppTypography.dynamicFont(style: .footnote, weight: .bold))
                        .foregroundColor(Color(UIColor.label))
                }
            }

            // Team names
            HStack {
                Text(homeTeam)
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color(UIColor.label))
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Text("Draw")
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color(UIColor.secondaryLabel))
                    .frame(maxWidth: .infinity, alignment: .center)

                Text(awayTeam)
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color(UIColor.label))
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
                    .frame(maxWidth: .infinity, alignment: .trailing)
            }
        }
    }
}

// Comparison Row Component
struct ComparisonRow: View {
    let title: String
    let homeValue: String?
    let awayValue: String?

    @State private var animatedHomeWidth: CGFloat = 0
    @State private var animatedAwayWidth: CGFloat = 0

    private var homePercent: Double {
        if let homeStr = homeValue?.replacingOccurrences(of: "%", with: ""),
           let homeVal = Double(homeStr) {
            return homeVal
        }
        return 50
    }

    private var awayPercent: Double {
        if let awayStr = awayValue?.replacingOccurrences(of: "%", with: ""),
           let awayVal = Double(awayStr) {
            return awayVal
        }
        return 50
    }

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
            // Title
            Text(title)
                .font(AppTypography.dynamicFont(style: .caption))
                .foregroundColor(AppColors.secondaryText)
                .frame(maxWidth: .infinity, alignment: .center)

            // Values
            HStack {
                Text(homeValue ?? "N/A")
                    .font(AppTypography.dynamicFont(style: .footnote, weight: .bold))
                    .foregroundColor(Color(UIColor.label))

                Spacer()

                Text(awayValue ?? "N/A")
                    .font(AppTypography.dynamicFont(style: .footnote, weight: .bold))
                    .foregroundColor(Color(UIColor.label))
            }

            // Comparison bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background
                    Rectangle()
                        .fill(Color(UIColor.tertiarySystemFill))
                        .frame(height: 6) // Increased height to match win probability bar
                        .cornerRadius(2)

                    // Home team bar (blue)
                    Rectangle()
                        .fill(Color(red: 0.2, green: 0.6, blue: 0.86)) // #3498DB - Blue for home team
                        .frame(width: animatedHomeWidth, height: 6) // Increased height to match win probability bar
                        .cornerRadius(2, corners: [.topLeft, .bottomLeft])

                    // Away team bar (red)
                    Rectangle()
                        .fill(Color(red: 0.91, green: 0.3, blue: 0.24)) // #E74C3C - Red for away team
                        .frame(width: animatedAwayWidth, height: 6) // Increased height to match win probability bar
                        .cornerRadius(2, corners: [.topRight, .bottomRight])
                        .position(x: geometry.size.width - animatedAwayWidth/2, y: 3) // Updated y position to match new height
                }
                .onAppear {
                    let totalWidth = geometry.size.width
                    let totalPercent = homePercent + awayPercent

                    withAnimation(.easeOut(duration: 0.8)) {
                        if totalPercent > 0 {
                            animatedHomeWidth = totalWidth * (homePercent / totalPercent)
                            animatedAwayWidth = totalWidth * (awayPercent / totalPercent)
                        } else {
                            animatedHomeWidth = totalWidth / 2
                            animatedAwayWidth = totalWidth / 2
                        }
                    }
                }
            }
            .frame(height: 6) // Increased height to match win probability bar
        }
    }
}

// Stat Item Component
struct StatItem: View {
    let title: String
    let value: String
    let icon: String
    var valueColor: Color = Color(UIColor.label)

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
            // Title
            Text(title)
                .font(AppTypography.dynamicFont(style: .caption))
                .foregroundColor(AppColors.secondaryText)

            // Value with icon
            HStack(spacing: AppLayout.spacingXS) {
                Image(systemName: icon)
                    .font(.system(size: AppLayout.iconSizeS))
                    .foregroundColor(valueColor)

                Text(value)
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                    .foregroundColor(valueColor)
            }
        }
    }
}

// LoadingView has been replaced by SimpleSkeletonListView

// Error View
struct ErrorView: View {
    let message: String

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: AppLayout.iconSizeL))
                .foregroundColor(Color.red)

            Text("Unable to load predictions")
                .font(AppTypography.dynamicFont(style: .headline))
                .foregroundColor(Color(UIColor.label))

            Text(message)
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(Color(UIColor.secondaryLabel))
                .multilineTextAlignment(.center)

            Button(action: {}) {
                Text("Try Again")
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                    .foregroundColor(AppColors.buttonText)
                    .padding(.horizontal, AppLayout.spacingL)
                    .padding(.vertical, AppLayout.spacingS)
                    .background(AppColors.Brand.primary)
                    .cornerRadius(AppLayout.cornerRadiusM)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .padding(AppLayout.spacingL)
    }
}

// Empty View
struct EmptyView: View {
    let message: String

    var body: some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "chart.bar.xaxis")
                .font(.system(size: AppLayout.iconSizeL))
                .foregroundColor(AppColors.secondaryText)

            Text("No Predictions Available")
                .font(AppTypography.dynamicFont(style: .headline))
                .foregroundColor(Color(UIColor.label))

            Text(message)
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(Color(UIColor.secondaryLabel))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .padding(AppLayout.spacingL)
    }
}

// MARK: - Preview
#Preview {
    PredictionsView(fixture: Fixture.mock)
}
