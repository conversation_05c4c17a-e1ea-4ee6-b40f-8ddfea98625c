import SwiftUI
import <PERSON><PERSON><PERSON>

/// A view that displays a league with its fixtures
struct LeagueFixtureItemView: View {
    // MARK: - Properties
    let leagueWithFixtures: LeagueWithFixtures
    @Binding var expandedLeagues: Set<Int>
    let viewModel: LeagueFixturesViewModel
    let toggleLeagueExpansion: (Int) -> Void
    let handlePaginationExpandedState: () -> Void
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // League Header with toggle button
            leagueHeaderView
            
            // Fixtures for this league - only show if expanded
            if expandedLeagues.contains(leagueWithFixtures.league.id) {
                fixturesListView
            }
        }
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        .padding(.bottom, 0) // No additional bottom padding as the LazyVStack spacing handles it
        .edgesIgnoringSafeArea(.horizontal)
    }
    
    // MARK: - League Header View
    private var leagueHeaderView: some View {
        HStack {
            // League info section
            leagueInfoView
            
            // Divider
            Divider()
                .frame(height: 24)
                .padding(.horizontal, AppLayout.spacingXS)
            
            // Toggle button with fixture count
            toggleButton
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.vertical, AppLayout.spacingS)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(AppColors.secondaryBackground)
        .accessibilityInfo(
            label: "\(leagueWithFixtures.league.name ?? "Unknown") league from \(leagueWithFixtures.league.country ?? "Unknown")",
            hint: "Tap left side to view league details, right side to toggle fixtures"
        )
        .onAppear {
            checkForPagination()
        }
    }
    
    // MARK: - League Info View
    private var leagueInfoView: some View {
        HStack {
            // League logo
            KFImage(URL(string: leagueWithFixtures.league.logo ?? ""))
                .resizable()
                .scaledToFit()
                .frame(width: AppLayout.iconSizeM, height: AppLayout.iconSizeM)
                .padding(.trailing, AppLayout.spacingXS)
            
            // League name and country
            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                // League name
                Text(leagueWithFixtures.league.name ?? "Unknown")
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                    .foregroundColor(Color(UIColor.label))
                
                // Country row
                HStack(spacing: AppLayout.spacingXS) {
                    countryFlagView
                    
                    Text(leagueWithFixtures.league.country ?? "Unknown")
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                }
            }
            
            Spacer()
        }
    }
    
    // MARK: - Country Flag View
    private var countryFlagView: some View {
        Group {
            if let flagURL = leagueWithFixtures.league.flag, !flagURL.isEmpty {
                KFImage(URL(string: flagURL))
                    .placeholder {
                        Image(systemName: "globe")
                            .font(.system(size: AppLayout.iconSizeS))
                            .foregroundColor(Color(UIColor.secondaryLabel))
                    }
                    .fade(duration: 0.25)
                    .retry(maxCount: 3, interval: .seconds(2))
                    .resizable()
                    .scaledToFit()
                    .frame(width: AppLayout.iconSizeS, height: AppLayout.iconSizeS)
            } else {
                Image(systemName: "globe")
                    .font(.system(size: AppLayout.iconSizeS))
                    .foregroundColor(Color(UIColor.secondaryLabel))
                    .frame(width: AppLayout.iconSizeS, height: AppLayout.iconSizeS)
            }
        }
    }
    
    // MARK: - Toggle Button
    private var toggleButton: some View {
        Button(action: {
            toggleLeagueExpansion(leagueWithFixtures.league.id)
        }) {
            HStack(spacing: AppLayout.spacingXS) {
                // Fixture count - only show when collapsed
                if !expandedLeagues.contains(leagueWithFixtures.league.id) {
                    Text("\(leagueWithFixtures.fixtures.count)")
                        .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                }
                
                // Toggle icon
                Image(systemName: expandedLeagues.contains(leagueWithFixtures.league.id) ? "chevron.up" : "chevron.down")
                    .font(.system(size: 14))
                    .foregroundColor(Color(UIColor.secondaryLabel))
            }
            .padding(.horizontal, AppLayout.spacingS)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                    .fill(Color(UIColor.tertiarySystemFill))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityInfo(
            label: expandedLeagues.contains(leagueWithFixtures.league.id) ? "Collapse fixtures" : "\(leagueWithFixtures.fixtures.count) fixtures",
            hint: expandedLeagues.contains(leagueWithFixtures.league.id) ? "Tap to hide fixtures" : "Tap to show fixtures"
        )
    }
    
    // MARK: - Fixtures List View
    private var fixturesListView: some View {
        ForEach(leagueWithFixtures.fixtures) { fixture in
            VStack {
                FixtureRowView(fixture: fixture)
                    .padding(.vertical, AppLayout.spacingS)
                    .onAppear {
                        checkForLastFixturePagination(fixture: fixture)
                    }
                
                // Add a separator if not the last item
                if fixture.id != leagueWithFixtures.fixtures.last?.id {
                    Divider()
                        .background(AppColors.separator)
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    private func checkForPagination() {
        // Check if this is the last league in the current page
        let isLastLeague = viewModel.leaguesWithFixtures.last?.id == leagueWithFixtures.id
        
        if isLastLeague {
            // Trigger loading more leagues even if the league is collapsed
            print("Last league header appeared - triggering pagination")
            viewModel.loadMoreLeagueFixturesIfNeeded()
            
            // Add a delay to ensure the new leagues are loaded before we try to expand them
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                // Check if we have more leagues now than before
                let loadingComplete = !viewModel.isLoadingMore
                let hasLeagues = viewModel.leaguesWithFixtures.count > 0
                
                if loadingComplete && hasLeagues {
                    print("Pagination triggered from league header - checking for updates")
                    handlePaginationExpandedState()
                }
            }
        }
    }
    
    private func checkForLastFixturePagination(fixture: Fixture) {
        // Check if this is one of the last fixtures in the last league
        let isLastFixture = fixture.id == leagueWithFixtures.fixtures.last?.id
        let isLastLeague = leagueWithFixtures.id == viewModel.leaguesWithFixtures.last?.id
        
        if isLastFixture && isLastLeague {
            // Trigger loading more leagues
            print("Last fixture in last league appeared - triggering pagination")
            viewModel.loadMoreLeagueFixturesIfNeeded()
            
            // Add a delay to ensure the new leagues are loaded before we try to expand them
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                // Check if we have more leagues now than before
                let loadingComplete = !viewModel.isLoadingMore
                let hasLeagues = viewModel.leaguesWithFixtures.count > 0
                
                if loadingComplete && hasLeagues {
                    print("Last fixture appeared - checking for pagination updates")
                    handlePaginationExpandedState()
                }
            }
        }
    }
}
