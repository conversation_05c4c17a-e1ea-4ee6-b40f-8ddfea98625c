import SwiftUI
import Kingfisher
import Foundation
import UIKit

// Enum to represent the selected tab
enum SelectedTab: String, CaseIterable, Identifiable {
    case overview = "Overview"
    case lineups = "Lineups"
    case stats = "Stats"
    case predictions = "Predictions"
    case odds = "Odds"
    case standings = "Table"
    case h2h = "H2H"
    var id: String { self.rawValue }
}

struct FixtureDetailView: View {
    @StateObject private var viewModel: FixtureDetailViewModel
    @State private var selectedTab: SelectedTab = .overview
    @EnvironmentObject private var authViewModel: AuthViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var refreshTimer: Timer?
    @State private var refreshTrigger: Int = 0

    let navigationSource: String?

    init(fixture: Fixture, navigationSource: String? = nil) {
        _viewModel = StateObject(wrappedValue: FixtureDetailViewModel(fixture: fixture))
        self.navigationSource = navigationSource
    }

    // Determine if the match is considered 'started' (not NS or TBD)
    private var isMatchStarted: Bool {
        let status = viewModel.fixture.status.short?.uppercased()
        // Add any other pre-game statuses here if needed
        return status != "NS" && status != "TBD"
    }

    // Determine if the match is finished
    private var isMatchFinished: Bool {
        let status = viewModel.fixture.status.short?.uppercased()
        return ["FT", "AET", "PEN", "AWD", "WO", "ABD", "CANC"].contains(status ?? "")
    }

    // Determine which tabs are available based on match status
    private var availableTabs: [SelectedTab] {
        var tabs: [SelectedTab] = [.overview, .lineups]

        // Stats are only available for matches that have started
        if isMatchStarted {
            tabs.append(.stats)
        }

        // Always show Predictions tab for all matches
        tabs.append(.predictions)

        // Only show Odds tab for matches that haven't finished
        if !isMatchFinished {
            tabs.append(.odds)
        }

        // Always add the Standings tab
        // We'll handle the case where standings aren't available in the StandingsView
        tabs.append(.standings)

        // Always add the H2H tab
        // We'll handle the case where H2H data isn't available in the HeadToHeadView
        tabs.append(.h2h)

        return tabs
    }

    // Check if the fixture is live
    private var isLive: Bool {
        let liveCodes = ["LIVE", "1H", "HT", "2H", "ET", "P", "Aw. ET"]
        return liveCodes.contains(viewModel.fixture.status.short ?? "")
    }

    // Start UI refresh timer for live fixtures
    private func startUIRefreshTimer() {
        stopUIRefreshTimer()

        if isLive {
            refreshTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
                DispatchQueue.main.async {
                    // Only update UI elements, don't trigger data refreshes frequently
                    // to avoid conflicts with socket updates
                    self.refreshTrigger += 1

                    // Only trigger data refresh as fallback if socket is disconnected
                    // and only every 60 seconds to minimize conflicts
                    if self.refreshTrigger % 30 == 0 && !SocketManager.shared.isConnected { // Every 60 seconds (30 * 2s)
                        Logger.info("Socket disconnected - using timer as fallback for fixture detail refresh", category: .network)
                        self.viewModel.refreshFixtureData()
                    }
                }
            }
        }
    }

    // Stop UI refresh timer
    private func stopUIRefreshTimer() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }

    // We no longer need the custom swipe handling since PageTabViewStyle handles it

    var body: some View {
        ZStack {
            // Background color that matches the header and tabs
            AppColors.tertiaryBackground.ignoresSafeArea()

            // Remove spacing between elements for a seamless look
            VStack(alignment: .leading, spacing: 0) {
                // Custom navigation header
                customNavigationHeader

                FixtureHeaderView(fixture: viewModel.fixture, refreshTrigger: refreshTrigger)
                    .background(AppColors.tertiaryBackground)
                    .edgesIgnoringSafeArea(.horizontal)

                // Custom Tab Selector
                TabSelector(selectedTab: $selectedTab, availableTabs: availableTabs)
                    .background(AppColors.tertiaryBackground)
                    .edgesIgnoringSafeArea(.horizontal)
                    .accessibilityInfo(
                        label: "Tab selector",
                        hint: "Select a tab to view different information about the match"
                    )

                // Page-style tab view that supports peeking at next/previous tabs
                TabView(selection: $selectedTab) {
                    // Only include tabs that are in availableTabs to fix swiping issue
                    ForEach(availableTabs, id: \.self) { tab in
                        Group {
                            switch tab {
                            case .overview:
                                OverviewView(fixture: viewModel.fixture, selectedTab: $selectedTab)
                                    .environmentObject(authViewModel)
                                    .refreshable {
                                        viewModel.refreshFixtureData()
                                    }
                            case .lineups:
                                LineupsView(lineups: viewModel.fixture.lineups, playerStatistics: viewModel.fixture.playerStatistics, fixture: viewModel.fixture)
                                    .refreshable {
                                        viewModel.refreshFixtureData()
                                    }
                            case .stats:
                                StatsView(fixture: viewModel.fixture)
                                    .refreshable {
                                        viewModel.refreshFixtureData()
                                    }
                            case .predictions:
                                PredictionsView(fixture: viewModel.fixture)
                                    .refreshable {
                                        viewModel.refreshFixtureData()
                                    }
                            case .odds:
                                OddsView(fixture: viewModel.fixture)
                                    .refreshable {
                                        viewModel.refreshFixtureData()
                                    }
                            case .standings:
                                StandingsView(fixture: viewModel.fixture)
                                    .refreshable {
                                        viewModel.refreshFixtureData()
                                    }
                            case .h2h:
                                HeadToHeadView(fixture: viewModel.fixture)
                                    .refreshable {
                                        viewModel.refreshFixtureData()
                                    }
                            }
                        }
                        .tag(tab)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .background(AppColors.background)
                .frame(maxHeight: .infinity)
                .onChange(of: selectedTab) { newValue in
                    // Add haptic feedback when changing tabs
                    let generator = UIImpactFeedbackGenerator(style: .light)
                    generator.impactOccurred()

                    // Ensure the tab is in the available tabs (important for conditional tabs like Stats)
                    if !availableTabs.contains(newValue) {
                        // If the user somehow swipes to a tab that's not available,
                        // reset to the first available tab
                        selectedTab = availableTabs.first ?? .overview
                    }
                }
            }
            .edgesIgnoringSafeArea(.horizontal) // Edge-to-edge design as preferred by the user

            // Show skeleton loading for initial load, not during pull-to-refresh
            if viewModel.isLoading && !viewModel.isPullToRefresh {
                SimpleSkeletonListView(itemCount: 8, showHeader: true)
                    .transition(.opacity)
                    .animation(.easeInOut(duration: 0.3), value: viewModel.isLoading)
            }
        }
        .navigationTitle("")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
        .onAppear {
            // Refresh fixture data when the view appears
            viewModel.refreshFixtureData()

            // Check notification status
            viewModel.checkNotificationStatus()

            // Start UI refresh timer for live fixtures
            if isLive {
                startUIRefreshTimer()

                // Ensure socket connection is active
                if !SocketManager.shared.isConnected {
                    SocketManager.shared.connect()
                }

                // Subscribe to live fixtures
                SocketManager.shared.subscribeToLiveFixtures()
            }
        }
        .onDisappear {
            // Clean up timer when view disappears
            stopUIRefreshTimer()
        }
        // Notification options popup
        .sheet(isPresented: $viewModel.showNotificationOptions) {
            NotificationOptionsView(
                isPresented: $viewModel.showNotificationOptions,
                onSubscribe: { preferences in
                    viewModel.subscribeToNotifications(preferences: preferences)
                }
            )
            .presentationDetents([.medium])
        }
        // Add swipe-to-go-back functionality
        .swipeToGoBack()
    }

    // MARK: - Custom Navigation Header
    private var customNavigationHeader: some View {
        HStack {
            // Back Button
            Button(action: {
                dismiss()
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                    Text(navigationSource ?? "Matches")
                        .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                }
                .foregroundColor(AppColors.Brand.primary)
            }
            .accessibilityLabel("Go back to \(navigationSource ?? "previous screen")")

            Spacer()

            // Only show bell icon for live or upcoming fixtures
            if viewModel.canShowNotifications {
                Button {
                    viewModel.toggleNotificationOptions()
                } label: {
                    Image(systemName: viewModel.notificationStatus.isSubscribed ? "bell.fill" : "bell")
                        .font(.system(size: 18))
                        .foregroundColor(viewModel.notificationStatus.isSubscribed ? AppColors.Brand.primary : AppColors.text)
                        .accessibilityLabel(viewModel.notificationStatus.isSubscribed ? "Notifications enabled" : "Enable notifications")
                        // Add animation for color change
                        .animation(.easeInOut(duration: 0.3), value: viewModel.notificationStatus.isSubscribed)
                }
                // Add haptic feedback when tapped
                .simultaneousGesture(TapGesture().onEnded {
                    let generator = UIImpactFeedbackGenerator(style: .medium)
                    generator.impactOccurred()
                })
            }
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.top, AppLayout.spacingS)
        .padding(.bottom, AppLayout.spacingXS)
        .background(AppColors.tertiaryBackground)
        .edgesIgnoringSafeArea(.horizontal)
    }
}

// MARK: - Tab Selector
struct TabSelector: View {
    @Binding var selectedTab: SelectedTab
    let availableTabs: [SelectedTab]

    var body: some View {
        ScrollViewReader { proxy in
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: AppLayout.spacingL) {
                    ForEach(availableTabs) { tab in
                        Button {
                            withAnimation {
                                selectedTab = tab
                            }
                        } label: {
                            VStack(spacing: 8) {
                                Text(tab.rawValue)
                                    .font(AppTypography.dynamicFont(style: .callout, weight: .bold))
                                    .foregroundColor(selectedTab == tab ? Color(UIColor.label) : Color(UIColor.secondaryLabel))

                                // Underline for selected tab
                                Rectangle()
                                    .frame(height: 2)
                                    .foregroundColor(selectedTab == tab ? AppColors.Brand.primary : Color.clear)
                            }
                        }
                        .id(tab) // Use the tab itself as the ID for scrolling
                        .accessibilityInfo(
                            label: tab.rawValue,
                            hint: selectedTab == tab ? "Selected tab" : "Tap to select this tab"
                        )
                    }
                }
                .padding(.horizontal, AppLayout.spacingM)
                // Removed vertical padding for sleeker design
            }
            .onChange(of: selectedTab) { newTab in
                // Scroll to the selected tab with animation when it changes
                withAnimation {
                    proxy.scrollTo(newTab, anchor: .center)
                }
            }
            .onAppear {
                if !availableTabs.contains(selectedTab) {
                    selectedTab = availableTabs.first ?? .overview
                } else {
                    // Ensure the initially selected tab is visible
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation {
                            proxy.scrollTo(selectedTab, anchor: .center)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Header View
private struct FixtureHeaderView: View {
    let fixture: Fixture
    let refreshTrigger: Int

    var body: some View {
        VStack(spacing: 0) {

            // Teams and Score
            VStack(spacing: AppLayout.spacingS) {
                // Team logos row
                HStack(alignment: .center, spacing: AppLayout.spacingS) {
                    // Home Team Logo
                    VStack(alignment: .center, spacing: AppLayout.spacingS) {
                        KFImage(URL(string: fixture.teams.home?.logo ?? ""))
                            .placeholder {
                                Image("goal")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: AppLayout.iconSizeS, height: AppLayout.iconSizeS)
                                    .foregroundColor(.white)
                            }
                            .resizable()
                            .scaledToFit()
                            .frame(width: 40, height: 40)
                    }
                    .frame(maxWidth: .infinity)
                    .accessibilityInfo(
                        label: fixture.teams.home?.name ?? "Home team",
                        hint: "Home team logo"
                    )

                    // Score in center
                    VStack(spacing: AppLayout.spacingS) {
                        scoreView(refreshTrigger: refreshTrigger)
                    }
                    .frame(minWidth: 70)
                    .accessibilityInfo(
                        label: getScoreAccessibilityLabel(),
                        hint: "Match score"
                    )

                    // Away Team Logo
                    VStack(alignment: .center, spacing: AppLayout.spacingS) {
                        KFImage(URL(string: fixture.teams.away?.logo ?? ""))
                            .placeholder {
                                Image("goal")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: AppLayout.iconSizeS, height: AppLayout.iconSizeS)
                                    .foregroundColor(.white)
                            }
                            .resizable()
                            .scaledToFit()
                            .frame(width: 40, height: 40)
                    }
                    .frame(maxWidth: .infinity)
                    .accessibilityInfo(
                        label: fixture.teams.away?.name ?? "Away team",
                        hint: "Away team logo"
                    )
                }

                // Team names and time/status row
                HStack(alignment: .center, spacing: AppLayout.spacingS) {
                    // Home Team Name
                    Text(fixture.teams.home?.name ?? "N/A")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(AppColors.text)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                        .minimumScaleFactor(0.7)
                        .frame(maxWidth: .infinity)

                    // Time/Status in center
                    timeStatusView(refreshTrigger: refreshTrigger)
                        .frame(minWidth: 70)
                        .accessibilityInfo(
                            label: getStatusAccessibilityLabel(),
                            hint: getStatusAccessibilityHint()
                        )

                    // Away Team Name
                    Text(fixture.teams.away?.name ?? "N/A")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(AppColors.text)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                        .minimumScaleFactor(0.7)
                        .frame(maxWidth: .infinity)
                }
            }
            .padding(.horizontal, AppLayout.spacingL)
            .padding(.vertical, AppLayout.spacingM)
        }
        // Removed vertical padding for true edge-to-edge design
    }



    // Score display only
    @ViewBuilder
    private func scoreView(refreshTrigger: Int) -> some View {
        let statusShort = fixture.status.short ?? ""

        switch statusShort {
        case "NS": // Not started - show kickoff time
            Text(fixture.kickoffDate, style: .time)
                .font(AppTypography.dynamicFont(style: .title2, weight: .semibold))
                .foregroundColor(AppColors.text)
        case "FT", "AET": // Finished (regular time or extra time)
            Text("\(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)")
                .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                .foregroundColor(AppColors.text)
        case "PEN": // Finished with penalty shootout
            Text("\(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)")
                .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                .foregroundColor(AppColors.text)
        case "1H", "HT", "2H", "ET", "P", "LIVE", "BREAK", "Aw. ET": // Live matches
            Text("\(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)")
                .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                .foregroundColor(AppColors.text)
                .id("score-detail-\(fixture.id)-\(fixture.goals.home ?? 0)-\(fixture.goals.away ?? 0)-\(refreshTrigger)")
        default: // Other statuses with scores
            Text("\(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)")
                .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                .foregroundColor(AppColors.text)
        }
    }

    // Time/Status display only
    @ViewBuilder
    private func timeStatusView(refreshTrigger: Int) -> some View {
        let statusShort = fixture.status.short ?? ""
        let kickoff = fixture.kickoffDate

        switch statusShort {
        case "NS": // Not started - show countdown
            CountdownView(targetDate: kickoff)
                .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
        case "FT": // Full time
            Text("Full Time")
                .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
        case "AET": // After extra time
            Text("After Extra Time")
                .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
        case "PEN": // Penalties
            if let penaltyScore = fixture.score.penalty {
                Text("Penalties \(penaltyScore.home ?? 0) - \(penaltyScore.away ?? 0)")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            } else {
                Text("Penalties")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            }
        case "HT": // Half time
            Text("Half Time")
                .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
        case "1H", "2H", "ET", "P", "LIVE", "BREAK", "Aw. ET": // Live matches
            if let elapsed = fixture.status.elapsed {
                let stoppageInfo = getStoppageTimeInfo(for: elapsed)
                Text(stoppageInfo.extraMinutes > 0 ? "\(stoppageInfo.baseTime)+\(stoppageInfo.extraMinutes)'" : "\(elapsed)'")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
                    .id("timer-detail-\(fixture.id)-\(elapsed)-\(fixture.status.extra ?? 0)-\(refreshTrigger)")
            } else {
                Text(statusShort)
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            }
        case "PST":
            statusBadge(text: "Postponed", color: AppColors.warning)
        case "CANC":
            statusBadge(text: "Cancelled", color: AppColors.error)
        case "SUSP":
            statusBadge(text: "Suspended", color: AppColors.warning)
        case "INT":
            statusBadge(text: "Interrupted", color: AppColors.warning)
        case "ABD":
            statusBadge(text: "Abandoned", color: AppColors.error)
        case "TBD":
            statusBadge(text: "TBD", color: AppColors.info)
        default:
            Text(statusShort)
                .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
        }
    }

    // Determines the text to display based on match status and time
    @ViewBuilder
    private func statusView(refreshTrigger: Int) -> some View {
        let statusShort = fixture.status.short ?? ""
        let kickoff = fixture.kickoffDate
        let calendar = Calendar.current

        switch statusShort {
        case "NS": // Not Started
            if calendar.isDateInToday(kickoff) {
                Text("Today")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)

                Text(kickoff, style: .time)
                    .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                    .foregroundColor(AppColors.text)
            } else if calendar.isDateInTomorrow(kickoff) {
                Text("Tomorrow")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)

                Text(kickoff, style: .time)
                    .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                    .foregroundColor(AppColors.text)
            } else {
                Text(kickoff, format: .dateTime.weekday(.abbreviated).day().month(.abbreviated))
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)

                Text(kickoff, style: .time)
                    .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                    .foregroundColor(AppColors.text)
            }
        case "FT", "AET": // Finished (regular time or extra time)
            Text("\(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)")
                .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                .foregroundColor(AppColors.text)

            Text(statusShort == "FT" ? "Full time" : "After extra time")
                .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                .foregroundColor(AppColors.secondaryText)

        case "PEN": // Finished with penalty shootout
            // Display regular time/extra time score
            Text("\(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)")
                .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                .foregroundColor(AppColors.text)

            // Display penalty shootout text with score
            if let penaltyScore = fixture.score.penalty {
                Text("Penalties \(penaltyScore.home ?? 0) - \(penaltyScore.away ?? 0)")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            } else {
                Text("Penalties")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            }
        case "1H", "HT", "2H", "ET", "P", "LIVE", "BREAK", "Aw. ET": // Live, Halftime, Break, Awaiting Extra Time etc.
            Text("\(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)")
                .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                .foregroundColor(AppColors.text)
                .id("score-detail-\(fixture.id)-\(fixture.goals.home ?? 0)-\(fixture.goals.away ?? 0)-\(refreshTrigger)")

            // Special handling for halftime
            if statusShort == "HT" {
                Text("Half time")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            }
            // For other live matches, display timer without background
            else if let elapsed = fixture.status.elapsed {
                // Get stoppage time information
                let stoppageInfo = getStoppageTimeInfo(for: elapsed)

                Text(stoppageInfo.extraMinutes > 0 ? "\(stoppageInfo.baseTime)+\(stoppageInfo.extraMinutes)'" : "\(elapsed)'")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
                    .id("timer-detail-\(fixture.id)-\(elapsed)-\(fixture.status.extra ?? 0)-\(refreshTrigger)")
            } else {
                Text(statusShort)
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            }
        case "PST":
            statusBadge(text: "Postponed", color: AppColors.warning)
        case "CANC":
            statusBadge(text: "Cancelled", color: AppColors.error)
        case "SUSP":
            statusBadge(text: "Suspended", color: AppColors.warning)
        case "INT":
            statusBadge(text: "Interrupted", color: AppColors.warning)
        case "ABD":
            statusBadge(text: "Abandoned", color: AppColors.error)
        case "TBD":
            statusBadge(text: "TBD", color: AppColors.info)
        default: // Other unknown statuses
            Text("\(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)")
                .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                .foregroundColor(AppColors.text)

            Text(statusShort)
                .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
        }
    }

    // Helper for status badge styling
    private func statusBadge(text: String, color: Color = AppColors.secondary, isBlinking: Bool = false) -> some View {
        let badge = Text(text)
            .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
            .foregroundColor(.white) // White text is appropriate for colored badges
            .padding(.horizontal, AppLayout.spacingS)
            .padding(.vertical, 4)
            .background(color)
            .cornerRadius(AppLayout.cornerRadiusS)

        // Apply blinking effect if needed
        if isBlinking {
            return AnyView(badge.blinking(blinkRate: 1.5, minOpacity: 0.7))
        } else {
            return AnyView(badge)
        }
    }



    // Helper for score accessibility
    private func getScoreAccessibilityLabel() -> String {
        let statusShort = fixture.status.short ?? ""

        switch statusShort {
        case "NS":
            return "Match not started"
        case "FT", "AET", "PEN":
            return "Final score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)"
        case "1H", "HT", "2H", "ET", "P", "LIVE", "BREAK", "Aw. ET":
            return "Current score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)"
        default:
            return "Score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)"
        }
    }

    // Helper for accessibility
    private func getStatusAccessibilityLabel() -> String {
        let statusShort = fixture.status.short ?? ""

        switch statusShort {
        case "NS":
            return "Match not started"
        case "FT", "AET":
            return "Final score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)"
        case "PEN":
            if let penaltyScore = fixture.score.penalty {
                return "Final score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0), Penalties: \(penaltyScore.home ?? 0) - \(penaltyScore.away ?? 0)"
            } else {
                return "Final score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0), Match decided by penalties"
            }
        case "1H", "HT", "2H", "ET", "P", "LIVE", "BREAK", "Aw. ET":
            return "Current score: \(fixture.goals.home ?? 0) - \(fixture.goals.away ?? 0)"
        default:
            return "Match status: \(fixture.status.long ?? statusShort)"
        }
    }

    // Helper for accessibility hint
    private func getStatusAccessibilityHint() -> String {
        let statusShort = fixture.status.short ?? ""

        switch statusShort {
        case "NS":
            let dateFormatter = DateFormatter()
            dateFormatter.dateStyle = .medium
            dateFormatter.timeStyle = .short
            return "Match starts on \(dateFormatter.string(from: fixture.kickoffDate))"
        case "1H", "2H", "ET", "P", "LIVE":
            if let elapsed = fixture.status.elapsed {
                let stoppageInfo = getStoppageTimeInfo(for: elapsed)
                let timeDisplay = stoppageInfo.extraMinutes > 0 ? "\(stoppageInfo.baseTime)+\(stoppageInfo.extraMinutes)" : "\(elapsed)"
                return "Match in progress, \(timeDisplay) minutes played"
            } else {
                return "Match in progress"
            }
        case "HT":
            return "Half time break"
        case "FT":
            return "Match finished"
        case "AET":
            return "Match finished after extra time"
        case "PEN":
            if let penaltyScore = fixture.score.penalty {
                return "Match finished after penalties with shootout score \(penaltyScore.home ?? 0)-\(penaltyScore.away ?? 0)"
            } else {
                return "Match finished after penalties"
            }
        default:
            return fixture.status.long ?? statusShort
        }
    }



    // Helper to get stoppage time information - simplified to use API data
    private func getStoppageTimeInfo(for currentElapsed: Int) -> (baseTime: Int, extraMinutes: Int) {
        // If the API provides explicit extra time, use it - even if it's 0
        if let statusExtra = fixture.status.extra {
            // Check the match status to determine which half we're in
            let status = fixture.status.short ?? ""
            let baseTime: Int

            // Determine the base time based on match status and elapsed time
            if status == "1H" || status == "HT" {
                // First half or halftime
                baseTime = 45
                return (baseTime: baseTime, extraMinutes: statusExtra)
            } else if status == "2H" {
                // Second half
                baseTime = 90
                return (baseTime: baseTime, extraMinutes: statusExtra)
            } else if status == "ET" && currentElapsed <= 105 {
                // First half of extra time
                baseTime = 105
                return (baseTime: baseTime, extraMinutes: statusExtra)
            } else if status == "ET" {
                // Second half of extra time
                baseTime = 120
                return (baseTime: baseTime, extraMinutes: statusExtra)
            }

            // If we can't determine from status, use elapsed time
            if currentElapsed >= 45 && currentElapsed < 50 {
                // This is likely first half stoppage time
                return (baseTime: 45, extraMinutes: statusExtra)
            } else if currentElapsed >= 90 && currentElapsed < 95 {
                // This is likely second half stoppage time
                return (baseTime: 90, extraMinutes: statusExtra)
            } else if currentElapsed >= 105 && currentElapsed < 110 {
                // This is likely first half of extra time stoppage
                return (baseTime: 105, extraMinutes: statusExtra)
            } else if currentElapsed >= 120 && currentElapsed < 125 {
                // This is likely second half of extra time stoppage
                return (baseTime: 120, extraMinutes: statusExtra)
            } else if currentElapsed <= 45 {
                // Fallback for first half
                return (baseTime: currentElapsed, extraMinutes: 0)
            } else if currentElapsed <= 90 {
                // Fallback for second half
                return (baseTime: currentElapsed, extraMinutes: 0)
            } else if currentElapsed <= 105 {
                // Fallback for first half of extra time
                return (baseTime: currentElapsed, extraMinutes: 0)
            } else {
                // Fallback for second half of extra time
                return (baseTime: currentElapsed, extraMinutes: 0)
            }
        }

        // Special case: Check if this is likely stoppage time based on elapsed time
        // This handles cases where the API doesn't provide extra time but the elapsed time suggests stoppage time
        if currentElapsed > 45 && currentElapsed < 50 {
            // This could be first half stoppage time or early second half
            let status = fixture.status.short ?? ""
            if status == "1H" || status == "HT" {
                // If it's first half or halftime, it's definitely first half stoppage time
                return (baseTime: 45, extraMinutes: currentElapsed - 45)
            } else if status == "2H" && currentElapsed == 46 {
                // If it's second half and minute 46, it's the start of the second half, not stoppage time
                // Show as 46' instead of 45+1'
                return (baseTime: 46, extraMinutes: 0)
            }
        } else if currentElapsed > 90 && currentElapsed < 95 {
            // This could be second half stoppage time or early extra time
            let status = fixture.status.short ?? ""
            if status == "2H" || status == "FT" {
                // If it's second half or full time, it's definitely second half stoppage time
                return (baseTime: 90, extraMinutes: currentElapsed - 90)
            }
        }

        // If no extra time is provided and not in a likely stoppage time range, just return the current elapsed time
        return (baseTime: currentElapsed, extraMinutes: 0)
    }
}

// MARK: - Countdown View Helper
private struct CountdownView: View {
    let targetDate: Date
    @State private var timeRemaining: String = ""
    @State private var timer: Timer?

    var body: some View {
        Text(timeRemaining)
            .onAppear {
                updateCountdown()
                startTimer()
            }
            .onDisappear {
                stopTimer()
            }
    }

    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            updateCountdown()
        }
    }

    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    private func updateCountdown() {
        let now = Date()
        let timeInterval = targetDate.timeIntervalSince(now)
        let calendar = Calendar.current

        if timeInterval <= 0 {
            timeRemaining = "00:00:00"
            stopTimer()
            return
        }

        // If more than 24 hours away, show relative day format
        if timeInterval > 86400 { // 24 hours = 86400 seconds
            if calendar.isDateInTomorrow(targetDate) {
                timeRemaining = "Tomorrow"
            } else {
                let days = Int(timeInterval) / 86400
                if days == 2 {
                    timeRemaining = "2 days"
                } else {
                    timeRemaining = "\(days) days"
                }
            }
        } else {
            // Less than 24 hours - show HH:MM:SS countdown
            let totalHours = Int(timeInterval) / 3600
            let minutes = Int(timeInterval) % 3600 / 60
            let seconds = Int(timeInterval) % 60

            // Format as HH:MM:SS with zero padding
            timeRemaining = String(format: "%02d:%02d:%02d", totalHours, minutes, seconds)
        }
    }
}

// MARK: - Team View Helper
private struct TeamView: View {
    let team: Fixture.TeamInfo?
    let isHome: Bool

    init(team: Fixture.TeamInfo?, isHome: Bool = true) {
        self.team = team
        self.isHome = isHome
    }

    var body: some View {
        teamContent
            .allowsHitTesting(false)
    }

    // Extract the team content to avoid duplication
    private var teamContent: some View {
        VStack(spacing: AppLayout.spacingS) {
            KFImage(URL(string: team?.logo ?? ""))
                .placeholder {
                    Image("goal")
                        .resizable()
                        .scaledToFit()
                        .frame(width: AppLayout.iconSizeS, height: AppLayout.iconSizeS)
                        .foregroundColor(.white)
                }
                .resizable()
                .scaledToFit()
                .frame(width: 40, height: 40)

            Text(team?.name ?? "N/A")
                .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                .foregroundColor(AppColors.text)
                .multilineTextAlignment(.center)
                .lineLimit(1)
                .minimumScaleFactor(0.7)
                .frame(maxWidth: 120)
        }
    }
}

// MARK: - Preview
struct FixtureDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView { // Wrap in NavigationView for preview context
            FixtureDetailView(fixture: Fixture.mock, navigationSource: "Matches") // Use the mock fixture
        }
    }
}
