import SwiftUI

// MARK: - Three Column Odds View
struct ThreeColumnOddsView: View {
    let fixture: Fixture
    let values: [BetValue]

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            ForEach(values) { value in
                OddsButton(
                    label: value.value,
                    odd: value.formattedOdd
                )
            }
        }
    }
}

// MARK: - Live Three Column Odds View
struct LiveThreeColumnOddsView: View {
    let fixture: Fixture
    let values: [LiveBetValue]

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            ForEach(values) { value in
                OddsButton(
                    label: value.value,
                    odd: value.formattedOdd,
                    isSuspended: value.suspended ?? false
                )
            }
        }
    }
}

// MARK: - Preview
#Preview {
    ThreeColumnOddsView(
        fixture: Fixture.mock,
        values: [
            BetValue(value: "Home/Draw", odd: "1.85", handicap: nil),
            BetValue(value: "Home/Away", odd: "1.45", handicap: nil),
            BetValue(value: "Draw/Away", odd: "1.25", handicap: nil)
        ]
    )
    .padding()
    .background(AppColors.background)
}

#Preview("Live") {
    LiveThreeColumnOddsView(
        fixture: Fixture.mockLive,
        values: [
            LiveBetValue(value: "Home/Draw", odd: "1.85", handicap: nil, main: true, suspended: false),
            LiveBetValue(value: "Home/Away", odd: "1.45", handicap: nil, main: true, suspended: false),
            LiveBetValue(value: "Draw/Away", odd: "1.25", handicap: nil, main: true, suspended: false)
        ]
    )
    .padding()
    .background(AppColors.background)
}
