import SwiftUI

struct EventsTimelineView: View {
    let events: [EventInfo]?
    let homeTeamId: Int?
    let awayTeamId: Int?
    let fixture: Fixture // Add fixture parameter to access score information
    var title: String?
    @State private var refreshTrigger: Int = 0
    @State private var timer: Timer?

    // Sort events in descending order (newest first) and filter out redundant red cards and penalty shootout events
    private var sortedEvents: [EventInfo] {
        // First, identify players with second yellow cards
        let playersWithSecondYellows = Set(playerYellowCardEvents.filter { $0.value.count > 1 }.keys)

        // Filter out direct red card events for players who have a second yellow
        // Also filter out penalty shootout events since we have a dedicated penalty section
        let filteredEvents = events?.filter { event ->  Bool in
            // Filter out penalty shootout events
            let isPenaltyShootoutEvent = event.comments?.contains("Penalty Shootout") == true ||
                                       event.detail.contains("Penalty Shootout") ||
                                       (event.type == "penalty" && event.time.elapsed >= 120)

            if isPenaltyShootoutEvent {
                return false
            }
            // Keep all non-card events
            if event.type != "Card" {
                return true
            }

            // Keep all yellow card events
            if event.detail.lowercased().contains("yellow") {
                return true
            }

            // For red card events, check if this player has a second yellow
            if event.detail.lowercased().contains("red") {
                if let playerId = event.player.id, playersWithSecondYellows.contains(playerId) {
                    // This is a direct red card for a player who already has a second yellow
                    // Filter it out to avoid duplication
                    return false
                }
            }

            // Keep all other events
            return true
        }

        // Sort the filtered events
        return filteredEvents?.sorted {
            // First compare by elapsed time
            if $0.time.elapsed != $1.time.elapsed {
                return $0.time.elapsed > $1.time.elapsed
            }

            // If elapsed time is the same, compare by extra time (if available)
            let extra0 = $0.time.extra ?? 0
            let extra1 = $1.time.extra ?? 0
            return extra0 > extra1
        } ?? []
    }

    // Track players with yellow cards to identify second yellows
    // This function returns a dictionary mapping player IDs to arrays of yellow card events
    private var playerYellowCardEvents: [Int: [EventInfo]] {
        var yellowCardEvents: [Int: [EventInfo]] = [:]

        // Process events in chronological order (ascending)
        let chronologicalEvents = events?.sorted {
            if $0.time.elapsed != $1.time.elapsed {
                return $0.time.elapsed < $1.time.elapsed
            }
            // If elapsed time is the same, compare by extra time (if available)
            let extra0 = $0.time.extra ?? 0
            let extra1 = $1.time.extra ?? 0
            return extra0 < extra1
        } ?? []

        for event in chronologicalEvents {
            if event.type == "Card" && event.detail.lowercased().contains("yellow") {
                if let playerId = event.player.id {
                    if yellowCardEvents[playerId] == nil {
                        yellowCardEvents[playerId] = []
                    }
                    yellowCardEvents[playerId]?.append(event)
                }
            }
        }

        return yellowCardEvents
    }

    // Check if an event is a second yellow card for a player
    private func isSecondYellowCard(_ event: EventInfo) -> Bool {
        // Only check yellow card events
        guard event.type == "Card" && event.detail.lowercased().contains("yellow") else {
            return false
        }

        // Need player ID to check
        guard let playerId = event.player.id else {
            return false
        }

        // Get all yellow card events for this player
        guard let playerEvents = playerYellowCardEvents[playerId], playerEvents.count > 1 else {
            return false
        }

        // Sort the events chronologically to ensure we're identifying the correct second yellow
        let sortedPlayerEvents = playerEvents.sorted {
            if $0.time.elapsed != $1.time.elapsed {
                return $0.time.elapsed < $1.time.elapsed
            }
            let extra0 = $0.time.extra ?? 0
            let extra1 = $1.time.extra ?? 0
            return extra0 < extra1
        }

        // Check if this is the second yellow card event for this player
        // We need to find this specific event in the player's yellow card events
        // and check if it's the second one
        if let index = sortedPlayerEvents.firstIndex(where: {
            $0.time.elapsed == event.time.elapsed &&
            $0.time.extra == event.time.extra
        }) {
            return index == 1 // This is the second yellow card (index 1)
        }

        return false
    }

    // Define a type to represent timeline items (either events or status separators)
    enum TimelineItem: Identifiable {
        case event(EventInfo)
        case statusSeparator(String, Int?, Int?) // status, homeScore, awayScore

        var id: String {
            switch self {
            case .event(let event):
                return event.id.uuidString
            case .statusSeparator(let status, let homeScore, let awayScore):
                return "separator-\(status)-\(homeScore ?? 0)-\(awayScore ?? 0)"
            }
        }
    }

    // Timeline items including both events and status separators
    private var timelineItems: [TimelineItem] {
        // If there are no events, return an empty array
        guard let events = events, !events.isEmpty else {
            return []
        }

        // Get the sorted events
        let sortedEventItems = sortedEvents.map { TimelineItem.event($0) }

        // Create a chronological version of the events (oldest first) for analysis
        let chronologicalEvents = events.sorted {
            if $0.time.elapsed != $1.time.elapsed {
                return $0.time.elapsed < $1.time.elapsed
            }
            let extra0 = $0.time.extra ?? 0
            let extra1 = $1.time.extra ?? 0
            return extra0 < extra1
        }

        // Get scores at different points in the match
        let htScore = getScoreAtHalfTime(events: chronologicalEvents)
        let ftScore = getScoreAtFullTime(events: chronologicalEvents)

        // For ET/AET separator, use the total final score (regular time + extra time)
        // This should show the complete score at the end of extra time, not just extra time goals
        let etScore: (home: Int?, away: Int?)
        etScore = (fixture.goals.home, fixture.goals.away)

        // Get the match status
        let matchStatus = fixture.status.short ?? ""

        // Create status separators based on match status and events
        var statusSeparators: [TimelineItem] = []

        // Check if there are events that are actually in extra time
        let hasExtraTimeEvents = chronologicalEvents.contains { event in
            return event.time.elapsed >= 91
        }

        // We can use this to add a second half of extra time separator if needed in the future
        // For now, we'll just remove it to avoid the warning

        // Note: We don't show penalty shootout separators in the match events timeline
        // since there's already a dedicated penalty shootout section in the UI

        // Add Half Time separator if the match has reached halftime
        // Show HT separator for any match that has progressed past the first half
        let matchReachedHalftime = (fixture.status.elapsed ?? 0) >= 45 ||
                                  matchStatus == "HT" ||
                                  matchStatus == "2H" ||
                                  matchStatus == "FT" ||
                                  matchStatus == "AET" ||
                                  matchStatus == "ET" ||
                                  matchStatus == "PEN"

        if matchReachedHalftime {
            statusSeparators.append(.statusSeparator("HT", htScore.home, htScore.away))
        }

        // Add Full Time separator if the match has ended regular time
        // Show FT separator for any match that has completed 90 minutes
        let matchEndedRegularTime = matchStatus == "FT" ||
                                   matchStatus == "AET" ||
                                   matchStatus == "ET" ||
                                   matchStatus == "PEN"

        if matchEndedRegularTime {
            statusSeparators.append(.statusSeparator("FT", ftScore.home, ftScore.away))
        }

        // Add Extra Time separator if the match actually went to extra time
        // This should appear after the FT separator and before any extra time events
        // Only show ET separator if the match has actually reached extra time
        let hasExtraTimeScore = fixture.score.extratime != nil &&
                               (fixture.score.extratime?.home != nil || fixture.score.extratime?.away != nil)
        let actuallyWentToExtraTime = matchStatus == "AET" ||
                                     matchStatus == "ET" ||
                                     hasExtraTimeScore

        if actuallyWentToExtraTime || (hasExtraTimeEvents && (matchStatus == "ET" || matchStatus == "AET")) {
            // Determine the correct separator text based on match outcome
            let separatorText = matchStatus == "PEN" ? "ET" : "AET"
            statusSeparators.append(.statusSeparator(separatorText, etScore.home, etScore.away))
        }

        // Note: We don't add penalty shootout separators to the match events timeline
        // since there's already a dedicated penalty shootout section in the UI

        // Combine events and separators
        var allItems = sortedEventItems + statusSeparators

        // Calculate the actual end of regular time based on events
        // This helps us place the FT separator correctly
        let regularTimeEndTime = calculateRegularTimeEnd(events: chronologicalEvents)
        let extraTimeStartTime = calculateExtraTimeStart(events: chronologicalEvents)

        // Assign a virtual time value to each item for sorting
        // This simplifies the sorting logic by using a single numeric value
        func getVirtualTime(_ item: TimelineItem) -> Double {
            switch item {
            case .event(let event):
                // For events, use their actual time (elapsed + extra)
                let baseTime = Double(event.time.elapsed)
                let extraTime = Double(event.time.extra ?? 0) / 10.0 // Convert extra time to decimal
                return baseTime + extraTime

            case .statusSeparator(let status, _, _):
                // For separators, assign a specific time value based on when they should appear
                switch status {
                case "HT":
                    // Half-time separator should appear right after 45' events
                    // but before any 46' events
                    return 45.9
                case "FT":
                    // Full-time separator should appear after all regular time events (including stoppage time)
                    // but before any extra time events
                    return regularTimeEndTime + 0.1
                case "ET", "AET":
                    // Extra-time separator should appear after all extra time events
                    // Find the latest extra time event and place separator after it
                    let latestExtraTimeEvent = chronologicalEvents
                        .filter { event in
                            // Extra time events are those at elapsed 91+ minutes
                            return event.time.elapsed >= 91
                        }
                        .max {
                            let time1 = Double($0.time.elapsed) + Double($0.time.extra ?? 0) / 10.0
                            let time2 = Double($1.time.elapsed) + Double($1.time.extra ?? 0) / 10.0
                            return time1 < time2
                        }

                    if let latestEvent = latestExtraTimeEvent {
                        let baseTime = Double(latestEvent.time.elapsed)
                        let extraTime = Double(latestEvent.time.extra ?? 0) / 10.0
                        return baseTime + extraTime + 0.1
                    }

                    // Fallback: place after calculated extra time start
                    return extraTimeStartTime > 0 ? extraTimeStartTime + 30.0 : 150.0

                default:
                    return 0.0
                }
            }
        }

        // Sort all items by virtual time (descending)
        allItems.sort { item1, item2 in
            return getVirtualTime(item1) > getVirtualTime(item2)
        }

        return allItems
    }

    // Helper function to calculate when regular time actually ended
    // This considers stoppage time events to place the FT separator correctly
    private func calculateRegularTimeEnd(events: [EventInfo]) -> Double {
        // Find the latest event that occurred in regular time (before extra time)
        // Regular time includes stoppage time but not extra time

        let regularTimeEvents = events.filter { event in
            let elapsed = event.time.elapsed

            // Regular time events are those that happen during the first 90 minutes
            // This includes:
            // - elapsed: 1-45 (first half)
            // - elapsed: 45, extra: X (first half injury time)
            // - elapsed: 46-90 (second half)
            // - elapsed: 90, extra: X (second half injury time)

            // Events at elapsed 91+ are extra time, regardless of extra field
            if elapsed >= 91 {
                return false // This is extra time
            }

            return true // This is regular time (including injury time)
        }

        if let latestRegularEvent = regularTimeEvents.max(by: {
            let time1 = Double($0.time.elapsed) + Double($0.time.extra ?? 0) / 10.0
            let time2 = Double($1.time.elapsed) + Double($1.time.extra ?? 0) / 10.0
            return time1 < time2
        }) {
            let baseTime = Double(latestRegularEvent.time.elapsed)
            let extraTime = Double(latestRegularEvent.time.extra ?? 0) / 10.0
            return baseTime + extraTime + 0.1 // Add small buffer
        }

        // Fallback: if no events found, use 90.9 as default end of regular time
        return 90.9
    }

    // Helper function to calculate when extra time starts
    private func calculateExtraTimeStart(events: [EventInfo]) -> Double {
        // Check the match status to understand the context
        let matchStatus = fixture.status.short ?? ""
        let isExtraTimeMatch = matchStatus == "AET" || matchStatus == "ET" || matchStatus == "PEN"

        if !isExtraTimeMatch {
            return 0.0 // No extra time
        }

        // Look for events that are clearly in extra time
        let extraTimeEvents = events.filter { event in
            let elapsed = event.time.elapsed

            // Extra time events are those that happen at elapsed 91+ minutes
            // This includes:
            // - elapsed: 91-105 (first half of extra time)
            // - elapsed: 105, extra: X (first half of extra time injury time)
            // - elapsed: 106-120 (second half of extra time)
            // - elapsed: 120, extra: X (second half of extra time injury time)
            // - elapsed: 121+ (penalty shootout time)

            return elapsed >= 91
        }

        if let earliestExtraEvent = extraTimeEvents.min(by: {
            let time1 = Double($0.time.elapsed) + Double($0.time.extra ?? 0) / 10.0
            let time2 = Double($1.time.elapsed) + Double($1.time.extra ?? 0) / 10.0
            return time1 < time2
        }) {
            let baseTime = Double(earliestExtraEvent.time.elapsed)
            let extraTime = Double(earliestExtraEvent.time.extra ?? 0) / 10.0
            return baseTime + extraTime
        }

        // If no clear extra time events found but match went to extra time, assume it starts at 91
        return 91.0
    }

    // Helper function to get the score at half time
    private func getScoreAtHalfTime(events: [EventInfo]) -> (home: Int?, away: Int?) {
        // Use the halftime score from the fixture if available
        if let halftimeScore = fixture.score.halftime {
            return (halftimeScore.home, halftimeScore.away)
        }

        // Otherwise calculate from events
        var homeScore = 0
        var awayScore = 0

        for event in events {
            // Only count events up to minute 45
            if event.time.elapsed > 45 {
                break
            }

            // Count goals
            if event.type == "Goal" && !event.detail.lowercased().contains("missed") {
                if event.team.id == homeTeamId {
                    // Check for own goal
                    if event.detail.lowercased().contains("own") {
                        awayScore += 1
                    } else {
                        homeScore += 1
                    }
                } else if event.team.id == awayTeamId {
                    // Check for own goal
                    if event.detail.lowercased().contains("own") {
                        homeScore += 1
                    } else {
                        awayScore += 1
                    }
                }
            }
        }

        return (homeScore, awayScore)
    }

    // Helper function to get the score at full time
    private func getScoreAtFullTime(events: [EventInfo]) -> (home: Int?, away: Int?) {
        // Use the fulltime score from the fixture if available
        if let fulltimeScore = fixture.score.fulltime {
            return (fulltimeScore.home, fulltimeScore.away)
        }

        // Otherwise calculate from events
        var homeScore = 0
        var awayScore = 0

        for event in events {
            // Only count events up to minute 90
            if event.time.elapsed > 90 {
                break
            }

            // Count goals
            if event.type == "Goal" && !event.detail.lowercased().contains("missed") {
                if event.team.id == homeTeamId {
                    // Check for own goal
                    if event.detail.lowercased().contains("own") {
                        awayScore += 1
                    } else {
                        homeScore += 1
                    }
                } else if event.team.id == awayTeamId {
                    // Check for own goal
                    if event.detail.lowercased().contains("own") {
                        homeScore += 1
                    } else {
                        awayScore += 1
                    }
                }
            }
        }

        return (homeScore, awayScore)
    }

    var body: some View {
        if timelineItems.isEmpty {
            VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                // Title if provided
                if let title = title {
                    Text(title)
                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                        .foregroundColor(Color(UIColor.label))
                        .padding(.horizontal, 0)
                        .padding(.bottom, AppLayout.spacingS)
                }

                Text("No events available for this match.")
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(Color(UIColor.secondaryLabel))
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(AppLayout.spacingM)
            }
            .background(AppColors.tertiaryBackground)
            .cornerRadius(AppLayout.cornerRadiusL)
            .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
        } else {
            VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                // Title if provided
                if let title = title {
                    Text(title)
                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                        .foregroundColor(Color(UIColor.label))
                        .padding(.bottom, AppLayout.spacingS)
                }

                ForEach(Array(timelineItems.enumerated()), id: \.element.id) { index, item in
                    switch item {
                    case .event(let event):
                        // Check if this is a second yellow card for the player
                        let isSecondYellow = isSecondYellowCard(event)

                        EventRow(event: event, homeTeamId: homeTeamId, awayTeamId: awayTeamId, isSecondYellowCard: isSecondYellow)
                            .environmentObject(FixtureViewModel(fixture: fixture))

                    case .statusSeparator(let status, let homeScore, let awayScore):
                        // Display status separator
                        StatusSeparatorView(status: status, homeScore: homeScore, awayScore: awayScore)
                    }
                }
            }
            .padding(AppLayout.spacingM)
            .background(AppColors.tertiaryBackground)
            .cornerRadius(AppLayout.cornerRadiusL)
            .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
            .onAppear {
                // Start timer for live fixtures to ensure events update
                if isLive {
                    startEventTimer()
                }
            }
            .onDisappear {
                // Clean up timer when view disappears
                stopEventTimer()
            }
            .id("events-timeline-\(fixture.id)-\(events?.count ?? 0)-\(refreshTrigger)")
        }
    }

    // Check if the fixture is live
    private var isLive: Bool {
        let liveCodes = ["LIVE", "1H", "HT", "2H", "ET", "P", "Aw. ET"]
        return liveCodes.contains(fixture.status.short ?? "")
    }

    // Start timer for live fixtures to ensure events update
    private func startEventTimer() {
        stopEventTimer()

        if isLive {
            timer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
                refreshTrigger += 1
            }
        }
    }

    // Stop event timer
    private func stopEventTimer() {
        timer?.invalidate()
        timer = nil
    }
}

// Represents a single row in the timeline
struct EventRow: View {
    let event: EventInfo
    let homeTeamId: Int?
    let awayTeamId: Int?
    let isSecondYellowCard: Bool

    // Access the fixture to check match status
    @EnvironmentObject private var fixtureViewModel: FixtureViewModel

    // Determine alignment based on home/away team event
    private var alignment: HorizontalAlignment {
        event.team.id == homeTeamId ? .leading : .trailing
    }

    private var isHomeEvent: Bool {
        event.team.id == homeTeamId
    }

    // Get event color based on type
    private var eventColor: Color {
        switch event.type {
        case "Goal":
            return AppColors.Sports.goal
        case "Card":
            if isSecondYellowCard || event.detail.lowercased().contains("red") {
                return AppColors.Sports.redCard.opacity(0.9)
            } else {
                return Color.yellow.opacity(0.8)
            }
        case "subst":
            return Color(UIColor.systemBlue)
        default:
            return AppColors.Brand.secondary
        }
    }

    var body: some View {
        Group {
            if isHomeEvent {
                // Home team format: min icon players
                HStack(spacing: AppLayout.spacingS) {
                    // Time
                    timeView

                    // Icon
                    eventIcon(type: event.type, detail: event.detail)
                        .foregroundColor(eventColor)

                    // Player info
                    playerInfoView(alignment: .leading)

                    Spacer()
                }
                .accessibilityInfo(
                    label: getAccessibilityLabel(),
                    hint: "Home team event at \(eventTimeString)"
                )
            } else {
                // Away team format: players icon min
                HStack(spacing: AppLayout.spacingS) {
                    Spacer()

                    // Player info
                    playerInfoView(alignment: .trailing)

                    // Icon
                    eventIcon(type: event.type, detail: event.detail)
                        .foregroundColor(eventColor)

                    // Time
                    timeView
                }
                .accessibilityInfo(
                    label: getAccessibilityLabel(),
                    hint: "Away team event at \(eventTimeString)"
                )
            }
        }
        .padding(.vertical, AppLayout.spacingXS)
    }

    // Time view to avoid duplication
    private var timeView: some View {
        Text(eventTimeString)
            .font(AppTypography.dynamicFont(style: .caption, weight: .bold))
            .foregroundColor(Color(UIColor.secondaryLabel))
            .padding(.horizontal, 4)
            .padding(.vertical, 2)
            .background(Color(UIColor.tertiarySystemFill).opacity(0.5))
            .cornerRadius(AppLayout.cornerRadiusS)
    }

    // Player info view
    @ViewBuilder
    private func playerInfoView(alignment: HorizontalAlignment) -> some View {
        VStack(alignment: alignment, spacing: AppLayout.spacingXS) {
            if event.type == "subst" {
                Text(event.player.name ?? "Player Out")
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(Color(UIColor.label))

                Text("for \(event.assist.name ?? "Player In")")
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color(UIColor.secondaryLabel))
            } else if event.type == "Goal" && event.detail.lowercased().contains("missed penalty") {
                Text(event.player.name ?? "N/A")
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(Color(UIColor.label))

                Text("Missed Penalty")
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color(UIColor.secondaryLabel))
            } else if event.type == "Goal" && event.assist.id != nil && !event.detail.lowercased().contains("missed") {
                Text(event.player.name ?? "N/A")
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(Color(UIColor.label))

                Text("Assist: \(event.assist.name ?? "N/A")")
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color(UIColor.secondaryLabel))
            } else if event.type == "Goal" {
                Text(event.player.name ?? "N/A")
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(Color(UIColor.label))

                if event.detail != "Normal Goal" && !event.detail.lowercased().contains("missed") {
                    Text(event.detail)
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                }
            } else {
                Text(event.player.name ?? "N/A")
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(Color(UIColor.label))

                // For Cards, show comments (reason) if available, otherwise show detail
                if event.type == "Card" {
                    if isSecondYellowCard {
                        Text("Second Yellow Card")
                            .font(AppTypography.dynamicFont(style: .caption))
                            .foregroundColor(Color(UIColor.secondaryLabel))
                    } else if !(event.comments ?? "").isEmpty {
                        Text(event.comments ?? event.detail)
                            .font(AppTypography.dynamicFont(style: .caption))
                            .foregroundColor(Color(UIColor.secondaryLabel))
                    } else {
                        Text(event.detail)
                            .font(AppTypography.dynamicFont(style: .caption))
                            .foregroundColor(Color(UIColor.secondaryLabel))
                    }
                } else {
                    Text(event.detail)
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                }
            }
        }
    }

    // Helper for formatted time string - simplified to use API data
    private var eventTimeString: String {
        let time = event.time.elapsed
        let extra = event.time.extra

        // Check if the match is in Extra Time
        let matchStatus = fixtureViewModel.fixture?.status.short ?? ""
        let isExtraTime = matchStatus == "ET" || matchStatus == "AET" || matchStatus == "PEN"

        // If extra time is provided by the API, this is a stoppage time event
        if let extra = extra, extra > 0 {
            // Determine which half's stoppage time based on the elapsed time
            if time == 45 {
                // First half stoppage time
                return "45+\(extra)'"
            } else if time == 90 {
                // Second half stoppage time - always show as 90+X regardless of match status
                // The API provides elapsed: 90, extra: X for stoppage time events
                return "90+\(extra)'"
            } else if time == 105 {
                // First half of extra time stoppage
                return "105+\(extra)'"
            } else if time == 120 {
                // Second half of extra time stoppage or penalty shootout events
                return "120+\(extra)'"
            }
        }

        // Special case: Check if this is a first half stoppage time event
        // This handles cases where the API reports elapsed time as 45+X without extra field
        if time > 45 && time < 50 && extra == nil {
            // If the match is in extra time and this is minute 46-49, it should be displayed as is
            if isExtraTime {
                return "\(time)'"
            }

            // Otherwise, check if this is likely a first half stoppage time event
            let isLikelyFirstHalfStoppage = isEventLikelyInFirstHalfStoppage()
            if isLikelyFirstHalfStoppage {
                let stoppageMinute = time - 45
                return "45+\(stoppageMinute)'"
            }
        }

        // Special case: Check if this is a second half stoppage time event
        // This handles cases where the API reports elapsed time as 90+X without extra field
        if time > 90 && time < 95 && extra == nil {
            // If the match is in extra time, events after minute 90 should be displayed as is (91', 92', etc.)
            if isExtraTime {
                return "\(time)'"
            }

            // Otherwise, check if this is likely a second half stoppage time event
            let isLikelySecondHalfStoppage = isEventLikelyInSecondHalfStoppage()
            if isLikelySecondHalfStoppage {
                let stoppageMinute = time - 90
                return "90+\(stoppageMinute)'"
            }
        }

        // For events without extra time, or outside stoppage time ranges
        // Just show the elapsed time
        return "\(time)'"
    }

    // Helper method to determine if an event is likely in first half stoppage time
    private func isEventLikelyInFirstHalfStoppage() -> Bool {
        // If the event is exactly at minute 45, it's likely first half regular time
        if event.time.elapsed == 45 {
            return false
        }

        // If the event is between 45 and 50 minutes, it could be either
        // first half stoppage time or early second half
        if event.time.elapsed > 45 && event.time.elapsed < 50 {
            // Look for clues in the event details
            // For example, if it mentions "first half" or similar
            if let comments = event.comments,
               (comments.lowercased().contains("first half") ||
                comments.lowercased().contains("1st half") ||
                comments.lowercased().contains("halftime") ||
                comments.lowercased().contains("half time")) {
                return true
            }

            // If the detail contains information about the half
            if event.detail.lowercased().contains("first half") ||
               event.detail.lowercased().contains("1st half") {
                return true
            }

            // For substitutions at minute 46, check if it's a second half event
            // If it's a substitution at the start of second half, it should NOT be treated as stoppage time
            if event.time.elapsed == 46 && event.type == "subst" && event.detail.contains("Substitution") {
                // This is likely a second half substitution, not stoppage time
                return false
            }

            // For other events at minute 46, we need to be more conservative
            // Only treat as stoppage time if there are clear indicators
            if event.time.elapsed == 46 {
                // Check for explicit indicators that this is a first half event
                if let comments = event.comments,
                   (comments.lowercased().contains("first half") ||
                    comments.lowercased().contains("1st half") ||
                    comments.lowercased().contains("halftime") ||
                    comments.lowercased().contains("half time")) {
                    return true
                }

                // The presence of comments that don't mention the half is NOT enough
                // to assume it's first half stoppage time
                // For minute 46, default to showing the actual elapsed time (46')
                return false
            }
        }

        return false
    }

    // Helper method to determine if an event is likely in second half stoppage time
    private func isEventLikelyInSecondHalfStoppage() -> Bool {
        // If the event is exactly at minute 90, it's likely second half regular time
        if event.time.elapsed == 90 {
            return false
        }

        // If the event is between 90 and 95 minutes, it could be either
        // second half stoppage time or early extra time
        if event.time.elapsed > 90 && event.time.elapsed < 95 {
            // Look for clues in the event details
            if let comments = event.comments,
               (comments.lowercased().contains("second half") ||
                comments.lowercased().contains("2nd half") ||
                comments.lowercased().contains("full time") ||
                comments.lowercased().contains("fulltime")) {
                return true
            }

            // If the detail contains information about the half
            if event.detail.lowercased().contains("second half") ||
               event.detail.lowercased().contains("2nd half") {
                return true
            }

            // If the elapsed time is very close to 90, it's more likely to be stoppage time
            // but we need to be more conservative
            if event.time.elapsed == 91 {
                // Only the first minute after 90 might be stoppage time
                return true
            }
        }

        return false
    }

    // Helper for event icons - using custom icons from Assets.xcassets
    @ViewBuilder
    private func eventIcon(type: String, detail: String) -> some View {
        let iconSize: CGFloat = 20 // Increased icon size

        switch type {
        case "Goal":
            if detail.lowercased().contains("missed penalty") {
                // Custom icon for missed penalty - ball with red cross
                ZStack {
                    // Ball icon
                    Image("goal")
                        .resizable()
                        .scaledToFit()
                        .frame(width: iconSize, height: iconSize)

                    // Red X overlay
                    Image(systemName: "xmark")
                        .font(.system(size: iconSize * 0.8, weight: .bold))
                        .foregroundColor(.red)
                }
            } else if detail.lowercased().contains("own") {
                Image("own-goal")
                    .resizable()
                    .scaledToFit()
                    .frame(width: iconSize, height: iconSize)
            } else if detail.lowercased().contains("penalty") {
                Image("penalty")
                    .resizable()
                    .scaledToFit()
                    .frame(width: iconSize, height: iconSize)
            } else {
                // Use goal icon for regular goals
                Image("goal")
                    .resizable()
                    .scaledToFit()
                    .frame(width: iconSize, height: iconSize)
            }
        case "Card":
            if isSecondYellowCard || (detail.lowercased().contains("second") && detail.lowercased().contains("yellow")) {
                // This is a second yellow card, show double yellow card icon
                Image("doubleyellow")
                    .resizable()
                    .scaledToFit()
                    .frame(width: iconSize, height: iconSize)
            } else if detail.lowercased().contains("yellow") && detail.lowercased().contains("red") {
                // This is a direct yellow-red card from API
                Image("doubleyellow")
                    .resizable()
                    .scaledToFit()
                    .frame(width: iconSize, height: iconSize)
            } else if detail.lowercased().contains("yellow") {
                // This is a regular yellow card
                Image("yellow-card")
                    .resizable()
                    .scaledToFit()
                    .frame(width: iconSize, height: iconSize)
            } else if !isSecondYellowCard && detail.lowercased().contains("red") {
                // This is a direct red card (and not a second yellow)
                Image("red-card")
                    .resizable()
                    .scaledToFit()
                    .frame(width: iconSize, height: iconSize)
            }
        case "subst":
            Image("substitution")
                .resizable()
                .scaledToFit()
                .frame(width: iconSize, height: iconSize)
        case "Var":
            Text("VAR")
                .font(AppTypography.dynamicFont(style: .caption, weight: .bold))
                .padding(3)
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                        .stroke(Color(UIColor.label), lineWidth: 1)
                )
        default:
            Image(systemName: "info.circle")
                .font(.system(size: iconSize))
        }
    }

    // Helper for accessibility label
    private func getAccessibilityLabel() -> String {
        switch event.type {
        case "Goal":
            if event.detail.lowercased().contains("missed penalty") {
                return "Missed penalty by \(event.player.name ?? "Unknown player")"
            } else if event.detail.lowercased().contains("own") {
                return "Own goal by \(event.player.name ?? "Unknown player")"
            } else if event.detail.lowercased().contains("penalty") {
                return "Penalty goal by \(event.player.name ?? "Unknown player")"
            } else {
                if event.assist.id != nil {
                    return "Goal by \(event.player.name ?? "Unknown player"), assisted by \(event.assist.name ?? "Unknown player")"
                } else {
                    return "Goal by \(event.player.name ?? "Unknown player")"
                }
            }
        case "Card":
            if isSecondYellowCard || (event.detail.lowercased().contains("second") && event.detail.lowercased().contains("yellow")) {
                return "Second yellow card resulting in red card for \(event.player.name ?? "Unknown player")"
            } else if event.detail.lowercased().contains("yellow") && event.detail.lowercased().contains("red") {
                return "Second yellow card resulting in red card for \(event.player.name ?? "Unknown player")"
            } else if event.detail.lowercased().contains("yellow") {
                return "Yellow card for \(event.player.name ?? "Unknown player")"
            } else if !isSecondYellowCard && event.detail.lowercased().contains("red") {
                return "Red card for \(event.player.name ?? "Unknown player")"
            } else {
                return "Card for \(event.player.name ?? "Unknown player")"
            }
        case "subst":
            return "Substitution: \(event.player.name ?? "Player") replaced by \(event.assist.name ?? "Player")"
        default:
            return "\(event.type) event for \(event.player.name ?? "Unknown player")"
        }
    }
}

// MARK: - Preview
struct EventsTimelineView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Regular match with events
            if let events = Fixture.mock.events {
                ScrollView(showsIndicators: false) {
                    EventsTimelineView(events: events,
                                       homeTeamId: Fixture.mock.homeTeam?.id,
                                       awayTeamId: Fixture.mock.awayTeam?.id,
                                       fixture: Fixture.mock)
                        .padding()
                }
                .previewDisplayName("Regular Match")
            }

            // Penalty shootout match
            if let events = Fixture.mockPenalty.events {
                ScrollView(showsIndicators: false) {
                    EventsTimelineView(events: events,
                                       homeTeamId: Fixture.mockPenalty.homeTeam?.id,
                                       awayTeamId: Fixture.mockPenalty.awayTeam?.id,
                                       fixture: Fixture.mockPenalty)
                        .padding()
                }
                .previewDisplayName("Penalty Shootout Match")
            }
        }
    }
}
