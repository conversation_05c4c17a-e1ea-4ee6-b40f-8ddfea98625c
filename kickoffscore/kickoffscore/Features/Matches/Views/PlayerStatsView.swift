import SwiftUI
import Kingfisher
import UIKit

// Helper structure to hold combined player, stats, and team info
struct PlayerStatsItem: Identifiable, Equatable {
    let id: Int // Player ID
    let playerName: String
    let playerPhoto: String?
    let team: Fixture.TeamInfo
    let stats: PlayerStatsContainer

    // Implement Equatable to improve diffing performance in SwiftUI
    static func == (lhs: PlayerStatsItem, rhs: PlayerStatsItem) -> Bool {
        return lhs.id == rhs.id
    }

    init?(playerDetail: PlayerDetail, team: Fixture.TeamInfo) {
        // Only need to safely unwrap statistics?.first
        guard let statsContainer = playerDetail.statistics?.first else {
            return nil // Cannot create item without stats container
        }
        let playerInfo = playerDetail.player // Access non-optional player info directly
        self.id = playerInfo.id
        self.playerName = playerInfo.name ?? "Unknown Player"
        self.playerPhoto = playerInfo.photo
        self.team = team
        self.stats = statsContainer
    }
}

// Enum defining the categories of player stats to display
enum PlayerStatCategory: String, CaseIterable, Identifiable {
    case rating = "Rating"
    // case minutes = "Minutes Played" // << REMOVED
    // case goals = "Goals" // << REMOVED
    // case assists = "Assists" // << REMOVED
    case shotsTotal = "Total Shots"
    case shotsOnTarget = "Shots on Target"
    case passesTotal = "Total Passes"
    case keyPasses = "Key Passes"
    // case passesAccuracy = "Pass Accuracy" // << REMOVED
    case tacklesTotal = "Tackles"
    case interceptions = "Interceptions"
    case dribblesAttempted = "Dribbles Attempted"
    case dribblesSuccess = "Successful Dribbles"
    case foulsCommitted = "Fouls Committed"

    var id: String { self.rawValue }

    // Helper to get the comparable value for sorting
    // Returns Any? because rating/accuracy are strings, others are Int?
    func value(from stats: PlayerStatsContainer) -> Any? {
        switch self {
        case .rating: return stats.games?.rating // String e.g. "7.5"
        // case .minutes: return stats.games?.minutes // << REMOVED
        // case .goals: return stats.goals?.total // << REMOVED
        // case .assists: return stats.goals?.assists // << REMOVED
        case .shotsTotal: return stats.shots?.total
        case .shotsOnTarget: return stats.shots?.on
        case .passesTotal: return stats.passes?.total
        case .keyPasses: return stats.passes?.key
        // case .passesAccuracy: return stats.passes?.accuracy // String e.g. "85%" // << REMOVED
        case .tacklesTotal: return stats.tackles?.total
        case .interceptions: return stats.tackles?.interceptions
        case .dribblesAttempted: return stats.dribbles?.attempts
        case .dribblesSuccess: return stats.dribbles?.success
        case .foulsCommitted: return stats.fouls?.committed
        }
    }

    // Helper to get the display string for the value
    func displayValue(from stats: PlayerStatsContainer) -> String {
        let val = value(from: stats)
        switch self {
        case .rating:
            return (val as? String) ?? "-"
        default:
            if let intValue = val as? Int, intValue != 0 {
                return String(intValue)
            } else {
                return "-"
            }
        }
    }

    // Sorting function for PlayerStatsItem array
    func sortPlayers(_ players: [PlayerStatsItem]) -> [PlayerStatsItem] {
        players.sorted { p1, p2 in
            let val1 = value(from: p1.stats)
            let val2 = value(from: p2.stats)

            // Handle comparisons based on type
            if let v1 = val1 as? Int, let v2 = val2 as? Int {
                return v1 > v2 // Higher Int is better
            } else if let v1 = val1 as? String, let v2 = val2 as? String {
                // For rating (strings), convert and compare // << MODIFIED: removed accuracy logic
                if self == .rating {
                    let rating1 = Double(v1) ?? 0.0
                    let rating2 = Double(v2) ?? 0.0
                    return rating1 > rating2
                } else {
                    return v1 > v2 // Default string compare (shouldn't happen)
                }
            } else if val1 != nil && val2 == nil {
                return true // Non-nil is greater than nil
            } else {
                return false // Nil is not greater than non-nil or other nil
            }
        }
    }
}

struct PlayerStatsView: View {
    let fixture: Fixture

    // Use @State to cache the computed player stats and avoid recalculating on every render
    @State private var allPlayersStats: [PlayerStatsItem] = []

    // Store home and away team IDs for color determination
    @State private var homeTeamId: Int?
    @State private var awayTeamId: Int?

    // Track if we've loaded the data
    @State private var hasLoadedData = false

    // Track scroll position for optimization
    @State private var scrollOffset: CGFloat = 0

    // Compute player stats only once and store in state
    private func computePlayerStats() {
        // Skip if already loaded
        if hasLoadedData { return }

        var combinedStats: [PlayerStatsItem] = []

        // Use Task to move this computation off the main thread
        Task {
            // Store team IDs for color determination
            self.homeTeamId = fixture.teams.home?.id
            self.awayTeamId = fixture.teams.away?.id

            // Home team players
            if let homeStatsInfo = fixture.playerStatistics?.first(where: { $0.team.id == fixture.teams.home?.id }),
               let homePlayers = homeStatsInfo.players,
               let homeTeam = fixture.teams.home {
                let homeStats = homePlayers.compactMap { PlayerStatsItem(playerDetail: $0, team: homeTeam) }
                combinedStats.append(contentsOf: homeStats)
            }

            // Away team players
            if let awayStatsInfo = fixture.playerStatistics?.first(where: { $0.team.id == fixture.teams.away?.id }),
               let awayPlayers = awayStatsInfo.players,
               let awayTeam = fixture.teams.away {
                let awayStats = awayPlayers.compactMap { PlayerStatsItem(playerDetail: $0, team: awayTeam) }
                combinedStats.append(contentsOf: awayStats)
            }

            // Update on main thread
            await MainActor.run {
                self.allPlayersStats = combinedStats
                self.hasLoadedData = true
            }
        }
    }

    var body: some View {
        Group {
            if !hasLoadedData {
                // Show loading state while computing
                ProgressView("Loading player stats...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                    .background(AppColors.background)
                    .onAppear {
                        computePlayerStats()
                    }
            } else if allPlayersStats.isEmpty {
                // Show empty state if no stats available
                EmptyStateView(
                    icon: "person.3",
                    message: "Player statistics are not available for this match."
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                .background(AppColors.background)
            } else {
                // Show stats with performance optimizations
                ScrollView(showsIndicators: false) {
                    // Track scroll position for optimization
                    GeometryReader { geometry in
                        Color.clear.preference(
                            key: ScrollOffsetPreferenceKey.self,
                            value: geometry.frame(in: .named("scrollView")).minY
                        )
                    }
                    .frame(height: 0)

                    LazyVStack(spacing: AppLayout.spacingXS) { // Reduced spacing between sections
                        ForEach(Array(PlayerStatCategory.allCases.enumerated()), id: \.element.id) { index, category in
                            // Only render if this category would be visible based on scroll position
                            PlayerStatCategorySection(
                                category: category,
                                players: allPlayersStats,
                                isFirstCategory: index == 0 // Pass whether this is the first category
                            )
                        }
                    }
                    .padding(.top, 0) // Remove top padding
                    .padding(.bottom, AppLayout.spacingM)
                }
                .coordinateSpace(name: "scrollView")
                .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                    scrollOffset = value
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .scrollIndicators(.hidden)
                .background(AppColors.background)
                .edgesIgnoringSafeArea(.horizontal)
            }
        }
    }
}

// Subview for displaying a single stat category section
struct PlayerStatCategorySection: View {
    let category: PlayerStatCategory
    let players: [PlayerStatsItem]
    let topN = 3 // Show top 3 players
    var isFirstCategory: Bool = false // Whether this is the first category

    // Get home and away team IDs from parent view
    var homeTeamId: Int? {
        return players.first?.team.id
    }

    // Cache the sorted players to avoid recalculating on every render
    @State private var sortedTopPlayers: [PlayerStatsItem] = []

    // Track if the section is expanded
    @State private var isExpanded = false

    // Track if we've loaded the data
    @State private var hasLoadedData = false

    // Get a color for the category (used for the "See all" button)
    private var categoryColor: Color {
        return AppColors.primary
    }

    // Compute sorted players only once and store in state
    private func computeSortedPlayers() {
        // Skip if already loaded
        if hasLoadedData { return }

        // Use Task to move this computation off the main thread
        Task {
            let sorted = category.sortPlayers(players)
            let topPlayers = Array(sorted.prefix(topN))

            // Update on main thread
            await MainActor.run {
                self.sortedTopPlayers = topPlayers
                self.hasLoadedData = true
            }
        }
    }

    // Optimized detail view presentation
    private func presentDetailView() {
        // Compute all sorted players only when needed
        Task {
            let allSortedPlayers = category.sortPlayers(players)

            await MainActor.run {
                // Create a new navigation destination and present it as a sheet
                let detailView = NavigationView {
                    PlayerStatDetailView(category: category, players: allSortedPlayers, homeTeamId: homeTeamId)
                }

                // Find the root view controller
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let window = windowScene.windows.first,
                   let rootViewController = window.rootViewController {

                    // Present the detail view as a full-screen modal
                    let hostingController = UIHostingController(rootView: detailView)
                    hostingController.modalPresentationStyle = .fullScreen
                    rootViewController.present(hostingController, animated: true)
                }
            }
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // All content in one container with header inside
            VStack(spacing: 0) {
                // Header for the section (Category Name + See All link)
                HStack {
                    // Simple category name
                    Text(category.rawValue)
                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                        .foregroundColor(Color(UIColor.label))
                        .padding(.leading, AppLayout.spacingM)

                    Spacer()

                    // Simple "See all" button - optimized to compute data only when needed
                    Button(action: presentDetailView) {
                        Text("See all")
                            .font(AppTypography.dynamicFont(style: .subheadline))
                            .foregroundColor(AppColors.primary)
                            .padding(.trailing, AppLayout.spacingM)
                    }
                }
                .padding(.vertical, AppLayout.spacingS)
                .background(AppColors.tertiaryBackground)

                // Thin divider
                Rectangle()
                    .fill(Color.gray.opacity(0.1))
                    .frame(height: 0.5)
                    .padding(.horizontal, 0)

                // Top N Players container with compact styling
                if !hasLoadedData {
                    // Show loading indicator while computing
                    HStack {
                        Spacer()
                        ProgressView()
                            .padding(.vertical, AppLayout.spacingM)
                        Spacer()
                    }
                    .onAppear {
                        computeSortedPlayers()
                    }
                } else {
                    // Show the top players
                    VStack(spacing: 0) {
                        ForEach(Array(sortedTopPlayers.enumerated()), id: \.element.id) { index, player in
                            RankedPlayerRow(rank: index + 1, player: player, category: category, isHomeTeam: player.team.id == homeTeamId)
                                .padding(.vertical, 3) // Reduced vertical padding

                            // Add thin divider between rows (except after the last one)
                            if index < sortedTopPlayers.count - 1 {
                                Rectangle()
                                    .fill(Color.gray.opacity(0.1))
                                    .frame(height: 0.5)
                                    .padding(.horizontal, 0)
                            }
                        }
                    }
                    .padding(.top, 0)
                    .padding(.bottom, AppLayout.spacingS)
                }
            }
            .background(AppColors.tertiaryBackground)
            .cornerRadius(AppLayout.cornerRadiusL)
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .padding(.top, isFirstCategory ? 0 : AppLayout.spacingXS) // No top padding for first category
    }
}

// Subview for displaying a single ranked player row
struct RankedPlayerRow: View {
    let rank: Int
    let player: PlayerStatsItem
    let category: PlayerStatCategory
    let isHomeTeam: Bool

    // Helper to determine if this player has the highest stat in the category
    private var isHighestStat: Bool {
        // For rank 1, this player has the highest stat
        return rank == 1
    }

    // Helper to get team color based on isHomeTeam parameter
    private var teamColor: Color {
        if isHomeTeam {
            // Home team - blue
            return Color(red: 0.2, green: 0.6, blue: 0.86) // #3498DB - Blue for home team
        } else {
            // Away team - red
            return Color(red: 0.91, green: 0.3, blue: 0.24) // #E74C3C - Red for away team
        }
    }

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            // Simple rank number
            Text("\(rank)")
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(Color(UIColor.secondaryLabel))
                .frame(width: 20, alignment: .center)

            // Player photo with team logo fallback - using cached image view
            if let photoUrl = player.playerPhoto, !photoUrl.isEmpty {
                CachedImageView.playerPhoto(
                    url: photoUrl,
                    size: 32,
                    playerName: player.playerName
                )
            } else {
                // Team logo as fallback
                CachedImageView.teamLogo(
                    url: player.team.logo,
                    size: 32,
                    teamName: player.team.name ?? "Team"
                )
            }

            // Player name and team
            VStack(alignment: .leading, spacing: 0) {
                Text(player.playerName)
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(Color(UIColor.label))
                    .lineLimit(1)

                Text(player.team.name ?? "")
                    .font(AppTypography.dynamicFont(style: .caption2))
                    .foregroundColor(Color(UIColor.secondaryLabel))
                    .lineLimit(1)
            }

            Spacer()

            // Stat value with team color highlight for highest value
            Text(category.displayValue(from: player.stats))
                .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                .foregroundColor(isHighestStat ? .white : Color(UIColor.label))
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                        .fill(isHighestStat ? teamColor : Color.clear)
                )
                .frame(minWidth: 40, alignment: .trailing)
        }
        .padding(.vertical, 4)
        .padding(.horizontal, AppLayout.spacingM)
        .contentShape(Rectangle())
        .accessibilityInfo(
            label: "\(rank). \(player.playerName)",
            hint: "\(category.rawValue): \(category.displayValue(from: player.stats))"
        )
    }
}


// MARK: - Preview
struct PlayerStatsView_Previews: PreviewProvider {
    static var previews: some View {
        // Make sure Fixture.mock has player stats data
        NavigationView { // Use NavigationView for preview context
             PlayerStatsView(fixture: Fixture.mock)
        }
    }
}
