import SwiftUI
import <PERSON><PERSON><PERSON>

struct VoteView: View {
    @ObservedObject var viewModel: VoteViewModel
    let homeTeam: Fixture.TeamInfo?
    let awayTeam: Fixture.TeamInfo?
    var title: String?
    @ObservedObject var oddsViewModel: OddsViewModel
    var fixture: Fixture? = nil

    // Helper to get match winner odds if available
    private var matchWinnerOdds: (home: (odd: String, bookmaker: String)?, draw: (odd: String, bookmaker: String)?, away: (odd: String, bookmaker: String)?)? {
        guard let odds = oddsViewModel.odds else {
            print("No odds available yet")
            return nil
        }
        print("Odds available: \(odds.id)")

        // Get match winner bet type (ID: 1)
        let matchWinnerBets: [BetData]
        if odds.isLive {
            // For live odds, we need to handle differently
            // First, try to get the appropriate time-based odds based on elapsed time
            if let fixture = fixture, let elapsed = fixture.status.elapsed {
                // Select the appropriate time-based bet type based on elapsed time
                let betTypeId: Int

                if elapsed <= 15 {
                    betTypeId = 22 // 1x2 - 15 minutes
                } else if elapsed <= 30 {
                    betTypeId = 34 // 1x2 - 30 minutes
                } else if elapsed <= 50 {
                    betTypeId = 41 // 1x2 - 50 minutes
                } else if elapsed <= 60 {
                    betTypeId = 50 // 1x2 - 60 minutes
                } else if elapsed <= 70 {
                    betTypeId = 56 // 1x2 - 70 minutes
                } else if elapsed <= 80 {
                    betTypeId = 52 // 1x2 - 80 minutes
                } else {
                    betTypeId = 1 // Default to regular match winner
                }

                // Try to get the time-based odds
                let timeBasedOdds = odds.getLiveOdds(forBetType: betTypeId)
                if !timeBasedOdds.isEmpty {
                    // Use the first bookmaker's odds
                    guard let firstLiveOdds = timeBasedOdds.first else { return nil }

                    var homeOdd: (odd: String, bookmaker: String)? = nil
                    var drawOdd: (odd: String, bookmaker: String)? = nil
                    var awayOdd: (odd: String, bookmaker: String)? = nil

                    // Find the home, draw, and away values
                    for value in firstLiveOdds.values {
                        if value.value.lowercased() == "home" {
                            // Clean up bookmaker name if it's "api-football live"
                            let bookmakerName = firstLiveOdds.bookmakerName.lowercased() == "api-football live" ? "Live Odds" : firstLiveOdds.bookmakerName
                            homeOdd = (value.formattedOdd, bookmakerName)
                        } else if value.value.lowercased() == "draw" {
                            let bookmakerName = firstLiveOdds.bookmakerName.lowercased() == "api-football live" ? "Live Odds" : firstLiveOdds.bookmakerName
                            drawOdd = (value.formattedOdd, bookmakerName)
                        } else if value.value.lowercased() == "away" {
                            let bookmakerName = firstLiveOdds.bookmakerName.lowercased() == "api-football live" ? "Live Odds" : firstLiveOdds.bookmakerName
                            awayOdd = (value.formattedOdd, bookmakerName)
                        }
                    }

                    return (homeOdd, drawOdd, awayOdd)
                }
            }

            // Fallback to regular match winner odds if time-based odds are not available
            let liveOdds = odds.getLiveOdds(forBetType: 1)
            if liveOdds.isEmpty {
                return nil
            }

            // Use the first bookmaker's odds
            guard let firstLiveOdds = liveOdds.first else { return nil }

            var homeOdd: (odd: String, bookmaker: String)? = nil
            var drawOdd: (odd: String, bookmaker: String)? = nil
            var awayOdd: (odd: String, bookmaker: String)? = nil

            // Find the home, draw, and away values
            for value in firstLiveOdds.values {
                if value.value.lowercased() == "home" {
                    // Clean up bookmaker name if it's "api-football live"
                    let bookmakerName = firstLiveOdds.bookmakerName.lowercased() == "api-football live" ? "Live Odds" : firstLiveOdds.bookmakerName
                    homeOdd = (value.formattedOdd, bookmakerName)
                } else if value.value.lowercased() == "draw" {
                    let bookmakerName = firstLiveOdds.bookmakerName.lowercased() == "api-football live" ? "Live Odds" : firstLiveOdds.bookmakerName
                    drawOdd = (value.formattedOdd, bookmakerName)
                } else if value.value.lowercased() == "away" {
                    let bookmakerName = firstLiveOdds.bookmakerName.lowercased() == "api-football live" ? "Live Odds" : firstLiveOdds.bookmakerName
                    awayOdd = (value.formattedOdd, bookmakerName)
                }
            }

            return (homeOdd, drawOdd, awayOdd)
        } else {
            // For pre-match odds
            matchWinnerBets = odds.getPreMatchOdds(forBetType: 1)
            if matchWinnerBets.isEmpty {
                return nil
            }

            // Use the first bookmaker's odds
            guard let firstBet = matchWinnerBets.first, let bookmaker = odds.preMatchOdds.first(where: { $0.bets.contains(where: { $0.id == 1 }) }) else { return nil }

            var homeOdd: (odd: String, bookmaker: String)? = nil
            var drawOdd: (odd: String, bookmaker: String)? = nil
            var awayOdd: (odd: String, bookmaker: String)? = nil

            // Find the home, draw, and away values
            for value in firstBet.values {
                if value.value.lowercased() == "home" {
                    homeOdd = (value.formattedOdd, bookmaker.name)
                } else if value.value.lowercased() == "draw" {
                    drawOdd = (value.formattedOdd, bookmaker.name)
                } else if value.value.lowercased() == "away" {
                    awayOdd = (value.formattedOdd, bookmaker.name)
                }
            }

            return (homeOdd, drawOdd, awayOdd)
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingM) {

            // Title and vote count in the same row
            if let title = title {
                HStack {
                    Text(title)
                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                        .foregroundColor(Color(UIColor.label))

                    Spacer()

                    // Total votes inline with title
                    if let totalVotes = viewModel.voteStatistics?.totalVotes, totalVotes > 0 {
                        Text("\(totalVotes) \(totalVotes == 1 ? "vote" : "votes")")
                            .font(AppTypography.dynamicFont(style: .caption))
                            .foregroundColor(Color(UIColor.secondaryLabel))
                    }
                }
                .padding(.bottom, AppLayout.spacingS)
            }

            // Vote options
            HStack(spacing: AppLayout.spacingS) {
                // Home Win
                VoteButton(
                    teamName: homeTeam?.name ?? "Home",
                    teamLogo: homeTeam?.logo,
                    voteOption: .homeWin,
                    isSelected: viewModel.userVote == .homeWin,
                    percentage: viewModel.voteStatistics?.homePercentage ?? 0,
                    isLeading: viewModel.voteStatistics?.leadingVote == .homeWin,
                    action: { viewModel.submitVote(option: .homeWin) },
                    userHasVoted: viewModel.userVote != nil,
                    odds: matchWinnerOdds?.home?.odd,
                    bookmakerName: matchWinnerOdds?.home?.bookmaker,
                    fixture: fixture,
                    viewModel: viewModel
                )

                // Draw
                VoteButton(
                    teamName: "Draw",
                    voteOption: .draw,
                    isSelected: viewModel.userVote == .draw,
                    percentage: viewModel.voteStatistics?.drawPercentage ?? 0,
                    isLeading: viewModel.voteStatistics?.leadingVote == .draw,
                    action: { viewModel.submitVote(option: .draw) },
                    userHasVoted: viewModel.userVote != nil,
                    odds: matchWinnerOdds?.draw?.odd,
                    bookmakerName: matchWinnerOdds?.draw?.bookmaker,
                    fixture: fixture,
                    viewModel: viewModel
                )

                // Away Win
                VoteButton(
                    teamName: awayTeam?.name ?? "Away",
                    teamLogo: awayTeam?.logo,
                    voteOption: .awayWin,
                    isSelected: viewModel.userVote == .awayWin,
                    percentage: viewModel.voteStatistics?.awayPercentage ?? 0,
                    isLeading: viewModel.voteStatistics?.leadingVote == .awayWin,
                    action: { viewModel.submitVote(option: .awayWin) },
                    userHasVoted: viewModel.userVote != nil,
                    odds: matchWinnerOdds?.away?.odd,
                    bookmakerName: matchWinnerOdds?.away?.bookmaker,
                    fixture: fixture,
                    viewModel: viewModel
                )
            }

            // Total votes section removed (now shown inline with title)

            // Error message
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color.red)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.top, AppLayout.spacingXS)
            }
        }
        .padding(AppLayout.spacingM)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .shadow(color: Color.black.opacity(0.07), radius: 5, x: 0, y: 2)
        .alert(isPresented: $viewModel.showLoginPrompt) {
            Alert(
                title: Text("Login Required"),
                message: Text("You need to be logged in to vote."),
                primaryButton: .default(Text("Login")) {
                    // Show login screen via the auth view model
                    print("User clicked login in alert, showing login view")
                    viewModel.authViewModel.showLoginView = true
                },
                secondaryButton: .cancel()
            )
        }
    }
}

// MARK: - Vote Button
struct VoteButton: View {
    let teamName: String
    var teamLogo: String? = nil
    let voteOption: VoteOption
    let isSelected: Bool
    let percentage: Double
    let isLeading: Bool
    let action: () -> Void
    var userHasVoted: Bool
    var odds: String? = nil
    var bookmakerName: String? = nil
    var fixture: Fixture? = nil
    @ObservedObject var viewModel: VoteViewModel

    var hasVoted: Bool {
        userHasVoted // If any option is selected, user has already voted
    }

    var body: some View {
        Button(action: action) {
            VStack(spacing: AppLayout.spacingXS) {
                // Team logo or Draw X in a button-like container
                ZStack {
                    // Button-like background
                    Circle()
                        .fill(isSelected ? AppColors.Brand.primary.opacity(0.1) : Color(UIColor.systemBackground))
                        .frame(width: 56, height: 56)
                        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                        .overlay(
                            Circle()
                                .stroke(isSelected ? AppColors.Brand.primary : Color(UIColor.systemGray5), lineWidth: 1)
                        )

                    // Icon/logo
                    if voteOption == .draw {
                        // X for draw option
                        Image(systemName: "xmark")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(isSelected ? AppColors.Brand.primary : Color(UIColor.secondaryLabel))
                    } else if let logoUrl = teamLogo, !logoUrl.isEmpty {
                        // Team logo for home/away
                        KFImage(URL(string: logoUrl))
                            .placeholder {
                                Image(systemName: "sportscourt")
                                    .foregroundColor(Color(UIColor.secondaryLabel))
                            }
                            .resizable()
                            .scaledToFit()
                            .frame(width: 32, height: 32)
                    }
                }

                // Show vote percentage if user has voted or voting is disabled
                if userHasVoted || (fixture != nil && viewModel.shouldShowVotePercentages(fixture: fixture!)) {
                    Text(String(format: "%.0f%%", percentage))
                        .font(AppTypography.dynamicFont(style: .caption, weight: isLeading ? .semibold : .regular))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                        .padding(.top, AppLayout.spacingXS)
                }

                // Always show odds if available
                if let odds = odds {
                    VStack(spacing: 2) {
                        Text(odds)
                            .font(AppTypography.dynamicFont(style: .caption, weight: .semibold))
                            .foregroundColor(AppColors.Brand.primary)

                        if let bookmakerName = bookmakerName {
                            Text(bookmakerName)
                                .font(AppTypography.dynamicFont(style: .caption2))
                                .foregroundColor(Color(UIColor.tertiaryLabel))
                        }
                    }
                    .padding(.top, userHasVoted ? 2 : AppLayout.spacingXS)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(AppLayout.spacingS)
            .background(Color.clear)
            .cornerRadius(AppLayout.cornerRadiusS)
        }
        .buttonStyle(PlainButtonStyle())
        // Disable the button if:
        // 1. The user has already voted and this isn't the selected option, or
        // 2. Voting is not allowed for this fixture (based on match status and elapsed time)
        .disabled((hasVoted && !isSelected) || (fixture != nil && !viewModel.isVotingAllowed(fixture: fixture!)))
    }
}

// MARK: - Preview
#if DEBUG
struct VoteView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Preview with user vote
            withVotePreview
                .previewDisplayName("With Vote")
                .previewLayout(.sizeThatFits)
                .padding()
                .background(AppColors.background)

            // Preview without user vote
            noVotePreview
                .previewDisplayName("No Vote")
                .previewLayout(.sizeThatFits)
                .padding()
                .background(AppColors.background)
        }
    }

    // Helper to create a preview with a vote
    static var withVotePreview: some View {
        let votedViewModel = VoteViewModel(fixtureId: 12345)
        // Simulate data for preview
        votedViewModel.voteStatistics = VoteStatistics(
            homeVotes: 150,
            drawVotes: 50,
            awayVotes: 100,
            totalVotes: 300
        )
        votedViewModel.userVote = .homeWin

        // Create mock odds view model
        let oddsViewModel = OddsViewModel()
        oddsViewModel.odds = Odds.mock

        return VoteView(
            viewModel: votedViewModel,
            homeTeam: Fixture.TeamInfo(id: 1, name: "Arsenal", logo: "https://media.api-sports.io/football/teams/42.png", winner: true),
            awayTeam: Fixture.TeamInfo(id: 2, name: "Chelsea", logo: "https://media.api-sports.io/football/teams/49.png", winner: false),
            oddsViewModel: oddsViewModel,
            fixture: Fixture.mock
        )
    }

    // Helper to create a preview without a vote
    static var noVotePreview: some View {
        let noVoteViewModel = VoteViewModel(fixtureId: 12345)
        // Simulate data for preview
        noVoteViewModel.voteStatistics = VoteStatistics(
            homeVotes: 150,
            drawVotes: 50,
            awayVotes: 100,
            totalVotes: 300
        )

        // Create mock odds view model
        let oddsViewModel = OddsViewModel()
        oddsViewModel.odds = Odds.mock

        return VoteView(
            viewModel: noVoteViewModel,
            homeTeam: Fixture.TeamInfo(id: 1, name: "Arsenal", logo: "https://media.api-sports.io/football/teams/42.png", winner: true),
            awayTeam: Fixture.TeamInfo(id: 2, name: "Chelsea", logo: "https://media.api-sports.io/football/teams/49.png", winner: false),
            oddsViewModel: oddsViewModel,
            fixture: Fixture.mockUpcoming
        )
    }
}
#endif
