import SwiftUI

/// A view that displays a loading indicator for pagination
struct PaginationLoadingView: View {
    // MARK: - Properties
    @ObservedObject var viewModel: LeagueFixturesViewModel
    let handlePaginationExpandedState: () -> Void

    // MARK: - Body
    var body: some View {
        HStack {
            Spacer()
            VStack(spacing: AppLayout.spacingXS) {
                ProgressView()
                    .scaleEffect(1.0)
                    .padding(.bottom, 4)

                Text("Loading more leagues...")
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color(UIColor.secondaryLabel))
            }
            .padding()
            Spacer()
        }
        .frame(height: 70)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusM)
        .padding(.bottom, 8) // Reduced bottom padding to match the compact layout
        .edgesIgnoringSafeArea(.horizontal)
        .accessibilityInfo(label: "Loading more leagues", hint: "Please wait while more leagues are being loaded")
        .onAppear {
            // When loading more indicator appears, prepare to handle pagination
            print("Loading more leagues indicator appeared - preparing for pagination")

            // Add a delay to ensure the new leagues are loaded before we try to expand them
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                let loadingComplete = !viewModel.isLoadingMore

                if loadingComplete {
                    print("Pagination completed - updating expanded state")
                    handlePaginationExpandedState()
                }
            }
        }
    }
}
