import Foundation
import Combine
import SwiftUI

class VoteViewModel: ObservableObject {
    // Published properties
    @Published var voteStatistics: VoteStatistics?
    @Published var userVote: VoteOption?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var isVoting: Bool = false
    @Published var showLoginPrompt: Bool = false

    // Private properties
    private let fixtureId: Int
    private var cancellables = Set<AnyCancellable>()

    // Environment object for auth
    @ObservedObject var authViewModel: AuthViewModel

    init(fixtureId: Int, authViewModel: AuthViewModel? = nil) {
        self.fixtureId = fixtureId
        // If no authViewModel is provided, create a new one
        self.authViewModel = authViewModel ?? AuthViewModel()

        fetchVoteStatistics()

        // Only fetch user vote if authenticated
        if self.authViewModel.isAuthenticated {
            fetchUserVote()
        }

        // Subscribe to authentication changes
        self.authViewModel.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                if isAuthenticated {
                    self?.fetchUserVote()
                } else {
                    self?.userVote = nil
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Public Methods

    /// Fetch vote statistics for the fixture
    func fetchVoteStatistics() {
        isLoading = true
        errorMessage = nil

        let endpoint = "/fixtures/votes/\(fixtureId)/stats"

        APIService.shared.fetchData(endpoint: endpoint) { [weak self] (result: Result<VoteStatistics, APIError>) in
            guard let self = self else { return }

            self.isLoading = false
            switch result {
            case .success(let statistics):
                self.voteStatistics = statistics
            case .failure(let error):
                self.errorMessage = "Failed to load vote statistics: \(error.localizedDescription)"
                print("Error fetching vote statistics: \(error.localizedDescription)")
            }
        }
    }

    /// Fetch the current user's vote for the fixture
    func fetchUserVote() {
        // Skip if we don't have authentication
        guard authViewModel.isAuthenticated else {
            return
        }

        let endpoint = "/fixtures/votes/\(fixtureId)/user"

        APIService.shared.fetchData(endpoint: endpoint) { [weak self] (result: Result<UserVote, APIError>) in
            guard let self = self else { return }

            switch result {
            case .success(let userVote):
                self.userVote = userVote.vote
            case .failure(let error):
                // Only log the error, don't show to user as this is not critical
                print("Error fetching user vote: \(error.localizedDescription)")
                // If 401 or 404, user hasn't voted yet
                if case .statusCode(let code) = error, code == 401 || code == 404 {
                    self.userVote = nil
                }
            }
        }
    }

    /// Submit a vote for the fixture
    func submitVote(option: VoteOption) {
        // Check if user is authenticated
        guard authViewModel.isAuthenticated else {
            print("User not authenticated, showing login prompt")
            showLoginPrompt = true
            return
        }

        // Check if user has already voted
        if userVote != nil {
            print("User has already voted, cannot change vote")
            errorMessage = "You have already voted on this match"
            return
        }

        print("User is authenticated, submitting vote")

        isVoting = true
        errorMessage = nil

        let endpoint = "/fixtures/votes/\(fixtureId)"
        let voteRequest = VoteRequest(vote: option)

        APIService.shared.postData(endpoint: endpoint, body: voteRequest) { [weak self] (result: Result<VoteResponse, APIError>) in
            guard let self = self else { return }

            self.isVoting = false
            switch result {
            case .success(let response):
                self.voteStatistics = response.stats
                self.userVote = option
            case .failure(let error):
                // Handle specific error cases
                if case .statusCode(let code) = error, code == 400 {
                    // This could be the "Voting is closed for this match" error
                    self.errorMessage = "Voting is no longer available for this match"
                } else {
                    self.errorMessage = "Failed to submit vote: \(error.localizedDescription)"
                }
                print("Error submitting vote: \(error.localizedDescription)")
            }
        }
    }

    /// Check if voting is allowed for a fixture based on its status
    func isVotingAllowed(fixture: Fixture) -> Bool {
        // Get the match status
        guard let status = fixture.status.short?.uppercased() else {
            // If no status, default to allowing votes
            return true
        }

        // If match is finished, don't allow voting
        if ["FT", "AET", "PEN"].contains(status) {
            return false
        }

        // If match is in progress, check the elapsed time
        if let elapsed = fixture.status.elapsed {
            // Allow voting until the 80th minute
            return elapsed <= 80
        }

        // For upcoming matches or other statuses, allow voting
        return true
    }

    /// Check if vote percentages should be shown
    /// This happens when voting is disabled or the match is finished
    func shouldShowVotePercentages(fixture: Fixture) -> Bool {
        // If user has already voted, always show percentages
        if userVote != nil {
            return true
        }

        // Get the match status
        guard let status = fixture.status.short?.uppercased() else {
            return false
        }

        // If match is finished, show percentages
        if ["FT", "AET", "PEN"].contains(status) {
            return true
        }

        // If match is in progress and beyond 80 minutes, show percentages
        if let elapsed = fixture.status.elapsed, elapsed > 80 {
            return true
        }

        return false
    }
}
