import Foundation
import Combine

@MainActor
class OddsViewModel: ObservableObject {
    @Published var odds: Odds?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var noOddsAvailable = false
    @Published var selectedBookmaker: BookmakerData?

    // UI state for dropdowns
    @Published var isBookmakerDropdownOpen = false
    // All bet types are expanded by default since we're using non-collapsible sections
    @Published var expandedBetTypes: Set<Int> = Set()

    private var cancellables = Set<AnyCancellable>()
    private let apiService = APIService.shared

    // Bet type names for targeted bet types organized by categories
    let betTypeNames: [Int: String] = [
        // Match Outcome
        1: "Match Winner",                 // Home, Draw, Away (1X2)
        2: "Home/Away",                    // Draw No Bet
        12: "Double Chance",               // Double Chance
        4: "Asian Handicap",               // Asian Handicap
        // Removed: 9: "Handicap Result",  // European Handicap

        // Goals
        5: "Goals Over/Under",             // Over/Under goals
        8: "Both Teams Score",             // Yes/No (BTTS)
        10: "Exact Score",                 // Exact score predictions
        14: "Team To Score First",         // Team to score first
        16: "Total - Home",                // Home team total goals
        17: "Total - Away",                // Away team total goals

        // Half-Time
        13: "First Half Winner",           // Home, Draw, Away in 1st half
        6: "Goals Over/Under First Half",  // Goals Over/Under in 1st half
        // Removed: 7: "HT/FT Double",     // Half Time/Full Time

        // Events
        45: "Corners Over Under",          // Corners over/under
        80: "Cards Over/Under",            // Cards over/under

        // Tournament
        61: "To Qualify",                  // Team to qualify (tournaments)

        // Live Time-Based
        19: "1x2 (1st Half)",              // 1X2 for first half
        22: "1x2 - 15 minutes",            // 1X2 at 15 minutes
        34: "1x2 - 30 minutes",            // 1X2 at 30 minutes
        41: "1x2 - 50 minutes",            // 1X2 at 50 minutes
        50: "1x2 - 60 minutes",            // 1X2 at 60 minutes
        56: "1x2 - 70 minutes",            // 1X2 at 70 minutes
        52: "1x2 - 80 minutes"             // 1X2 at 80 minutes
    ]

    // Bet type categories for UI organization
    let betTypeCategories: [String: [Int]] = [
        "Match Outcome": [1, 2, 12, 4],
        "Goals": [5, 8, 10, 14, 16, 17],
        "Half-Time": [13, 6, 19],
        "Events": [45, 80],
        "Tournament": [61],
        "Live Time-Based": [22, 34, 41, 50, 56, 52]
    ]

    // Function to get bet type ID from name (for handling API responses with names but no IDs)
    func getBetTypeId(from name: String) -> Int? {
        let lowercaseName = name.lowercased()

        // Check for player shots on target specifically
        if lowercaseName.contains("player") && lowercaseName.contains("shot") && lowercaseName.contains("target") {
            return 51
        }

        // Check for other bet types
        for (id, betName) in betTypeNames {
            if betName.lowercased() == lowercaseName ||
               betName.lowercased().contains(lowercaseName) {
                return id
            }
        }

        return nil
    }

    // Function to get a more user-friendly name for display
    func getDisplayName(for betTypeId: Int) -> String {
        // Simply return the name from our dictionary, with a fallback to "Unknown Bet Type"
        return betTypeNames[betTypeId] ?? "Unknown Bet Type"
    }

    // Function to get the category for a bet type
    func getCategory(for betTypeId: Int) -> String {
        for (category, ids) in betTypeCategories {
            if ids.contains(betTypeId) {
                return category
            }
        }
        return "Other"
    }

    // Function to get all bet types in a specific category
    func getBetTypes(in category: String) -> [Int] {
        return betTypeCategories[category] ?? []
    }

    // Get all categories in a specific order
    var orderedCategories: [String] {
        let order = ["Match Outcome", "Goals", "Half-Time", "Events", "Tournament", "Live Time-Based"]
        return order.filter { betTypeCategories[$0] != nil }
    }

    // Try to load odds data from cache to display immediately while fresh data loads
    func loadCachedOddsIfAvailable(fixtureId: Int) -> Bool {
        // Use the OddsCache to load cached bookmakers
        if let bookmakers = OddsCache.shared.loadBookmakers(forFixtureId: fixtureId) {
            if !bookmakers.isEmpty {
                // Get the timestamp from UserDefaults (small data)
                let timestampKey = "odds_\(fixtureId)_timestamp"
                let cachedTimestamp = UserDefaults.standard.object(forKey: timestampKey) as? TimeInterval ?? Date().timeIntervalSince1970

                // Create Odds object with cached data
                self.odds = Odds(
                    id: fixtureId,
                    update: Date(timeIntervalSince1970: cachedTimestamp),
                    isLive: false,
                    preMatchOdds: bookmakers,
                    liveOdds: []
                )

                // Set default selected bookmaker
                self.selectedBookmaker = bookmakers.first
                return true
            }
        }

        return false
    }

    // Fetch odds for a fixture
    func fetchOdds(fixtureId: Int) {
        // First try to load from cache for immediate display
        let hasCachedData = loadCachedOddsIfAvailable(fixtureId: fixtureId)

        // If we have cached data, we'll show that immediately but still load fresh data
        // If no cached data, show loading indicator
        isLoading = !hasCachedData
        errorMessage = nil
        noOddsAvailable = false

        let endpoint = "/odds"
        // Use the targeted parameter to get only relevant bet types
        let parameters = ["fixture": String(fixtureId), "targeted": "true"]

        apiService.fetchData(endpoint: endpoint, parameters: parameters) { [weak self] (result: Result<OddsResponse, APIError>) in
            guard let self = self else { return }

            // Process data in background thread for better performance
            DispatchQueue.global(qos: .userInitiated).async {
                // Process the result

                switch result {
                case .success(let response):
                    if !response.isEmpty {
                        // Process data on background thread
                        var processedOdds: Odds? = nil
                        var processedBookmaker: BookmakerData? = nil

                        if !response.isEmpty {
                            // Group odds by bookmaker (more efficiently)
                            var bookmakerMap = [Int: BookmakerData]()

                            // First pass: collect all bookmakers
                            for oddsDoc in response {
                                for bookmaker in oddsDoc.bookmakers {
                                    if bookmakerMap[bookmaker.id] == nil {
                                        bookmakerMap[bookmaker.id] = bookmaker
                                    } else {
                                        // We'll merge bets in the second pass
                                        continue
                                    }
                                }
                            }

                            // Second pass: merge all bets for each bookmaker
                            for oddsDoc in response {
                                for bookmaker in oddsDoc.bookmakers {
                                    guard let existingBookmaker = bookmakerMap[bookmaker.id] else { continue }

                                    // Create a set of existing bet IDs for faster lookup
                                    let existingBetIds = Set(existingBookmaker.bets.map { $0.id })

                                    // Find bets that don't exist in the current bookmaker
                                    let newBets = bookmaker.bets.filter { !existingBetIds.contains($0.id) }

                                    // Create a new bookmaker with merged bets
                                    if !newBets.isEmpty {
                                        var mergedBets = existingBookmaker.bets
                                        mergedBets.append(contentsOf: newBets)

                                        // Create a new bookmaker instance with the merged bets
                                        let updatedBookmaker = BookmakerData(
                                            id: existingBookmaker.id,
                                            name: existingBookmaker.name,
                                            bets: mergedBets
                                        )

                                        bookmakerMap[bookmaker.id] = updatedBookmaker
                                    }
                                }
                            }

                            // Convert the map to an array
                            let bookmakers = Array(bookmakerMap.values)

                            if !bookmakers.isEmpty {
                                // Create Odds object with pre-match odds
                                let updateDate = ISO8601DateFormatter().date(from: response.first?.update ?? "") ?? Date()
                                processedOdds = Odds(
                                    id: fixtureId,
                                    update: updateDate,
                                    isLive: false,
                                    preMatchOdds: bookmakers,
                                    liveOdds: []
                                )

                                // Set default selected bookmaker
                                processedBookmaker = bookmakers.first

                                // Cache the processed data using OddsCache
                                OddsCache.shared.saveBookmakers(bookmakers, forFixtureId: fixtureId)
                            }
                        }

                        // Update UI on main thread with processed data
                        DispatchQueue.main.async {
                            self.isLoading = false

                            if let processedData = processedOdds {
                                self.odds = processedData
                                self.selectedBookmaker = processedBookmaker
                            } else {
                                self.noOddsAvailable = true
                            }
                        }
                    } else {
                        DispatchQueue.main.async {
                            self.isLoading = false
                            self.noOddsAvailable = true
                        }
                    }

                case .failure(let error):
                    DispatchQueue.main.async {
                        self.isLoading = false
                        self.errorMessage = "Failed to load odds: \(error.localizedDescription)"
                    }
                }
            }
        }
    }

    // Fetch live odds for a fixture
    func fetchLiveOdds(fixtureId: Int) {
        isLoading = true
        errorMessage = nil
        noOddsAvailable = false

        let endpoint = "/odds/live"
        // Use the targeted parameter to get only relevant bet types when supported
        let parameters = ["fixture": String(fixtureId), "targeted": "true"]

        apiService.fetchData(endpoint: endpoint, parameters: parameters) { [weak self] (result: Result<OddsResponse, APIError>) in
            guard let self = self else { return }

            // Process data in background thread for better performance
            DispatchQueue.global(qos: .userInitiated).async {
                var processedLiveOdds: [LiveOddsData] = []
                var updateDate = Date()

                switch result {
                case .success(let response):
                    if !response.isEmpty {
                        updateDate = ISO8601DateFormatter().date(from: response.first?.update ?? "") ?? Date()

                        // Process each live odds document
                        for oddsDoc in response {
                            for bookmaker in oddsDoc.bookmakers {
                                let bookmakerName = bookmaker.name

                                for bet in bookmaker.bets {
                                    // Check for player shots on target specifically
                                    let betId = bet.name.lowercased().contains("player") &&
                                               bet.name.lowercased().contains("shot") &&
                                               bet.name.lowercased().contains("target") ? 51 : bet.id

                                    // Convert BetValue objects to LiveBetValue objects
                                    let liveBetValues = bet.values.map { betValue in
                                        LiveBetValue(
                                            value: betValue.value,
                                            odd: betValue.odd,
                                            handicap: betValue.handicap,
                                            main: true,
                                            suspended: false
                                        )
                                    }

                                    let liveOddsData = LiveOddsData(
                                        id: betId,
                                        name: bet.name,
                                        bookmakerName: bookmakerName,
                                        bookmakerLogo: "",  // No logo in API response
                                        values: liveBetValues
                                    )

                                    // Only add if not already present
                                    if !processedLiveOdds.contains(where: { $0.id == liveOddsData.id }) {
                                        processedLiveOdds.append(liveOddsData)
                                    }
                                }
                            }
                        }
                    }

                    DispatchQueue.main.async {
                        self.isLoading = false

                        if !processedLiveOdds.isEmpty {
                            // Create or update Odds object with live odds
                            if let existingOdds = self.odds {
                                self.odds = Odds(
                                    id: existingOdds.id,
                                    update: updateDate,
                                    isLive: true,
                                    preMatchOdds: existingOdds.preMatchOdds,
                                    liveOdds: processedLiveOdds
                                )
                            } else {
                                self.odds = Odds(
                                    id: fixtureId,
                                    update: updateDate,
                                    isLive: true,
                                    preMatchOdds: [],
                                    liveOdds: processedLiveOdds
                                )
                            }
                        } else {
                            // If no live odds are available, fetch pre-match odds instead
                            self.fetchOdds(fixtureId: fixtureId)
                        }
                    }

                case .failure(_):
                    DispatchQueue.main.async {
                        self.isLoading = false
                        // If live odds fetch fails, try pre-match odds instead
                        self.fetchOdds(fixtureId: fixtureId)
                    }
                }
            }
        }
    }

    // Fetch appropriate odds based on fixture status
    func fetchAppropriateOdds(for fixture: Fixture) {
        // Check if the match is live
        let status = fixture.status.short?.uppercased() ?? ""
        let isLive = ["1H", "HT", "2H", "ET", "P", "LIVE", "BREAK"].contains(status)

        if isLive {
            // Fetch live odds for live matches
            fetchLiveOdds(fixtureId: fixture.id)
        } else {
            // Fetch pre-match odds for upcoming or finished matches
            fetchOdds(fixtureId: fixture.id)
        }
    }

    // Select a bookmaker
    func selectBookmaker(_ bookmaker: BookmakerData) {
        selectedBookmaker = bookmaker
    }

    // Always return true since all bet types are now always expanded
    func isBetTypeExpanded(_ betTypeId: Int) -> Bool {
        return true
    }

    // This function is kept for compatibility but no longer changes state
    func toggleBetTypeExpanded(_ betTypeId: Int) {
        // No-op since all sections are always expanded
    }

    // Get name for a bet type ID
    func getBetTypeName(for betTypeId: Int) -> String {
        return getDisplayName(for: betTypeId)
    }

    // Get available bet types based on current odds
    var availableBetTypes: [Int] {
        if let odds = odds {
            // Get all defined bet type IDs from our categories
            let definedBetTypeIds = Set(betTypeCategories.values.flatMap { $0 })

            if odds.isLive {
                // For live odds, get unique bet type IDs that are in our defined categories
                let liveOddsIds = odds.liveOdds.map { $0.id }
                return liveOddsIds.filter { definedBetTypeIds.contains($0) }.sorted()
            } else {
                // For pre-match odds, get unique bet type IDs from all bookmakers
                let allBetTypeIds = odds.preMatchOdds.flatMap { bookmaker in
                    bookmaker.bets.map { $0.id }
                }
                // Filter to only include bet types that are in our defined categories
                return Array(Set(allBetTypeIds)).filter { definedBetTypeIds.contains($0) }.sorted()
            }
        }
        return []
    }

    // Get available bet types organized by category
    var availableBetTypesByCategory: [String: [Int]] {
        var result: [String: [Int]] = [:]
        let available = availableBetTypes

        for (category, betTypes) in betTypeCategories {
            let availableInCategory = betTypes.filter { available.contains($0) }
            if !availableInCategory.isEmpty {
                result[category] = availableInCategory
            }
        }

        // We're no longer adding uncategorized bet types to the "Other" category
        // This ensures only our explicitly defined bet types are shown

        return result
    }

    // Get available categories with bet types
    var availableCategories: [String] {
        let categories = Array(availableBetTypesByCategory.keys)
        return orderedCategories.filter { categories.contains($0) }
        // We no longer add the "Other" category
    }

    // Get available bookmakers
    var availableBookmakers: [BookmakerData] {
        return odds?.preMatchOdds ?? []
    }

    // Get bet data for a specific bet type from the selected bookmaker
    func getBetData(for betTypeId: Int) -> BetData? {
        guard let odds = odds, !odds.isLive, let selectedBookmaker = selectedBookmaker else {
            return nil
        }

        return selectedBookmaker.bets.first { $0.id == betTypeId }
    }

    // Get live odds data for a specific bet type
    func getLiveOddsData(for betTypeId: Int) -> LiveOddsData? {
        guard let odds = odds, odds.isLive else {
            return nil
        }

        return odds.liveOdds.first { $0.id == betTypeId }
    }
}
