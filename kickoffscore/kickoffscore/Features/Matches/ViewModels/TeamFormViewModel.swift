import Foundation
import Combine

@MainActor
class TeamFormViewModel: ObservableObject {
    @Published var homeTeamForm: String?
    @Published var awayTeamForm: String?
    @Published var homeTeamDetailedForm: [DetailedFormResult]?
    @Published var awayTeamDetailedForm: [DetailedFormResult]?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var isExpanded: Bool = false

    private let service = TeamFormService()
    private var cancellables = Set<AnyCancellable>()

    func fetchTeamForm(fixture: Fixture) {
        guard let _ = fixture.teams.home?.id, let _ = fixture.teams.away?.id else {
            errorMessage = "Team information not available"
            return
        }

        isLoading = true
        errorMessage = nil

        // Fetch team form for both teams
        service.fetchTeamFormForFixture(fixture: fixture)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    print("Error fetching team form: \(error.localizedDescription)")
                    self?.useMockData(fixture: fixture)
                }
                self?.isLoading = false
            }, receiveValue: { [weak self] (homeFormResponse, awayFormResponse) in
                // Use the received form data or fall back to mock data if nil
                if let homeFormResponse = homeFormResponse {
                    self?.homeTeamForm = homeFormResponse.form
                    self?.homeTeamDetailedForm = homeFormResponse.detailedForm
                } else if let homeTeamId = fixture.teams.home?.id {
                    self?.homeTeamForm = self?.service.generateMockForm(teamId: homeTeamId)
                    self?.homeTeamDetailedForm = nil
                }

                if let awayFormResponse = awayFormResponse {
                    self?.awayTeamForm = awayFormResponse.form
                    self?.awayTeamDetailedForm = awayFormResponse.detailedForm
                } else if let awayTeamId = fixture.teams.away?.id {
                    self?.awayTeamForm = self?.service.generateMockForm(teamId: awayTeamId)
                    self?.awayTeamDetailedForm = nil
                }

                self?.isLoading = false
            })
            .store(in: &cancellables)
    }

    private func useMockData(fixture: Fixture) {
        // Use mock data as fallback
        if let homeTeamId = fixture.teams.home?.id {
            homeTeamForm = service.generateMockForm(teamId: homeTeamId)
            homeTeamDetailedForm = nil
        } else {
            homeTeamForm = "WDWLW"
            homeTeamDetailedForm = nil
        }

        if let awayTeamId = fixture.teams.away?.id {
            awayTeamForm = service.generateMockForm(teamId: awayTeamId)
            awayTeamDetailedForm = nil
        } else {
            awayTeamForm = "LDWLW"
            awayTeamDetailedForm = nil
        }
    }

    func toggleExpansion() {
        // Only toggle if we have data for at least one team
        let hasHomeData = homeTeamDetailedForm != nil && !homeTeamDetailedForm!.isEmpty
        let hasAwayData = awayTeamDetailedForm != nil && !awayTeamDetailedForm!.isEmpty

        if hasHomeData || hasAwayData {
            isExpanded.toggle()
        }
    }

    var canExpand: Bool {
        let hasHomeData = homeTeamDetailedForm != nil && !homeTeamDetailedForm!.isEmpty
        let hasAwayData = awayTeamDetailedForm != nil && !awayTeamDetailedForm!.isEmpty
        return hasHomeData || hasAwayData
    }
}
