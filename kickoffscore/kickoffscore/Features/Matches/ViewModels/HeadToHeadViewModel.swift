import Foundation
import Combine

// MARK: - Head to Head Summary Model
struct HeadToHeadSummary {
    let totalMatches: Int
    let homeWins: Int
    let awayWins: Int
    let draws: Int

    // Calculated properties
    var homeWinPercentage: Double {
        return totalMatches > 0 ? Double(homeWins) / Double(totalMatches) * 100 : 0
    }

    var awayWinPercentage: Double {
        return totalMatches > 0 ? Double(awayWins) / Double(totalMatches) * 100 : 0
    }

    var drawPercentage: Double {
        return totalMatches > 0 ? Double(draws) / Double(totalMatches) * 100 : 0
    }

    // Goal statistics
    let totalGoals: Int
    let homeGoals: Int
    let awayGoals: Int
    let bothTeamsScoredCount: Int
    let homeCleanSheets: Int
    let awayCleanSheets: Int

    var averageGoalsPerMatch: Double {
        return totalMatches > 0 ? Double(totalGoals) / Double(totalMatches) : 0
    }

    var bothTeamsScoredPercentage: Int {
        return totalMatches > 0 ? Int(Double(bothTeamsScoredCount) / Double(totalMatches) * 100) : 0
    }
}

// MARK: - H2H Match Model
struct HeadToHeadMatch: Identifiable {
    let id: Int
    let date: Date
    let leagueName: String
    let leagueLogo: String?
    let homeTeamId: Int
    let homeTeamName: String
    let homeTeamLogo: String?
    let awayTeamId: Int
    let awayTeamName: String
    let awayTeamLogo: String?
    let homeGoals: Int
    let awayGoals: Int
    let halfTimeHomeGoals: Int
    let halfTimeAwayGoals: Int
    let homeTeamWinner: Bool
    let awayTeamWinner: Bool
    let venue: String?

    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }
}

// MARK: - Head to Head API Response
// Note: These types are specific to the HeadToHeadViewModel and are not used elsewhere
struct HeadToHeadFixtureData: Decodable {
    let fixture: HeadToHeadFixtureInfo
    let league: HeadToHeadLeagueInfo
    let teams: HeadToHeadTeamsInfo
    let goals: HeadToHeadGoalsInfo
    let score: HeadToHeadScoreInfo
}

struct HeadToHeadFixtureInfo: Decodable {
    let id: Int
    let date: String
    let venue: HeadToHeadVenueInfo?
    let status: HeadToHeadStatusInfo
}

struct HeadToHeadVenueInfo: Decodable {
    let name: String?
    let city: String?
}

struct HeadToHeadStatusInfo: Decodable {
    let short: String
    let long: String
    let elapsed: Int?
}

struct HeadToHeadLeagueInfo: Decodable {
    let id: Int
    let name: String
    let logo: String?
    let season: Int
}

struct HeadToHeadTeamsInfo: Decodable {
    let home: HeadToHeadTeamInfo
    let away: HeadToHeadTeamInfo
}

struct HeadToHeadTeamInfo: Decodable {
    let id: Int
    let name: String
    let logo: String?
    let winner: Bool?
}

struct HeadToHeadGoalsInfo: Decodable {
    let home: Int?
    let away: Int?
}

struct HeadToHeadScoreInfo: Decodable {
    let halftime: HeadToHeadGoalsInfo
    let fulltime: HeadToHeadGoalsInfo
}

// MARK: - ViewModel
class HeadToHeadViewModel: ObservableObject {
    @Published var h2hMatches: [HeadToHeadMatch] = []
    @Published var h2hSummary: HeadToHeadSummary?
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let service = HeadToHeadService()
    private var cancellables = Set<AnyCancellable>()

    func loadHeadToHeadData(homeTeamId: Int, awayTeamId: Int) {
        print("Loading H2H data for teams: \(homeTeamId) vs \(awayTeamId)")
        isLoading = true
        errorMessage = nil
        h2hMatches = []
        h2hSummary = nil

        service.fetchHeadToHead(homeTeamId: homeTeamId, awayTeamId: awayTeamId)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] completion in
                guard let self = self else { return }
                self.isLoading = false

                if case .failure(let error) = completion {
                    self.errorMessage = "Failed to load head-to-head data: \(error.localizedDescription)"
                    print("Error loading H2H data: \(error)")
                }
            }, receiveValue: { [weak self] fixtures in
                guard let self = self else { return }
                print("Received \(fixtures.count) fixtures in ViewModel")
                self.processHeadToHeadData(fixtures, homeTeamId: homeTeamId, awayTeamId: awayTeamId)
            })
            .store(in: &cancellables)
    }

    private func processHeadToHeadData(_ fixtures: [HeadToHeadFixtureData], homeTeamId: Int, awayTeamId: Int) {
        print("Processing \(fixtures.count) H2H fixtures")

        var matches: [HeadToHeadMatch] = []

        // Store team names from the API response for later use
        var homeTeamName: String?
        var awayTeamName: String?

        // Find the team names that correspond to the homeTeamId and awayTeamId
        for fixture in fixtures {
            if fixture.teams.home.id == homeTeamId {
                homeTeamName = fixture.teams.home.name
            } else if fixture.teams.away.id == homeTeamId {
                homeTeamName = fixture.teams.away.name
            }

            if fixture.teams.home.id == awayTeamId {
                awayTeamName = fixture.teams.home.name
            } else if fixture.teams.away.id == awayTeamId {
                awayTeamName = fixture.teams.away.name
            }

            // If we've found both team names, we can break
            if homeTeamName != nil && awayTeamName != nil {
                break
            }
        }

        print("Team names from IDs - Home (\(homeTeamId)): \(homeTeamName ?? "Unknown"), Away (\(awayTeamId)): \(awayTeamName ?? "Unknown")")

        for fixture in fixtures {
            print("Processing fixture: \(fixture.fixture.id), date: \(fixture.fixture.date), status: \(fixture.fixture.status.short)")
            print("Teams: \(fixture.teams.home.id) (\(fixture.teams.home.name)) vs \(fixture.teams.away.id) (\(fixture.teams.away.name))")

            // Use DateUtility to parse the date
            if let date = DateUtility.parseAPIDate(fixture.fixture.date) {
                // Create match object with team IDs
                let match = HeadToHeadMatch(
                    id: fixture.fixture.id,
                    date: date,
                    leagueName: fixture.league.name,
                    leagueLogo: fixture.league.logo,
                    homeTeamId: fixture.teams.home.id,
                    homeTeamName: fixture.teams.home.name,
                    homeTeamLogo: fixture.teams.home.logo,
                    awayTeamId: fixture.teams.away.id,
                    awayTeamName: fixture.teams.away.name,
                    awayTeamLogo: fixture.teams.away.logo,
                    homeGoals: fixture.goals.home ?? 0,
                    awayGoals: fixture.goals.away ?? 0,
                    halfTimeHomeGoals: fixture.score.halftime.home ?? 0,
                    halfTimeAwayGoals: fixture.score.halftime.away ?? 0,
                    homeTeamWinner: fixture.teams.home.winner ?? false,
                    awayTeamWinner: fixture.teams.away.winner ?? false,
                    venue: fixture.fixture.venue?.name
                )
                matches.append(match)
            } else {
                print("Failed to parse date: \(fixture.fixture.date) with DateUtility")
            }
        }

        print("Created \(matches.count) match objects")

        // Sort matches by date (newest first)
        self.h2hMatches = matches.sorted { $0.date > $1.date }
        print("Sorted matches: \(self.h2hMatches.count)")

        // Calculate summary statistics
        calculateSummary(matches: matches, homeTeamId: homeTeamId, awayTeamId: awayTeamId)
    }

    private func calculateSummary(matches: [HeadToHeadMatch], homeTeamId: Int, awayTeamId: Int) {
        guard !matches.isEmpty else {
            self.h2hSummary = nil
            return
        }

        let totalMatches = matches.count

        // Find team names for logging purposes
        var homeTeamName: String?
        var awayTeamName: String?

        // Find the home team name
        for match in matches {
            if match.homeTeamId == homeTeamId {
                homeTeamName = match.homeTeamName
                break
            } else if match.awayTeamId == homeTeamId {
                homeTeamName = match.awayTeamName
                break
            }
        }

        // Find the away team name
        for match in matches {
            if match.homeTeamId == awayTeamId {
                awayTeamName = match.homeTeamName
                break
            } else if match.awayTeamId == awayTeamId {
                awayTeamName = match.awayTeamName
                break
            }
        }

        print("Calculating summary for \(homeTeamName ?? "Unknown") vs \(awayTeamName ?? "Unknown")")
        print("Total matches: \(totalMatches)")
        print("Current home team ID: \(homeTeamId), Current away team ID: \(awayTeamId)")

        // Count wins and draws
        var homeWins = 0
        var awayWins = 0
        var draws = 0

        for match in matches {
            print("Processing match: \(match.homeTeamName) (\(match.homeTeamId)) \(match.homeGoals)-\(match.awayGoals) \(match.awayTeamName) (\(match.awayTeamId))")

            // If it's a draw
            if match.homeGoals == match.awayGoals {
                draws += 1
                print("Draw: \(match.homeTeamName) \(match.homeGoals)-\(match.awayGoals) \(match.awayTeamName)")
                continue
            }

            // Determine which team won this match
            let homeTeamWon = match.homeGoals > match.awayGoals

            // Check if the current home team (from fixture) won this match
            if (match.homeTeamId == homeTeamId && homeTeamWon) ||
               (match.awayTeamId == homeTeamId && !homeTeamWon) {
                homeWins += 1
                if match.homeTeamId == homeTeamId {
                    print("\(match.homeTeamName) (current home team) won as home team: \(match.homeGoals)-\(match.awayGoals)")
                } else {
                    print("\(match.awayTeamName) (current home team) won as away team: \(match.homeGoals)-\(match.awayGoals)")
                }
            }
            // Check if the current away team (from fixture) won this match
            else if (match.homeTeamId == awayTeamId && homeTeamWon) ||
                    (match.awayTeamId == awayTeamId && !homeTeamWon) {
                awayWins += 1
                if match.homeTeamId == awayTeamId {
                    print("\(match.homeTeamName) (current away team) won as home team: \(match.homeGoals)-\(match.awayGoals)")
                } else {
                    print("\(match.awayTeamName) (current away team) won as away team: \(match.homeGoals)-\(match.awayGoals)")
                }
            }
        }

        print("Win counts - Home (\(homeTeamName ?? "Unknown")): \(homeWins), Away (\(awayTeamName ?? "Unknown")): \(awayWins), Draws: \(draws)")

        // Calculate goal statistics
        let totalGoals = matches.reduce(0) { $0 + $1.homeGoals + $1.awayGoals }

        // Calculate goals scored by the current home team (from fixture)
        let homeGoals = matches.reduce(0) { result, match in
            var goals = 0
            if match.homeTeamId == homeTeamId {
                goals = match.homeGoals
                print("\(match.homeTeamName) (current home team) scored \(goals) goals as home team")
            } else if match.awayTeamId == homeTeamId {
                goals = match.awayGoals
                print("\(match.awayTeamName) (current home team) scored \(goals) goals as away team")
            }
            return result + goals
        }

        // Calculate goals scored by the current away team (from fixture)
        let awayGoals = matches.reduce(0) { result, match in
            var goals = 0
            if match.homeTeamId == awayTeamId {
                goals = match.homeGoals
                print("\(match.homeTeamName) (current away team) scored \(goals) goals as home team")
            } else if match.awayTeamId == awayTeamId {
                goals = match.awayGoals
                print("\(match.awayTeamName) (current away team) scored \(goals) goals as away team")
            }
            return result + goals
        }

        print("Goal statistics - Total: \(totalGoals), \(homeTeamName ?? "Home"): \(homeGoals), \(awayTeamName ?? "Away"): \(awayGoals)")

        // Both teams scored count
        let bothTeamsScoredCount = matches.filter { $0.homeGoals > 0 && $0.awayGoals > 0 }.count

        // Clean sheets for the current home team (from fixture)
        let homeCleanSheets = matches.filter { match in
            (match.homeTeamId == homeTeamId && match.awayGoals == 0) ||
            (match.awayTeamId == homeTeamId && match.homeGoals == 0)
        }.count

        // Clean sheets for the current away team (from fixture)
        let awayCleanSheets = matches.filter { match in
            (match.homeTeamId == awayTeamId && match.awayGoals == 0) ||
            (match.awayTeamId == awayTeamId && match.homeGoals == 0)
        }.count

        print("Clean sheets - \(homeTeamName ?? "Home"): \(homeCleanSheets), \(awayTeamName ?? "Away"): \(awayCleanSheets)")

        // Create summary
        self.h2hSummary = HeadToHeadSummary(
            totalMatches: totalMatches,
            homeWins: homeWins,
            awayWins: awayWins,
            draws: draws,
            totalGoals: totalGoals,
            homeGoals: homeGoals,
            awayGoals: awayGoals,
            bothTeamsScoredCount: bothTeamsScoredCount,
            homeCleanSheets: homeCleanSheets,
            awayCleanSheets: awayCleanSheets
        )
    }
}
