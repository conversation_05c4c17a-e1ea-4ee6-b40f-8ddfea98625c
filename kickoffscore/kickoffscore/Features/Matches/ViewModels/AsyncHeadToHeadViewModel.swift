import Foundation
import Combine

/// Head-to-head view model using async/await pattern
@MainActor
class AsyncHeadToHeadViewModel: ObservableObject {
    @Published var h2hMatches: [HeadToHeadMatch] = []
    @Published var h2hSummary: HeadToHeadSummary?
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let service = AsyncHeadToHeadService()

    /// Load head-to-head data for two teams
    /// - Parameters:
    ///   - homeTeamId: The ID of the home team
    ///   - awayTeamId: The ID of the away team
    ///   - forceRefresh: Whether to force a refresh from the network
    func loadHeadToHeadData(homeTeamId: Int, awayTeamId: Int, forceRefresh: Bool = false) async {
        print("Loading H2H data for teams: \(homeTeamId) vs \(awayTeamId), forceRefresh: \(forceRefresh)")
        isLoading = true
        errorMessage = nil

        // Only clear existing data if not forcing refresh
        // This allows us to show existing data while refreshing
        if !forceRefresh {
            h2hMatches = []
            h2hSummary = nil
        }

        do {
            // Check network connectivity
            if forceRefresh && !NetworkMonitor.shared.isNetworkAvailable() {
                throw APIError.custom("No internet connection. Please check your network settings and try again.")
            }

            // Fetch head-to-head data
            let fixtures = try await service.fetchHeadToHead(
                homeTeamId: homeTeamId,
                awayTeamId: awayTeamId,
                forceRefresh: forceRefresh
            )

            // Process the data
            processHeadToHeadData(fixtures, homeTeamId: homeTeamId, awayTeamId: awayTeamId)

            isLoading = false
        } catch {
            isLoading = false

            // Format user-friendly error message
            if let apiError = error as? APIError {
                switch apiError {
                case .invalidURL:
                    errorMessage = "Invalid URL. Please try again later."
                case .requestFailed(let underlyingError):
                    errorMessage = "Network request failed: \(underlyingError.localizedDescription)"
                case .invalidResponse:
                    errorMessage = "Invalid response from server. Please try again later."
                case .statusCode(let code):
                    errorMessage = "Server error (code \(code)). Please try again later."
                case .decodingError:
                    errorMessage = "Error processing data from server. Please try again later."
                case .noData:
                    errorMessage = "No data received from server. Please try again later."
                case .custom(let message):
                    errorMessage = message
                }
            } else {
                errorMessage = "Failed to load head-to-head data: \(error.localizedDescription)"
            }

            print("Error loading H2H data: \(error)")
        }
    }

    /// Process head-to-head fixture data into match objects
    /// - Parameters:
    ///   - fixtures: The head-to-head fixture data
    ///   - homeTeamId: The ID of the home team
    ///   - awayTeamId: The ID of the away team
    private func processHeadToHeadData(_ fixtures: [HeadToHeadFixtureData], homeTeamId: Int, awayTeamId: Int) {
        print("Processing \(fixtures.count) H2H fixtures")

        var matches: [HeadToHeadMatch] = []

        // Store team names from the API response for later use
        var homeTeamName: String?
        var awayTeamName: String?

        // Find the team names that correspond to the homeTeamId and awayTeamId
        for fixture in fixtures {
            if fixture.teams.home.id == homeTeamId {
                homeTeamName = fixture.teams.home.name
            } else if fixture.teams.away.id == homeTeamId {
                homeTeamName = fixture.teams.away.name
            }

            if fixture.teams.home.id == awayTeamId {
                awayTeamName = fixture.teams.home.name
            } else if fixture.teams.away.id == awayTeamId {
                awayTeamName = fixture.teams.away.name
            }

            // If we've found both team names, we can break
            if homeTeamName != nil && awayTeamName != nil {
                break
            }
        }

        print("Team names from IDs - Home (\(homeTeamId)): \(homeTeamName ?? "Unknown"), Away (\(awayTeamId)): \(awayTeamName ?? "Unknown")")

        for fixture in fixtures {
            print("Processing fixture: \(fixture.fixture.id), date: \(fixture.fixture.date), status: \(fixture.fixture.status.short)")
            print("Teams: \(fixture.teams.home.id) (\(fixture.teams.home.name)) vs \(fixture.teams.away.id) (\(fixture.teams.away.name))")

            // Parse the date
            if let date = DateUtility.parseAPIDate(fixture.fixture.date) {
                // Create match object with team IDs
                let match = HeadToHeadMatch(
                    id: fixture.fixture.id,
                    date: date,
                    leagueName: fixture.league.name,
                    leagueLogo: fixture.league.logo,
                    homeTeamId: fixture.teams.home.id,
                    homeTeamName: fixture.teams.home.name,
                    homeTeamLogo: fixture.teams.home.logo,
                    awayTeamId: fixture.teams.away.id,
                    awayTeamName: fixture.teams.away.name,
                    awayTeamLogo: fixture.teams.away.logo,
                    homeGoals: fixture.goals.home ?? 0,
                    awayGoals: fixture.goals.away ?? 0,
                    halfTimeHomeGoals: fixture.score.halftime.home ?? 0,
                    halfTimeAwayGoals: fixture.score.halftime.away ?? 0,
                    homeTeamWinner: fixture.teams.home.winner ?? false,
                    awayTeamWinner: fixture.teams.away.winner ?? false,
                    venue: fixture.fixture.venue?.name
                )
                matches.append(match)
            } else {
                print("Failed to parse date: \(fixture.fixture.date) with DateUtility")
            }
        }

        print("Created \(matches.count) match objects")

        // Sort matches by date (newest first)
        self.h2hMatches = matches.sorted { $0.date > $1.date }
        print("Sorted matches: \(self.h2hMatches.count)")

        // Calculate summary statistics
        calculateSummary(matches: matches, homeTeamId: homeTeamId, awayTeamId: awayTeamId)
    }

    /// Calculate summary statistics for head-to-head matches
    /// - Parameters:
    ///   - matches: The head-to-head matches
    ///   - homeTeamId: The ID of the home team in the current fixture
    ///   - awayTeamId: The ID of the away team in the current fixture
    private func calculateSummary(matches: [HeadToHeadMatch], homeTeamId: Int, awayTeamId: Int) {
        guard !matches.isEmpty else {
            self.h2hSummary = nil
            return
        }

        let totalMatches = matches.count

        // Find team names for logging purposes
        var homeTeamName: String?
        var awayTeamName: String?

        // Find the home team name
        for match in matches {
            if match.homeTeamId == homeTeamId {
                homeTeamName = match.homeTeamName
                break
            } else if match.awayTeamId == homeTeamId {
                homeTeamName = match.awayTeamName
                break
            }
        }

        // Find the away team name
        for match in matches {
            if match.homeTeamId == awayTeamId {
                awayTeamName = match.homeTeamName
                break
            } else if match.awayTeamId == awayTeamId {
                awayTeamName = match.awayTeamName
                break
            }
        }

        print("Calculating summary for \(homeTeamName ?? "Unknown") vs \(awayTeamName ?? "Unknown")")
        print("Total matches: \(totalMatches)")
        print("Current home team ID: \(homeTeamId), Current away team ID: \(awayTeamId)")

        // Count wins and draws
        var homeWins = 0
        var awayWins = 0
        var draws = 0

        for match in matches {
            print("Processing match: \(match.homeTeamName) (\(match.homeTeamId)) \(match.homeGoals)-\(match.awayGoals) \(match.awayTeamName) (\(match.awayTeamId))")

            // If it's a draw
            if match.homeGoals == match.awayGoals {
                draws += 1
                print("Draw: \(match.homeTeamName) \(match.homeGoals)-\(match.awayGoals) \(match.awayTeamName)")
                continue
            }

            // Determine which team won this match
            let homeTeamWon = match.homeGoals > match.awayGoals

            // Check if the current home team (from fixture) won this match
            if (match.homeTeamId == homeTeamId && homeTeamWon) ||
               (match.awayTeamId == homeTeamId && !homeTeamWon) {
                homeWins += 1
                if match.homeTeamId == homeTeamId {
                    print("\(match.homeTeamName) (current home team) won as home team: \(match.homeGoals)-\(match.awayGoals)")
                } else {
                    print("\(match.awayTeamName) (current home team) won as away team: \(match.homeGoals)-\(match.awayGoals)")
                }
            }
            // Check if the current away team (from fixture) won this match
            else if (match.homeTeamId == awayTeamId && homeTeamWon) ||
                    (match.awayTeamId == awayTeamId && !homeTeamWon) {
                awayWins += 1
                if match.homeTeamId == awayTeamId {
                    print("\(match.homeTeamName) (current away team) won as home team: \(match.homeGoals)-\(match.awayGoals)")
                } else {
                    print("\(match.awayTeamName) (current away team) won as away team: \(match.homeGoals)-\(match.awayGoals)")
                }
            }
        }

        print("Win counts - Home (\(homeTeamName ?? "Unknown")): \(homeWins), Away (\(awayTeamName ?? "Unknown")): \(awayWins), Draws: \(draws)")

        // Calculate goal statistics
        let totalGoals = matches.reduce(0) { $0 + $1.homeGoals + $1.awayGoals }

        // Calculate goals scored by the current home team (from fixture)
        let homeGoals = matches.reduce(0) { result, match in
            var goals = 0
            if match.homeTeamId == homeTeamId {
                goals = match.homeGoals
                print("\(match.homeTeamName) (current home team) scored \(goals) goals as home team")
            } else if match.awayTeamId == homeTeamId {
                goals = match.awayGoals
                print("\(match.awayTeamName) (current home team) scored \(goals) goals as away team")
            }
            return result + goals
        }

        // Calculate goals scored by the current away team (from fixture)
        let awayGoals = matches.reduce(0) { result, match in
            var goals = 0
            if match.homeTeamId == awayTeamId {
                goals = match.homeGoals
                print("\(match.homeTeamName) (current away team) scored \(goals) goals as home team")
            } else if match.awayTeamId == awayTeamId {
                goals = match.awayGoals
                print("\(match.awayTeamName) (current away team) scored \(goals) goals as away team")
            }
            return result + goals
        }

        print("Goal statistics - Total: \(totalGoals), \(homeTeamName ?? "Home"): \(homeGoals), \(awayTeamName ?? "Away"): \(awayGoals)")

        // Both teams scored count
        let bothTeamsScoredCount = matches.filter { $0.homeGoals > 0 && $0.awayGoals > 0 }.count

        // Clean sheets for the current home team (from fixture)
        let homeCleanSheets = matches.filter { match in
            (match.homeTeamId == homeTeamId && match.awayGoals == 0) ||
            (match.awayTeamId == homeTeamId && match.homeGoals == 0)
        }.count

        // Clean sheets for the current away team (from fixture)
        let awayCleanSheets = matches.filter { match in
            (match.homeTeamId == awayTeamId && match.awayGoals == 0) ||
            (match.awayTeamId == awayTeamId && match.homeGoals == 0)
        }.count

        print("Clean sheets - \(homeTeamName ?? "Home"): \(homeCleanSheets), \(awayTeamName ?? "Away"): \(awayCleanSheets)")

        // Create summary
        self.h2hSummary = HeadToHeadSummary(
            totalMatches: totalMatches,
            homeWins: homeWins,
            awayWins: awayWins,
            draws: draws,
            totalGoals: totalGoals,
            homeGoals: homeGoals,
            awayGoals: awayGoals,
            bothTeamsScoredCount: bothTeamsScoredCount,
            homeCleanSheets: homeCleanSheets,
            awayCleanSheets: awayCleanSheets
        )
    }
}
