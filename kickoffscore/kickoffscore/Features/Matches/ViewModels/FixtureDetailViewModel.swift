import Foundation
import Combine
import SwiftUI
import SocketIO
import UIKit
import UserNotifications

@MainActor
class FixtureDetailViewModel: ObservableObject {
    // Published properties for UI updates
    @Published var fixture: Fixture
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var isPullToRefresh: Bool = false // Track if refresh is from pull-to-refresh
    @Published var notificationStatus: FixtureSubscriptionStatus = .notSubscribed
    @Published var showNotificationOptions: Bool = false

    // Flags to track refresh source
    private var isAutoRefresh: Bool = false

    // Auto-refresh properties
    private let refreshInterval: TimeInterval = 15 // 15 seconds to match backend update frequency
    private var cancellables = Set<AnyCancellable>()
    private var timerCancellable: AnyCancellable?

    // App state observation
    private var appStateObserver: AnyCancellable?

    // Socket subscription
    private var socketSubscription: AnyCancellable?

    // Live status codes
    private let liveStatusCodes = ["LIVE", "1H", "HT", "2H", "ET", "P", "Aw. ET", "SUSP", "INT"]

    // Upcoming status codes
    private let upcomingStatusCodes = ["NS", "TBD", "PST", "CANC", "ABD", "AWD", "WO"]

    init(fixture: Fixture) {
        self.fixture = fixture
        self.fixtureId = fixture.id

        // Start auto-refresh if the fixture is live
        setupAutoRefreshIfNeeded()

        // Observe app state changes to pause/resume auto-refresh
        setupAppStateObserver()

        // Setup socket subscription for real-time updates
        setupSocketSubscription()

        // Check if notifications are available for this fixture
        checkNotificationAvailability()
    }

    // Store fixture ID for use in deinit
    private let fixtureId: Int

    deinit {
        // Make sure to cancel all subscriptions when the view model is deallocated
        timerCancellable?.cancel()
        appStateObserver?.cancel()
        socketSubscription?.cancel()
        cancellables.forEach { $0.cancel() }
        cancellables.removeAll()

        // Unsubscribe from socket updates using the stored fixture ID
        // Create a local copy to avoid capturing self in the Task
        let idToUnsubscribe = fixtureId

        // Use a detached task to avoid capturing self
        Task.detached {
            SocketManager.shared.unsubscribeFromFixture(fixtureId: idToUnsubscribe)
        }
    }

    // Check if the fixture is currently live
    var isLive: Bool {
        guard let statusCode = fixture.status.short else { return false }
        return liveStatusCodes.contains(statusCode)
    }

    // Check if the fixture is upcoming
    var isUpcoming: Bool {
        guard let statusCode = fixture.status.short else { return false }
        return upcomingStatusCodes.contains(statusCode)
    }

    // Check if notifications should be available for this fixture
    var canShowNotifications: Bool {
        return isLive || isUpcoming
    }

    // Public method to check notification status (called from view)
    func checkNotificationStatus() {
        checkNotificationAvailability()
    }

    // Check if notifications are available for this fixture
    private func checkNotificationAvailability() {
        // Only show notifications for live or upcoming fixtures
        if !canShowNotifications {
            notificationStatus = .notSubscribed
            return
        }

        // First check if we have a cached status
        // UserDefaults.bool returns a non-optional with default value false
        let cachedKey = "fixture_\(fixtureId)_subscribed"
        if UserDefaults.standard.object(forKey: cachedKey) != nil {
            let cachedStatus = UserDefaults.standard.bool(forKey: cachedKey)
            if cachedStatus {
                // If we have cached preferences, use them
                if let preferencesData = UserDefaults.standard.data(forKey: "fixture_\(fixtureId)_preferences"),
                   let preferences = try? JSONDecoder().decode(NotificationPreference.self, from: preferencesData) {
                    notificationStatus = .subscribed(preferences)
                    return
                }
            }
        }

        // Set to loading while we check with the server
        notificationStatus = .loading

        // Check with the server if the user is subscribed
        if NotificationService.shared.hasValidDeviceToken() {
            NotificationService.shared.checkFixtureSubscription(fixtureId: fixtureId)
                .receive(on: DispatchQueue.main)
                .sink(receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        print("Error checking fixture subscription: \(error)")
                        self?.notificationStatus = .notSubscribed
                    }
                }, receiveValue: { [weak self] (isSubscribed, preferences) in
                    guard let self = self else { return }

                    if isSubscribed, let preferences = preferences {
                        // Update local cache
                        UserDefaults.standard.set(true, forKey: "fixture_\(self.fixtureId)_subscribed")
                        if let preferencesData = try? JSONEncoder().encode(preferences) {
                            UserDefaults.standard.set(preferencesData, forKey: "fixture_\(self.fixtureId)_preferences")
                        }

                        // Update UI
                        self.notificationStatus = .subscribed(preferences)
                    } else {
                        // Update local cache
                        UserDefaults.standard.set(false, forKey: "fixture_\(self.fixtureId)_subscribed")
                        UserDefaults.standard.removeObject(forKey: "fixture_\(self.fixtureId)_preferences")

                        // Update UI
                        self.notificationStatus = .notSubscribed
                    }
                })
                .store(in: &cancellables)
        } else {
            // No token available, so we're not subscribed
            notificationStatus = .notSubscribed
        }
    }

    // Toggle notification options popup
    func toggleNotificationOptions() {
        // Check if we have a device token before showing the options
        if !NotificationService.shared.hasValidDeviceToken() {
            // Request push notification permissions
            UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, _ in
                DispatchQueue.main.async {
                    if granted {
                        // Register for remote notifications
                        UIApplication.shared.registerForRemoteNotifications()

                        // Show the notification options after a short delay to allow token registration
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            self.showNotificationOptions = true
                        }
                    } else {
                        // Show an alert that notifications are disabled
                        self.errorMessage = "Push notifications are disabled. Please enable them in Settings to receive match updates."
                    }
                }
            }
        } else {
            // We have a token, so just toggle the options
            showNotificationOptions.toggle()
        }
    }

    // Subscribe to notifications for this fixture
    func subscribeToNotifications(preferences: NotificationPreference) {
        // Set status to loading
        notificationStatus = .loading

        // Call the notification service to subscribe
        NotificationService.shared.subscribeToFixture(fixtureId: fixtureId, preferences: preferences)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    print("Error subscribing to notifications: \(error)")
                    self?.notificationStatus = .notSubscribed
                }
            }, receiveValue: { [weak self] success in
                if success {
                    // Provide haptic feedback on successful subscription
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    guard let self = self else { return }

                    // Update local cache
                    UserDefaults.standard.set(true, forKey: "fixture_\(self.fixtureId)_subscribed")
                    if let preferencesData = try? JSONEncoder().encode(preferences) {
                        UserDefaults.standard.set(preferencesData, forKey: "fixture_\(self.fixtureId)_preferences")
                    }

                    // Update status
                    self.notificationStatus = .subscribed(preferences)
                } else {
                    // Provide error feedback
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.error)

                    self?.notificationStatus = .notSubscribed
                }
            })
            .store(in: &cancellables)
    }

    // Unsubscribe from notifications for this fixture
    func unsubscribeFromNotifications() {
        // Set status to loading
        notificationStatus = .loading

        // Call the notification service to unsubscribe
        NotificationService.shared.unsubscribeFromFixture(fixtureId: fixtureId)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    print("Error unsubscribing from notifications: \(error)")

                    // Provide error feedback
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.error)

                    // Revert to previous status
                    if let preferences = self?.notificationStatus.preference {
                        self?.notificationStatus = .subscribed(preferences)
                    } else {
                        self?.notificationStatus = .notSubscribed
                    }
                }
            }, receiveValue: { [weak self] success in
                if success {
                    // Provide haptic feedback on successful unsubscription
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    guard let self = self else { return }

                    // Update local cache
                    UserDefaults.standard.set(false, forKey: "fixture_\(self.fixtureId)_subscribed")
                    UserDefaults.standard.removeObject(forKey: "fixture_\(self.fixtureId)_preferences")

                    self.notificationStatus = .notSubscribed
                } else {
                    // Provide error feedback
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)

                    // Revert to previous status
                    if let preferences = self?.notificationStatus.preference {
                        self?.notificationStatus = .subscribed(preferences)
                    } else {
                        self?.notificationStatus = .notSubscribed
                    }
                }
            })
            .store(in: &cancellables)
    }

    // Setup observer for app state changes
    private func setupAppStateObserver() {
        // We no longer need to manually handle socket connections in background
        // as SocketManager now handles this automatically

        // We still need to handle the timer-based fallback mechanism
        appStateObserver = NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)
            .sink { [weak self] _ in
                // App is going to background, stop auto-refresh timer
                self?.stopAutoRefresh()
            }

        NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)
            .sink { [weak self] _ in
                // App is coming to foreground, restart auto-refresh timer if needed
                self?.setupAutoRefreshIfNeeded()

                // Force refresh to get the latest data
                if self?.isLive == true {
                    Task { @MainActor [weak self] in
                        self?.refreshFixtureData()
                    }
                }
            }
            .store(in: &cancellables)
    }

    // Setup socket subscription for real-time fixture updates
    private func setupSocketSubscription() {
        // Subscribe to fixture updates via socket using the stored fixture ID
        SocketManager.shared.subscribeToFixture(fixtureId: fixtureId)

        // Listen for fixture updates
        socketSubscription = SocketManager.shared.fixtureUpdates()
            .filter { $0.id == self.fixtureId } // Only process updates for this fixture
            .sink { [weak self] updatedFixture in
                guard let self = self else { return }

                Logger.info("Received real-time update for fixture ID: \(updatedFixture.id)", category: .network)

                // Debug: Log fixture details for troubleshooting
                if let elapsed = updatedFixture.status.elapsed {
                    Logger.debug("Socket update - Fixture \(updatedFixture.id): \(updatedFixture.teams.home?.name ?? "Home") vs \(updatedFixture.teams.away?.name ?? "Away"), Status: \(updatedFixture.status.short ?? "nil"), Elapsed: \(elapsed), Extra: \(updatedFixture.status.extra ?? 0), Score: \(updatedFixture.goals.home ?? 0)-\(updatedFixture.goals.away ?? 0), Events: \(updatedFixture.events?.count ?? 0)", category: .network)
                }

                // Preserve existing team ratings if the socket update doesn't include them
                // This prevents team ratings from disappearing during real-time updates
                let fixtureToUpdate: Fixture
                if updatedFixture.teamRatings == nil && self.fixture.teamRatings != nil {
                    // Create a new fixture instance with preserved team ratings
                    fixtureToUpdate = Fixture(
                        id: updatedFixture.id,
                        referee: updatedFixture.referee,
                        timezone: updatedFixture.timezone,
                        date: updatedFixture.date,
                        timestamp: updatedFixture.timestamp,
                        venue: updatedFixture.venue,
                        status: updatedFixture.status,
                        league: updatedFixture.league,
                        teams: updatedFixture.teams,
                        goals: updatedFixture.goals,
                        score: updatedFixture.score,
                        lineups: updatedFixture.lineups,
                        statistics: updatedFixture.statistics,
                        playerStatistics: updatedFixture.playerStatistics,
                        events: updatedFixture.events,
                        teamRatings: self.fixture.teamRatings // Preserve existing team ratings
                    )
                    Logger.debug("Preserved existing team ratings during socket update", category: .network)
                } else {
                    fixtureToUpdate = updatedFixture
                }

                // Update the fixture with the new data
                self.fixture = fixtureToUpdate

                // Check if we need to start or stop auto-refresh based on the updated status
                self.setupAutoRefreshIfNeeded()
            }
    }

    // Setup auto-refresh timer if the fixture is live
    // This runs as a fallback mechanism to ensure updates even if socket fails
    func setupAutoRefreshIfNeeded() {
        // First stop any existing timer
        stopAutoRefresh()

        // Start the timer if the fixture is live (regardless of socket connection)
        // Socket updates are primary, timer is fallback
        if isLive {
            Logger.info("Setting up auto-refresh for live fixture ID: \(fixtureId) (Socket connected: \(SocketManager.shared.isConnected))", category: .network)

            // Create a new timer using Combine with shorter interval for more responsive updates
            timerCancellable = Timer.publish(every: 10.0, on: .main, in: .common) // Reduced from 30s to 10s
                .autoconnect()
                .sink { [weak self] _ in
                    Task { @MainActor [weak self] in
                        // Set auto-refresh flag to true for seamless updates
                        self?.isAutoRefresh = true
                        self?.refreshFixtureData()
                    }
                }
        }
    }

    // Stop the auto-refresh timer
    func stopAutoRefresh() {
        timerCancellable?.cancel()
        timerCancellable = nil
    }

    // Manually refresh the fixture data
    func refreshFixtureData() {
        // Set pull-to-refresh flag since this is triggered by the refreshable modifier
        isPullToRefresh = true
        fetchFixtureDetails(fixtureId: fixtureId)
    }

    // Fetch the latest fixture details from the API
    func fetchFixtureDetails(fixtureId: Int) {
        // Only show loading indicator if it's not an auto-refresh
        if !isAutoRefresh {
            isLoading = true
        }
        errorMessage = nil

        let endpoint = "/fixtures"
        let parameters = ["id": String(fixtureId)]

        print("Fetching fixture details for ID: \(fixtureId)")

        // Store current team ratings to preserve them if new data doesn't include them
        let currentTeamRatings = self.fixture.teamRatings

        APIService.shared.fetchData(endpoint: endpoint, parameters: parameters) { [weak self] (result: Result<Fixture, APIError>) in
            guard let self = self else { return }

            // Reset all loading and refresh flags
            self.isLoading = false
            self.isAutoRefresh = false
            self.isPullToRefresh = false

            switch result {
            case .success(let updatedFixture):
                print("Successfully fetched updated fixture details for ID: \(fixtureId)")

                // Preserve existing team ratings if the new data doesn't include them
                // This prevents team ratings from disappearing during refresh
                let fixtureToUpdate: Fixture
                if updatedFixture.teamRatings == nil && currentTeamRatings != nil {
                    // Create a new fixture instance with preserved team ratings
                    fixtureToUpdate = Fixture(
                        id: updatedFixture.id,
                        referee: updatedFixture.referee,
                        timezone: updatedFixture.timezone,
                        date: updatedFixture.date,
                        timestamp: updatedFixture.timestamp,
                        venue: updatedFixture.venue,
                        status: updatedFixture.status,
                        league: updatedFixture.league,
                        teams: updatedFixture.teams,
                        goals: updatedFixture.goals,
                        score: updatedFixture.score,
                        lineups: updatedFixture.lineups,
                        statistics: updatedFixture.statistics,
                        playerStatistics: updatedFixture.playerStatistics,
                        events: updatedFixture.events,
                        teamRatings: currentTeamRatings // Preserve existing team ratings
                    )
                    print("Preserved existing team ratings during fixture refresh")
                } else {
                    fixtureToUpdate = updatedFixture
                }

                // Update the fixture with the new data
                self.fixture = fixtureToUpdate

                // Fetch halftime statistics if the match has started
                if let statusCode = self.fixture.status.short,
                   !self.upcomingStatusCodes.contains(statusCode) {
                    self.fetchHalftimeStatistics(fixtureId: fixtureId)
                }

                // Check if we need to start or stop auto-refresh based on the updated status
                self.setupAutoRefreshIfNeeded()

            case .failure(let error):
                self.errorMessage = error.localizedDescription
                print("Error fetching fixture details for ID \(fixtureId): \(error.localizedDescription)")
            }
        }
    }

    // Fetch halftime statistics for the fixture
    private func fetchHalftimeStatistics(fixtureId: Int) {
        let endpoint = "/fixtures/\(fixtureId)/statistics"
        let parameters = ["half": "true"]

        print("Fetching halftime statistics for fixture ID: \(fixtureId)")

        APIService.shared.fetchData(endpoint: endpoint, parameters: parameters) { [weak self] (result: Result<[TeamStatistics], APIError>) in
            guard let self = self else { return }

            switch result {
            case .success(let teamStats):
                print("Successfully fetched halftime statistics for fixture ID: \(fixtureId)")

                // Create a new fixture with the updated statistics
                var updatedFixture = self.fixture
                updatedFixture.statistics = teamStats

                // Update the fixture with the new data
                self.fixture = updatedFixture

            case .failure(let error):
                // Just log the error but don't show it to the user since this is supplementary data
                print("Error fetching halftime statistics for fixture ID \(fixtureId): \(error.localizedDescription)")
            }
        }
    }
}
