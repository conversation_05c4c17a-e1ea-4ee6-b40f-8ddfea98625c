import Foundation
import Combine

@MainActor
class PredictionViewModel: ObservableObject {
    @Published var prediction: Prediction?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    func fetchPrediction(fixtureId: Int) {
        isLoading = true
        errorMessage = nil

        let endpoint = "/predictions"
        let parameters = ["fixture": String(fixtureId)]

        print("Fetching prediction for fixture ID: \(fixtureId)")

        // Use the PredictionResponse model to decode the API response
        APIService.shared.fetchData(endpoint: endpoint, parameters: parameters) { [weak self] (result: Result<PredictionResponse, APIError>) in
            guard let self = self else { return }

            self.isLoading = false
            switch result {
            case .success(let predictionResponse):
                print("Successfully fetched prediction for fixture ID: \(fixtureId)")
                // Convert the API response to our UI model
                self.prediction = predictionResponse.toPrediction(fixtureId: fixtureId)
            case .failure(let error):
                self.errorMessage = error.localizedDescription
                print("Error fetching prediction for fixture ID \(fixtureId): \(error.localizedDescription)")
            }
        }
    }
}
