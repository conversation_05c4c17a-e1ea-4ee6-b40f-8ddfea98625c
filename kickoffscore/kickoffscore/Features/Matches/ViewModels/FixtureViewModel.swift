import Foundation
import SwiftUI

// A simplified ViewModel for passing fixture data to child views
class FixtureViewModel: ObservableObject {
    @Published var fixture: Fixture?
    
    init(fixture: Fixture) {
        self.fixture = fixture
    }
    
    // Check if the fixture is in Extra Time
    var isExtraTime: Bool {
        guard let status = fixture?.status.short else { return false }
        return status == "ET" || status == "AET" || status == "PEN"
    }
}
