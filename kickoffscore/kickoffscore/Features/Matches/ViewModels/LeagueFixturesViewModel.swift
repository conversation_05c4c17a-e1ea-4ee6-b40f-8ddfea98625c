import Foundation
import Combine
import SwiftUI
import SocketIO

@MainActor
class LeagueFixturesViewModel: ObservableObject {

    // Published properties for UI updates
    @Published var leaguesWithFixtures: [LeagueWithFixtures] = []
    @Published var isLoading: Bool = false
    @Published var isLoadingMore: Bool = false
    @Published var errorMessage: String? = nil
    @Published var showDatePicker: Bool = false
    @Published var isRefreshingLiveFixtures: Bool = false

    // Flag to track if refresh is from auto-refresh timer
    private var isAutoRefresh: Bool = false

    // Pagination properties
    private var currentPage: Int = 1
    private var totalPages: Int = 1
    private(set) var hasMorePages: Bool = true // Changed to private(set) to allow reading from view
    private var leaguesPerPage: Int = 6 // Default leagues per page
    private var currentDate: Date = Date() // Track current date for pagination

    // Auto-refresh properties
    private var timerCancellable: AnyCancellable?
    private let refreshInterval: TimeInterval = 15 // 15 seconds to match backend update frequency

    // Socket subscription properties
    private var liveFixturesSubscription: AnyCancellable?
    private var leagueFixturesSubscription: AnyCancellable?

    // Live status codes - made public for use in views
    let liveStatusCodes = ["LIVE", "1H", "HT", "2H", "ET", "P", "Aw. ET", "SUSP", "INT"]

    // Debouncing properties
    private var lastRefreshTime: Date = Date.distantPast
    private let minimumRefreshInterval: TimeInterval = 5 // Minimum 5 seconds between refreshes

    // Track last socket update time to prevent stale data overwrites
    private var lastSocketUpdateTime: Date = Date.distantPast
    private var socketUpdatedFixtureIds: Set<Int> = []

    private var cancellables = Set<AnyCancellable>()

    init() {
        // Setup app state observer to handle background/foreground transitions
        setupAppStateObserver()

        // Setup socket subscriptions for real-time updates
        setupSocketSubscriptions()
    }

    // Setup socket subscriptions for real-time fixture updates
    private func setupSocketSubscriptions() {
        // Subscribe to live fixtures updates
        liveFixturesSubscription = SocketManager.shared.liveFixturesUpdates()
            .sink { [weak self] fixtures in
                guard let self = self else { return }

                Logger.info("Received real-time socket update for \(fixtures.count) live fixtures", category: .network)

                // Debug: Log more details about the received fixtures
                for fixture in fixtures {
                    if let elapsed = fixture.status.elapsed {
                        Logger.debug("Socket update - Fixture \(fixture.id): \(fixture.teams.home?.name ?? "Home") vs \(fixture.teams.away?.name ?? "Away"), Status: \(fixture.status.short ?? "nil"), Elapsed: \(elapsed), Extra: \(fixture.status.extra ?? 0)", category: .network)
                    }
                }

                // Track socket update time and fixture IDs to prevent stale data overwrites
                self.lastSocketUpdateTime = Date()
                self.socketUpdatedFixtureIds = Set(fixtures.map { $0.id })

                // Clear socket update tracking after 30 seconds to allow normal API updates
                DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                    let timeSinceUpdate = Date().timeIntervalSince(self.lastSocketUpdateTime)
                    if timeSinceUpdate >= 30 {
                        self.socketUpdatedFixtureIds.removeAll()
                        Logger.debug("Cleared socket update tracking after 30 seconds", category: .data)
                    }
                }

                // Immediately invalidate cache for these fixtures to prevent stale data
                for fixture in fixtures {
                    CacheManager.shared.invalidateCache(keyPattern: "*id=\(fixture.id)*")
                }

                // Trigger general live data cache invalidation
                CacheManager.shared.triggerInvalidation(event: "live_data_updated")

                // Only process if we're viewing today's fixtures
                let isToday = Calendar.current.isDateInToday(self.currentDate)
                if isToday {
                    // Update our existing leagues with the live fixture data immediately
                    // This ensures socket updates take priority over any cached or API data
                    self.updateLiveFixturesById(fixtures)
                } else {
                    Logger.debug("Ignoring socket update - not viewing today's fixtures", category: .network)
                }
            }

        // Subscribe to league fixtures updates
        leagueFixturesSubscription = SocketManager.shared.leagueFixturesUpdates()
            .sink { [weak self] update in
                guard let self = self else { return }

                let leagueId = update.leagueId
                let fixtures = update.fixtures

                Logger.info("Received real-time update for league ID: \(leagueId) with \(fixtures.count) fixtures", category: .network)

                // Only process if we're viewing today's fixtures
                let isToday = Calendar.current.isDateInToday(self.currentDate)
                if isToday {
                    // Find the league in our existing data
                    if let leagueIndex = self.leaguesWithFixtures.firstIndex(where: { $0.league.id == leagueId }) {
                        let currentLeague = self.leaguesWithFixtures[leagueIndex]

                        // Create a dictionary of existing fixtures by ID for quick lookup
                        var fixturesById = Dictionary(uniqueKeysWithValues:
                            currentLeague.fixtures.map { ($0.id, $0) }
                        )

                        // Update or add fixtures from the socket update
                        for updatedFixture in fixtures {
                            fixturesById[updatedFixture.id] = updatedFixture
                        }

                        // Convert back to array and sort by match time
                        let fixturesArray = Array(fixturesById.values)
                        let mergedFixtures = sortFixturesByKickoffDate(fixturesArray)

                        // Create updated league with merged fixtures
                        let updatedLeague = LeagueWithFixtures(
                            league: currentLeague.league,
                            fixtures: mergedFixtures
                        )

                        self.leaguesWithFixtures[leagueIndex] = updatedLeague

                        Logger.info("Updated fixtures for league: \(updatedLeague.league.name ?? "Unknown") - merged \(fixtures.count) updated fixtures with \(currentLeague.fixtures.count) existing fixtures", category: .data)
                    }
                }
            }

        // Subscribe to all live fixtures
        SocketManager.shared.subscribeToLiveFixtures()
    }

    // Observe app state changes to pause/resume auto-refresh
    private func setupAppStateObserver() {
        // We no longer need to manually handle socket connections in background
        // as SocketManager now handles this automatically

        // We still need to handle the timer-based fallback mechanism and data refresh
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                guard let self = self else { return }

                // When app comes to foreground, check if we need to refresh fixtures
                let calendar = Calendar.current
                let isToday = calendar.isDateInToday(self.currentDate)

                if isToday {
                    // We don't know the live filter state here, so we'll check if we have live fixtures
                    // and refresh accordingly. The view will handle setting up auto-refresh with the
                    // correct live filter state when it appears.

                    // Immediately refresh live fixtures to get the latest data
                    // This is important even with socket connections, as we might have missed updates
                    self.refreshLiveFixtures()

                    // Setup auto-refresh timer if needed (as fallback)
                    self.checkForLiveFixturesAndSetupAutoRefresh()
                } else {
                    // For non-today dates, check if we need to refresh recently live fixtures
                    // This helps prevent showing outdated statuses for matches that have finished
                    let hasRecentlyLiveFixtures = self.checkForRecentlyLiveFixtures(in: self.currentDate)

                    if hasRecentlyLiveFixtures {
                        // Force a refresh of fixtures for dates that might have recently live matches
                        Logger.info("App returned to foreground - refreshing fixtures for date with potentially recently live matches", category: .network)
                        self.fetchLeagueFixtures(date: self.currentDate)
                    }
                }
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                // When app goes to background, stop auto-refresh timer to save resources
                // Socket connections are now handled by SocketManager
                self?.stopAutoRefresh()
            }
            .store(in: &cancellables)
    }

    deinit {
        // Cancel all Combine subscriptions
        timerCancellable?.cancel()
        liveFixturesSubscription?.cancel()
        leagueFixturesSubscription?.cancel()
        cancellables.forEach { $0.cancel() }
        cancellables.removeAll()

        // Unsubscribe from socket updates
        // Use a detached task to avoid capturing self
        Task.detached {
            SocketManager.shared.unsubscribeFromLiveFixtures()
        }
    }

    func fetchLeagueFixtures(date: Date) {
        // Validate the date - don't allow dates too far in the future
        let calendar = Calendar.current
        let now = Date()
        let maxAllowedDate = calendar.date(byAdding: .year, value: 1, to: now) ?? now

        // If the date is more than a year in the future, use today's date instead
        let validatedDate: Date
        if date > maxAllowedDate {
            Logger.warning("Date \(date) is too far in the future. Using today's date instead.", category: .network)
            validatedDate = now
        } else {
            validatedDate = date
        }

        // Reset pagination when fetching fixtures for a new date
        currentPage = 1
        hasMorePages = true

        // Stop auto-refresh if the date changes to a non-today date
        let isToday = Calendar.current.isDateInToday(validatedDate)
        if !isToday {
            stopAutoRefresh()
        }

        currentDate = validatedDate
        isLoading = true
        errorMessage = nil
        leaguesWithFixtures = [] // Clear previous fixtures

        // Use Task for async/await pattern
        Task {
            await fetchLeagueFixturesPageAsync(date: validatedDate, page: currentPage)

            // If today, check if we need to setup auto-refresh for live fixtures
            if isToday {
                checkForLiveFixturesAndSetupAutoRefresh()
            }
        }
    }

    // Async version of fetchLeagueFixturesPage
    private func fetchLeagueFixturesPageAsync(date: Date, page: Int) async {
        // Set appropriate loading state
        if page == 1 {
            isLoading = true
        } else {
            isLoadingMore = true
        }

        // Format date to YYYY-MM-DD string for the API
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)

        let endpoint = "/league-fixtures"
        var parameters: [String: String] = ["date": dateString]

        // Add pagination parameters
        parameters["page"] = String(page)
        parameters["leaguesPerPage"] = String(leaguesPerPage)

        // Add timezone parameter to get fixtures in user's local timezone
        parameters["timezone"] = TimeZone.current.identifier

        print("Fetching league fixtures for date: \(dateString), page: \(page)")

        do {
            // Determine if we should force refresh based on the date
            let isToday = Calendar.current.isDateInToday(date)
            let forceRefresh = isToday // Force refresh for today's fixtures

            // Determine cache duration based on the date and fixture status
            let cacheDuration: TimeInterval
            if isToday {
                cacheDuration = 15 // 15 seconds for today's fixtures
            } else {
                // For non-today dates, check if we have any fixtures that might be finishing soon
                // This helps prevent showing outdated statuses (like 90+5) for matches that have finished
                let hasRecentlyLiveFixtures = self.checkForRecentlyLiveFixtures(in: currentDate)
                cacheDuration = hasRecentlyLiveFixtures ? 60 : 3600 // 1 minute if recently live, otherwise 1 hour
            }

            // Use AsyncAPIService with appropriate caching
            let response = try await AsyncAPIService.shared.fetchData(
                endpoint: endpoint,
                parameters: parameters,
                forceRefresh: forceRefresh,
                cacheDuration: cacheDuration
            ) as LeagueFixturesResponse

            // Reset loading states
            isLoading = false
            isLoadingMore = false

            // Update pagination info
            totalPages = response.pagination.totalPages
            hasMorePages = response.pagination.hasNextPage
            currentPage = response.pagination.page

            // Process the leagues to fix flag URLs
            let processedData = processLeagueFlags(leagues: response.data)

            if page == 1 {
                // First page - replace existing leagues
                leaguesWithFixtures = processedData
                print("Replaced leagues array with \(leaguesWithFixtures.count) leagues")
            } else {
                // Check for duplicates before appending
                let existingLeagueIds = Set(leaguesWithFixtures.map { $0.league.id })
                let newLeagues = processedData.filter { newLeague in
                    !existingLeagueIds.contains(newLeague.league.id)
                }

                if newLeagues.count < processedData.count {
                    print("⚠️ Filtered out \(processedData.count - newLeagues.count) duplicate leagues")
                }

                // Subsequent pages - append to existing leagues
                leaguesWithFixtures.append(contentsOf: newLeagues)
                print("Appended \(newLeagues.count) new leagues, total now: \(leaguesWithFixtures.count)")
            }

            // Check if we need to setup auto-refresh for live fixtures
            checkForLiveFixturesAndSetupAutoRefresh()

            print("Successfully fetched league fixtures (Page \(page)/\(totalPages))")

        } catch {
            // Handle errors
            isLoading = false
            isLoadingMore = false
            errorMessage = error.localizedDescription
            print("Error fetching league fixtures: \(error.localizedDescription)")
        }
    }

    func fetchLeagueFixturesPage(date: Date, page: Int) {
        // Set appropriate loading state
        if page == 1 {
            isLoading = true
        } else {
            isLoadingMore = true
        }

        errorMessage = nil

        // Format date to YYYY-MM-DD string for the API
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)

        let endpoint = "/league-fixtures"
        var parameters: [String: String] = ["date": dateString]

        // Add pagination parameters
        parameters["page"] = String(page)
        parameters["leaguesPerPage"] = String(leaguesPerPage)

        // Add timezone parameter to get fixtures in user's local timezone
        parameters["timezone"] = TimeZone.current.identifier

        Logger.info("Fetching league fixtures for date: \(dateString), page: \(page)", category: .network)

        APIService.shared.fetchData(endpoint: endpoint, parameters: parameters) { [weak self] (result: Result<LeagueFixturesResponse, APIError>) in
            guard let self = self else { return }

            // Reset loading states
            self.isLoading = false
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                // Update pagination info
                self.totalPages = response.pagination.totalPages
                self.hasMorePages = response.pagination.hasNextPage
                self.currentPage = response.pagination.page

                // Print some debug info about the response
                Logger.debug("Pagination info: page \(response.pagination.page)/\(response.pagination.totalPages), hasNextPage: \(response.pagination.hasNextPage)", category: .data)
                Logger.info("Received \(response.data.count) leagues with fixtures", category: .data)

                // Process the leagues to fix flag URLs
                let processedData = self.processLeagueFlags(leagues: response.data)

                if page == 1 {
                    // First page - replace existing leagues
                    self.leaguesWithFixtures = processedData
                    Logger.info("Replaced leagues array with \(self.leaguesWithFixtures.count) leagues", category: .data)
                } else {
                    // Check for duplicates before appending
                    let existingLeagueIds = Set(self.leaguesWithFixtures.map { $0.league.id })
                    let newLeagues = processedData.filter { newLeague in
                        !existingLeagueIds.contains(newLeague.league.id)
                    }

                    if newLeagues.count < processedData.count {
                        Logger.warning("Filtered out \(processedData.count - newLeagues.count) duplicate leagues", category: .data)
                    }

                    // Subsequent pages - append to existing leagues
                    self.leaguesWithFixtures.append(contentsOf: newLeagues)
                    Logger.info("Appended \(newLeagues.count) new leagues, total now: \(self.leaguesWithFixtures.count)", category: .data)
                }

                // Check if we need to setup auto-refresh for live fixtures
                self.checkForLiveFixturesAndSetupAutoRefresh()

                Logger.info("Successfully fetched league fixtures (Page \(page)/\(self.totalPages))", category: .network)

            case .failure(let error):
                self.errorMessage = error.localizedDescription
                Logger.error("Error fetching league fixtures: \(error.localizedDescription)", category: .network)
            }
        }
    }

    func loadMoreLeagueFixturesIfNeeded() {
        guard !isLoading && !isLoadingMore && hasMorePages else {
            Logger.info("Skipping loadMoreLeagueFixturesIfNeeded - isLoading: \(isLoading), isLoadingMore: \(isLoadingMore), hasMorePages: \(hasMorePages)", category: .network)
            return
        }

        // Add a small delay to prevent multiple rapid calls
        Task { @MainActor in
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

            // Double-check conditions after the delay
            guard !isLoading && !isLoadingMore && hasMorePages else {
                Logger.info("Skipping loadMoreLeagueFixturesIfNeeded after delay - conditions changed", category: .network)
                return
            }

            let nextPage = currentPage + 1

            // Add validation for maximum page number to prevent excessive API calls
            let maxPageLimit = 10 // Reasonable limit to prevent excessive pagination

            if nextPage > maxPageLimit {
                Logger.warning("Reached maximum page limit (\(maxPageLimit)). Stopping pagination.", category: .network)
                hasMorePages = false
                return
            }

            Logger.info("Loading more league fixtures - requesting page \(nextPage) of \(totalPages)", category: .network)

            // Print current leagues count for debugging
            let currentLeaguesCount = leaguesWithFixtures.count
            Logger.info("Current leagues count before loading more: \(currentLeaguesCount)", category: .network)

            await fetchLeagueFixturesPageAsync(date: currentDate, page: nextPage)

            // After loading, check if we actually got more leagues
            let newLeaguesCount = leaguesWithFixtures.count
            Logger.info("New leagues count after loading more: \(newLeaguesCount) (added \(newLeaguesCount - currentLeaguesCount))", category: .network)
        }
    }

    // Convenience function to fetch today's fixtures
    func fetchLeagueFixturesForToday() {
        fetchLeagueFixtures(date: Date())
    }

    // Process league flags to ensure they display correctly
    private func processLeagueFlags(leagues: [LeagueWithFixtures]) -> [LeagueWithFixtures] {
        return leagues.map { leagueWithFixtures in
            let league = leagueWithFixtures.league

            // Debug: Print league info including flag URL
            print("League: \(league.name ?? "Unknown"), Country: \(league.country ?? "Unknown")")
            print("Original Flag URL: \(league.flag ?? "nil")")

            // Ensure the flag URL is valid and not empty
            var flagURL = league.flag

            // Convert SVG URLs to PNG URLs for better compatibility
            if let url = flagURL, url.hasSuffix(".svg") {
                // Extract the country code from the URL
                if let countryCode = url.split(separator: "/").last?.split(separator: ".").first {
                    // Use a PNG flag service instead
                    flagURL = "https://flagcdn.com/w80/\(countryCode).png"
                    print("Converted SVG to PNG flag URL: \(flagURL ?? "nil")")
                }
            }

            // If no flag URL is provided or conversion failed, try to construct one based on country code
            if flagURL == nil || flagURL?.isEmpty == true {
                if let countryName = league.country?.lowercased() {
                    print("Attempting to construct flag URL for country: \(countryName)")

                    // Map country names to ISO codes for common countries
                    let countryCodeMap: [String: String] = [
                        "england": "gb-eng",
                        "scotland": "gb-sct",
                        "wales": "gb-wls",
                        "northern ireland": "gb-nir",
                        "algeria": "dz",
                        "france": "fr",
                        "spain": "es",
                        "germany": "de",
                        "italy": "it",
                        "brazil": "br",
                        "argentina": "ar",
                        "portugal": "pt",
                        "netherlands": "nl",
                        "belgium": "be",
                        "world": "un" // United Nations flag for "World"
                    ]

                    if let countryCode = countryCodeMap[countryName] {
                        // Use PNG format instead of SVG
                        flagURL = "https://flagcdn.com/w80/\(countryCode).png"
                        print("Constructed PNG flag URL: \(flagURL ?? "nil")")
                    }
                }
            }

            // Create a new LeagueInfo with the updated flag URL
            let updatedLeague = Fixture.LeagueInfo(
                id: league.id,
                name: league.name,
                country: league.country,
                logo: league.logo,
                flag: flagURL,
                season: league.season,
                round: league.round,
                coverage: league.coverage
            )

            // Sort fixtures by kickoff date to ensure consistent ordering
            let sortedFixtures = sortFixturesByKickoffDate(leagueWithFixtures.fixtures)

            // Create a new LeagueWithFixtures with the updated league info and sorted fixtures
            return LeagueWithFixtures(league: updatedLeague, fixtures: sortedFixtures)
        }
    }

    // MARK: - Auto-refresh for Live Fixtures

    // Check if there are any live fixtures in the current data
    var hasLiveFixtures: Bool {
        for league in leaguesWithFixtures {
            for fixture in league.fixtures {
                if let status = fixture.status.short, liveStatusCodes.contains(status) {
                    return true
                }
            }
        }
        return false
    }

    // Setup auto-refresh timer if there are live fixtures
    // This is a fallback mechanism in case socket connection fails
    func setupAutoRefreshIfNeeded(liveFilterActive: Bool = false) {
        // First stop any existing timer
        stopAutoRefresh()

        // Only start the timer if we're viewing today's fixtures and there are live fixtures
        // or if the live filter is active (which implies we want to see live fixtures)
        let calendar = Calendar.current
        let isToday = calendar.isDateInToday(currentDate)

        // Subscribe to relevant leagues for socket updates
        if isToday {
            // Subscribe to all leagues in our current view
            // Create a local copy of league IDs to avoid capturing self in any potential async operations
            let leagueIds = leaguesWithFixtures.map { $0.league.id }
            for leagueId in leagueIds {
                SocketManager.shared.subscribeToLeague(leagueId: leagueId)
            }
        }

        // Only use timer as fallback if socket is not connected
        if isToday && (hasLiveFixtures || liveFilterActive) && !SocketManager.shared.isConnected {
            Logger.info("Setting up fallback auto-refresh for live fixtures (live filter: \(liveFilterActive ? "active" : "inactive"))", category: .network)

            // Use the same refresh interval regardless of connection type
            let adaptiveRefreshInterval = refreshInterval

            // Create a timer using Combine for reliable execution
            timerCancellable = Timer.publish(every: adaptiveRefreshInterval, on: .main, in: .common)
                .autoconnect()
                .sink { [weak self] _ in
                    Task { @MainActor [weak self] in
                        guard let self = self else { return }

                        // Skip refresh if already refreshing
                        if self.isRefreshingLiveFixtures {
                            Logger.debug("Skipping auto-refresh - already refreshing", category: .network)
                            return
                        }

                        // Skip refresh if app is in background
                        if UIApplication.shared.applicationState != .active {
                            Logger.debug("Skipping auto-refresh - app is in background", category: .network)
                            return
                        }

                        // Skip refresh if no network connection
                        if !NetworkMonitor.shared.isConnected {
                            Logger.debug("Skipping auto-refresh - no network connection", category: .network)
                            return
                        }

                        // Set auto-refresh flag to true for seamless updates
                        self.isAutoRefresh = true
                        // Pass the live filter state to the refresh method
                        self.refreshLiveFixtures(liveFilterActive: liveFilterActive)
                    }
                }

            // Immediately refresh to get the latest data
            refreshLiveFixtures(liveFilterActive: liveFilterActive)
        } else if isToday && (hasLiveFixtures || liveFilterActive) {
            // If socket is connected, just do an initial refresh
            refreshLiveFixtures(liveFilterActive: liveFilterActive)
        }
    }

    // Stop the auto-refresh timer
    func stopAutoRefresh() {
        timerCancellable?.cancel()
        timerCancellable = nil
    }

    func refreshLiveFixtures(liveFilterActive: Bool = false) {
        // Implement debouncing to prevent rapid successive calls
        let now = Date()
        let timeSinceLastRefresh = now.timeIntervalSince(lastRefreshTime)

        // Skip if we refreshed too recently
        if timeSinceLastRefresh < minimumRefreshInterval {
            Logger.debug("Skipping refresh - too soon since last refresh (\(Int(timeSinceLastRefresh))s)", category: .network)
            return
        }

        lastRefreshTime = now

        // Set flag to indicate we're refreshing live fixtures
        isRefreshingLiveFixtures = true

        // Prepare date string
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: currentDate)

        // First, collect all fixture IDs from our current data (both live and non-live)
        var allFixtureIds: [Int] = []
        var liveFixtureIds: [Int] = []
        var recentlyLiveFixtureIds: [Int] = []

        for league in leaguesWithFixtures {
            for fixture in league.fixtures {
                allFixtureIds.append(fixture.id)

                if let status = fixture.status.short, liveStatusCodes.contains(status) {
                    liveFixtureIds.append(fixture.id)
                }

                // Track fixtures that are in late stages of the game (80+ minutes or in injury time)
                // These might finish soon and disappear from live endpoint
                if let status = fixture.status.short, let elapsed = fixture.status.elapsed {
                    if (status == "2H" && elapsed >= 80) || status.contains("ET") || status.contains("+") {
                        recentlyLiveFixtureIds.append(fixture.id)
                    }
                }
            }
        }

        // If we don't have any fixtures, fetch everything from scratch
        if allFixtureIds.isEmpty {
            Logger.info("No fixtures found, fetching all fixtures for date: \(dateString)", category: .data)
            fetchLeagueFixtures(date: currentDate)
            return
        }

        // Use Task for async/await pattern
        Task {
            do {
                // Step 1: First fetch all currently live fixtures
                Logger.info("Fetching all live fixtures using live=true parameter", category: .network)

                let liveFixtures = try await AsyncAPIService.shared.fetchData(
                    endpoint: "/fixtures",
                    parameters: ["live": "true"],
                    forceRefresh: true,
                    cacheDuration: 0 // No cache - always get fresh data and immediately replace old cache
                ) as [Fixture]

                Logger.info("Successfully fetched \(liveFixtures.count) live fixtures", category: .network)

                // Create a set of fixture IDs that are currently live according to the API
                let currentlyLiveFixtureIds = Set(liveFixtures.map { $0.id })

                // Step 2: Check fixtures we displayed as live but might now be finished
                // These are fixtures that we were displaying as live but are no longer in the live endpoint
                var potentiallyFinishedFixtures: [Fixture] = []
                var batchesToFetch: [[Int]] = []

                // Identify fixtures that were live in our previous data but are not in the live endpoint response
                let potentiallyFinishedIds = Set(liveFixtureIds).union(Set(recentlyLiveFixtureIds)).subtracting(currentlyLiveFixtureIds)

                if !potentiallyFinishedIds.isEmpty {
                    Logger.info("Checking \(potentiallyFinishedIds.count) potentially finished fixtures", category: .network)

                    // Split into batches of maximum 20 IDs (API limitation)
                    let potentiallyFinishedIdsArray = Array(potentiallyFinishedIds)
                    let batchSize = 20

                    for i in stride(from: 0, to: potentiallyFinishedIdsArray.count, by: batchSize) {
                        let endIndex = min(i + batchSize, potentiallyFinishedIdsArray.count)
                        let batch = Array(potentiallyFinishedIdsArray[i..<endIndex])
                        batchesToFetch.append(batch)
                    }

                    // Fetch each batch sequentially
                    for (index, batch) in batchesToFetch.enumerated() {
                        if !batch.isEmpty {
                            Logger.info("Fetching batch \(index + 1)/\(batchesToFetch.count) of \(batch.count) fixtures", category: .network)

                            // Convert batch to comma-separated string
                            let idsParam = batch.map { String($0) }.joined(separator: "-")

                            let batchFixtures = try await AsyncAPIService.shared.fetchData(
                                endpoint: "/fixtures",
                                parameters: ["ids": idsParam],
                                forceRefresh: true,
                                cacheDuration: 3600 // Cache for 1 hour (3600 seconds) for finished matches
                            ) as [Fixture]

                            potentiallyFinishedFixtures.append(contentsOf: batchFixtures)
                        }
                    }

                    Logger.info("Successfully fetched \(potentiallyFinishedFixtures.count) potentially finished fixtures", category: .network)
                }

                // Combine live fixtures with potentially finished fixtures for a complete update
                let updatedFixtures = liveFixtures + potentiallyFinishedFixtures

                // Group all updated fixtures by league
                if !updatedFixtures.isEmpty {
                    let fixturesByLeague = Dictionary(grouping: updatedFixtures) { $0.league.id }
                    // Remove unused variable
                    // let existingLeagueIds = Set(leaguesWithFixtures.map { $0.league.id })

                    // Update existing leagues with the updated fixtures
                    for (leagueIndex, league) in leaguesWithFixtures.enumerated() {
                        if let leagueFixtures = fixturesByLeague[league.league.id] {
                            // Create a dictionary of fixtures by ID for quick lookup
                            let fixturesById = Dictionary(uniqueKeysWithValues: leagueFixtures.map { ($0.id, $0) })

                            var updatedLeagueFixtures = league.fixtures
                            var hasUpdates = false
                            var updatedFixtureIds = Set<Int>()

                            // Update existing fixtures
                            for (fixtureIndex, fixture) in updatedLeagueFixtures.enumerated() {
                                if let updatedFixture = fixturesById[fixture.id] {
                                    // Check if this fixture was recently updated via socket
                                    // If so, only update if the API data is newer or if enough time has passed
                                    let timeSinceSocketUpdate = Date().timeIntervalSince(lastSocketUpdateTime)
                                    let wasRecentlyUpdatedBySocket = socketUpdatedFixtureIds.contains(fixture.id) && timeSinceSocketUpdate < 10 // 10 seconds grace period

                                    if wasRecentlyUpdatedBySocket {
                                        Logger.debug("Skipping API update for fixture \(fixture.id) - recently updated via socket (\(String(format: "%.1f", timeSinceSocketUpdate))s ago)", category: .data)
                                        continue
                                    }
                                    // Log status and score changes
                                    if updatedFixture.status.short != fixture.status.short ||
                                       updatedFixture.status.elapsed != fixture.status.elapsed ||
                                       updatedFixture.status.extra != fixture.status.extra {

                                        // Create more detailed log message that includes extra time if present
                                        let oldStatus = "\(fixture.status.short ?? "nil")"
                                        let newStatus = "\(updatedFixture.status.short ?? "nil")"

                                        // Add detailed elapsed time info
                                        let oldElapsed = fixture.status.elapsed ?? 0
                                        let oldExtra = fixture.status.extra
                                        let newElapsed = updatedFixture.status.elapsed ?? 0
                                        let newExtra = updatedFixture.status.extra

                                        // Format old time display
                                        var oldTimeDisplay = "\(oldElapsed)"
                                        if let extra = oldExtra, extra > 0 {
                                            // Check the match status to determine which half we're in
                                            let status = fixture.status.short ?? ""
                                            let baseTime: Int

                                            // Determine the base time based on match status and elapsed time
                                            if status == "1H" || status == "HT" {
                                                // First half or halftime
                                                baseTime = 45
                                            } else if status == "2H" {
                                                // Second half
                                                baseTime = 90
                                            } else if status == "ET" && oldElapsed <= 105 {
                                                // First half of extra time
                                                baseTime = 105
                                            } else if status == "ET" {
                                                // Second half of extra time
                                                baseTime = 120
                                            } else if oldElapsed <= 45 {
                                                // Fallback for first half
                                                baseTime = 45
                                            } else {
                                                // Fallback for second half or later
                                                baseTime = 90
                                            }

                                            oldTimeDisplay = "\(baseTime)+\(extra)"
                                        }

                                        // Format new time display
                                        var newTimeDisplay = "\(newElapsed)"
                                        if let extra = newExtra, extra > 0 {
                                            // Check the match status to determine which half we're in
                                            let status = updatedFixture.status.short ?? ""
                                            let baseTime: Int

                                            // Determine the base time based on match status and elapsed time
                                            if status == "1H" || status == "HT" {
                                                // First half or halftime
                                                baseTime = 45
                                            } else if status == "2H" {
                                                // Second half
                                                baseTime = 90
                                            } else if status == "ET" && newElapsed <= 105 {
                                                // First half of extra time
                                                baseTime = 105
                                            } else if status == "ET" {
                                                // Second half of extra time
                                                baseTime = 120
                                            } else if newElapsed <= 45 {
                                                // Fallback for first half
                                                baseTime = 45
                                            } else {
                                                // Fallback for second half or later
                                                baseTime = 90
                                            }

                                            newTimeDisplay = "\(baseTime)+\(extra)"
                                        }

                                        Logger.info("Fixture \(fixture.id) status changed: \(oldStatus) -> \(newStatus), time: \(oldTimeDisplay)' -> \(newTimeDisplay)'", category: .data)
                                    }

                                    if updatedFixture.goals.home != fixture.goals.home ||
                                       updatedFixture.goals.away != fixture.goals.away {
                                        Logger.info("Fixture \(fixture.id) score changed: \(fixture.goals.home ?? 0)-\(fixture.goals.away ?? 0) -> \(updatedFixture.goals.home ?? 0)-\(updatedFixture.goals.away ?? 0)", category: .data)
                                    }

                                    updatedLeagueFixtures[fixtureIndex] = updatedFixture
                                    updatedFixtureIds.insert(fixture.id)
                                    hasUpdates = true
                                }
                            }

                            // Only update if there were changes
                            if hasUpdates {
                                // Sort fixtures by kickoff date to maintain consistent order
                                let sortedFixtures = sortFixturesByKickoffDate(updatedLeagueFixtures)

                                let updatedLeague = LeagueWithFixtures(
                                    league: league.league,
                                    fixtures: sortedFixtures
                                )

                                leaguesWithFixtures[leagueIndex] = updatedLeague
                                Logger.info("Updated fixtures for league: \(league.league.name ?? "Unknown")", category: .data)
                            }
                        }
                    }
                }

                // Reset flags
                self.isRefreshingLiveFixtures = false
                self.isAutoRefresh = false

                // Check if we need to continue auto-refresh with the live filter state
                self.checkForLiveFixturesAndSetupAutoRefresh(liveFilterActive: liveFilterActive)

            } catch {
                // Handle errors
                Logger.error("Error refreshing live fixtures: \(error.localizedDescription)", category: .network)

                // Reset flags
                self.isRefreshingLiveFixtures = false
                self.isAutoRefresh = false

                // Even on error, check if we need to continue auto-refresh with the live filter state
                self.checkForLiveFixturesAndSetupAutoRefresh(liveFilterActive: liveFilterActive)
            }
        }
    }

    // Update live fixtures using direct fixture data
    @MainActor
    private func updateLiveFixturesById(_ updatedFixtures: [Fixture], liveFilterActive: Bool = false) {
        // Create a dictionary of fixtures by ID for quick lookup
        let fixturesById = Dictionary(uniqueKeysWithValues: updatedFixtures.map { ($0.id, $0) })

        Logger.info("Processing socket update for \(updatedFixtures.count) fixtures - prioritizing real-time data", category: .data)

        // Update our existing leagues with the updated fixture data
        for (leagueIndex, league) in leaguesWithFixtures.enumerated() {
            var updatedLeagueFixtures = league.fixtures
            var hasUpdates = false

            // Update fixtures that match IDs in our updated data
            for (fixtureIndex, fixture) in updatedLeagueFixtures.enumerated() {
                if let updatedFixture = fixturesById[fixture.id] {
                    // Always accept socket updates as they are the most current
                    // Socket updates should take priority over any cached or API data
                    // Log the status change if it's different
                    if updatedFixture.status.short != fixture.status.short ||
                       updatedFixture.status.elapsed != fixture.status.elapsed ||
                       updatedFixture.status.extra != fixture.status.extra {
                        // Create more detailed log message that includes extra time if present
                        let oldStatus = "\(fixture.status.short ?? "nil")"
                        let newStatus = "\(updatedFixture.status.short ?? "nil")"

                        // Add detailed elapsed time info
                        let oldElapsed = fixture.status.elapsed ?? 0
                        let oldExtra = fixture.status.extra
                        let newElapsed = updatedFixture.status.elapsed ?? 0
                        let newExtra = updatedFixture.status.extra

                        // Format old time display
                        var oldTimeDisplay = "\(oldElapsed)"
                        if let extra = oldExtra, extra > 0 {
                            // Check the match status to determine which half we're in
                            let status = fixture.status.short ?? ""
                            let baseTime: Int

                            // Determine the base time based on match status and elapsed time
                            if status == "1H" || status == "HT" {
                                // First half or halftime
                                baseTime = 45
                            } else if status == "2H" {
                                // Second half
                                baseTime = 90
                            } else if status == "ET" && oldElapsed <= 105 {
                                // First half of extra time
                                baseTime = 105
                            } else if status == "ET" {
                                // Second half of extra time
                                baseTime = 120
                            } else if oldElapsed <= 45 {
                                // Fallback for first half
                                baseTime = 45
                            } else {
                                // Fallback for second half or later
                                baseTime = 90
                            }

                            oldTimeDisplay = "\(baseTime)+\(extra)"
                        }

                        // Format new time display
                        var newTimeDisplay = "\(newElapsed)"
                        if let extra = newExtra, extra > 0 {
                            // Check the match status to determine which half we're in
                            let status = updatedFixture.status.short ?? ""
                            let baseTime: Int

                            // Determine the base time based on match status and elapsed time
                            if status == "1H" || status == "HT" {
                                // First half or halftime
                                baseTime = 45
                            } else if status == "2H" {
                                // Second half
                                baseTime = 90
                            } else if status == "ET" && newElapsed <= 105 {
                                // First half of extra time
                                baseTime = 105
                            } else if status == "ET" {
                                // Second half of extra time
                                baseTime = 120
                            } else if newElapsed <= 45 {
                                // Fallback for first half
                                baseTime = 45
                            } else {
                                // Fallback for second half or later
                                baseTime = 90
                            }

                            newTimeDisplay = "\(baseTime)+\(extra)"
                        }

                        Logger.info("Fixture \(fixture.id) status changed: \(oldStatus) -> \(newStatus), time: \(oldTimeDisplay)' -> \(newTimeDisplay)'", category: .data)
                    }

                    // Log score changes if they're different
                    if updatedFixture.goals.home != fixture.goals.home ||
                       updatedFixture.goals.away != fixture.goals.away {
                        Logger.info("Fixture \(fixture.id) score changed: \(fixture.goals.home ?? 0)-\(fixture.goals.away ?? 0) -> \(updatedFixture.goals.home ?? 0)-\(updatedFixture.goals.away ?? 0)", category: .data)
                    }

                    updatedLeagueFixtures[fixtureIndex] = updatedFixture
                    hasUpdates = true
                }
            }

            // Only update the league if we made changes
            if hasUpdates {
                // Sort fixtures by kickoff date to maintain consistent order
                let sortedFixtures = sortFixturesByKickoffDate(updatedLeagueFixtures)

                let updatedLeague = LeagueWithFixtures(
                    league: league.league,
                    fixtures: sortedFixtures
                )

                leaguesWithFixtures[leagueIndex] = updatedLeague
                Logger.info("Updated fixtures for league: \(league.league.name ?? "Unknown")", category: .data)
            }
        }

        // Check if we need to continue auto-refresh
        checkForLiveFixturesAndSetupAutoRefresh(liveFilterActive: liveFilterActive)
    }

    // Update only the live fixtures in our existing data
    @MainActor
    private func updateLiveFixtures(with liveLeagues: [LeagueWithFixtures], liveFilterActive: Bool = false) {
        // Create a dictionary of live fixtures by league ID for quick lookup
        var liveFixturesByLeague: [Int: [Fixture]] = [:]
        var totalLiveFixtures = 0

        for liveLeague in liveLeagues {
            liveFixturesByLeague[liveLeague.league.id] = liveLeague.fixtures
            totalLiveFixtures += liveLeague.fixtures.count
        }

        Logger.info("Updating \(totalLiveFixtures) live fixtures across \(liveLeagues.count) leagues", category: .data)

        // Update our existing leagues with the live fixture data
        for (index, league) in leaguesWithFixtures.enumerated() {
            if let liveFixtures = liveFixturesByLeague[league.league.id] {
                // Create a dictionary of live fixtures by fixture ID for quick lookup
                let liveFixturesById = Dictionary(uniqueKeysWithValues: liveFixtures.map { ($0.id, $0) })
                var updatedCount = 0

                // Update only the fixtures that are live
                var updatedFixtures = league.fixtures

                for (fixtureIndex, fixture) in updatedFixtures.enumerated() {
                    if let liveFixture = liveFixturesById[fixture.id] {
                        // Log the status change if it's different
                        if liveFixture.status.short != fixture.status.short ||
                           liveFixture.status.elapsed != fixture.status.elapsed ||
                           liveFixture.status.extra != fixture.status.extra {
                            // Create more detailed log message that includes extra time if present
                            let oldStatus = "\(fixture.status.short ?? "nil")"
                            let newStatus = "\(liveFixture.status.short ?? "nil")"

                            // Add detailed elapsed time info
                            let oldElapsed = fixture.status.elapsed ?? 0
                            let oldExtra = fixture.status.extra
                            let newElapsed = liveFixture.status.elapsed ?? 0
                            let newExtra = liveFixture.status.extra

                            // Format old time display
                            var oldTimeDisplay = "\(oldElapsed)"
                            if let extra = oldExtra, extra > 0 {
                                let baseTime = oldElapsed <= 60 ? 45 : 90
                                oldTimeDisplay = "\(baseTime)+\(extra)"
                            }

                            // Format new time display
                            var newTimeDisplay = "\(newElapsed)"
                            if let extra = newExtra, extra > 0 {
                                let baseTime = newElapsed <= 60 ? 45 : 90
                                newTimeDisplay = "\(baseTime)+\(extra)"
                            }

                            Logger.info("Fixture \(fixture.id) status changed: \(oldStatus) -> \(newStatus), time: \(oldTimeDisplay)' -> \(newTimeDisplay)'", category: .data)
                        }

                        // Log score changes if they're different
                        if liveFixture.goals.home != fixture.goals.home ||
                           liveFixture.goals.away != fixture.goals.away {
                            Logger.info("Fixture \(fixture.id) score changed: \(fixture.goals.home ?? 0)-\(fixture.goals.away ?? 0) -> \(liveFixture.goals.home ?? 0)-\(liveFixture.goals.away ?? 0)", category: .data)
                        }

                        updatedFixtures[fixtureIndex] = liveFixture
                        updatedCount += 1
                    }
                }

                if updatedCount > 0 {
                    Logger.info("Updated \(updatedCount) fixtures in league: \(league.league.name ?? "Unknown")", category: .data)

                    // Sort fixtures by kickoff date to maintain consistent order
                    let sortedFixtures = sortFixturesByKickoffDate(updatedFixtures)

                    // Create a new league with the updated fixtures
                    let updatedLeague = LeagueWithFixtures(
                        league: league.league,
                        fixtures: sortedFixtures
                    )

                    // Update the league in our array
                    leaguesWithFixtures[index] = updatedLeague
                }
            }
        }

        // Check if we need to continue auto-refresh
        checkForLiveFixturesAndSetupAutoRefresh(liveFilterActive: liveFilterActive)
    }

    // MARK: - Live Filter Support

    /// Fetch all live fixtures regardless of pagination when live filter is toggled on
    func fetchAllLiveFixtures() {
        // Show loading indicator
        isLoading = true
        errorMessage = nil

        Logger.info("Fetching all live fixtures for live filter", category: .network)

        // Use Task for async/await pattern
        Task {
            do {
                // Use the live=true parameter to get all live fixtures in one request
                let liveFixtures = try await AsyncAPIService.shared.fetchData(
                    endpoint: "/fixtures",
                    parameters: ["live": "true"],
                    forceRefresh: true,
                    cacheDuration: 0 // No cache - always get fresh data
                ) as [Fixture]

                Logger.info("Successfully fetched \(liveFixtures.count) live fixtures for live filter", category: .network)

                // If no live fixtures found, keep the existing fixtures but set loading to false
                // This allows the UI to show the "No live matches" message
                if liveFixtures.isEmpty {
                    isLoading = false
                    // We intentionally don't return here, allowing the code to continue
                    // and set leaguesWithFixtures to an empty array, which will trigger
                    // the "No live matches" message in the UI
                }

                // Group live fixtures by league
                let liveFixturesByLeague = Dictionary(grouping: liveFixtures) { $0.league.id }

                // Create LeagueWithFixtures objects
                var liveLeagues: [LeagueWithFixtures] = []

                for (_, fixtures) in liveFixturesByLeague {
                    // Use the first fixture to get league info
                    guard let firstFixture = fixtures.first else { continue }

                    // Sort fixtures by kickoff date to ensure consistent ordering
                    let sortedFixtures = sortFixturesByKickoffDate(fixtures)

                    // Create a new LeagueWithFixtures with sorted fixtures
                    let league = LeagueWithFixtures(
                        league: firstFixture.league,
                        fixtures: sortedFixtures
                    )

                    liveLeagues.append(league)
                }

                // Sort leagues by tier and priority to match backend sorting
                // This ensures leagues are displayed in the same order as in normal view
                liveLeagues.sort { (a, b) -> Bool in
                    // Get league tiers (lower tier number = higher popularity)
                    let tierA = getLeagueTier(leagueId: a.league.id)
                    let tierB = getLeagueTier(leagueId: b.league.id)

                    // First sort by tier
                    if tierA != tierB {
                        return tierA < tierB // Lower tier number first (Tier 1 before Tier 2)
                    }

                    // If tiers are the same, sort by priority within tier
                    let priorityA = getLeaguePriority(leagueId: a.league.id)
                    let priorityB = getLeaguePriority(leagueId: b.league.id)

                    if priorityA != priorityB {
                        return priorityA < priorityB // Lower priority number first
                    }

                    // If priorities are the same, sort by country
                    let countryA = a.league.country ?? ""
                    let countryB = b.league.country ?? ""
                    if countryA != countryB {
                        return countryA < countryB
                    }

                    // Finally, sort by league name
                    let nameA = a.league.name ?? ""
                    let nameB = b.league.name ?? ""
                    return nameA < nameB
                }

                // Process league flags
                let processedLiveLeagues = self.processLeagueFlags(leagues: liveLeagues)

                // Replace the existing leagues with just the live ones
                self.leaguesWithFixtures = processedLiveLeagues

                // Reset pagination since we're now showing a filtered view
                self.currentPage = 1
                self.hasMorePages = false

                // Reset loading state
                self.isLoading = false

                // Setup auto-refresh for live fixtures
                self.setupAutoRefreshIfNeeded(liveFilterActive: true)

            } catch {
                // Handle errors
                isLoading = false
                errorMessage = error.localizedDescription
                Logger.error("Error fetching live fixtures for live filter: \(error.localizedDescription)", category: .network)
            }
        }
    }

    // Check for live fixtures and setup auto-refresh after loading data
    private func checkForLiveFixturesAndSetupAutoRefresh(liveFilterActive: Bool = false) {
        // Check if we have any live fixtures or if live filter is active
        if hasLiveFixtures || liveFilterActive {
            // Setup auto-refresh with live filter state
            setupAutoRefreshIfNeeded(liveFilterActive: liveFilterActive)
        } else {
            // Stop auto-refresh if there are no live fixtures and live filter is not active
            stopAutoRefresh()
        }
    }

    // MARK: - Cache Optimization Helpers

    /// Check if there are any fixtures that might have been live recently for the given date
    /// This helps prevent showing outdated statuses for matches that have finished
    func checkForRecentlyLiveFixtures(in date: Date) -> Bool {
        // Get the current time
        let now = Date()

        // Only check for recently live fixtures if the date is within the last 24 hours
        let calendar = Calendar.current
        let yesterday = calendar.date(byAdding: .day, value: -1, to: now) ?? now

        // If the date is older than yesterday, no need to check
        if date < yesterday {
            return false
        }

        // Check if we have any fixtures that might be in the late stages of a match
        // or might have recently finished
        let matchEndWindow = calendar.date(byAdding: .hour, value: -3, to: now) ?? now

        // If the date is today or yesterday, and it's within the last 3 hours window,
        // consider it as potentially having recently live fixtures
        return date >= matchEndWindow
    }

    // MARK: - League Tier Helpers

    /// Get the tier of a league (1-6), with 1 being highest priority
    /// This matches the backend's leagueTiers.ts configuration
    private func getLeagueTier(leagueId: Int) -> Int {
        // Tier 1: Pinnacle Global & Continental Competitions
        let tier1Leagues = [1, 8, 4, 9, 6, 7, 2, 3, 13]

        // Tier 2: The "Big 5" European Leagues
        let tier2Leagues = [39, 140, 78, 135, 61]

        // Tier 3: Major Domestic Cups, Other Strong Continental & Key Leagues
        let tier3Leagues = [45, 143, 81, 137, 848, 66, 48, 71, 128, 253, 307, 88, 94, 203, 15, 32, 34]

        // Tier 4: Strong Second Tiers, Other Notable Leagues & Cups
        let tier4Leagues = [40, 79, 141, 136, 62, 188, 262, 17, 12, 856, 11, 73, 130, 144, 179, 408, 181, 90, 96, 206, 218, 207, 197, 106, 119, 235, 286, 210, 333, 283, 271, 345, 332, 315, 239, 265, 281, 250, 252, 268, 270, 242, 292, 98, 169, 305, 301, 200, 202, 186, 233, 288, 525, 254, 44, 480, 524, 22]

        // Check which tier the league belongs to
        if tier1Leagues.contains(leagueId) {
            return 1
        } else if tier2Leagues.contains(leagueId) {
            return 2
        } else if tier3Leagues.contains(leagueId) {
            return 3
        } else if tier4Leagues.contains(leagueId) {
            return 4
        }

        // Default to tier 5 for other leagues
        return 5
    }

    /// Helper method to sort fixtures consistently by kickoff date
    /// Use this method whenever fixtures need to be sorted to ensure consistent ordering
    /// When kickoff dates are identical, uses fixture ID as a secondary sort key for stable ordering
    private func sortFixturesByKickoffDate(_ fixtures: [Fixture]) -> [Fixture] {
        // Check for fixtures with identical kickoff times and log them for debugging
        let kickoffGroups = Dictionary(grouping: fixtures) { $0.kickoffDate }
        for (kickoffTime, fixturesWithSameTime) in kickoffGroups {
            if fixturesWithSameTime.count > 1 {
                let fixtureInfo = fixturesWithSameTime.map { fixture in
                    return "\(fixture.teams.home?.name ?? "Unknown") vs \(fixture.teams.away?.name ?? "Unknown") (ID: \(fixture.id))"
                }.joined(separator: ", ")

                Logger.info("Multiple fixtures with same kickoff time (\(kickoffTime)): \(fixtureInfo)", category: .data)
            }
        }

        return fixtures.sorted { fixture1, fixture2 in
            // First compare by kickoff date
            if fixture1.kickoffDate != fixture2.kickoffDate {
                return fixture1.kickoffDate < fixture2.kickoffDate
            }

            // If kickoff dates are identical, sort by fixture ID for stable ordering
            let result = fixture1.id < fixture2.id

            // Log when we're using the secondary sort key
            if fixture1.kickoffDate == fixture2.kickoffDate {
                Logger.debug("Using fixture ID as tiebreaker for same kickoff time: \(fixture1.teams.home?.name ?? "Unknown") vs \(fixture1.teams.away?.name ?? "Unknown") (ID: \(fixture1.id)) and \(fixture2.teams.home?.name ?? "Unknown") vs \(fixture2.teams.away?.name ?? "Unknown") (ID: \(fixture2.id)) - Result: \(result ? "first before second" : "second before first")", category: .data)
            }

            return result
        }
    }

    /// Get the priority of a league within its tier
    /// This matches the backend's leaguePriorityMap configuration
    private func getLeaguePriority(leagueId: Int) -> Int {
        // Define priorities for key leagues in each tier
        let priorityMap: [Int: Int] = [
            // Tier 1
            1: 1, 8: 2, 4: 3, 2: 4, 3: 5, 9: 6, 13: 7, 6: 8, 7: 9,

            // Tier 2
            39: 1, 140: 2, 78: 3, 135: 4, 61: 5,

            // Tier 3
            45: 1, 143: 2, 81: 3, 137: 4, 848: 5, 66: 6, 48: 7, 71: 8, 128: 9, 253: 10, 307: 11, 88: 12, 94: 13, 203: 14, 15: 15, 32: 16, 34: 17,

            // Tier 4 (partial list of most important ones)
            40: 1, 79: 2, 141: 3, 136: 4, 62: 5, 188: 6, 262: 7, 17: 8, 12: 9, 856: 10
        ]

        // Return the priority if defined, otherwise use a high number (lower priority)
        return priorityMap[leagueId] ?? 999
    }
}
