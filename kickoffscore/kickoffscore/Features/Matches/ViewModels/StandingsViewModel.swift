import Foundation
import Combine

// Enum for standings filter options
enum StandingsFilter: String, CaseIterable {
    case overall = "Overall"
    case home = "Home"
    case away = "Away"
}

// Enum for standings view mode options
enum StandingsViewMode: String, CaseIterable {
    case short = "Short"
    case full = "Full"
    case form = "Form"
}

@MainActor
class StandingsViewModel: ObservableObject {
    // Published properties for UI updates
    @Published var standings: [[TeamStanding]] = []
    @Published var filteredStandings: [[TeamStanding]] = []
    @Published var selectedFilter: StandingsFilter = .overall
    @Published var selectedViewMode: StandingsViewMode = .short
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil

    // Fixture information
    private let leagueId: Int
    private let season: Int?
    private var isCupCompetition: Bool = false

    // Initialize with fixture to extract league and season
    init(fixture: Fixture) {
        self.leagueId = fixture.league.id

        // Get the current year
        let currentYear = Calendar.current.component(.year, from: Date())

        // Use the fixture's season if available, otherwise determine appropriate season
        if let fixtureSeason = fixture.league.season {
            self.season = fixtureSeason
        } else {
            // For FIFA Club World Cup, default to 2025
            if fixture.league.id == 15 {
                self.season = 2025
            } else {
                // For other leagues, try current year first
                self.season = currentYear
            }
        }

        // Check if this is a cup competition based on the round name or league ID
        if let round = fixture.league.round?.lowercased() {
            if round.contains("final") ||
               round.contains("semi") ||
               round.contains("quarter") ||
               round.contains("round of") ||
               round.contains("group") {
                isCupCompetition = true
            }
        }
        
        // Also check for specific cup competition league IDs
        let cupCompetitionLeagueIds = [
            15, // FIFA Club World Cup
            2,  // UEFA Champions League
            3,  // UEFA Europa League
            848, // UEFA Europa Conference League
            1,  // World Cup
            8,  // World Cup
            4,  // European Championship (UEFA Euro)
            9,  // Copa America (CONMEBOL)
            6,  // Africa Cup of Nations
            7,  // AFC Asian Cup
            17, // AFC Champions League Elite
            1132, // AFC Champions League Two
            13, // Copa Libertadores (CONMEBOL)
            11, // Copa Sudamericana (CONMEBOL)
            12, // CAF Champions League
            22, // CONCACAF Gold Cup
            856 // CONCACAF Champions Cup
        ]
        if cupCompetitionLeagueIds.contains(leagueId) {
            isCupCompetition = true
        }

        // Debug: Print league and season information
        print("StandingsViewModel initialized with leagueId: \(leagueId), season: \(String(describing: season)), isCup: \(isCupCompetition)")
    }

    // Fetch standings data from API
    func fetchStandings() {
        // Always use the season value set in the initializer
        guard let seasonToUse = season else {
            self.errorMessage = "Season information is not available"
            return
        }

        print("Using season: \(seasonToUse) for standings fetch")

        // Debug: Print the API base URL
        print("API Base URL: \(Constants.apiBaseURL)")

        isLoading = true
        errorMessage = nil

        let endpoint = "/standings"
        let parameters = [
            "leagueId": String(leagueId),
            "season": String(seasonToUse)
        ]

        print("Fetching standings for league: \(leagueId), season: \(seasonToUse)")

        // Use [[TeamStanding]] directly instead of StandingResponse
        APIService.shared.fetchData(endpoint: endpoint, parameters: parameters) { [weak self] (result: Result<[[TeamStanding]], APIError>) in
            guard let self = self else { return }

            self.isLoading = false

            switch result {
            case .success(let standingsArray):
                print("Successfully fetched standings for league: \(self.leagueId)")

                // Debug: Print status values for each team
                for (groupIndex, group) in standingsArray.enumerated() {
                    print("Group \(groupIndex) standings:")
                    for standing in group {
                        print("Team: \(standing.team.name), Rank: \(standing.rank), Status: \(standing.status ?? "nil")")
                    }
                }

                // For cup competitions, we need to organize standings by group
                if self.isCupCompetition {
                    // Organize standings by group
                    self.organizeStandingsByGroup(standingsArray)
                } else {
                    self.standings = standingsArray
                }

                // Apply the current filter to the standings
                self.applyFilter()

                // If standings array is empty, set error message
                if self.standings.isEmpty || self.standings[0].isEmpty {
                    self.errorMessage = "No standings available for this league"
                }

            case .failure(let error):
                self.errorMessage = error.localizedDescription
                print("Error fetching standings: \(error.localizedDescription)")

                // If we get a decoding error, try with a different season
                if error.localizedDescription.contains("Decoding Error") {
                    self.tryAlternativeSeason()
                }
            }
        }
    }

    // Try fetching standings with an alternative season
    private func tryAlternativeSeason() {
        // Determine the alternative season based on league type
        let alternativeSeason: Int
        
        // For FIFA Club World Cup and other major tournaments, try 2025
        if leagueId == 15 { // FIFA Club World Cup
            alternativeSeason = 2025
        } else {
            // For other leagues, try 2024 as fallback
            alternativeSeason = 2024
        }
        
        // If current season failed and we haven't tried the alternative yet
        if season != alternativeSeason {
            print("Trying alternative season: \(alternativeSeason)")

            isLoading = true
            errorMessage = nil

            let endpoint = "/standings"
            let parameters = [
                "leagueId": String(leagueId),
                "season": String(alternativeSeason)
            ]

            print("Fetching standings for league: \(leagueId), season: \(alternativeSeason)")

            // Use [[TeamStanding]] directly
            APIService.shared.fetchData(endpoint: endpoint, parameters: parameters) { [weak self] (result: Result<[[TeamStanding]], APIError>) in
                guard let self = self else { return }

                self.isLoading = false

                switch result {
                case .success(let standingsArray):
                    print("Successfully fetched standings with alternative season for league: \(self.leagueId)")

                    // Debug: Print status values for each team with alternative season
                    for (groupIndex, group) in standingsArray.enumerated() {
                        print("Alternative Season - Group \(groupIndex) standings:")
                        for standing in group {
                            print("Team: \(standing.team.name), Rank: \(standing.rank), Status: \(standing.status ?? "nil")")
                        }
                    }

                    // For cup competitions, we need to organize standings by group
                    if self.isCupCompetition {
                        // Organize standings by group
                        self.organizeStandingsByGroup(standingsArray)
                    } else {
                        self.standings = standingsArray
                    }

                    // Apply the current filter to the standings
                    self.applyFilter()

                    // If standings array is empty, set error message
                    if self.standings.isEmpty || self.standings[0].isEmpty {
                        self.errorMessage = "No standings available for this league"
                    }

                case .failure(let error):
                    self.errorMessage = error.localizedDescription
                    print("Error fetching standings with alternative season: \(error.localizedDescription)")
                }
            }
        }
    }

    // Set the filter and apply it to the standings
    func setFilter(_ filter: StandingsFilter) {
        selectedFilter = filter
        applyFilter()
    }

    // Apply the current filter to the standings
    func applyFilter() {
        // Create a mutable copy of the standings
        var filteredGroups: [[TeamStanding]] = []

        // Process each group
        for group in standings {
            switch selectedFilter {
            case .overall:
                // For overall, use the original standings as they come from the API
                filteredGroups.append(group)

            case .home:
                // For home, sort by home points (wins * 3 + draws * 1)
                let sortedGroup = group.sorted { team1, team2 in
                    // Calculate home points
                    let points1 = (team1.home.win ?? 0) * 3 + (team1.home.draw ?? 0)
                    let points2 = (team2.home.win ?? 0) * 3 + (team2.home.draw ?? 0)

                    if points1 != points2 {
                        return points1 > points2 // Higher points first
                    }

                    // If points are equal, use goal difference
                    let goalDiff1 = (team1.home.goals?.for_ ?? 0) - (team1.home.goals?.against ?? 0)
                    let goalDiff2 = (team2.home.goals?.for_ ?? 0) - (team2.home.goals?.against ?? 0)

                    if goalDiff1 != goalDiff2 {
                        return goalDiff1 > goalDiff2 // Better goal difference first
                    }

                    // If goal difference is equal, use goals scored
                    return (team1.home.goals?.for_ ?? 0) > (team2.home.goals?.for_ ?? 0) // More goals first
                }
                filteredGroups.append(sortedGroup)

            case .away:
                // For away, sort by away points (wins * 3 + draws * 1)
                let sortedGroup = group.sorted { team1, team2 in
                    // Calculate away points
                    let points1 = (team1.away.win ?? 0) * 3 + (team1.away.draw ?? 0)
                    let points2 = (team2.away.win ?? 0) * 3 + (team2.away.draw ?? 0)

                    if points1 != points2 {
                        return points1 > points2 // Higher points first
                    }

                    // If points are equal, use goal difference
                    let goalDiff1 = (team1.away.goals?.for_ ?? 0) - (team1.away.goals?.against ?? 0)
                    let goalDiff2 = (team2.away.goals?.for_ ?? 0) - (team2.away.goals?.against ?? 0)

                    if goalDiff1 != goalDiff2 {
                        return goalDiff1 > goalDiff2 // Better goal difference first
                    }

                    // If goal difference is equal, use goals scored
                    return (team1.away.goals?.for_ ?? 0) > (team2.away.goals?.for_ ?? 0) // More goals first
                }
                filteredGroups.append(sortedGroup)
            }
        }

        // Update the filtered standings
        filteredStandings = filteredGroups
    }

    // Find the position of a specific team in the standings
    func findTeamPosition(teamId: Int) -> (group: Int, position: Int)? {
        for (groupIndex, group) in filteredStandings.enumerated() {
            if let teamPosition = group.firstIndex(where: { $0.team.id == teamId }) {
                return (groupIndex, teamPosition)
            }
        }
        return nil
    }

    // Get standings for a specific team
    func getTeamStanding(teamId: Int) -> TeamStanding? {
        guard let position = findTeamPosition(teamId: teamId) else {
            return nil
        }
        return filteredStandings[position.group][position.position]
    }

    // Organize standings by group for cup competitions
    private func organizeStandingsByGroup(_ standingsArray: [[TeamStanding]]) {
        // Flatten the array to get all teams
        let allTeams = standingsArray.flatMap { $0 }
        
        print("Organizing \(allTeams.count) teams by group")
        for team in allTeams {
            print("Team: \(team.team.name), Group: \(team.group ?? "nil")")
        }

        // Group teams by their group property
        let groupedTeams = Dictionary(grouping: allTeams) { team in
            return team.group ?? "Unknown Group"
        }
        
        print("Found \(groupedTeams.count) groups: \(Array(groupedTeams.keys))")

        // Sort the groups by name
        let sortedGroups = groupedTeams.sorted { group1, group2 in
            // Extract group letters/numbers for sorting
            let group1Name = group1.key
            let group2Name = group2.key

            // Try to extract group letters (e.g., "Group A", "Group B")
            if group1Name.contains("Group") && group2Name.contains("Group") {
                // Extract the last character of each group name
                if let lastChar1 = group1Name.last, let lastChar2 = group2Name.last {
                    return lastChar1 < lastChar2
                }
            }

            // Default to alphabetical sorting
            return group1Name < group2Name
        }

        // Create a new array of standings organized by group
        var organizedStandings: [[TeamStanding]] = []

        for (_, teams) in sortedGroups {
            // Sort teams within each group by rank
            let sortedTeams = teams.sorted { $0.rank < $1.rank }
            organizedStandings.append(sortedTeams)
        }

        // Update the standings property
        self.standings = organizedStandings
    }
}
