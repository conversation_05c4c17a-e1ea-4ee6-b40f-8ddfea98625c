import SwiftUI

struct RegisterView: View {
    @ObservedObject var viewModel: AuthViewModel
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var name = ""
    @Environment(\.dismiss) private var dismiss

    // Validation
    private var passwordsMatch: Bool {
        password == confirmPassword
    }

    private var isFormValid: Bool {
        !email.isEmpty && !password.isEmpty && !confirmPassword.isEmpty && passwordsMatch && password.count >= 6
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Apply dynamic background based on color scheme
                Color.primary.opacity(0.03).ignoresSafeArea()

                ScrollView {
                    VStack(spacing: AppLayout.spacingL) {
                        // Logo or App Name
                        Text("KickoffScore")
                            .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                            .foregroundColor(AppColors.Brand.primary)
                            .padding(.top, AppLayout.spacingXL)

                        // Register Form
                        VStack(spacing: AppLayout.spacingM) {
                            // Name Field (Optional)
                            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                                Text("Name (Optional)")
                                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                    .foregroundColor(Color(UIColor.label))

                                TextField("Enter your name", text: $name)
                                    .autocapitalization(.words)
                                    .padding()
                                    .background(AppColors.secondaryBackground)
                                    .cornerRadius(AppLayout.cornerRadiusM)
                            }

                            // Email Field
                            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                                Text("Email")
                                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                    .foregroundColor(Color(UIColor.label))

                                TextField("Enter your email", text: $email)
                                    .keyboardType(.emailAddress)
                                    .autocapitalization(.none)
                                    .disableAutocorrection(true)
                                    .padding()
                                    .background(AppColors.secondaryBackground)
                                    .cornerRadius(AppLayout.cornerRadiusM)
                            }

                            // Password Field
                            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                                Text("Password")
                                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                    .foregroundColor(Color(UIColor.label))

                                SecureField("Enter your password", text: $password)
                                    .padding()
                                    .background(AppColors.secondaryBackground)
                                    .cornerRadius(AppLayout.cornerRadiusM)

                                if !password.isEmpty && password.count < 6 {
                                    Text("Password must be at least 6 characters")
                                        .font(AppTypography.dynamicFont(style: .caption))
                                        .foregroundColor(Color.red)
                                        .padding(.top, 4)
                                }
                            }

                            // Confirm Password Field
                            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                                Text("Confirm Password")
                                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                    .foregroundColor(Color(UIColor.label))

                                SecureField("Confirm your password", text: $confirmPassword)
                                    .padding()
                                    .background(AppColors.secondaryBackground)
                                    .cornerRadius(AppLayout.cornerRadiusM)

                                if !confirmPassword.isEmpty && !passwordsMatch {
                                    Text("Passwords do not match")
                                        .font(AppTypography.dynamicFont(style: .caption))
                                        .foregroundColor(Color.red)
                                        .padding(.top, 4)
                                }
                            }

                            // Error Message
                            if let errorMessage = viewModel.errorMessage {
                                Text(errorMessage)
                                    .font(AppTypography.dynamicFont(style: .caption))
                                    .foregroundColor(AppColors.error)
                                    .padding(.top, AppLayout.spacingXS)
                            }

                            // Register Button
                            Button {
                                viewModel.register(email: email, password: password, name: name.isEmpty ? nil : name)
                            } label: {
                                if viewModel.isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: AppColors.primaryButtonText))
                                } else {
                                    Text("Register")
                                        .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                                        .foregroundColor(AppColors.primaryButtonText)
                                }
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(isFormValid ? AppColors.Brand.primary : AppColors.Brand.primary.opacity(0.7))
                            .cornerRadius(AppLayout.cornerRadiusM)
                            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                            .disabled(viewModel.isLoading || !isFormValid)
                        }
                        .padding(AppLayout.spacingL)
                        .background(AppColors.secondaryBackground)
                        .cornerRadius(AppLayout.cornerRadiusL)
                        .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
                        .padding(.horizontal, AppLayout.spacingM)

                        Spacer()
                    }
                    .padding(.bottom, AppLayout.spacingXL)
                }
            }
            .navigationTitle("Register")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar(content: {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "xmark")
                            .foregroundColor(AppColors.Brand.primary)
                            .font(.system(size: 16, weight: .medium))
                            .padding(8)
                            .background(Color(UIColor.tertiarySystemBackground))
                            .clipShape(Circle())
                    }
                }
            })
            .onChange(of: viewModel.isAuthenticated) { newValue in
                if newValue {
                    dismiss()
                }
            }
        }
    }
}

#Preview {
    RegisterView(viewModel: AuthViewModel())
}
