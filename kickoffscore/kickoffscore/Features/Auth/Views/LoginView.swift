import SwiftUI

struct LoginView: View {
    @ObservedObject var viewModel: AuthViewModel
    @State private var email = ""
    @State private var password = ""
    @State private var showRegister = false
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ZStack {
                // Apply dynamic background based on color scheme
                Color.primary.opacity(0.03).ignoresSafeArea()

                ScrollView {
                    VStack(spacing: AppLayout.spacingL) {
                        // Logo or App Name
                        Text("KickoffScore")
                            .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                            .foregroundColor(AppColors.Brand.primary)
                            .padding(.top, AppLayout.spacingXL)

                        // Login Form
                        VStack(spacing: AppLayout.spacingM) {
                            // Email Field
                            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                                Text("Email")
                                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                    .foregroundColor(Color(UIColor.label))

                                TextField("Enter your email", text: $email)
                                    .keyboardType(.emailAddress)
                                    .autocapitalization(.none)
                                    .disableAutocorrection(true)
                                    .padding()
                                    .background(AppColors.secondaryBackground)
                                    .cornerRadius(AppLayout.cornerRadiusM)
                            }

                            // Password Field
                            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                                Text("Password")
                                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                    .foregroundColor(Color(UIColor.label))

                                SecureField("Enter your password", text: $password)
                                    .padding()
                                    .background(AppColors.secondaryBackground)
                                    .cornerRadius(AppLayout.cornerRadiusM)
                            }

                            // Error Message
                            if let errorMessage = viewModel.errorMessage {
                                Text(errorMessage)
                                    .font(AppTypography.dynamicFont(style: .caption))
                                    .foregroundColor(Color.red)
                                    .padding(.top, AppLayout.spacingXS)
                            }

                            // Login Button
                            Button {
                                viewModel.login(email: email, password: password)
                            } label: {
                                if viewModel.isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: AppColors.primaryButtonText))
                                } else {
                                    Text("Login")
                                        .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                                        .foregroundColor(AppColors.primaryButtonText)
                                }
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(AppColors.Brand.primary)
                            .cornerRadius(AppLayout.cornerRadiusM)
                            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                            .disabled(viewModel.isLoading || email.isEmpty || password.isEmpty)
                            .opacity(email.isEmpty || password.isEmpty ? 0.7 : 1)

                            // Register Link
                            Button {
                                showRegister = true
                            } label: {
                                Text("Don't have an account? Register")
                                    .font(AppTypography.dynamicFont(style: .subheadline))
                                    .foregroundColor(AppColors.Brand.secondary)
                            }
                            .padding(.top, AppLayout.spacingS)
                        }
                        .padding(AppLayout.spacingL)
                        .background(AppColors.secondaryBackground)
                        .cornerRadius(AppLayout.cornerRadiusL)
                        .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
                        .padding(.horizontal, AppLayout.spacingM)

                        Spacer()
                    }
                    .padding(.bottom, AppLayout.spacingXL)
                }
            }
            .navigationTitle("Login")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar(content: {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "xmark")
                            .foregroundColor(AppColors.Brand.primary)
                            .font(.system(size: 16, weight: .medium))
                            .padding(8)
                            .background(Color(UIColor.tertiarySystemBackground))
                            .clipShape(Circle())
                    }
                }
            })
            .onChange(of: viewModel.isAuthenticated) { newValue in
                if newValue {
                    dismiss()
                }
            }
            .sheet(isPresented: $showRegister) {
                RegisterView(viewModel: viewModel)
            }
        }
    }
}

#Preview {
    LoginView(viewModel: AuthViewModel())
}
