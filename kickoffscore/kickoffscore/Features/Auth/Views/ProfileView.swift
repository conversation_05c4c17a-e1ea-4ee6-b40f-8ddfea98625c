import SwiftUI
import UIKit

struct ProfileView: View {
    @ObservedObject var viewModel: AuthViewModel
    @StateObject private var votesViewModel = UserVotesViewModel()
    @EnvironmentObject private var navigationState: NavigationStateManager
    @State private var showLogin = false
    @State private var showImagePicker = false
    @State private var selectedImage: UIImage?
    @State private var isUploadingImage = false
    @State private var showImageUploadError = false
    @State private var imageUploadErrorMessage = ""
    // No longer need dismiss environment since this is now a tab

    // Sample favorite teams (replace with actual data)
    private let favoriteTeams = ["chelsea", "nepal", "champions-league", "shakhtar", "premier-league"]

    // Function to upload the selected image
    private func uploadProfileImage() {
        guard let image = selectedImage else { return }

        isUploadingImage = true

        viewModel.uploadProfileImage(image) { success in
            isUploadingImage = false

            if !success {
                imageUploadErrorMessage = viewModel.errorMessage ?? "Failed to upload image"
                showImageUploadError = true
            }
        }
    }

    var body: some View {
        ZStack {
            // Use brand background that adapts to dark/light mode
            AppColors.background.ignoresSafeArea()

            VStack(spacing: 0) {
                // Custom header bar
                ZStack {
                    // Title (centered)
                    Text("Profile")
                        .font(AppTypography.dynamicFont(style: .headline, weight: .semibold))
                        .foregroundColor(Color(UIColor.label))
                        .frame(maxWidth: .infinity, alignment: .center)

                    // Action buttons (right aligned)
                    HStack {
                        Spacer()

                        HStack(spacing: 12) {
                            Button {
                                // Share action
                            } label: {
                                Image(systemName: "square.and.arrow.up")
                                    .font(.system(size: 16))
                                    .foregroundColor(Color(UIColor.label))
                                    .frame(width: 36, height: 36)
                                    .background(Color(UIColor.tertiarySystemFill))
                                    .clipShape(Circle())
                            }

                            Button {
                                // Settings action
                            } label: {
                                Image(systemName: "gearshape")
                                    .font(.system(size: 16))
                                    .foregroundColor(Color(UIColor.label))
                                    .frame(width: 36, height: 36)
                                    .background(Color(UIColor.tertiarySystemFill))
                                    .clipShape(Circle())
                            }
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 8)
                .padding(.bottom, 8)

                ScrollView {
                    VStack(spacing: 24) {

                        if viewModel.isAuthenticated, let user = viewModel.currentUser {
                            // Logged in user profile

                            // Profile image with stats - centered at top
                            VStack(spacing: 4) {
                                // Profile image - clickable to upload new image
                                Button {
                                    showImagePicker = true
                                } label: {
                                    ZStack {
                                        // Dark circle background
                                         Circle()
                                            .fill(Color(UIColor.tertiarySystemBackground))
                                            .frame(width: 100, height: 100)

                                        // White semicircle at bottom
                                        Circle()
                                            .fill(Color.white)
                                            .frame(width: 100, height: 100)
                                            .offset(y: 50)
                                            .clipShape(Circle())

                                        // Profile image or placeholder
                                        if let selectedImage = selectedImage {
                                            // Show selected image (not yet uploaded)
                                            Image(uiImage: selectedImage)
                                                .resizable()
                                                .scaledToFill()
                                                .frame(width: 80, height: 80)
                                                .clipShape(Circle())
                                        } else if let profileImageUrl = user.profileImage, !profileImageUrl.isEmpty {
                                            // Show profile image from URL
                                            if let imageUrl = URL(string: profileImageUrl) {
                                                // Use a simpler version of AsyncImage
                                                AsyncImage(url: imageUrl, scale: 1.0) { image in
                                                    image
                                                        .resizable()
                                                        .scaledToFill()
                                                        .frame(width: 80, height: 80)
                                                        .clipShape(Circle())
                                                } placeholder: {
                                                    ProgressView()
                                                        .frame(width: 80, height: 80)
                                                }
                                            } else {
                                                // Fallback if URL is invalid
                                                Image(systemName: "person.fill")
                                                    .font(.system(size: 40))
                                                    .foregroundColor(Color(UIColor.label))
                                                    .frame(width: 80, height: 80)
                                                    .background(Circle().fill(Color.gray.opacity(0.3)))
                                            }
                                        } else {
                                            // Gray circle for the profile
                                            Circle()
                                                .fill(Color(UIColor.tertiarySystemBackground))
                                                .frame(width: 80, height: 80)

                                            // Person icon
                                            Image(systemName: "person.fill")
                                                .font(.system(size: 40))
                                                .foregroundColor(Color(UIColor.label))
                                        }

                                        // Camera icon or loading indicator
                                        if isUploadingImage {
                                            ProgressView()
                                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                            .scaleEffect(1.5)
                                            .frame(width: 30, height: 30)
                                            .background(Circle().fill(AppColors.Brand.primary))
                                                .offset(x: 30, y: 30)
                                        } else {
                                            Image(systemName: "camera.circle.fill")
                                                .font(.system(size: 24))
                                            .foregroundColor(AppColors.background)
                                            .background(Circle().fill(AppColors.Brand.primary))
                                                .offset(x: 30, y: 30)
                                        }
                                    }
                                }
                                .padding(.top, 10)
                                .accessibilityLabel("Upload profile picture")

                                // Stats row (30 points, 1 badge) - exactly as in screenshot
                                HStack(spacing: 8) {
                                    Text("30")
                                        .font(.system(size: 22, weight: .bold))
                                        .foregroundColor(Color(UIColor.label))

                                    Image(systemName: "p.circle.fill")
                                        .font(.system(size: 22))
                                        .foregroundColor(.blue)
                                        .padding(.horizontal, 2)

                                    Text("1")
                                        .font(.system(size: 22, weight: .bold))
                                        .foregroundColor(Color(UIColor.label))

                                    Image(systemName: "star.fill")
                                        .font(AppTypography.dynamicFont(style: .footnote))
                                        .foregroundColor(Color(UIColor.secondaryLabel))
                                }
                                .padding(.top, 8)

                                // Username
                                Text(user.name ?? "Test")
                                    .font(AppTypography.dynamicFont(style: .subheadline))
                                    .foregroundColor(Color(UIColor.secondaryLabel))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.bottom, 10)

                            // Complete profile button - matching screenshot exactly
                            Button {
                                // Action for complete profile
                            } label: {
                                HStack {
                                    Image(systemName: "person.fill")
                                        .foregroundColor(.white)
                                        .frame(width: 30, height: 30)
                                        .background(Color.gray.opacity(0.3))
                                        .clipShape(Circle())

                                    VStack(alignment: .leading, spacing: 2) {
                                        Text("Complete your profile")
                                            .font(AppTypography.dynamicFont(style: .callout, weight: .medium))
                                        .foregroundColor(Color(UIColor.label))

                                        Text("We just need a few more details")
                                            .font(.system(size: 14))
                                            .foregroundColor(Color(UIColor.secondaryLabel))
                                    }

                                    Spacer()

                                    Image(systemName: "chevron.right")
                                        .foregroundColor(Color(UIColor.secondaryLabel))
                                }
                                .padding(.vertical, 16)
                                .padding(.horizontal, 14)
                                .background(AppColors.secondaryBackground)
                                .cornerRadius(AppLayout.cornerRadiusL)
                                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                                .padding(.horizontal)
                            }



                            // Following section - matching screenshot
                            VStack(alignment: .leading, spacing: 12) {
                                HStack {
                                    Text("Following")
                                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                                        .foregroundColor(Color(UIColor.label))

                                    Spacer()

                                    Button {
                                        // See all action
                                    } label: {
                                        Text("See all")
                                            .font(.system(size: 14))
                                            .foregroundColor(Color(UIColor.secondaryLabel))
                                    }
                                }
                                .padding(.horizontal)

                                // Team logos row - matching screenshot
                                ScrollView(.horizontal, showsIndicators: false) {
                                    HStack(spacing: 16) {
                                        ForEach(favoriteTeams, id: \.self) { _ in
                                            VStack {
                                                ZStack {
                                                    Circle()
                                                        .background(AppColors.Brand.secondary.opacity(0.15))
                                                        .frame(width: 60, height: 60)

                                                    // Team logo placeholder - soccer field
                                                    Image(systemName: "sportscourt.fill")
                                                        .font(.system(size: 24))
                                                        .foregroundColor(Color(UIColor.label))

                                                    // Heart icon in top right
                                                    Image(systemName: "heart.fill")
                                                        .font(.system(size: 12))
                                                        .foregroundColor(.red)
                                                        .offset(x: 20, y: -20)
                                                }
                                            }
                                        }
                                    }
                                    .padding(.horizontal)
                                }

                                // Locker room section
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Locker room")
                                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                                        .foregroundColor(Color(UIColor.label))
                                        .padding(.horizontal)

                                    // My Votes (using NavigationStateManager)
                                    Button {
                                        navigationState.navigateToUserVotes()
                                    } label: {
                                        HStack {
                                            Image(systemName: "checkmark.circle")
                                                .font(.system(size: 20))
                                                .foregroundColor(Color(UIColor.label))

                                            Text("My Votes")
                                                .font(.system(size: 16))
                                                .foregroundColor(Color(UIColor.label))

                                            Spacer()

                                            Text("\(votesViewModel.voteCount)")
                                                .font(.system(size: 14))
                                                .foregroundColor(Color(UIColor.secondaryLabel))
                                                .padding(.trailing, 4)

                                            Image(systemName: "chevron.right")
                                                .foregroundColor(Color(UIColor.secondaryLabel))
                                        }
                                        .padding()
                                        .background(Color.gray.opacity(0.2))
                                        .cornerRadius(12)
                                        .padding(.horizontal)
                                    }
                                }
                                .padding(.top, 8)

                                // Logout Button
                                Button {
                                    viewModel.logout()
                                } label: {
                                    HStack {
                                        Image(systemName: "rectangle.portrait.and.arrow.right")
                                            .font(.system(size: 20))
                                            .foregroundColor(.red)

                                        Text("Logout")
                                            .font(.system(size: 16))
                                            .foregroundColor(.red)

                                        Spacer()
                                    }
                                    .padding()
                                    .background(Color.gray.opacity(0.2))
                                    .cornerRadius(12)
                                    .padding(.horizontal)
                                }
                                .padding(.top, 16)
                            }
                            .padding(.top, 24)

                        } else {
                            // Not logged in view - matching style of other buttons
                            VStack(spacing: 20) {
                                Text("You're not signed in")
                                    .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                                    .foregroundColor(Color(UIColor.label))
                                    .padding(.top, 40)

                                Text("Sign in to track your favorite teams and get personalized content")
                                    .font(AppTypography.dynamicFont(style: .body))
                                    .foregroundColor(Color(UIColor.secondaryLabel))
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal, 32)

                                Button {
                                    showLogin = true
                                } label: {
                                    Text("Sign In")
                                        .font(AppTypography.dynamicFont(style: .headline, weight: .semibold))
                                        .foregroundColor(AppColors.primaryButtonText)
                                        .frame(maxWidth: .infinity, minHeight: 48)
                                        .background(AppColors.Brand.primary)
                                        .cornerRadius(AppLayout.cornerRadiusM)
                                }
                                .padding(.horizontal)
                            }



                            // Following section (empty state or sample) - matching screenshot
                            VStack(alignment: .leading, spacing: 12) {
                                HStack {
                                    Text("Following")
                                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                                        .foregroundColor(Color(UIColor.label))

                                    Spacer()

                                    Button {
                                        // See all action
                                    } label: {
                                        Text("See all")
                                            .font(.system(size: 14))
                                            .foregroundColor(Color(UIColor.secondaryLabel))
                                    }
                                }
                                .padding(.horizontal)

                                // Team logos row (sample) - matching screenshot
                                ScrollView(.horizontal, showsIndicators: false) {
                                    HStack(spacing: 16) {
                                        ForEach(favoriteTeams, id: \.self) { _ in
                                            VStack {
                                                ZStack {
                                                    Circle()
                                                        .background(AppColors.Brand.secondary.opacity(0.15))
                                                        .frame(width: 60, height: 60)

                                                    // Team logo placeholder - soccer field
                                                    Image(systemName: "sportscourt.fill")
                                                        .font(.system(size: 24))
                                                        .foregroundColor(Color(UIColor.label))

                                                    // Heart icon in top right
                                                    Image(systemName: "heart.fill")
                                                        .font(.system(size: 12))
                                                        .foregroundColor(.red)
                                                        .offset(x: 20, y: -20)
                                                }
                                            }
                                        }
                                    }
                                    .padding(.horizontal)
                                }

                                // Locker room section
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Locker room")
                                        .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                                        .foregroundColor(Color(UIColor.label))
                                        .padding(.horizontal)
                                }
                                .padding(.top, 8)
                            }
                            .padding(.top, 24)
                        }
                    }
                    .padding(.bottom, 32)
                }
            }
        }
        .sheet(isPresented: $showLogin) {
            LoginView(viewModel: viewModel)
        }
        .sheet(isPresented: $showImagePicker, onDismiss: {
            if selectedImage != nil {
                uploadProfileImage()
            }
        }) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .alert("Image Upload Failed", isPresented: $showImageUploadError) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(imageUploadErrorMessage)
        }
        .onAppear {
            // Refresh user data when the view appears
            if viewModel.isAuthenticated {
                viewModel.refreshUserProfile()

                // Only fetch the vote count, not the full vote data
                votesViewModel.fetchVoteCount()
            }
        }
        .navigationTitle("Profile")
        .navigationBarHidden(true)
    }
}

// MARK: - Helper Views

struct StatCard: View {
    let title: String
    let value: String

    var body: some View {
        VStack(spacing: AppLayout.spacingXS) {
            Text(value)
                .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                .foregroundColor(AppColors.Brand.primary)

            Text(title)
                .font(AppTypography.dynamicFont(style: .subheadline))
                .foregroundColor(Color(UIColor.secondaryLabel))
        }
        .padding(AppLayout.spacingM)
        .frame(maxWidth: .infinity)
        .background(AppColors.secondaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    var action: (() -> Void)? = nil

    var body: some View {
        Button {
            action?()
        } label: {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(AppColors.Brand.primary)
                    .frame(width: 24, height: 24)

                Text(title)
                    .font(AppTypography.dynamicFont(style: .body))
                    .foregroundColor(Color(UIColor.label))

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(Color(UIColor.secondaryLabel))
            }
            .padding(AppLayout.spacingM)
        }
    }
}

#Preview {
    TabView {
        NavigationView {
            ProfileView(viewModel: AuthViewModel())
        }
        .tabItem {
            Image(systemName: "person.crop.circle")
            Text("Profile")
        }
    }
}
