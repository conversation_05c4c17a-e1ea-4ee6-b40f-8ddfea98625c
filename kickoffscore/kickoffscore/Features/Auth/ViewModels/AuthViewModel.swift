import Foundation
import Combine
import UIKit

class AuthViewModel: ObservableObject {
    @Published var currentUser: User?
    @Published var isAuthenticated = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showLoginView = false
    // No longer need showProfileView since profile is now a tab

    private var cancellables = Set<AnyCancellable>()

    init() {
        // Check if user is already logged in
        checkAuthStatus()
    }

    // Check if user is already authenticated
    func checkAuthStatus() {
        if APIService.shared.isAuthenticated {
            Logger.info("Auth token found, fetching user profile", category: .security)
            // Fetch user profile
            fetchUserProfile()
        } else {
            Logger.info("No auth token found, user is not authenticated", category: .security)
            isAuthenticated = false
            currentUser = nil
        }
    }

    // MARK: - Enhanced Security Methods

    /// Logout with secure cleanup
    func secureLogout() {
        Logger.info("Performing secure logout", category: .security)

        isLoading = true

        // Clear all secure data
        SecureConfig.clearAllSecureData()

        // Clear API service auth state
        APIService.shared.clearAuthToken()

        // Reset view model state
        currentUser = nil
        isAuthenticated = false
        errorMessage = nil

        isLoading = false

        Logger.info("Secure logout completed", category: .security)
    }

    /// Validate current session security
    func validateSessionSecurity() {
        guard SecureConfig.isSecureEnvironment() else {
            Logger.warning("Insecure environment detected, logging out", category: .security)
            secureLogout()
            errorMessage = "Security validation failed. Please restart the app."
            return
        }

        // Additional security checks can be added here
        Logger.debug("Session security validation passed", category: .security)
    }

    // Login user
    func login(email: String, password: String) {
        isLoading = true
        errorMessage = nil

        let loginRequest = LoginRequest(email: email, password: password)

        APIService.shared.postData(endpoint: "/users/login", body: loginRequest) { [weak self] (result: Result<AuthResponse, APIError>) in
            guard let self = self else { return }

            self.isLoading = false
            switch result {
            case .success(let response):
                print("Login successful, saving token: \(response.token)")
                // Save token
                APIService.shared.saveAuthToken(response.token)

                // Update user and authentication state
                self.currentUser = response.user
                self.isAuthenticated = true
                print("User authenticated: \(self.isAuthenticated), User: \(self.currentUser?.name ?? "Unknown")")

            case .failure(let error):
                self.errorMessage = "Login failed: \(error.localizedDescription)"
                print("Login error: \(error.localizedDescription)")
            }
        }
    }

    // Register user
    func register(email: String, password: String, name: String?) {
        isLoading = true
        errorMessage = nil

        let registerRequest = RegisterRequest(email: email, password: password, name: name)

        APIService.shared.postData(endpoint: "/users/register", body: registerRequest) { [weak self] (result: Result<AuthResponse, APIError>) in
            guard let self = self else { return }

            self.isLoading = false
            switch result {
            case .success(let response):
                print("Registration successful, saving token: \(response.token)")
                // Save token
                APIService.shared.saveAuthToken(response.token)

                // Update user and authentication state
                self.currentUser = response.user
                self.isAuthenticated = true
                print("User authenticated: \(self.isAuthenticated), User: \(self.currentUser?.name ?? "Unknown")")

            case .failure(let error):
                self.errorMessage = "Registration failed: \(error.localizedDescription)"
                print("Registration error: \(error.localizedDescription)")
            }
        }
    }

    // Fetch user profile
    func fetchUserProfile() {
        isLoading = true
        errorMessage = nil

        print("Fetching user profile...")
        APIService.shared.fetchData(endpoint: "/users/me") { [weak self] (result: Result<User, APIError>) in
            guard let self = self else { return }

            self.isLoading = false
            switch result {
            case .success(let user):
                print("User profile fetched successfully: \(user.name ?? "Unknown")")
                self.currentUser = user
                self.isAuthenticated = true
                print("Authentication state updated: isAuthenticated=\(self.isAuthenticated)")

            case .failure(let error):
                print("Failed to fetch user profile: \(error.localizedDescription)")
                // If we get an authentication error, clear the token
                if case .statusCode(let code) = error, code == 401 {
                    print("Authentication error (401), logging out")
                    self.logout()
                }

                self.errorMessage = "Failed to fetch profile: \(error.localizedDescription)"
                print("Profile fetch error: \(error.localizedDescription)")
            }
        }
    }

    // Logout user
    func logout() {
        // Clear token
        APIService.shared.clearAuthToken()

        // Update state
        currentUser = nil
        isAuthenticated = false
    }

    // Upload profile image
    func uploadProfileImage(_ image: UIImage, completion: @escaping (Bool) -> Void) {
        guard isAuthenticated else {
            print("Cannot upload image: User not authenticated")
            completion(false)
            return
        }

        isLoading = true
        errorMessage = nil

        // Convert image to data with adaptive compression
        let fiveMB = 5 * 1024 * 1024 // 5MB in bytes

        // Start with high quality
        var compressionQuality: CGFloat = 0.9
        var imageData = image.jpegData(compressionQuality: compressionQuality)

        // If the image is too large, gradually reduce quality until it's under 5MB
        while let data = imageData, data.count > fiveMB && compressionQuality > 0.1 {
            compressionQuality -= 0.1
            imageData = image.jpegData(compressionQuality: compressionQuality)
            print("Compressing image: \(data.count) bytes, quality: \(compressionQuality)")
        }

        // Check if we have valid data
        guard let finalImageData = imageData else {
            print("Failed to convert image to data")
            isLoading = false
            errorMessage = "Failed to process image"
            completion(false)
            return
        }

        // Final size check
        if finalImageData.count > fiveMB {
            print("Image is still too large after compression: \(finalImageData.count) bytes")
            isLoading = false
            errorMessage = "Image is too large. Please select a smaller image (under 5MB)."
            completion(false)
            return
        }

        print("Final image size: \(finalImageData.count) bytes, compression quality: \(compressionQuality)")

        // Create form data
        let formData = MultipartFormData()
        formData.append(finalImageData, withName: "profileImage", fileName: "profile.jpg", mimeType: "image/jpeg")

        // Upload image
        APIService.shared.uploadData(endpoint: "/users/profile-image", formData: formData) { [weak self] (result: Result<User, APIError>) in
            guard let self = self else { return }

            self.isLoading = false
            switch result {
            case .success(let updatedUser):
                print("Profile image uploaded successfully")
                self.currentUser = updatedUser
                completion(true)

            case .failure(let error):
                print("Failed to upload profile image: \(error.localizedDescription)")
                self.errorMessage = "Failed to upload image: \(error.localizedDescription)"
                completion(false)
            }
        }
    }

    // Refresh user profile data
    func refreshUserProfile() {
        guard isAuthenticated else { return }

        isLoading = true
        errorMessage = nil

        APIService.shared.fetchData(endpoint: "/users/me") { [weak self] (result: Result<User, APIError>) in
            guard let self = self else { return }

            self.isLoading = false
            switch result {
            case .success(let user):
                print("User profile refreshed successfully")
                self.currentUser = user

            case .failure(let error):
                print("Failed to refresh user profile: \(error.localizedDescription)")
                self.errorMessage = "Failed to refresh profile: \(error.localizedDescription)"
            }
        }
    }
}
