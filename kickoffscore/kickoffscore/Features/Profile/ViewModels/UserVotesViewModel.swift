import Foundation
import Combine

class UserVotesViewModel: ObservableObject {
    @Published var userVotes: [UserVoteDetail] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var voteCount = 0
    @Published var selectedDate: Date = Date()

    // Vote statistics
    @Published var totalVotes: Int = 0
    @Published var correctVotes: Int = 0
    @Published var incorrectVotes: Int = 0

    // Votes organized by date
    @Published var votesByDate: [Date: [UserVoteDetail]] = [:]
    @Published var availableDates: [Date] = []

    private var cancellables = Set<AnyCancellable>()
    private let dateFormatter = DateFormatter()

    init() {
        dateFormatter.dateFormat = "yyyy-MM-dd"
        // We'll fetch votes when the view appears instead of on initialization
    }

    // Get fixture date from a vote
    private func getFixtureDate(from vote: UserVoteDetail) -> Date? {
        if let fixture = vote.fixture {
            let dateString = fixture.date
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
            formatter.locale = Locale(identifier: "en_US_POSIX")
            if let date = formatter.date(from: dateString) {
                // Return just the date part (no time)
                return Calendar.current.startOfDay(for: date)
            }
        }
        return nil
    }

    // Organize votes by date
    private func organizeVotesByDate() {
        votesByDate.removeAll()
        availableDates.removeAll()

        // Calculate statistics
        totalVotes = userVotes.count
        correctVotes = userVotes.filter { $0.voteResult?.isCorrect == true }.count
        incorrectVotes = userVotes.filter { $0.voteResult?.isCorrect == false }.count

        // Group votes by fixture date
        for vote in userVotes {
            if let fixtureDate = getFixtureDate(from: vote) {
                if votesByDate[fixtureDate] == nil {
                    votesByDate[fixtureDate] = []
                    availableDates.append(fixtureDate)
                }
                votesByDate[fixtureDate]?.append(vote)
            }
        }

        // Sort dates in descending order (newest first)
        availableDates.sort(by: >)

        // If we have dates, set the selected date to the most recent one
        if let mostRecentDate = availableDates.first {
            selectedDate = mostRecentDate
        }
    }

    // Get votes for the selected date
    func votesForSelectedDate() -> [UserVoteDetail] {
        return votesByDate[selectedDate] ?? []
    }

    // Format date for display
    func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }

    // Fetch only the vote count for the profile view
    func fetchVoteCount() {
        // Check if user is authenticated
        guard APIService.shared.getAuthToken() != nil else {
            return
        }

        // Use the endpoint to get just the count of user votes
        APIService.shared.fetchData(endpoint: "/fixtures/votes/count") { [weak self] (result: Result<VoteCountResponse, APIError>) in
            guard let self = self else { return }

            DispatchQueue.main.async {
                switch result {
                case .success(let countResponse):
                    self.voteCount = countResponse.count
                    print("Fetched vote count: \(countResponse.count)")
                case .failure(let error):
                    print("Error fetching vote count: \(error.localizedDescription)")
                    // Fallback to zero if there's an error
                    self.voteCount = 0
                }
            }
        }
    }

    // Fetch votes for a specific date
    func fetchUserVotesForDate(date: Date) {
        isLoading = true
        errorMessage = nil

        // Format the date for the API request
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)

        // Check if user is authenticated
        guard APIService.shared.getAuthToken() != nil else {
            self.errorMessage = "Not authenticated"
            self.isLoading = false
            return
        }

        // Use the endpoint with parameters instead of appending to the URL
        let endpoint = "/fixtures/votes/user"
        let parameters = [
            "date": dateString,
            "timezone": TimeZone.current.identifier
        ]
        APIService.shared.fetchData(endpoint: endpoint, parameters: parameters) { [weak self] (result: Result<UserVotesList, APIError>) in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isLoading = false

                switch result {
                case .success(let votesList):
                    self.userVotes = votesList.votes
                    print("Fetched \(votesList.votes.count) user votes for date \(dateString)")

                    // Organize votes by date
                    self.organizeVotesByDate()

                case .failure(let error):
                    self.errorMessage = "Failed to load votes: \(error.localizedDescription)"
                    print("Error fetching user votes: \(error.localizedDescription)")
                }
            }
        }
    }

    // Fetch all available dates with votes
    func fetchAvailableDates() {
        isLoading = true
        errorMessage = nil

        // Check if user is authenticated
        guard APIService.shared.getAuthToken() != nil else {
            self.errorMessage = "Not authenticated"
            self.isLoading = false
            return
        }

        // Use the endpoint to get all dates with votes
        APIService.shared.fetchData(endpoint: "/fixtures/votes/dates") { [weak self] (result: Result<VoteDatesResponse, APIError>) in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isLoading = false

                switch result {
                case .success(let datesResponse):
                    // Convert string dates to Date objects
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd"

                    self.availableDates = datesResponse.dates.compactMap { dateString in
                        formatter.date(from: dateString)
                    }

                    // Sort dates in descending order (newest first)
                    self.availableDates.sort(by: >)

                    // If we have dates, set the selected date to the most recent one
                    if let mostRecentDate = self.availableDates.first {
                        self.selectedDate = mostRecentDate
                        // Fetch votes for the selected date
                        self.fetchUserVotesForDate(date: mostRecentDate)
                    }

                case .failure(let error):
                    self.errorMessage = "Failed to load vote dates: \(error.localizedDescription)"
                    print("Error fetching vote dates: \(error.localizedDescription)")
                }
            }
        }
    }

    // Original method - kept for backward compatibility but modified to use the new methods
    func fetchUserVotes() {
        // First fetch available dates, which will then fetch votes for the most recent date
        fetchAvailableDates()
    }
}
