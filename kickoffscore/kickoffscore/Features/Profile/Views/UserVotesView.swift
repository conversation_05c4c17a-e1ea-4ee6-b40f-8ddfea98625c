import SwiftUI
import Kingfisher
import Foundation

struct UserVotesView: View {
    @StateObject private var viewModel = UserVotesViewModel()

    var body: some View {
        ZStack {
            // Apply dynamic background color
            AppColors.background.ignoresSafeArea()

            if viewModel.isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                    .scaleEffect(1.5)
                    .foregroundColor(Color(UIColor.label))
            } else if let errorMessage = viewModel.errorMessage {
                VStack(spacing: AppLayout.spacingL) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(AppTypography.dynamicFont(style: .largeTitle, weight: .medium))
                        .foregroundColor(Color.red)

                    Text("Error")
                        .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                        .foregroundColor(Color(UIColor.label))

                    Text(errorMessage)
                        .font(AppTypography.dynamicFont(style: .body))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, AppLayout.spacingL)

                    Button {
                        viewModel.fetchAvailableDates()
                    } label: {
                        Text("Try Again")
                            .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                            .foregroundColor(.white)
                            .padding(.horizontal, AppLayout.spacingL)
                            .padding(.vertical, AppLayout.spacingM)
                            .background(AppColors.Brand.primary)
                            .cornerRadius(AppLayout.cornerRadiusM)
                    }
                }
            } else if viewModel.userVotes.isEmpty {
                VStack(spacing: AppLayout.spacingL) {
                    Image(systemName: "chart.bar.xaxis")
                        .font(AppTypography.dynamicFont(style: .largeTitle, weight: .medium))
                        .foregroundColor(Color(UIColor.secondaryLabel))

                    Text("No Votes Yet")
                        .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                        .foregroundColor(Color(UIColor.label))

                    Text("You haven't voted on any matches yet. Go to a match and vote to see your voting history here.")
                        .font(AppTypography.dynamicFont(style: .body))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, AppLayout.spacingL)
                }
            } else {
                VStack(spacing: 0) {
                    // Stats summary card
                    VStack(spacing: 16) {
                        Text("Voting Statistics")
                            .font(AppTypography.dynamicFont(style: .headline, weight: .bold))
                            .foregroundColor(Color(UIColor.label))
                            .frame(maxWidth: .infinity, alignment: .leading)

                        HStack(spacing: 12) {
                            // Total votes
                            VStack(spacing: 4) {
                                Text("\(viewModel.totalVotes)")
                                    .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                                    .foregroundColor(Color(UIColor.label))

                                Text("Total")
                                    .font(AppTypography.dynamicFont(style: .subheadline))
                                    .foregroundColor(Color(UIColor.secondaryLabel))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, AppLayout.spacingS)
                            .background(AppColors.secondaryBackground)
                            .cornerRadius(AppLayout.cornerRadiusM)

                            // Correct votes
                            VStack(spacing: 4) {
                                Text("\(viewModel.correctVotes)")
                                    .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                                    .foregroundColor(.green)

                                Text("Correct")
                                    .font(AppTypography.dynamicFont(style: .subheadline))
                                    .foregroundColor(Color(UIColor.secondaryLabel))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, AppLayout.spacingS)
                            .background(AppColors.secondaryBackground)
                            .cornerRadius(AppLayout.cornerRadiusM)

                            // Incorrect votes
                            VStack(spacing: 4) {
                                Text("\(viewModel.incorrectVotes)")
                                    .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                                    .foregroundColor(.red)

                                Text("Incorrect")
                                    .font(AppTypography.dynamicFont(style: .subheadline))
                                    .foregroundColor(Color(UIColor.secondaryLabel))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, AppLayout.spacingS)
                            .background(AppColors.secondaryBackground)
                            .cornerRadius(AppLayout.cornerRadiusM)
                        }

                        // Success rate
                        let successRate = viewModel.totalVotes > 0 ? Double(viewModel.correctVotes) / Double(viewModel.totalVotes) * 100 : 0

                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Success Rate")
                                    .font(AppTypography.dynamicFont(style: .subheadline))
                                    .foregroundColor(Color(UIColor.secondaryLabel))

                                Spacer()

                                Text("\(Int(successRate))%")
                                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                                    .foregroundColor(Color(UIColor.label))
                            }

                            // Progress bar
                            GeometryReader { geometry in
                                ZStack(alignment: .leading) {
                                    Rectangle()
                                        .fill(Color(UIColor.tertiarySystemFill))
                                        .frame(width: geometry.size.width, height: 8)
                                        .cornerRadius(4)

                                    Rectangle()
                                        .fill(LinearGradient(
                                            gradient: Gradient(colors: [AppColors.Brand.primary, AppColors.Sports.win]),
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        ))
                                        .frame(width: geometry.size.width * CGFloat(successRate / 100), height: 8)
                                        .cornerRadius(4)
                                }
                            }
                            .frame(height: 8)
                        }
                    }
                    .padding()
                    .background(AppColors.secondaryBackground)
                    .cornerRadius(AppLayout.cornerRadiusL)
                    .padding(.horizontal)
                    .padding(.top)

                    // Date selector
                    if !viewModel.availableDates.isEmpty {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                ForEach(viewModel.availableDates, id: \.self) { date in
                                    Button {
                                        viewModel.selectedDate = date
                                        viewModel.fetchUserVotesForDate(date: date)
                                    } label: {
                                        Text(viewModel.formatDate(date))
                                            .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                            .padding(.vertical, 8)
                                            .padding(.horizontal, 12)
                                            .background(
                                                Calendar.current.isDate(date, inSameDayAs: viewModel.selectedDate) ?
                                                AppColors.Brand.primary : Color(UIColor.systemGray5)
                                            )
                                            .foregroundColor(
                                                Calendar.current.isDate(date, inSameDayAs: viewModel.selectedDate) ?
                                                Color.white : Color(UIColor.secondaryLabel)
                                            )
                                            .cornerRadius(16)
                                    }
                                }
                            }
                            .padding(.horizontal)
                            .padding(.vertical, AppLayout.spacingS)
                        }
                        .background(AppColors.background)
                    }

                    // Votes for selected date
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(viewModel.votesForSelectedDate()) { vote in
                                CompactVoteCard(vote: vote)
                            }
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 8)
                    }
                }
            }
        }
        .navigationTitle("My Votes")
        .navigationBarTitleDisplayMode(.inline)
        // Add swipe-to-go-back functionality
        .swipeToGoBack()
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button {
                    // Refresh the current date's votes
                    viewModel.fetchUserVotesForDate(date: viewModel.selectedDate)
                } label: {
                    Image(systemName: "arrow.clockwise")
                        .foregroundColor(Color(UIColor.label))
                }
            }
        }
        .onAppear {
            // First fetch available dates, which will then fetch votes for the most recent date
            viewModel.fetchAvailableDates()
        }
    }
}

// Compact vote card for the redesigned view
struct CompactVoteCard: View {
    let vote: UserVoteDetail
    @EnvironmentObject private var navigationState: NavigationStateManager
    @State private var isLoading = false

    // Format time only
    private var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: vote.createdAt)
    }

    // Get match status or time
    private var matchStatus: String {
        if let fixture = vote.fixture, let status = fixture.fixture.status.short {
            if status == "FT" {
                return "Finished"
            } else if status == "NS" {
                return "Not Started"
            } else if status == "1H" || status == "2H" {
                return "Live"
            } else {
                return status
            }
        }
        return "Unknown"
    }

    // Get match score
    private var scoreText: String {
        if let fixture = vote.fixture {
            if let homeGoals = fixture.goals.home,
               let awayGoals = fixture.goals.away {
                return "\(homeGoals) - \(awayGoals)"
            }
        }
        return "vs"
    }

    var body: some View {
        Group {
            if vote.fixture != nil {
                // If we already have the fixture, fetch the full fixture first
                Button(action: {
                    // Prevent multiple taps while loading
                    guard !isLoading else { return }

                    isLoading = true

                    // Fetch the fixture
                    APIService.shared.fetchData(endpoint: "/fixtures/id/\(vote.fixtureId)") { (result: Result<FixtureResponse, APIError>) in
                        DispatchQueue.main.async {
                            switch result {
                            case .success(let response):
                                if let fixture = response.response.first {
                                    // Navigate using NavigationStateManager
                                    navigationState.navigateToFixtureDetail(fixture: fixture)
                                }
                            case .failure(let error):
                                print("Error fetching fixture: \(error.localizedDescription)")
                            }
                            isLoading = false
                        }
                    }
                }) {
                    cardContent
                }
            } else {
                // If we need to fetch the fixture first
                Button(action: {
                    // Prevent multiple taps while loading
                    guard !isLoading else { return }

                    isLoading = true

                    // Fetch the fixture
                    APIService.shared.fetchData(endpoint: "/fixtures/id/\(vote.fixtureId)") { (result: Result<FixtureResponse, APIError>) in
                        DispatchQueue.main.async {
                            switch result {
                            case .success(let response):
                                if let fixture = response.response.first {
                                    // Navigate using NavigationStateManager
                                    navigationState.navigateToFixtureDetail(fixture: fixture)
                                }
                            case .failure(let error):
                                print("Error fetching fixture: \(error.localizedDescription)")
                            }
                            isLoading = false
                        }
                    }
                }) {
                    cardContent
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .overlay(
            isLoading ?
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle())
                .scaleEffect(1.0)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black.opacity(0.2))
                .cornerRadius(12)
            : nil
        )
    }

    // Card content view
    private var cardContent: some View {
        HStack(spacing: 12) {
            // Team logos and score
            HStack(spacing: 8) {
                // Home team logo
                if let fixture = vote.fixture {
                    KFImage(URL(string: fixture.teams.home.logo ?? ""))
                        .placeholder {
                            Image(systemName: "sportscourt")
                                .foregroundColor(Color(UIColor.secondaryLabel))
                        }
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                } else {
                    Image(systemName: "sportscourt")
                        .foregroundColor(Color(UIColor.secondaryLabel))
                        .frame(width: 30, height: 30)
                }

                // Score or VS
                Text(scoreText)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(Color(UIColor.label))

                // Away team logo
                if let fixture = vote.fixture {
                    KFImage(URL(string: fixture.teams.away.logo ?? ""))
                        .placeholder {
                            Image(systemName: "sportscourt")
                                .foregroundColor(Color(UIColor.secondaryLabel))
                        }
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                } else {
                    Image(systemName: "sportscourt")
                        .foregroundColor(Color(UIColor.secondaryLabel))
                        .frame(width: 30, height: 30)
                }
            }
            .frame(width: 110)

            // Team names
            if let fixture = vote.fixture {
                VStack(alignment: .leading, spacing: 2) {
                    Text(fixture.teams.home.name)
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(Color(UIColor.label))
                        .lineLimit(1)

                    Text(fixture.teams.away.name)
                        .font(AppTypography.dynamicFont(style: .caption))
                        .foregroundColor(Color(UIColor.label))
                        .lineLimit(1)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                Text("Fixture ID: \(vote.fixtureId)")
                    .font(AppTypography.dynamicFont(style: .caption))
                    .foregroundColor(Color(UIColor.label))
                    .lineLimit(1)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }

            // Vote and result
            VStack(alignment: .trailing, spacing: 2) {
                // Your vote
                HStack(spacing: 4) {
                    Text(vote.vote.displayName)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColors.Brand.primary)

                    // Result indicator
                    if let voteResult = vote.voteResult, let isCorrect = voteResult.isCorrect {
                        Image(systemName: isCorrect ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .font(AppTypography.dynamicFont(style: .caption))
                            .foregroundColor(isCorrect ? .green : .red)
                    }
                }

                // Match status
                Text(matchStatus)
                    .font(AppTypography.dynamicFont(style: .caption2))
                    .foregroundColor(Color(UIColor.secondaryLabel))
            }
            .frame(width: 80)
        }
        .padding(12)
        .background(AppColors.secondaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
    }
}

#Preview {
    UserVotesView()
}
