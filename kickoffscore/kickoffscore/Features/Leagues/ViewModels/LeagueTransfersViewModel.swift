import Foundation
import SwiftUI

@MainActor
class LeagueTransfersViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var transfers: [Transfer] = []
    @Published var recentTransfers: [TransferInfo] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Services
    private let transferService = AsyncTransferService.shared
    
    // MARK: - Public Methods
    
    /// Load transfers for a specific league
    /// - Parameter leagueId: The ID of the league to load transfers for
    func loadTransfers(leagueId: Int) async {
        guard !isLoading else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let fetchedTransfers = try await transferService.fetchLeagueTransfers(leagueId: leagueId)
            
            transfers = fetchedTransfers
            recentTransfers = transferService.getRecentTransfers(from: fetchedTransfers)
            
        } catch {
            errorMessage = "Failed to load transfers: \(error.localizedDescription)"
            print("Error loading transfers: \(error)")
        }
        
        isLoading = false
    }
    
    /// Refresh transfers for a specific league
    /// - Parameter leagueId: The ID of the league to refresh transfers for
    func refreshTransfers(leagueId: Int) async {
        errorMessage = nil
        
        do {
            let fetchedTransfers = try await transferService.fetchLeagueTransfers(
                leagueId: leagueId,
                forceRefresh: true
            )
            
            transfers = fetchedTransfers
            recentTransfers = transferService.getRecentTransfers(from: fetchedTransfers)
            
        } catch {
            errorMessage = "Failed to refresh transfers: \(error.localizedDescription)"
            print("Error refreshing transfers: \(error)")
        }
    }
    
    /// Get the transfer object for a specific transfer info
    /// - Parameter transferInfo: The transfer info to find the parent transfer for
    /// - Returns: The Transfer object containing this transfer info
    func getTransfer(for transferInfo: TransferInfo) -> Transfer? {
        return transfers.first { transfer in
            transfer.transfers.contains { $0.id == transferInfo.id }
        }
    }
    
    /// Filter transfers by type
    /// - Parameter type: The transfer type to filter by (e.g., "loan", "permanent")
    func filterTransfers(by type: String) {
        let filteredTransfers = transferService.filterTransfers(transfers, byType: type)
        recentTransfers = transferService.getRecentTransfers(from: filteredTransfers)
    }
    
    /// Reset filter to show all transfers
    func resetFilter() {
        recentTransfers = transferService.getRecentTransfers(from: transfers)
    }
    
    /// Get transfers involving a specific team
    /// - Parameter teamId: The ID of the team to filter by
    func filterTransfers(involvingTeam teamId: Int) {
        let filteredTransfers = transferService.filterTransfers(transfers, involvingTeam: teamId)
        recentTransfers = transferService.getRecentTransfers(from: filteredTransfers)
    }
}

// MARK: - Transfer Statistics

extension LeagueTransfersViewModel {
    /// Get the count of loan transfers
    var loanTransfersCount: Int {
        recentTransfers.filter { $0.isLoan }.count
    }
    
    /// Get the count of permanent transfers
    var permanentTransfersCount: Int {
        recentTransfers.filter { !$0.isLoan }.count
    }
    
    /// Get the most active teams (teams with most transfers)
    var mostActiveTeams: [(teamName: String, count: Int)] {
        var teamCounts: [String: Int] = [:]
        
        for transferInfo in recentTransfers {
            teamCounts[transferInfo.teams.in.name, default: 0] += 1
            teamCounts[transferInfo.teams.out.name, default: 0] += 1
        }
        
        return teamCounts.sorted { $0.value > $1.value }
            .prefix(5)
            .map { (teamName: $0.key, count: $0.value) }
    }
    
    /// Get transfers from the last 30 days
    var recentTransfersLast30Days: [TransferInfo] {
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        
        return recentTransfers.filter { transferInfo in
            guard let transferDate = transferInfo.formattedDate else { return false }
            return transferDate >= thirtyDaysAgo
        }
    }
}
