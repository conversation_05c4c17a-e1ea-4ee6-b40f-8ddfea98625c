import Foundation
import SwiftUI

@MainActor
class KnockoutBracketViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var tournament: KnockoutTournament?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let fixtureService = AsyncFixtureService.shared
    private let leagueId: Int
    private var season: Int
    
    // MARK: - Initialization
    init(leagueId: Int, season: Int) {
        self.leagueId = leagueId
        self.season = season
    }
    
    // MARK: - Public Methods
    
    /// Update the season and reload knockout data
    func updateSeason(_ newSeason: Int) async {
        guard newSeason != season else { return }
        season = newSeason
        await loadKnockoutBracket()
    }
    
    /// Load knockout bracket data for the league and season
    func loadKnockoutBracket() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Fetch all fixtures for the league and season
            let fixtures = try await fixtureService.fetchFixturesByLeague(
                leagueId: leagueId,
                season: season,
                forceRefresh: false
            )
            
            // Convert to knockout tournament structure
            if let knockoutTournament = fixtures.toKnockoutTournament(leagueId: leagueId, season: season) {
                tournament = knockoutTournament
                
                if knockoutTournament.brackets.isEmpty {
                    errorMessage = "No knockout stages available for this competition"
                }
            } else {
                tournament = nil
                errorMessage = "No knockout stages found for this competition"
            }
            
        } catch {
            errorMessage = "Failed to load knockout bracket: \(error.localizedDescription)"
            tournament = nil
        }
        
        isLoading = false
    }
    
    /// Refresh knockout bracket with force refresh
    func refreshKnockoutBracket() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Fetch all fixtures with force refresh
            let fixtures = try await fixtureService.fetchFixturesByLeague(
                leagueId: leagueId,
                season: season,
                forceRefresh: true
            )
            
            // Convert to knockout tournament structure
            if let knockoutTournament = fixtures.toKnockoutTournament(leagueId: leagueId, season: season) {
                tournament = knockoutTournament
                
                if knockoutTournament.brackets.isEmpty {
                    errorMessage = "No knockout stages available for this competition"
                }
            } else {
                tournament = nil
                errorMessage = "No knockout stages found for this competition"
            }
            
        } catch {
            errorMessage = "Failed to refresh knockout bracket: \(error.localizedDescription)"
            tournament = nil
        }
        
        isLoading = false
    }
    
    /// Check if the competition has knockout stages
    func hasKnockoutStages() -> Bool {
        return tournament?.hasKnockoutStages ?? false
    }
    
    /// Check if knockout stages should be shown for this competition
    func shouldShowKnockoutTab() -> Bool {
        // Only show for cup competitions that have knockout stages
        guard CompetitionTypeUtils.isCupCompetition(leagueId: leagueId) else { return false }
        return hasKnockoutStages()
    }
    
    // MARK: - Helper Methods
    
    /// Get the current active round name for display
    func getActiveRoundName() -> String? {
        return tournament?.activeBracket?.displayName
    }
    
    /// Get live matches across all brackets
    func getLiveMatches() -> [KnockoutMatchData] {
        return tournament?.allMatches.filter { match in
            ["LIVE", "1H", "HT", "2H", "ET", "P"].contains(match.status)
        } ?? []
    }
    
    /// Get upcoming matches across all brackets
    func getUpcomingMatches() -> [KnockoutMatchData] {
        return tournament?.allMatches.filter { match in
            ["NS", "TBD", "PST"].contains(match.status)
        } ?? []
    }
}
