import Foundation
import SwiftUI

@MainActor
class LeagueStandingsViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var standings: [[TeamStanding]] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let standingsService = AsyncStandingService.shared
    private let leagueId: Int
    private var season: Int
    
    // Create a StandingsViewModel for legend compatibility
    lazy var standingsViewModel: StandingsViewModel = {
        // Create a dummy fixture for the StandingsViewModel
        let dummyFixture = createDummyFixture()
        return StandingsViewModel(fixture: dummyFixture)
    }()
    
    // MARK: - Initialization
    init(leagueId: Int, season: Int) {
        self.leagueId = leagueId
        self.season = season
    }
    
    // MARK: - Public Methods
    
    /// Update the season and reload standings
    func updateSeason(_ newSeason: Int) async {
        guard newSeason != season else { return }
        season = newSeason
        await loadStandings()
    }
    
    /// Load standings for the league and season
    func loadStandings() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let standingsData = try await standingsService.fetchStandings(
                leagueId: leagueId,
                season: season,
                forceRefresh: false
            )
            
            standings = standingsData
            
            if standings.isEmpty || standings.allSatisfy({ $0.isEmpty }) {
                errorMessage = "No standings available for this season"
            }
            
        } catch {
            errorMessage = "Failed to load standings: \(error.localizedDescription)"
            standings = []
        }
        
        isLoading = false
    }
    
    /// Refresh standings with force refresh
    func refreshStandings() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let standingsData = try await standingsService.fetchStandings(
                leagueId: leagueId,
                season: season,
                forceRefresh: true
            )
            
            standings = standingsData
            
            if standings.isEmpty || standings.allSatisfy({ $0.isEmpty }) {
                errorMessage = "No standings available for this season"
            }
            
        } catch {
            errorMessage = "Failed to refresh standings: \(error.localizedDescription)"
            standings = []
        }
        
        isLoading = false
    }
    
    // MARK: - Helper Methods
    
    /// Create a dummy fixture for StandingsViewModel compatibility
    private func createDummyFixture() -> Fixture {
        return Fixture(
            id: 0,
            referee: nil,
            timezone: "UTC",
            date: "2024-01-01T00:00:00+00:00",
            timestamp: Int(Date().timeIntervalSince1970),
            venue: Fixture.Venue(id: nil, name: nil, city: nil),
            status: Fixture.Status(long: "Not Started", short: "NS", elapsed: nil, extra: nil),
            league: Fixture.LeagueInfo(
                id: leagueId,
                name: "League",
                country: "Country",
                logo: nil,
                flag: nil,
                season: season,
                round: "Regular Season",
                coverage: nil
            ),
            teams: Fixture.Teams(home: nil, away: nil),
            goals: Fixture.Goals(home: nil, away: nil),
            score: Fixture.Score(
                halftime: Fixture.Score.Halftime(home: nil, away: nil),
                fulltime: Fixture.Score.Fulltime(home: nil, away: nil),
                extratime: Fixture.Score.Extratime(home: nil, away: nil),
                penalty: Fixture.Score.Penalty(home: nil, away: nil)
            )
        )
    }
}
