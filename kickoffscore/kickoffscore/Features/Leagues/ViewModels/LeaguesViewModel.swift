import Foundation
import SwiftUI

@MainActor
class LeaguesViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var leagues: [LeagueData] = []
    @Published var groupedLeagues: [LeagueGroup] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var searchText = ""
    @Published var expandedSections: Set<String> = []

    // MARK: - Private Properties
    private let leagueService = AsyncLeagueService.shared

    // MARK: - Computed Properties
    var filteredLeagues: [LeagueGroup] {
        if searchText.isEmpty {
            return groupedLeagues
        }

        return groupedLeagues.compactMap { group in
            let filteredLeagues = group.leagues.filter { league in
                league.league.name.localizedCaseInsensitiveContains(searchText) ||
                league.country.name.localizedCaseInsensitiveContains(searchText)
            }

            if filteredLeagues.isEmpty {
                return nil
            }

            return LeagueGroup(
                id: group.id,
                title: group.title,
                leagues: filteredLeagues,
                isCountryGroup: group.isCountryGroup,
                flagURL: group.flagURL
            )
        }
    }

    // MARK: - Initialization
    init() {
        // Initialize with some expanded sections
        expandedSections = ["World", "Europe"]
    }

    // MARK: - Public Methods
    func loadLeagues() async {
        isLoading = true
        errorMessage = nil

        do {
            let fetchedLeagues = try await leagueService.fetchLeagues()

            // Filter leagues to only include those in leagueTiers.ts
            let filteredLeagues = filterLeaguesByTiers(fetchedLeagues)

            // Process league flags to ensure they display correctly
            let processedLeagues = processLeagueFlags(filteredLeagues)

            leagues = processedLeagues
            groupedLeagues = organizeLeaguesIntoGroups(processedLeagues)

        } catch {
            errorMessage = "Failed to load leagues: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func toggleSection(_ sectionId: String) {
        if expandedSections.contains(sectionId) {
            expandedSections.remove(sectionId)
        } else {
            expandedSections.insert(sectionId)
        }
    }

    func isSectionExpanded(_ sectionId: String) -> Bool {
        return expandedSections.contains(sectionId)
    }

    // MARK: - Private Methods
    private func filterLeaguesByTiers(_ leagues: [LeagueData]) -> [LeagueData] {
        // League IDs from leagueTiers.ts (Tier 1-6)
        let allowedLeagueIds = Set([
            // Tier 1: Pinnacle Global & Continental Competitions
            1, 8, 4, 5, 2, 3,

            // Tier 2: The "Big 5" European Leagues + Major Continental
            39, 140, 78, 135, 61, 9, 13, 6, 7,

            // Tier 3: Major Domestic Cups, Other Strong Continental & Key Leagues
            45, 143, 81, 137, 848, 66, 48, 71, 128, 253, 307, 88, 94, 203, 15,
            32, 34, 30, 29, 31, 36,

            // Tier 4: Strong Second Tiers, Other Notable Leagues & Cups
            40, 79, 141, 136, 62, 667, 10, 188, 262, 17, 12, 856, 11, 73, 130,
            144, 179, 408, 181, 90, 96, 206, 218, 207, 197, 106, 119, 235, 286,
            210, 333, 283, 271, 345, 332, 315, 239, 265, 281, 250, 252, 268, 270,
            242, 292, 98, 169, 305, 301, 200, 202, 186, 233, 288, 525, 254, 44,
            480, 524, 22,

            // Tier 5: Mid-Level European Leagues, Cups & Other Top Divisions
            41, 80, 435, 436, 942, 943, 138, 63, 89, 95, 865, 204, 145, 219, 208,
            494, 107, 120, 237, 236, 287, 211, 334, 284, 272, 346, 506, 310, 342,
            419, 116, 172, 318, 329, 367, 327, 383, 373, 365, 362, 261, 393, 394,
            355, 371, 357, 299, 344, 162, 396, 129, 240, 266, 269, 243, 263, 293,
            99, 170, 290, 323, 274, 278, 340, 296, 380, 387, 389, 276, 330, 390,
            417, 398, 369, 589, 1132, 220, 209, 108, 121, 147, 212, 335, 285, 273,
            347, 680, 314, 732, 375, 321, 384, 241, 267, 294, 171, 545, 298, 822,
            514, 511, 714, 167, 359, 757, 698,

            // Tier 6: Lower Divisions & Less Globally Prominent Competitions
            42, 492, 109, 173, 319, 297, 300, 311, 709, 420, 1049, 117, 486, 707,
            328, 657, 366, 246, 326, 544, 382, 498, 665, 364, 658, 361, 358, 374,
            955, 954, 475, 624, 1113, 573, 575, 574, 572, 476, 625, 247, 248, 249,
            564, 115, 563, 565, 33
        ])

        return leagues.filter { allowedLeagueIds.contains($0.league.id) }
    }

    private func organizeLeaguesIntoGroups(_ leagues: [LeagueData]) -> [LeagueGroup] {
        var groups: [LeagueGroup] = []

        // Add Top Competitions header (this will be handled in the UI)

        // Add individual top competition leagues (not as a dropdown)
        let topCompetitions = getTopCompetitions(leagues)
        for league in topCompetitions {
            groups.append(LeagueGroup(
                id: "TopCompetition_\(league.league.id)",
                title: league.league.name,
                leagues: [league],
                isCountryGroup: false,
                flagURL: nil
            ))
        }

        // Add "All Competitions" header (this will be handled in the UI)

        // Include all leagues in country sections (don't filter out top competitions)
        // This allows top competitions to appear both in "Top Competitions" and their respective countries

        // Separate leagues by type and region (including top competitions)
        let worldLeagues = getWorldLeagues(leagues)
        let europeanLeagues = getEuropeanLeagues(leagues)
        let asianLeagues = getAsianLeagues(leagues)
        let africanLeagues = getAfricanLeagues(leagues)
        let southAmericanLeagues = getSouthAmericanLeagues(leagues)
        let northCentralAmericanLeagues = getNorthCentralAmericanLeagues(leagues)
        let oceaniaLeagues = getOceaniaLeagues(leagues)

        // Collect all continental groups
        var continentalGroups: [LeagueGroup] = []

        // Add World section (only truly global competitions)
        if !worldLeagues.isEmpty {
            continentalGroups.append(LeagueGroup(
                id: "World",
                title: "World",
                leagues: worldLeagues,
                isCountryGroup: false,
                flagURL: "🌍"
            ))
        }

        // Add continental sections with continental competitions first
        if !europeanLeagues.isEmpty {
            continentalGroups.append(contentsOf: organizeLeaguesByContinent(europeanLeagues, continentTitle: "Europe", continentFlag: "🇪🇺"))
        }

        if !asianLeagues.isEmpty {
            continentalGroups.append(contentsOf: organizeLeaguesByContinent(asianLeagues, continentTitle: "Asia", continentFlag: "🌏"))
        }

        if !africanLeagues.isEmpty {
            continentalGroups.append(contentsOf: organizeLeaguesByContinent(africanLeagues, continentTitle: "Africa", continentFlag: "🌍"))
        }

        if !southAmericanLeagues.isEmpty {
            continentalGroups.append(contentsOf: organizeLeaguesByContinent(southAmericanLeagues, continentTitle: "South America", continentFlag: "🌎"))
        }

        if !northCentralAmericanLeagues.isEmpty {
            continentalGroups.append(contentsOf: organizeLeaguesByContinent(northCentralAmericanLeagues, continentTitle: "North & Central America", continentFlag: "🌎"))
        }

        if !oceaniaLeagues.isEmpty {
            continentalGroups.append(contentsOf: organizeLeaguesByContinent(oceaniaLeagues, continentTitle: "Oceania", continentFlag: "🇦🇺"))
        }

        // Sort continental groups alphabetically by title
        continentalGroups.sort { $0.title < $1.title }

        // Add sorted continental groups to main groups
        groups.append(contentsOf: continentalGroups)

        return groups
    }

    private func organizeLeaguesByContinent(_ leagues: [LeagueData], continentTitle: String, continentFlag: String) -> [LeagueGroup] {
        var groups: [LeagueGroup] = []

        // Get continental competition IDs for this continent
        let continentalCompetitionIds = getContinentalCompetitionIds(for: continentTitle)

        // Separate continental competitions from domestic leagues
        let continentalCompetitions = leagues.filter { continentalCompetitionIds.contains($0.league.id) }
        let domesticLeagues = leagues.filter { !continentalCompetitionIds.contains($0.league.id) }

        // Add continental competitions group first if any exist
        if !continentalCompetitions.isEmpty {
            groups.append(LeagueGroup(
                id: "\(continentTitle)_Continental",
                title: continentTitle,
                leagues: continentalCompetitions.sorted { $0.league.name < $1.league.name },
                isCountryGroup: false,
                flagURL: continentFlag
            ))
        }

        // Add domestic leagues organized by country
        if !domesticLeagues.isEmpty {
            groups.append(contentsOf: organizeLeaguesByCountry(domesticLeagues, continentTitle: continentTitle, continentFlag: continentFlag))
        }

        return groups
    }

    private func organizeLeaguesByCountry(_ leagues: [LeagueData], continentTitle: String, continentFlag: String) -> [LeagueGroup] {
        // Group leagues by country
        let groupedByCountry = Dictionary(grouping: leagues) { $0.country.name }

        // Sort countries alphabetically
        let sortedCountries = groupedByCountry.keys.sorted()

        return sortedCountries.map { countryName in
            let countryLeagues = groupedByCountry[countryName] ?? []
            let flagURL = countryLeagues.first?.country.flag ?? getCountryFlagEmoji(countryName)

            return LeagueGroup(
                id: "\(continentTitle)_\(countryName)",
                title: countryName,
                leagues: countryLeagues.sorted { $0.league.name < $1.league.name },
                isCountryGroup: true,
                flagURL: flagURL
            )
        }
    }

    private func getContinentalCompetitionIds(for continent: String) -> Set<Int> {
        switch continent {
        case "Europe":
            return Set([2, 3, 848, 4, 5, 32, 525])
        case "Asia":
            return Set([7, 17, 1132, 30])
        case "Africa":
            return Set([6, 12, 29, 36])
        case "South America":
            return Set([9, 13, 11, 34]) // CONMEBOL competitions
        case "North & Central America":
            return Set([22, 31, 856, 254]) // CONCACAF competitions
        case "Oceania":
            return Set([33])
        default:
            return Set()
        }
    }

    // MARK: - Regional League Filters
    private func getTopCompetitions(_ leagues: [LeagueData]) -> [LeagueData] {
        // Top competitions: UEFA Champions League, UEFA Europa League, Premier League, LaLiga, Bundesliga, Serie A, Ligue 1
        let topCompetitionIds = Set([
            2,   // UEFA Champions League
            3,   // UEFA Europa League
            39,  // Premier League
            140, // LaLiga
            78,  // Bundesliga
            135, // Serie A
            61   // Ligue 1
        ])

        return leagues.filter { topCompetitionIds.contains($0.league.id) }
            .sorted { $0.league.name < $1.league.name }
    }

    private func getWorldLeagues(_ leagues: [LeagueData]) -> [LeagueData] {
        // Only truly global FIFA competitions
        let worldLeagueIds = Set([
            1,   // World Cup
            8,   // World Cup (duplicate entry)
            15,  // FIFA Club World Cup
            480, // Olympic Games (Men)
            524  // Olympic Games (Women)
        ])
        return leagues.filter { worldLeagueIds.contains($0.league.id) }
            .sorted { $0.league.name < $1.league.name }
    }

    private func getEuropeanLeagues(_ leagues: [LeagueData]) -> [LeagueData] {
        let europeanCountries = Set([
            "England", "Spain", "Germany", "Italy", "France", "Netherlands", "Portugal", "Turkey",
            "Belgium", "Scotland", "Northern Ireland", "Austria", "Switzerland", "Greece", "Poland",
            "Denmark", "Russia", "Serbia", "Croatia", "Ukraine", "Romania", "Hungary", "Czech Republic",
            "Slovakia", "Bosnia and Herzegovina", "Albania", "Armenia", "Azerbaijan", "Belarus",
            "Bulgaria", "Cyprus", "Estonia", "Faroe Islands", "Georgia", "Israel", "Slovenia",
            "Latvia", "Lithuania", "Luxembourg", "Malta", "Moldova", "Montenegro", "North Macedonia",
            "Ireland", "Iceland", "Finland", "Sweden", "Norway"
        ])

        // Include UEFA competitions
        let uefaCompetitionIds = Set([
            2,   // Champions League (UEFA)
            3,   // Europa League (UEFA)
            848, // Europa Conference League (UEFA)
            4,   // European Championship (UEFA Euro) / European Championship Qualification
            5,   // UEFA Nations League
            32,  // WC Qualification Europe (UEFA)
            525  // UEFA Champions League Women
        ])

        return leagues.filter { league in
            europeanCountries.contains(league.country.name) || uefaCompetitionIds.contains(league.league.id)
        }
    }

    private func getSouthAmericanLeagues(_ leagues: [LeagueData]) -> [LeagueData] {
        let southAmericanCountries = Set([
            "Brazil", "Argentina", "Colombia", "Chile", "Peru", "Paraguay", "Uruguay", "Ecuador",
            "Venezuela", "Bolivia"
        ])

        // Include CONMEBOL competitions
        let conmebolCompetitionIds = Set([
            9,   // Copa America (CONMEBOL)
            13,  // Copa Libertadores (CONMEBOL)
            11,  // Copa Sudamericana (CONMEBOL)
            34   // WC Qualification South America (CONMEBOL)
        ])

        return leagues.filter { league in
            southAmericanCountries.contains(league.country.name) || conmebolCompetitionIds.contains(league.league.id)
        }
    }

    private func getNorthCentralAmericanLeagues(_ leagues: [LeagueData]) -> [LeagueData] {
        let northCentralAmericanCountries = Set([
            "USA", "Mexico", "Costa Rica", "Nicaragua", "Canada"
        ])

        // Include CONCACAF competitions
        let concacafCompetitionIds = Set([
            22,  // CONCACAF Gold Cup
            31,  // WC Qualification Concacaf
            856, // CONCACAF Champions Cup (North/Central America & Caribbean)
            254  // NWSL (Note: While domestic USA, falls under CONCACAF for categorization)
        ])

        return leagues.filter { league in
            northCentralAmericanCountries.contains(league.country.name) || concacafCompetitionIds.contains(league.league.id)
        }
    }

    private func getAsianLeagues(_ leagues: [LeagueData]) -> [LeagueData] {
        let asianCountries = Set([
            "South Korea", "Japan", "China", "Qatar", "UAE", "Iran", "India", "Indonesia",
            "Malaysia", "Vietnam", "Thailand", "Hong Kong", "Jordan", "Kazakhstan", "Kuwait",
            "Lebanon", "Bahrain", "Bangladesh", "Uzbekistan", "Taiwan"
        ])

        // Include AFC competitions
        let afcCompetitionIds = Set([
            7,    // AFC Asian Cup (Asia)
            17,   // AFC Champions League Elite
            1132, // AFC Champions League Two (AFC Challenge League)
            30    // WC Qualification Asia (AFC)
        ])

        return leagues.filter { league in
            asianCountries.contains(league.country.name) || afcCompetitionIds.contains(league.league.id)
        }
    }

    private func getAfricanLeagues(_ leagues: [LeagueData]) -> [LeagueData] {
        let africanCountries = Set([
            "Morocco", "Tunisia", "Algeria", "Egypt", "South Africa", "Kenya"
        ])

        // Include CAF competitions
        let cafCompetitionIds = Set([
            6,  // Africa Cup of Nations
            12, // CAF Champions League (Africa)
            29, // WC Qualification Africa (CAF)
            36  // Africa Cup of Nations Qualifications
        ])

        return leagues.filter { league in
            africanCountries.contains(league.country.name) || cafCompetitionIds.contains(league.league.id)
        }
    }

    private func getOceaniaLeagues(_ leagues: [LeagueData]) -> [LeagueData] {
        let oceaniaCountries = Set([
            "Australia", "New Zealand"
        ])

        // Include OFC competitions
        let ofcCompetitionIds = Set([
            33  // WC Qualification Oceania
        ])

        return leagues.filter { league in
            oceaniaCountries.contains(league.country.name) || ofcCompetitionIds.contains(league.league.id)
        }
    }

    // MARK: - Helper Methods
    private func getCountryFlagEmoji(_ countryName: String) -> String {
        let countryFlags: [String: String] = [
            // Europe
            "England": "🏴󠁧󠁢󠁥󠁮󠁧󠁿", "Spain": "🇪🇸", "Germany": "🇩🇪", "Italy": "🇮🇹", "France": "🇫🇷",
            "Netherlands": "🇳🇱", "Portugal": "🇵🇹", "Turkey": "🇹🇷", "Belgium": "🇧🇪", "Scotland": "🏴󠁧󠁢󠁳󠁣󠁴󠁿",
            "Northern Ireland": "🇬🇧", "Austria": "🇦🇹", "Switzerland": "🇨🇭", "Greece": "🇬🇷", "Poland": "🇵🇱",
            "Denmark": "🇩🇰", "Russia": "🇷🇺", "Serbia": "🇷🇸", "Croatia": "🇭🇷", "Ukraine": "🇺🇦",
            "Romania": "🇷🇴", "Hungary": "🇭🇺", "Czech Republic": "🇨🇿", "Slovakia": "🇸🇰",
            "Bosnia and Herzegovina": "🇧🇦", "Albania": "🇦🇱", "Armenia": "🇦🇲", "Azerbaijan": "🇦🇿",
            "Belarus": "🇧🇾", "Bulgaria": "🇧🇬", "Cyprus": "🇨🇾", "Estonia": "🇪🇪", "Faroe Islands": "🇫🇴",
            "Georgia": "🇬🇪", "Israel": "🇮🇱", "Slovenia": "🇸🇮", "Latvia": "🇱🇻", "Lithuania": "🇱🇹",
            "Luxembourg": "🇱🇺", "Malta": "🇲🇹", "Moldova": "🇲🇩", "Montenegro": "🇲🇪",
            "North Macedonia": "🇲🇰", "Ireland": "🇮🇪", "Iceland": "🇮🇸", "Finland": "🇫🇮",
            "Sweden": "🇸🇪", "Norway": "🇳🇴",

            // Americas
            "USA": "🇺🇸", "Mexico": "🇲🇽", "Costa Rica": "🇨🇷", "Nicaragua": "🇳🇮", "Canada": "🇨🇦",
            "Brazil": "🇧🇷", "Argentina": "🇦🇷", "Colombia": "🇨🇴", "Chile": "🇨🇱", "Peru": "🇵🇪",
            "Paraguay": "🇵🇾", "Uruguay": "🇺🇾", "Ecuador": "🇪🇨", "Venezuela": "🇻🇪", "Bolivia": "🇧🇴",

            // Asia
            "South Korea": "🇰🇷", "Japan": "🇯🇵", "China": "🇨🇳", "Qatar": "🇶🇦", "UAE": "🇦🇪",
            "Iran": "🇮🇷", "India": "🇮🇳", "Indonesia": "🇮🇩", "Malaysia": "🇲🇾", "Vietnam": "🇻🇳",
            "Thailand": "🇹🇭", "Hong Kong": "🇭🇰", "Jordan": "🇯🇴", "Kazakhstan": "🇰🇿", "Kuwait": "🇰🇼",
            "Lebanon": "🇱🇧", "Bahrain": "🇧🇭", "Bangladesh": "🇧🇩", "Uzbekistan": "🇺🇿", "Taiwan": "🇹🇼",

            // Africa
            "Morocco": "🇲🇦", "Tunisia": "🇹🇳", "Algeria": "🇩🇿", "Egypt": "🇪🇬", "South Africa": "🇿🇦", "Kenya": "🇰🇪",

            // Oceania
            "Australia": "🇦🇺", "New Zealand": "🇳🇿"
        ]

        return countryFlags[countryName] ?? "🏳️"
    }

    // Process league flags to ensure they display correctly
    private func processLeagueFlags(_ leagues: [LeagueData]) -> [LeagueData] {
        return leagues.map { league in
            // Ensure the flag URL is valid and not empty
            var flagURL = league.country.flag

            // Convert SVG URLs to PNG URLs for better compatibility
            if let url = flagURL, url.hasSuffix(".svg") {
                // Extract the country code from the URL
                if let countryCode = url.split(separator: "/").last?.split(separator: ".").first {
                    // Use a PNG flag service instead
                    flagURL = "https://flagcdn.com/w80/\(countryCode).png"
                }
            }

            // If no flag URL is provided or conversion failed, try to construct one based on country code
            if flagURL == nil || flagURL?.isEmpty == true {
                let countryName = league.country.name.lowercased()

                // Map country names to ISO codes for common countries
                let countryCodeMap: [String: String] = [
                    "england": "gb-eng",
                    "scotland": "gb-sct",
                    "wales": "gb-wls",
                    "northern ireland": "gb-nir",
                    "algeria": "dz",
                    "france": "fr",
                    "spain": "es",
                    "germany": "de",
                    "italy": "it",
                    "brazil": "br",
                    "argentina": "ar",
                    "portugal": "pt",
                    "netherlands": "nl",
                    "belgium": "be",
                    "world": "un" // United Nations flag for "World"
                ]

                if let countryCode = countryCodeMap[countryName] {
                    // Use PNG format instead of SVG
                    flagURL = "https://flagcdn.com/w80/\(countryCode).png"
                }
            }

            // Create a new CountryInfo with the updated flag URL
            let updatedCountry = CountryInfo(
                name: league.country.name,
                code: league.country.code,
                flag: flagURL
            )

            // Create a new LeagueData with the updated country info
            return LeagueData(
                league: league.league,
                country: updatedCountry,
                seasons: league.seasons
            )
        }
    }
}

// MARK: - LeagueGroup Model
struct LeagueGroup: Identifiable, Hashable {
    let id: String
    let title: String
    let leagues: [LeagueData]
    let isCountryGroup: Bool
    let flagURL: String?

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: LeagueGroup, rhs: LeagueGroup) -> Bool {
        lhs.id == rhs.id
    }
}
