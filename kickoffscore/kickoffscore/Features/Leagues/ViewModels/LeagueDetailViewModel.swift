import Foundation
import SwiftUI

@MainActor
class LeagueDetailViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var league: LeagueData
    @Published var availableSeasons: [SeasonOption] = []
    @Published var selectedSeason: SeasonOption?
    @Published var selectedTabIndex: Int = 0
    @Published var isSeasonDropdownOpen: Bool = false
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var isFavorited: Bool = false

    // MARK: - Services
    private let leagueService = AsyncLeagueService.shared

    // MARK: - Season Persistence
    private var seasonPersistenceKey: String {
        return "selectedSeason_\(league.league.id)"
    }

    // MARK: - Knockout Support
    @Published var knockoutViewModel: KnockoutBracketViewModel?
    @Published var shouldShowKnockoutTab: Bool = false

    // MARK: - Tab Configuration
    /// Dynamic tabs based on competition type
    var tabs: [String] {
        var baseTabs = ["Overview", "Matches", "Standings"]

        // Add Knockout tab for cup competitions that have knockout stages
        if shouldShowKnockoutTab {
            baseTabs.append("Knockout")
        }

        baseTabs.append(contentsOf: ["Team Stats", "Player Stats"])

        // Only add Transfers tab for league competitions (not cup competitions)
        if league.isLeagueCompetition {
            baseTabs.append("Transfers")
        }

        return baseTabs
    }

    // MARK: - Initialization
    init(league: LeagueData) {
        self.league = league

        // Try to load previously selected season first
        if let savedSeason = loadSelectedSeason() {
            self.selectedSeason = savedSeason
        } else {
            // Set initial selected season from league data
            // Prefer current season (marked as active) over any specific year
            if let currentSeason = league.seasons.first(where: { $0.current }) {
                self.selectedSeason = SeasonOption(year: currentSeason.year)
            } else if let latestSeason = league.seasons.max(by: { $0.year < $1.year }) {
                self.selectedSeason = SeasonOption(year: latestSeason.year)
            } else if league.seasons.contains(where: { $0.year == 2024 }) {
                self.selectedSeason = SeasonOption(year: 2024)
            }
        }
    }

    // MARK: - Public Methods

    /// Load available seasons for the league
    func loadSeasons() async {
        isLoading = true
        errorMessage = nil

        do {
            let seasons = try await leagueService.fetchLeagueSeasons()

            // Convert to SeasonOption objects and filter to only include seasons available for this league
            let leagueSeasonYears = Set(league.seasons.map { $0.year })

            // For each API season, check if it matches any league season
            // The API season should directly match the league season year
            availableSeasons = seasons.compactMap { apiSeason in
                // Check if this API season matches any league season directly
                if leagueSeasonYears.contains(apiSeason) {
                    return SeasonOption(year: apiSeason)
                }
                return nil
            }

            // If no selected season, prefer current season or most recent available
            if selectedSeason == nil {
                if let currentSeason = league.seasons.first(where: { $0.current }),
                   let currentSeasonOption = availableSeasons.first(where: { $0.year == currentSeason.year }) {
                    selectedSeason = currentSeasonOption
                } else if let firstSeason = availableSeasons.first {
                    selectedSeason = firstSeason
                }
            }

        } catch {
            errorMessage = "Failed to load seasons: \(error.localizedDescription)"
        }

        isLoading = false
    }

    /// Select a new season
    func selectSeason(_ season: SeasonOption) {
        selectedSeason = season
        isSeasonDropdownOpen = false

        // Save the selected season for this league
        saveSelectedSeason(season)

        // Notify that season has changed - the LeagueFixturesRoundView will automatically
        // reload when the season changes since it's passed as a parameter
    }

    /// Close the season dropdown
    func closeSeasonDropdown() {
        isSeasonDropdownOpen = false
    }

    /// Toggle favorite status
    func toggleFavorite() {
        isFavorited.toggle()
        // TODO: Implement actual favorite persistence
    }

    /// Select a tab
    func selectTab(_ index: Int) {
        guard index >= 0 && index < tabs.count else { return }
        selectedTabIndex = index
    }

    /// Get the current season display text
    var currentSeasonText: String {
        selectedSeason?.displayText ?? "Select Season"
    }

    /// Check if notifications are enabled for this league
    var notificationsEnabled: Bool {
        // TODO: Implement actual notification status check
        return false
    }

    /// Toggle notifications for this league
    func toggleNotifications() {
        // TODO: Implement notification toggle
    }

    /// Refresh league data to get latest season information
    func refreshLeagueData() async {
        do {
            if let updatedLeague = try await leagueService.fetchLeagueById(leagueId: league.league.id, forceRefresh: true) {
                // Store the currently selected season to preserve user's choice
                let previouslySelectedSeason = selectedSeason

                // Update the league data
                league = updatedLeague

                // Only reset to current season if no season was previously selected
                // This preserves the user's season selection when navigating back from fixture detail
                if previouslySelectedSeason == nil {
                    if let currentSeason = league.seasons.first(where: { $0.current }) {
                        selectedSeason = SeasonOption(year: currentSeason.year)
                    }
                } else {
                    // Keep the previously selected season
                    selectedSeason = previouslySelectedSeason
                }

                // Update knockout functionality
                await updateKnockoutSupport()
            }
        } catch {
            // Silently handle error - the existing season selection will remain
        }
    }

    // MARK: - Knockout Support Methods

    /// Initialize knockout support for cup competitions
    func initializeKnockoutSupport() async {
        guard CompetitionTypeUtils.isCupCompetition(leagueId: league.league.id) else {
            shouldShowKnockoutTab = false
            knockoutViewModel = nil
            return
        }

        // Create knockout view model
        let season = selectedSeason?.year ?? league.seasons.first?.year ?? 2024
        knockoutViewModel = KnockoutBracketViewModel(leagueId: league.league.id, season: season)

        // Check if knockout stages should be shown
        await updateKnockoutSupport()
    }

    /// Update knockout support based on current data
    private func updateKnockoutSupport() async {
        guard let knockoutVM = knockoutViewModel else {
            shouldShowKnockoutTab = false
            return
        }

        // Load knockout data to check if stages exist
        await knockoutVM.loadKnockoutBracket()
        shouldShowKnockoutTab = knockoutVM.shouldShowKnockoutTab()
    }

    /// Update knockout season when league season changes
    func updateKnockoutSeason() async {
        guard let knockoutVM = knockoutViewModel,
              let season = selectedSeason?.year else { return }

        await knockoutVM.updateSeason(season)
        shouldShowKnockoutTab = knockoutVM.shouldShowKnockoutTab()
    }

    // MARK: - Season Persistence Methods

    /// Save the selected season for this league
    private func saveSelectedSeason(_ season: SeasonOption) {
        UserDefaults.standard.set(season.year, forKey: seasonPersistenceKey)
        print("💾 Saved season \(season.year) for league \(league.league.id)")
    }

    /// Load the previously selected season for this league
    private func loadSelectedSeason() -> SeasonOption? {
        let savedYear = UserDefaults.standard.integer(forKey: seasonPersistenceKey)
        if savedYear != 0 {
            print("📂 Loaded saved season \(savedYear) for league \(league.league.id)")
            return SeasonOption(year: savedYear)
        }
        return nil
    }

    /// Clear the saved season (useful when completely exiting league view)
    func clearSavedSeason() {
        UserDefaults.standard.removeObject(forKey: seasonPersistenceKey)
        print("🗑️ Cleared saved season for league \(league.league.id)")
    }

    /// Reset to current season (useful for fresh starts)
    func resetToCurrentSeason() {
        if let currentSeason = league.seasons.first(where: { $0.current }) {
            let currentSeasonOption = SeasonOption(year: currentSeason.year)
            selectedSeason = currentSeasonOption
            saveSelectedSeason(currentSeasonOption)
            print("🔄 Reset to current season \(currentSeason.year) for league \(league.league.id)")
        }
    }
}
