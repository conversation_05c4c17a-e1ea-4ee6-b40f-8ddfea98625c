import Foundation
import SwiftUI

@MainActor
class LeagueFixturesRoundViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var fixturesByRounds: LeagueFixturesByRounds?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var expandedRounds: Set<String> = []
    @Published var selectedRound: FixtureRound?

    // MARK: - Private Properties
    private let fixtureService = AsyncFixtureService.shared
    private let leagueId: Int
    private var season: Int
    

    
    // MARK: - Initialization
    init(leagueId: Int, season: Int) {
        self.leagueId = leagueId
        self.season = season
    }
    
    // MARK: - Public Methods

    /// Update the season and reload fixtures
    func updateSeason(_ newSeason: Int) async {
        guard newSeason != season else { return }
        season = newSeason
        await loadFixtures()
    }

    /// Load fixtures for the league and organize by rounds
    func loadFixtures() async {
        isLoading = true
        errorMessage = nil

        do {
            // Fetch all fixtures for the league and season
            let fixtures = try await fixtureService.fetchFixturesByLeague(
                leagueId: leagueId,
                season: season,
                forceRefresh: false
            )

            // Group fixtures by rounds
            let rounds = fixtures.groupedByRounds()

            // Create the organized structure
            fixturesByRounds = LeagueFixturesByRounds(
                leagueId: leagueId,
                season: season,
                rounds: rounds
            )
            
            // Auto-expand rounds with live fixtures
            expandRoundsWithLiveFixtures()

            // Set default selected round to current/most recent round
            setDefaultSelectedRound()

        } catch {
            errorMessage = "Failed to load fixtures: \(error.localizedDescription)"
            Logger.error("Failed to load fixtures for league \(leagueId), season \(season): \(error)", category: .data)
        }

        isLoading = false
    }
    
    /// Refresh fixtures data
    func refreshFixtures() async {
        do {
            // Force refresh from network
            let fixtures = try await fixtureService.fetchFixturesByLeague(
                leagueId: leagueId,
                season: season,
                forceRefresh: true
            )
            
            // Group fixtures by rounds
            let rounds = fixtures.groupedByRounds()
            
            // Update the organized structure
            fixturesByRounds = LeagueFixturesByRounds(
                leagueId: leagueId,
                season: season,
                rounds: rounds
            )
            
            // Auto-expand rounds with live fixtures
            expandRoundsWithLiveFixtures()

            // Update selected round if it still exists, otherwise set default
            updateSelectedRoundAfterRefresh()

        } catch {
            errorMessage = "Failed to refresh fixtures: \(error.localizedDescription)"
            Logger.error("Failed to refresh fixtures for league \(leagueId), season \(season): \(error)", category: .data)
        }
    }
    
    /// Toggle expansion state of a round
    func toggleRoundExpansion(_ roundName: String) {
        if expandedRounds.contains(roundName) {
            expandedRounds.remove(roundName)
        } else {
            expandedRounds.insert(roundName)
        }
    }
    
    /// Check if a round is expanded
    func isRoundExpanded(_ roundName: String) -> Bool {
        expandedRounds.contains(roundName)
    }
    

    
    /// Get sorted rounds for display
    var sortedRounds: [FixtureRound] {
        guard let fixturesByRounds = fixturesByRounds else { return [] }
        return fixturesByRounds.sortedRounds
    }

    /// Get rounds to display - always show only the selected round
    var displayedRounds: [FixtureRound] {
        if let selectedRound = selectedRound {
            return [selectedRound]
        } else {
            return []
        }
    }

    /// Check if there are any fixtures
    var hasFixtures: Bool {
        !sortedRounds.isEmpty
    }

    /// Get the display name for the selected round
    var selectedRoundDisplayName: String {
        guard let selectedRound = selectedRound else {
            return "Select Matchweek"
        }
        return formatRoundName(selectedRound.name)
    }

    // MARK: - Public Methods for Round Selection

    /// Select a specific round
    func selectRound(_ round: FixtureRound) {
        selectedRound = round
    }
    
    // MARK: - Private Methods

    /// Auto-expand rounds that have live fixtures
    private func expandRoundsWithLiveFixtures() {
        guard let fixturesByRounds = fixturesByRounds else { return }

        for round in fixturesByRounds.liveRounds {
            expandedRounds.insert(round.name)
        }
    }

    /// Set the default selected round (prioritize active/current matchweek)
    private func setDefaultSelectedRound() {
        guard let fixturesByRounds = fixturesByRounds else { return }

        // Priority 1: Round with live fixtures (currently active)
        if let liveRound = fixturesByRounds.liveRounds.first {
            selectedRound = liveRound
            return
        }

        // Priority 2: Current matchweek - round with upcoming fixtures closest to today
        let upcomingRounds = fixturesByRounds.upcomingRounds
        if let currentMatchweek = findCurrentMatchweek(from: upcomingRounds) {
            selectedRound = currentMatchweek
            return
        }

        // Priority 3: For finished seasons, show the last/final matchweek
        let finishedRounds = fixturesByRounds.finishedRounds
        if !finishedRounds.isEmpty && upcomingRounds.isEmpty {
            // This is a finished season - show the last matchweek
            if let lastMatchweek = findLastMatchweek(from: finishedRounds) {
                selectedRound = lastMatchweek
                return
            }
        }

        // Priority 4: Most recent active round (just finished, but season still ongoing)
        if let recentFinishedRound = finishedRounds.last {
            selectedRound = recentFinishedRound
            return
        }

        // Fallback: First available round
        selectedRound = fixturesByRounds.sortedRounds.first
    }

    /// Find the current matchweek from upcoming rounds (closest to today)
    private func findCurrentMatchweek(from upcomingRounds: [FixtureRound]) -> FixtureRound? {
        let now = Date()
        var closestRound: FixtureRound?
        var closestTimeDifference: TimeInterval = .greatestFiniteMagnitude

        for round in upcomingRounds {
            // Find the earliest fixture in this round
            if let earliestFixture = round.fixtures.min(by: { fixture1, fixture2 in
                guard let timestamp1 = fixture1.timestamp,
                      let timestamp2 = fixture2.timestamp else {
                    return false
                }
                return timestamp1 < timestamp2
            }) {
                if let fixtureTimestamp = earliestFixture.timestamp {
                    let fixtureDate = Date(timeIntervalSince1970: TimeInterval(fixtureTimestamp))
                    let timeDifference = abs(fixtureDate.timeIntervalSince(now))

                    // Only consider fixtures that are upcoming (not in the past)
                    if fixtureDate >= now && timeDifference < closestTimeDifference {
                        closestTimeDifference = timeDifference
                        closestRound = round
                    }
                }
            }
        }

        return closestRound
    }

    /// Find the last matchweek for finished seasons (highest round number or latest date)
    private func findLastMatchweek(from finishedRounds: [FixtureRound]) -> FixtureRound? {
        // Try to find the round with the highest number first (e.g., "Matchweek 38")
        let roundsWithNumbers = finishedRounds.compactMap { round -> (FixtureRound, Int)? in
            if let roundNumber = extractRoundNumber(from: round.name) {
                return (round, roundNumber)
            }
            return nil
        }

        if !roundsWithNumbers.isEmpty {
            // Return the round with the highest number
            let lastRoundByNumber = roundsWithNumbers.max { $0.1 < $1.1 }
            return lastRoundByNumber?.0
        }

        // Fallback: Find the round with the latest fixture date
        var latestRound: FixtureRound?
        var latestTimestamp: Int = 0

        for round in finishedRounds {
            // Find the latest fixture in this round
            if let latestFixture = round.fixtures.max(by: { fixture1, fixture2 in
                guard let timestamp1 = fixture1.timestamp,
                      let timestamp2 = fixture2.timestamp else {
                    return false
                }
                return timestamp1 < timestamp2
            }) {
                if let fixtureTimestamp = latestFixture.timestamp,
                   fixtureTimestamp > latestTimestamp {
                    latestTimestamp = fixtureTimestamp
                    latestRound = round
                }
            }
        }

        return latestRound
    }

    /// Update selected round after refresh, maintaining selection if possible
    private func updateSelectedRoundAfterRefresh() {
        guard let fixturesByRounds = fixturesByRounds else { return }

        // If we had a selected round, try to find it in the updated data
        if let currentSelectedRound = selectedRound {
            if let updatedRound = fixturesByRounds.rounds.first(where: { $0.name == currentSelectedRound.name }) {
                selectedRound = updatedRound
                return
            }
        }

        // If previous selection is no longer available, set default
        setDefaultSelectedRound()
    }

    /// Format round name for display (e.g., "Regular Season - 15" -> "Matchweek 15")
    private func formatRoundName(_ roundName: String) -> String {
        // Transform "Regular Season - X" to "Matchweek X"
        if roundName.hasPrefix("Regular Season - ") {
            let number = roundName.replacingOccurrences(of: "Regular Season - ", with: "")
            return "Matchweek \(number)"
        }

        // Handle other common round name patterns
        if roundName.contains("Regular Season") {
            let components = roundName.components(separatedBy: " - ")
            if components.count > 1, let number = components.last {
                return "Matchweek \(number)"
            }
        }

        // Return original name for other formats (like "Final", "Semi-final", etc.)
        return roundName
    }
    
    /// Extract round number from round name for sorting
    private func extractRoundNumber(from roundName: String) -> Int? {
        let pattern = #"(\d+)"#
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: roundName.utf16.count)
        
        if let match = regex?.firstMatch(in: roundName, range: range) {
            let numberRange = Range(match.range(at: 1), in: roundName)
            if let numberRange = numberRange {
                return Int(String(roundName[numberRange]))
            }
        }
        
        return nil
    }
}
