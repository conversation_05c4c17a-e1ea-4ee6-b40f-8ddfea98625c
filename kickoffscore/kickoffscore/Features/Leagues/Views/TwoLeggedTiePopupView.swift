import SwiftUI
import <PERSON><PERSON>er

/// Popup view that displays both legs of a two-legged knockout tie
struct TwoLeggedTiePopupView: View {
    let matchData: KnockoutMatchData
    @Binding var isPresented: Bool
    let onFixtureSelected: (Int) -> Void
    @EnvironmentObject private var navigationState: NavigationStateManager
    @State private var fixtures: [Fixture] = []
    @State private var isLoading = true
    @State private var errorMessage: String?
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection

            // Content
            if isLoading {
                loadingView
            } else if let error = errorMessage {
                errorView(error)
            } else {
                contentView
            }

            // Close button
            closeButtonSection
        }
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusL)
        .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
        .padding(.horizontal, AppLayout.spacingL)
        .task {
            await loadFixtureDetails()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: AppLayout.spacingXS) {
            Text("Two-legged tie")
                .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                .foregroundColor(AppColors.text)
                .multilineTextAlignment(.center)
        }
        .padding(.top, AppLayout.spacingM)
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.bottom, AppLayout.spacingXS)
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: AppLayout.spacingM) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(AppColors.Brand.primary)
            
            Text("Loading match details...")
                .font(AppTypography.dynamicFont(style: .body, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
        }
        .frame(height: 200)
    }
    
    // MARK: - Error View
    private func errorView(_ error: String) -> some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 32))
                .foregroundColor(AppColors.error)
            
            Text("Unable to load match details")
                .font(AppTypography.dynamicFont(style: .body, weight: .medium))
                .foregroundColor(AppColors.text)
            
            Text(error)
                .font(AppTypography.dynamicFont(style: .caption, weight: .regular))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
        }
        .frame(height: 200)
        .padding(.horizontal, AppLayout.spacingL)
    }
    
    // MARK: - Content View
    private var contentView: some View {
        VStack(spacing: AppLayout.spacingM) {
            ForEach(Array(fixtures.enumerated()), id: \.element.id) { index, fixture in
                Button(action: {
                    handleFixtureTap(fixture: fixture)
                }) {
                    fixtureRow(fixture: fixture, legNumber: index + 1)
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle()) // Make the entire row tappable
                .highPriorityGesture(
                    TapGesture()
                        .onEnded { _ in
                            handleFixtureTap(fixture: fixture)
                        }
                )
            }
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.vertical, AppLayout.spacingM)
    }

    // MARK: - Fixture Tap Handler
    private func handleFixtureTap(fixture: Fixture) {
        // Add haptic feedback for better UX
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // Close popup and navigate to fixture detail
        isPresented = false

        // Small delay to allow popup to close smoothly
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            navigationState.navigateToFixtureDetail(fixture: fixture)
        }
    }
    
    // MARK: - Fixture Row
    private func fixtureRow(fixture: Fixture, legNumber: Int) -> some View {
        VStack(spacing: AppLayout.spacingXS) {
            // Date indicator - centered and better formatted
            Text(formatLegDate(fixture.date))
                .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
                .frame(maxWidth: .infinity, alignment: .center)

            // Match details - more compact layout
            HStack(spacing: AppLayout.spacingS) {
                // Home team (left side) - more flexible width
                HStack(spacing: AppLayout.spacingXS) {
                    Text(fixture.teams.home?.name ?? "Home")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(AppColors.text)
                        .lineLimit(1)
                        .minimumScaleFactor(0.7) // Allow text to scale down
                        .multilineTextAlignment(.trailing)

                    CachedImageView.teamLogo(
                        url: fixture.teams.home?.logo,
                        size: 20,
                        teamName: fixture.teams.home?.name ?? "Home"
                    )
                }
                .frame(maxWidth: .infinity, alignment: .trailing)

                // Score (center) - more compact
                HStack(spacing: 2) {
                    Text("\(fixture.goals.home ?? 0)")
                        .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                        .foregroundColor(AppColors.text)
                        .frame(minWidth: 20)

                    Text("-")
                        .font(AppTypography.dynamicFont(style: .title3, weight: .medium))
                        .foregroundColor(AppColors.secondaryText)

                    Text("\(fixture.goals.away ?? 0)")
                        .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                        .foregroundColor(AppColors.text)
                        .frame(minWidth: 20)
                }
                .padding(.horizontal, AppLayout.spacingXS)

                // Away team (right side) - more flexible width
                HStack(spacing: AppLayout.spacingXS) {
                    CachedImageView.teamLogo(
                        url: fixture.teams.away?.logo,
                        size: 20,
                        teamName: fixture.teams.away?.name ?? "Away"
                    )

                    Text(fixture.teams.away?.name ?? "Away")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(AppColors.text)
                        .lineLimit(1)
                        .minimumScaleFactor(0.7) // Allow text to scale down
                        .multilineTextAlignment(.leading)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .padding(.vertical, AppLayout.spacingS)
        .padding(.horizontal, AppLayout.spacingM)
    }
    
    // MARK: - Close Button Section
    private var closeButtonSection: some View {
        Button(action: {
            handleCloseButtonTap()
        }) {
            Text("Close")
                .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                .foregroundColor(AppColors.Brand.primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, AppLayout.spacingM)
        }
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle()) // Make the entire button area tappable
        .highPriorityGesture(
            TapGesture()
                .onEnded { _ in
                    handleCloseButtonTap()
                }
        )
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.bottom, AppLayout.spacingM)
    }

    // MARK: - Close Button Handler
    private func handleCloseButtonTap() {
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // Close the popup with animation
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            isPresented = false
        }
    }
    
    // MARK: - Helper Methods
    private func loadFixtureDetails() async {
        guard matchData.isAggregate && matchData.fixtureIds.count > 1 else {
            errorMessage = "Invalid fixture data"
            isLoading = false
            return
        }
        
        var loadedFixtures: [Fixture] = []
        
        for fixtureId in matchData.fixtureIds {
            do {
                let fixture = try await fetchFixture(id: fixtureId)
                loadedFixtures.append(fixture)
            } catch {
                print("Error loading fixture \(fixtureId): \(error)")
            }
        }
        
        await MainActor.run {
            if loadedFixtures.isEmpty {
                errorMessage = "Unable to load fixture details"
            } else {
                fixtures = loadedFixtures.sorted { fixture1, fixture2 in
                    // Sort by date to show chronological order
                    fixture1.date < fixture2.date
                }
            }
            isLoading = false
        }
    }
    
    private func fetchFixture(id: Int) async throws -> Fixture {
        return try await withCheckedThrowingContinuation { continuation in
            // When fetching by ID, the API returns a single fixture directly, not wrapped in a response array
            APIService.shared.fetchData(endpoint: "/fixtures", parameters: ["id": String(id)]) { (result: Result<Fixture, APIError>) in
                switch result {
                case .success(let fixture):
                    continuation.resume(returning: fixture)
                case .failure(let error):
                    print("Error fetching fixture \(id): \(error)")
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    private func formatDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
        
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MMM d, yyyy"
            return displayFormatter.string(from: date)
        }
        
        return dateString
    }
    
    private func formatLegDate(_ dateString: String) -> String {
        print("🗓️ Formatting date string: '\(dateString)'")

        let formatters = [
            "yyyy-MM-dd'T'HH:mm:ssZ",
            "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
            "yyyy-MM-dd'T'HH:mm:ss+HH:mm",
            "yyyy-MM-dd'T'HH:mm:ss.SSS+HH:mm",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd"
        ]

        for formatString in formatters {
            let formatter = DateFormatter()
            formatter.dateFormat = formatString
            formatter.locale = Locale(identifier: "en_US_POSIX")
            formatter.timeZone = TimeZone(secondsFromGMT: 0)

            if let date = formatter.date(from: dateString) {
                let displayFormatter = DateFormatter()
                displayFormatter.dateFormat = "EEE d MMM" // Wed 5 Mar
                displayFormatter.locale = Locale.current
                let result = displayFormatter.string(from: date)
                print("✅ Successfully formatted to: '\(result)'")
                return result
            }
        }

        print("❌ Failed to parse date, returning original: '\(dateString)'")
        return dateString
    }
}

// MARK: - Preview
struct TwoLeggedTiePopupView_Previews: PreviewProvider {
    static var previews: some View {
        let mockMatchData = KnockoutMatchData(
            id: 1,
            homeTeam: KnockoutTeam(id: 1, name: "PSG", logo: nil),
            awayTeam: KnockoutTeam(id: 2, name: "Brest", logo: nil),
            homeScore: 7,
            awayScore: 3,
            homePenaltyScore: nil,
            awayPenaltyScore: nil,
            status: "FT",
            date: "2024-02-19T20:00:00Z",
            isFinished: true,
            isPenalties: false,
            isAggregate: true,
            fixtureIds: [1, 2]
        )

        TwoLeggedTiePopupView(
            matchData: mockMatchData,
            isPresented: .constant(true),
            onFixtureSelected: { _ in }
        )
        .environmentObject(NavigationStateManager())
        .background(Color.black.opacity(0.3))
    }
}
