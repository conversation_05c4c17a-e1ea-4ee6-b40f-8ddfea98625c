import SwiftUI

struct LeagueStandingsView: View {
    let leagueId: Int
    let season: Int
    @StateObject private var viewModel: LeagueStandingsViewModel
    
    init(leagueId: Int, season: Int) {
        self.leagueId = leagueId
        self.season = season
        self._viewModel = StateObject(wrappedValue: LeagueStandingsViewModel(leagueId: leagueId, season: season))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            if viewModel.isLoading {
                loadingView
            } else if let errorMessage = viewModel.errorMessage {
                errorView(message: errorMessage)
            } else if !viewModel.standings.isEmpty {
                standingsContent
            } else {
                emptyStateView
            }
        }
        .background(AppColors.background)
        .task {
            await viewModel.loadStandings()
        }
        .task(id: season) {
            // Update season and reload standings when season changes
            await viewModel.updateSeason(season)
        }
        .refreshable {
            await viewModel.refreshStandings()
        }
    }
    
    // MARK: - Content Views
    private var loadingView: some View {
        VStack(spacing: AppLayout.spacingL) {
            ForEach(0..<12, id: \.self) { _ in
                SkeletonStandingRowView()
            }
        }
        .padding(AppLayout.spacingM)
    }
    
    private func errorView(message: String) -> some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.Brand.secondary)

            Text("Error Loading Standings")
                .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                .foregroundColor(AppColors.text)

            Text(message)
                .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingL)

            Button("Try Again") {
                Task {
                    await viewModel.refreshStandings()
                }
            }
            .prominentButtonStyle()
        }
        .padding(AppLayout.spacingL)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "list.number")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.Brand.secondary)

            Text("No Standings Available")
                .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                .foregroundColor(AppColors.text)

            Text("Standings are not available for this season.")
                .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
        }
        .padding(AppLayout.spacingL)
    }
    
    private var standingsContent: some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: AppLayout.spacingM) {
                ForEach(Array(viewModel.standings.enumerated()), id: \.offset) { index, group in
                    CombinedStandingsView(
                        standings: group,
                        homeTeamId: nil,
                        awayTeamId: nil,
                        viewModel: viewModel.standingsViewModel
                    )
                }

                // Legend at the bottom
                if !viewModel.standings.isEmpty {
                    LeagueStandingsLegendView()
                        .padding([.top], AppLayout.spacingM)
                }
            }
            .padding(.top, AppLayout.spacingS)
            .padding(.bottom, AppLayout.spacingM)
            .padding(.horizontal, 0) // Remove horizontal padding for edge-to-edge
        }
        .background(AppColors.background)
        .edgesIgnoringSafeArea(.horizontal) // Ignore safe area for edge-to-edge
    }
}

// MARK: - Skeleton Views
struct SkeletonStandingRowView: View {
    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            // Position
            RoundedRectangle(cornerRadius: 4)
                .fill(AppColors.skeleton)
                .frame(width: 20, height: 16)
            
            // Team logo
            Circle()
                .fill(AppColors.skeleton)
                .frame(width: 24, height: 24)
            
            // Team name
            RoundedRectangle(cornerRadius: 4)
                .fill(AppColors.skeleton)
                .frame(height: 16)
            
            Spacer()
            
            // Stats
            ForEach(0..<4, id: \.self) { _ in
                RoundedRectangle(cornerRadius: 4)
                    .fill(AppColors.skeleton)
                    .frame(width: 24, height: 16)
            }
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.vertical, AppLayout.spacingS)
        .shimmering()
    }
}

// MARK: - League Standings Legend View
struct LeagueStandingsLegendView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingM) {
            Text("Table Legend")
                .font(AppTypography.dynamicFont(style: .headline, weight: .semibold))
                .foregroundColor(AppColors.text)

            VStack(alignment: .leading, spacing: AppLayout.spacingS) {
                Text("Abbreviations")
                    .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                    .foregroundColor(AppColors.text)

                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), alignment: .leading), count: 2), spacing: AppLayout.spacingS) {
                    LegendItem(key: "MP", value: "Matches Played")
                    LegendItem(key: "W", value: "Wins")
                    LegendItem(key: "D", value: "Draws")
                    LegendItem(key: "L", value: "Losses")
                    LegendItem(key: "GF", value: "Goals For")
                    LegendItem(key: "GA", value: "Goals Against")
                    LegendItem(key: "GD", value: "Goal Difference")
                    LegendItem(key: "Pts", value: "Points")
                }
            }
        }
        .padding(AppLayout.spacingM)
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusM)
        .padding(.horizontal, AppLayout.spacingM)
    }
}

// Helper view for legend items
private struct LegendItem: View {
    let key: String
    let value: String

    var body: some View {
        HStack(spacing: AppLayout.spacingS) {
            Text(key)
                .font(AppTypography.dynamicFont(style: .caption, weight: .bold))
                .foregroundColor(AppColors.secondaryText)
                .frame(width: 30, alignment: .leading)

            Text(value)
                .font(AppTypography.dynamicFont(style: .caption))
                .foregroundColor(AppColors.text)
        }
    }
}

// MARK: - Preview
#Preview {
    LeagueStandingsView(leagueId: 39, season: 2024)
}
