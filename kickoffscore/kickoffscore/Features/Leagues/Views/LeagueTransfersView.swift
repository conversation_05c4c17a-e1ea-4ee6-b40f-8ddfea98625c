import SwiftUI
import King<PERSON>er

struct LeagueTransfersView: View {
    let leagueId: Int
    
    @StateObject private var viewModel = LeagueTransfersViewModel()
    
    var body: some View {
        VStack(spacing: 0) {
            if viewModel.isLoading {
                loadingView
            } else if let errorMessage = viewModel.errorMessage {
                errorView(message: errorMessage)
            } else if !viewModel.recentTransfers.isEmpty {
                transfersContent
            } else {
                emptyStateView
            }
        }
        .background(AppColors.background)
        .task {
            await viewModel.loadTransfers(leagueId: leagueId)
        }
        .refreshable {
            await viewModel.refreshTransfers(leagueId: leagueId)
        }
    }
    
    // MARK: - Content Views
    private var loadingView: some View {
        VStack(spacing: AppLayout.spacingM) {
            ForEach(0..<8, id: \.self) { _ in
                SkeletonTransferCardView()
            }
        }
        .padding(AppLayout.spacingM)
    }
    
    private var transfersContent: some View {
        ScrollView {
            LazyVStack(spacing: AppLayout.spacingM) {
                ForEach(viewModel.recentTransfers, id: \.id) { transferInfo in
                    if let transfer = viewModel.getTransfer(for: transferInfo) {
                        TransferCardView(
                            transfer: transfer,
                            transferInfo: transferInfo
                        )
                    }
                }
            }
            .padding(AppLayout.spacingM)
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: AppLayout.spacingL) {
            Image(systemName: "arrow.left.arrow.right")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.Brand.secondary)
            
            Text("No Transfers")
                .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                .foregroundColor(AppColors.text)
            
            Text("No recent transfer activity found for this league.")
                .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingL)
        }
        .padding(.vertical, AppLayout.spacingXXL)
    }
    
    private func errorView(message: String) -> some View {
        VStack(spacing: AppLayout.spacingL) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.error)
            
            Text("Error Loading Transfers")
                .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                .foregroundColor(AppColors.text)
            
            Text(message)
                .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingL)
            
            Button("Try Again") {
                Task {
                    await viewModel.refreshTransfers(leagueId: leagueId)
                }
            }
            .prominentButtonStyle()
        }
        .padding(.vertical, AppLayout.spacingXXL)
    }
}

// MARK: - Transfer Card View

struct TransferCardView: View {
    let transfer: Transfer
    let transferInfo: TransferInfo
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with transfer type and time
            HStack {
                Text("DONE DEAL")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .bold))
                    .foregroundColor(AppColors.text)
                
                Spacer()
                
                Text(transferInfo.timeAgo)
                    .font(AppTypography.dynamicFont(style: .caption, weight: .regular))
                    .foregroundColor(AppColors.secondaryText)
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.top, AppLayout.spacingM)
            
            // Main content
            HStack(spacing: AppLayout.spacingM) {
                // Player photo with fallback to initials
                CachedImageView.playerPhoto(
                    url: "https://media.api-sports.io/football/players/\(transfer.player.id).png",
                    size: 50,
                    playerName: transfer.player.name
                )
                
                // Player info and transfer details
                VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                    // Player name
                    Text(transfer.player.name)
                        .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                        .foregroundColor(AppColors.text)
                        .lineLimit(1)
                    
                    // Position placeholder (we don't have this data yet)
                    Text("Player")
                        .font(AppTypography.dynamicFont(style: .caption, weight: .regular))
                        .foregroundColor(AppColors.secondaryText)
                    
                    // Transfer type badge
                    HStack {
                        Text(transferInfo.displayType)
                            .font(AppTypography.dynamicFont(style: .caption2, weight: .bold))
                            .foregroundColor(.white)
                            .padding(.horizontal, AppLayout.spacingS)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(transferInfo.isLoan ? AppColors.Brand.secondary : AppColors.success)
                            )
                        
                        Spacer()
                    }
                }
                
                Spacer()
                
                // Team logo
                TeamLogo(url: transferInfo.teams.in.logo, teamName: transferInfo.teams.in.name)
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, AppLayout.spacingS)
            
            // Teams transfer direction
            HStack {
                Text(transferInfo.teams.out.name)
                    .font(AppTypography.dynamicFont(style: .caption, weight: .regular))
                    .foregroundColor(AppColors.secondaryText)
                    .lineLimit(1)
                
                Image(systemName: "arrow.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
                
                Text(transferInfo.teams.in.name)
                    .font(AppTypography.dynamicFont(style: .caption, weight: .regular))
                    .foregroundColor(AppColors.secondaryText)
                    .lineLimit(1)
                
                Spacer()
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.bottom, AppLayout.spacingM)
        }
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusM)
    }
    
    // Helper View for Team Logo
    private func TeamLogo(url: String?, teamName: String) -> some View {
        CachedImageView.teamLogo(
            url: url,
            size: 24,
            teamName: teamName
        )
    }
}

// MARK: - Skeleton Loading View

struct SkeletonTransferCardView: View {
    var body: some View {
        VStack(spacing: 0) {
            // Header skeleton
            HStack {
                RoundedRectangle(cornerRadius: 4)
                    .fill(AppColors.skeleton)
                    .frame(width: 80, height: 12)
                
                Spacer()
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(AppColors.skeleton)
                    .frame(width: 60, height: 12)
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.top, AppLayout.spacingM)
            
            // Main content skeleton
            HStack(spacing: AppLayout.spacingM) {
                Circle()
                    .fill(AppColors.skeleton)
                    .frame(width: 50, height: 50)

                VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(AppColors.skeleton)
                        .frame(width: 120, height: 16)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(AppColors.skeleton)
                        .frame(width: 60, height: 12)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(AppColors.skeleton)
                        .frame(width: 40, height: 16)
                }

                Spacer()

                Circle()
                    .fill(AppColors.skeleton)
                    .frame(width: 24, height: 24)
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, AppLayout.spacingS)
            
            // Teams skeleton
            HStack {
                RoundedRectangle(cornerRadius: 4)
                    .fill(AppColors.skeleton)
                    .frame(width: 80, height: 12)
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(AppColors.skeleton)
                    .frame(width: 12, height: 12)
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(AppColors.skeleton)
                    .frame(width: 80, height: 12)
                
                Spacer()
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.bottom, AppLayout.spacingM)
        }
        .background(AppColors.tertiaryBackground)
        .cornerRadius(AppLayout.cornerRadiusM)
        .redacted(reason: .placeholder)
    }
}

// MARK: - Preview

#Preview {
    LeagueTransfersView(leagueId: 39)
        .background(AppColors.background)
}
