import SwiftUI

/// Column indicator for knockout bracket navigation
struct KnockoutColumnIndicator: View {
    let columnNames: [String]
    @Binding var focusedColumnIndex: Int
    
    var body: some View {
        ScrollViewReader { scrollProxy in
            ScrollView(.horizontal, showsIndicators: false) {
                scrollViewContent
            }
            .onChange(of: focusedColumnIndex) { newIndex in
                scroll(to: newIndex, using: scrollProxy)
            }
        }
    }
    
    var scrollViewContent: some View {
        HStack(spacing: AppLayout.spacingXL) {
            ForEach(0..<columnNames.count, id: \.self) { columnIndex in
                indicator(for: columnIndex)
            }
        }
        .padding(.horizontal, AppLayout.spacingL)
        .padding(.vertical, AppLayout.spacingM)
    }
    
    private func indicator(for columnIndex: Int) -> some View {
        Button(action: { didTapColumnIndicator(at: columnIndex) }) {
            VStack(spacing: AppLayout.spacingXS) {
                Text(columnNames[columnIndex])
                    .font(AppTypography.dynamicFont(style: .callout, weight: .bold))
                    .foregroundColor(columnIndex == focusedColumnIndex ? AppColors.Brand.primary : AppColors.secondaryText)
                    .multilineTextAlignment(.center)

                // Underline indicator
                Rectangle()
                    .frame(height: 2)
                    .foregroundColor(columnIndex == focusedColumnIndex ? AppColors.Brand.primary : Color.clear)
            }
            .id(columnIndex)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("Select \(columnNames[columnIndex])")
        .accessibilityHint("Tap to view \(columnNames[columnIndex]) bracket")
        // Ensure the button takes priority over any scroll gestures
        .highPriorityGesture(
            TapGesture()
                .onEnded { _ in
                    // Add haptic feedback for better UX
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()

                    didTapColumnIndicator(at: columnIndex)
                }
        )
    }
    
    private func scroll(to index: Int, using scrollProxy: ScrollViewProxy) {
        withAnimation(.easeInOut(duration: 0.3)) {
            scrollProxy.scrollTo(index, anchor: .center)
        }
    }
    
    private func didTapColumnIndicator(at index: Int) {
        withAnimation(.easeInOut(duration: 0.3)) {
            focusedColumnIndex = index
        }
    }
}

// MARK: - Preview
struct KnockoutColumnIndicator_Previews: PreviewProvider {
    static var previews: some View {
        KnockoutColumnIndicatorPreview()
            .background(AppColors.background)
    }
}

private struct KnockoutColumnIndicatorPreview: View {
    @State private var focusedColumnIndex = 0

    var body: some View {
        KnockoutColumnIndicator(
            columnNames: ["Playoff round", "Round of 16", "Quarterfinals", "Semifinals", "Final"],
            focusedColumnIndex: $focusedColumnIndex
        )
    }
}
