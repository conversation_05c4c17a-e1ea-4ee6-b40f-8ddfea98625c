import SwiftUI

/// A pull-down button component for selecting matchweeks following Apple's design guidelines
struct MatchweekPullDownButton: View {
    let selectedRoundName: String
    let availableRounds: [FixtureRound]
    let onRoundSelected: (FixtureRound) -> Void
    
    var body: some View {
        Menu {
            ForEach(availableRounds, id: \.id) { round in
                Button(action: {
                    onRoundSelected(round)
                }) {
                    HStack {
                        Text(formatRoundName(round.name))
                            .font(AppTypography.dynamicFont(style: .body, weight: .medium))
                            .frame(maxWidth: .infinity, alignment: .leading)

                        // Show checkmark for selected round
                        if round.name == getCurrentRoundName() {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppColors.Brand.primary)
                                .font(.system(size: 14, weight: .semibold))
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        } label: {
            HStack(spacing: AppLayout.spacingS) {
                Text(selectedRoundName)
                    .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                    .foregroundColor(AppColors.text)
                    .lineLimit(1)

                Spacer()

                Image(systemName: "chevron.down")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, AppLayout.spacingS)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                    .fill(AppColors.tertiaryBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                            .stroke(AppColors.separator.opacity(0.5), lineWidth: 0.5)
                    )
            )
        }
        .menuStyle(BorderlessButtonMenuStyle())
        .menuOrder(.priority)
        .frame(maxWidth: .infinity)
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Helper Methods
    
    /// Get the current round name from selected round name for comparison
    private func getCurrentRoundName() -> String {
        // Find the round that matches the selected display name
        for round in availableRounds {
            if formatRoundName(round.name) == selectedRoundName {
                return round.name
            }
        }
        return ""
    }
    
    /// Format round name for display (consistent with MatchdaySection)
    private func formatRoundName(_ roundName: String) -> String {
        // Transform "Regular Season - X" to "Matchweek X"
        if roundName.hasPrefix("Regular Season - ") {
            let number = roundName.replacingOccurrences(of: "Regular Season - ", with: "")
            return "Matchweek \(number)"
        }

        // Handle other common round name patterns
        if roundName.contains("Regular Season") {
            let components = roundName.components(separatedBy: " - ")
            if components.count > 1, let number = components.last {
                return "Matchweek \(number)"
            }
        }

        // Return original name for other formats (like "Final", "Semi-final", etc.)
        return roundName
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        MatchweekPullDownButton(
            selectedRoundName: "Matchweek 15",
            availableRounds: [
                FixtureRound(name: "Regular Season - 14", fixtures: []),
                FixtureRound(name: "Regular Season - 15", fixtures: []),
                FixtureRound(name: "Regular Season - 16", fixtures: [])
            ],
            onRoundSelected: { _ in }
        )

        MatchweekPullDownButton(
            selectedRoundName: "Final",
            availableRounds: [
                FixtureRound(name: "Semi-final", fixtures: []),
                FixtureRound(name: "Final", fixtures: [])
            ],
            onRoundSelected: { _ in }
        )
    }
    .padding()
    .background(AppColors.background)
}
