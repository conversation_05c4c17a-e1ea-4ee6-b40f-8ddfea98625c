import SwiftUI

/// Main knockout bracket view with horizontal scrolling and snap gestures
struct KnockoutBracketView: View {
    let tournament: KnockoutTournament
    
    // MARK: - State Properties
    @State private var dragOffsetX: CGFloat = 0
    @State private var focusedColumnIndex: Int = 0
    @State private var showTwoLeggedTiePopup = false
    @State private var selectedMatchData: KnockoutMatchData?

    // MARK: - Environment Objects
    @EnvironmentObject private var navigationState: NavigationStateManager
    
    // MARK: - Layout Constants
    private let columnWidth: CGFloat = UIScreen.main.bounds.width * 0.9
    
    private var numberOfColumns: Int {
        max(1, tournament.brackets.count) // Ensure at least 1 to prevent division by zero
    }

    private var offsetX: CGFloat {
        let baseOffset = -CGFloat(focusedColumnIndex) * columnWidth
        let totalOffset = baseOffset + dragOffsetX + leadingOffsetXForCurrentColumn

        // Ensure we never return NaN or infinite values
        if totalOffset.isNaN || totalOffset.isInfinite {
            return 0
        }

        return totalOffset
    }

    private var leadingOffsetXForCurrentColumn: CGFloat {
        let screenWidth = UIScreen.main.bounds.width

        // Ensure screen width is valid
        guard screenWidth > 0 && !screenWidth.isNaN else { return 0 }

        if focusedColumnIndex == 0 {
            return 0
        } else if focusedColumnIndex == numberOfColumns - 1 {
            return screenWidth * 0.10
        } else {
            return screenWidth * 0.05
        }
    }
    
    var body: some View {
        ScrollViewReader { scrollViewProxy in
            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: 0) {
                    KnockoutColumnIndicator(
                        columnNames: tournament.brackets.map { $0.displayName },
                        focusedColumnIndex: $focusedColumnIndex
                    )
                    .id("scroll-to-top-anchor")

                    ScrollView(.horizontal, showsIndicators: false) {
                        columns
                            .offset(x: offsetX)
                            .padding(.bottom, AppLayout.spacingL) // Add bottom padding for better scrolling
                    }
                    .frame(width: UIScreen.main.bounds.size.width)
                    .scrollDisabled(true)
                    .gesture(DragGesture(minimumDistance: 12, coordinateSpace: .global)
                        .onChanged(updateCurrentOffsetX)
                        .onEnded(handleDragEnded)
                    )
                }
            }

            .onChange(of: focusedColumnIndex) { _ in
                withAnimation(.easeInOut(duration: 0.3)) {
                    scrollViewProxy.scrollTo("scroll-to-top-anchor")
                }
            }
        }
        .background(AppColors.background)
        .onAppear {
            setDefaultFocusedColumn()
        }
        .overlay(
            // Two-legged tie popup overlay
            Group {
                if showTwoLeggedTiePopup, let matchData = selectedMatchData {
                    Color.black.opacity(0.4)
                        .ignoresSafeArea()
                        .onTapGesture {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                showTwoLeggedTiePopup = false
                            }
                        }

                    TwoLeggedTiePopupView(
                        matchData: matchData,
                        isPresented: $showTwoLeggedTiePopup,
                        onFixtureSelected: { fixtureId in
                            // This callback is handled within the popup view
                        }
                    )
                    .environmentObject(navigationState)
                    .transition(.opacity.combined(with: .scale(scale: 0.9)))
                }
            }
        )
    }
    
    // MARK: - Content Views
    
    private var columns: some View {
        HStack(alignment: .top, spacing: 0) {
            ForEach(0..<tournament.brackets.count, id: \.self) { columnIndex in
                KnockoutBracketColumnView(
                    bracket: tournament.brackets[columnIndex],
                    columnIndex: columnIndex,
                    focusedColumnIndex: focusedColumnIndex,
                    lastColumnIndex: numberOfColumns - 1,
                    didTapCell: { matchData in
                        handleFixtureTap(matchData: matchData)
                    }
                )
                .frame(width: max(100, columnWidth)) // Ensure minimum width
            }
        }
        .frame(width: max(100, CGFloat(numberOfColumns) * columnWidth)) // Ensure minimum total width
    }
    
    // MARK: - Gesture Handling
    
    private func updateCurrentOffsetX(_ dragGestureValue: DragGesture.Value) {
        dragOffsetX = dragGestureValue.translation.width
    }
    
    private func handleDragEnded(_ gestureValue: DragGesture.Value) {
        let isScrollingRight = dragOffsetX < 0
        let didScrollEnough = abs(gestureValue.translation.width) > columnWidth * 0.5
        let isFirstColumn = focusedColumnIndex == 0
        let isLastColumn = focusedColumnIndex == numberOfColumns - 1
        
        guard didScrollEnough else {
            return stayAtSameIndex()
        }
        
        if isScrollingRight, !isLastColumn {
            moveToRight()
        } else if !isScrollingRight, !isFirstColumn {
            moveToLeft()
        } else {
            stayAtSameIndex()
        }
    }
    
    private func moveToLeft() {
        withAnimation(.easeInOut(duration: 0.3)) {
            focusedColumnIndex = max(0, focusedColumnIndex - 1)
            dragOffsetX = 0
        }
    }

    private func moveToRight() {
        withAnimation(.easeInOut(duration: 0.3)) {
            focusedColumnIndex = min(numberOfColumns - 1, focusedColumnIndex + 1)
            dragOffsetX = 0
        }
    }
    
    private func stayAtSameIndex() {
        withAnimation(.easeInOut(duration: 0.3)) {
            dragOffsetX = 0
        }
    }
    
    // MARK: - Helper Methods

    private func setDefaultFocusedColumn() {
        // Ensure we have valid brackets
        guard !tournament.brackets.isEmpty else {
            focusedColumnIndex = 0
            return
        }

        // Focus on the active bracket (live > upcoming > most recent completed)
        if let activeBracket = tournament.activeBracket,
           let index = tournament.brackets.firstIndex(where: { $0.id == activeBracket.id }) {
            focusedColumnIndex = max(0, min(index, numberOfColumns - 1))
        } else {
            // Default to first column
            focusedColumnIndex = 0
        }
    }

    private func handleFixtureTap(matchData: KnockoutMatchData) {
        // Check if this is a two-legged tie (aggregate match with multiple fixture IDs)
        if matchData.isAggregate && matchData.fixtureIds.count > 1 {
            // Show popup for two-legged tie
            selectedMatchData = matchData
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                showTwoLeggedTiePopup = true
            }
        } else {
            // Single match - navigate directly to fixture detail
            navigateToSingleFixture(matchData: matchData)
        }
    }

    private func navigateToSingleFixture(matchData: KnockoutMatchData) {
        // Create a Fixture object from the KnockoutMatchData
        let fixture = Fixture(
            id: matchData.id,
            referee: nil,
            timezone: nil,
            date: matchData.date ?? "",
            timestamp: nil,
            venue: nil,
            status: Fixture.Status(
                long: matchData.status,
                short: matchData.status,
                elapsed: nil,
                extra: nil
            ),
            league: Fixture.LeagueInfo(
                id: 0, // Default league ID for knockout matches
                name: "Knockout Tournament",
                country: nil,
                logo: nil,
                flag: nil,
                season: nil,
                round: nil,
                coverage: nil
            ),
            teams: Fixture.Teams(
                home: Fixture.TeamInfo(
                    id: matchData.homeTeam.id,
                    name: matchData.homeTeam.name,
                    logo: matchData.homeTeam.logo,
                    winner: matchData.winner?.id == matchData.homeTeam.id
                ),
                away: Fixture.TeamInfo(
                    id: matchData.awayTeam.id,
                    name: matchData.awayTeam.name,
                    logo: matchData.awayTeam.logo,
                    winner: matchData.winner?.id == matchData.awayTeam.id
                )
            ),
            goals: Fixture.Goals(
                home: matchData.homeScore,
                away: matchData.awayScore
            ),
            score: Fixture.Score(
                halftime: nil,
                fulltime: Fixture.Score.Fulltime(
                    home: matchData.homeScore,
                    away: matchData.awayScore
                ),
                extratime: nil,
                penalty: matchData.isPenalties ? Fixture.Score.Penalty(
                    home: matchData.homePenaltyScore,
                    away: matchData.awayPenaltyScore
                ) : nil
            ),
            lineups: nil,
            statistics: nil,
            playerStatistics: nil,
            events: nil
        )

        // Navigate using the navigation state
        navigationState.navigateToFixtureDetail(fixture: fixture)
    }
}



// MARK: - Preview
struct KnockoutBracketView_Previews: PreviewProvider {
    static var previews: some View {
        let mockMatches = [
            KnockoutMatchData(
                id: 1,
                homeTeam: KnockoutTeam(id: 1, name: "Chelsea", logo: nil),
                awayTeam: KnockoutTeam(id: 2, name: "Arsenal", logo: nil),
                homeScore: 3,
                awayScore: 3,
                homePenaltyScore: 4,
                awayPenaltyScore: 2,
                status: "FT",
                date: "2024-03-15T20:00:00Z",
                isFinished: true,
                isPenalties: true,
                isAggregate: true,
                fixtureIds: [1, 2]
            )
        ]

        let mockBrackets = [
            KnockoutBracket(name: "Semi-finals", matches: mockMatches, roundIndex: 1),
            KnockoutBracket(name: "Final", matches: [], roundIndex: 2)
        ]

        let mockTournament = KnockoutTournament(
            leagueId: 2,
            season: 2024,
            brackets: mockBrackets
        )

        KnockoutBracketView(tournament: mockTournament)
    }
}
