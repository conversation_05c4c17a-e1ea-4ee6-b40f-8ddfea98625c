import SwiftUI

struct LeagueDetailView: View {
    let league: LeagueData
    @StateObject private var viewModel: LeagueDetailViewModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var navigationState: NavigationStateManager

    init(league: LeagueData) {
        self.league = league
        self._viewModel = StateObject(wrappedValue: LeagueDetailViewModel(league: league))
    }

    var body: some View {
        ZStack {
            AppColors.background.ignoresSafeArea()

            // Main content - this should never move
            VStack(spacing: 0) {
                // Header Section (without season selector)
                headerSectionWithoutSeasonSelector

                // Tab Navigation
                tabNavigationSection

                // Tab Content
                tabContentSection
            }
        }
        .overlay(
            // Season Selector Overlay - positioned as overlay to not affect main layout
            Group {
                if viewModel.isSeasonDropdownOpen {
                    seasonDropdownOverlay
                }
            }
        )
        .navigationBarHidden(true)
        .onTapGesture {
            // Close dropdown when tapping outside
            if viewModel.isSeasonDropdownOpen {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    viewModel.closeSeasonDropdown()
                }
            }
        }
        .task {
            await viewModel.loadSeasons()
            await viewModel.refreshLeagueData()
            await viewModel.initializeKnockoutSupport()
        }
        .onChange(of: viewModel.selectedSeason) { _ in
            Task {
                await viewModel.updateKnockoutSeason()
            }
        }
        // Add swipe-to-go-back functionality
        .swipeToGoBack()
        .onDisappear {
            // Only clear saved season if we're completely leaving the league view
            // (not just navigating to a fixture detail)
            if !navigationState.isInDetailView {
                viewModel.clearSavedSeason()
            }
        }
    }

    // MARK: - Header Section Without Season Selector
    private var headerSectionWithoutSeasonSelector: some View {
        VStack(spacing: 0) {
            // Top Bar with Back Button and Action Icons
            topBarSection

            // League Info Section
            leagueInfoSection

            // Season Selector Button (inline)
            seasonSelectorButton
        }
        .background(AppColors.tertiaryBackground)
        .edgesIgnoringSafeArea(.horizontal)
    }

    // MARK: - Top Bar Section
    private var topBarSection: some View {
        HStack {
            // Back Button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(AppColors.text)
            }

            Spacer()

            // Action Icons
            HStack(spacing: AppLayout.spacingM) {
                // Notifications Bell
                Button(action: {
                    viewModel.toggleNotifications()
                }) {
                    Image(systemName: viewModel.notificationsEnabled ? "bell.fill" : "bell")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(viewModel.notificationsEnabled ? AppColors.Brand.secondary : AppColors.text)
                }

                // Favorite Star
                Button(action: {
                    viewModel.toggleFavorite()
                }) {
                    Image(systemName: viewModel.isFavorited ? "star.fill" : "star")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(viewModel.isFavorited ? AppColors.Brand.secondary : AppColors.text)
                }
            }
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.top, AppLayout.spacingS)
        .padding(.bottom, AppLayout.spacingM)
    }

    // MARK: - League Info Section
    private var leagueInfoSection: some View {
        VStack(spacing: AppLayout.spacingM) {
            // League Logo - using cached image view for persistence
            CachedImageView.leagueLogo(
                url: league.league.logo,
                size: 64,
                leagueName: league.league.name
            )

            // League Name
            Text(league.league.name)
                .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                .foregroundColor(AppColors.text)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .padding(.horizontal, AppLayout.spacingL)
    }

    // MARK: - Season Selector Button (Plain Text Style)
    private var seasonSelectorButton: some View {
        Button(action: {
            // Only allow dropdown if seasons are loaded
            guard !viewModel.availableSeasons.isEmpty else { return }

            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                viewModel.isSeasonDropdownOpen.toggle()
            }
        }) {
            HStack(spacing: AppLayout.spacingXS) {
                if viewModel.isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(AppColors.secondaryText)

                    Text("Loading seasons...")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                        .foregroundColor(AppColors.secondaryText)
                } else {
                    Text(viewModel.currentSeasonText)
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .bold))
                        .foregroundColor(AppColors.text)

                    if !viewModel.availableSeasons.isEmpty {
                        Image(systemName: "chevron.down")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(AppColors.text)
                            .rotationEffect(.degrees(viewModel.isSeasonDropdownOpen ? 180 : 0))
                            .animation(.easeInOut(duration: 0.2), value: viewModel.isSeasonDropdownOpen)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(viewModel.isLoading || viewModel.availableSeasons.isEmpty)
        .padding(.horizontal, AppLayout.spacingL)
        .padding(.bottom, AppLayout.spacingM)
    }

    // MARK: - Season Dropdown Overlay
    private var seasonDropdownOverlay: some View {
        GeometryReader { geometry in
            ZStack {
                // Semi-transparent background
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            viewModel.closeSeasonDropdown()
                        }
                    }

                // Dropdown content positioned below the season selector
                VStack(alignment: .center) {
                    Spacer()
                        .frame(height: 190) // Position below header, slightly higher

                    // Compact dropdown with fixed width, centered
                    VStack(spacing: 0) {
                        ScrollView(showsIndicators: false) {
                            VStack(spacing: 0) {
                                ForEach(viewModel.availableSeasons) { season in
                                    Button(action: {
                                        viewModel.selectSeason(season)
                                    }) {
                                        HStack {
                                            Text(season.displayText)
                                                .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                                                .foregroundColor(AppColors.text)

                                            Spacer()

                                            if viewModel.selectedSeason?.id == season.id {
                                                Image(systemName: "checkmark")
                                                    .font(.system(size: 14, weight: .bold))
                                                    .foregroundColor(AppColors.Brand.primary)
                                            }
                                        }
                                        .padding(.horizontal, AppLayout.spacingS)
                                        .padding(.vertical, AppLayout.spacingS)
                                        .background(
                                            viewModel.selectedSeason?.id == season.id ?
                                            AppColors.Brand.primary.opacity(0.1) :
                                            AppColors.tertiaryBackground
                                        )
                                    }

                                    if season.id != viewModel.availableSeasons.last?.id {
                                        Divider()
                                            .padding(.leading, AppLayout.spacingS)
                                    }
                                }
                            }
                        }
                        .frame(maxHeight: 300) // Increased height for longer menu
                    }
                    .frame(width: 140) // Slightly wider for better readability
                    .background(AppColors.tertiaryBackground)
                    .cornerRadius(AppLayout.cornerRadiusM)
                    .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)
                    .transition(.move(edge: .top).combined(with: .opacity))

                    Spacer()
                }
            }
        }
        .allowsHitTesting(true)
    }

    // MARK: - Tab Navigation Section
    private var tabNavigationSection: some View {
        ScrollViewReader { proxy in
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: AppLayout.spacingL) {
                    ForEach(Array(viewModel.tabs.enumerated()), id: \.offset) { index, tab in
                        Button(action: {
                            // Close season dropdown if open
                            if viewModel.isSeasonDropdownOpen {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                    viewModel.isSeasonDropdownOpen = false
                                }
                            }

                            withAnimation(.easeInOut(duration: 0.2)) {
                                viewModel.selectTab(index)
                            }
                        }) {
                            VStack(spacing: 8) {
                                Text(tab)
                                    .font(AppTypography.dynamicFont(style: .callout, weight: .bold))
                                    .foregroundColor(viewModel.selectedTabIndex == index ? Color(UIColor.label) : Color(UIColor.secondaryLabel))

                                // Underline for selected tab
                                Rectangle()
                                    .frame(height: 2)
                                    .foregroundColor(viewModel.selectedTabIndex == index ? AppColors.Brand.primary : Color.clear)
                            }
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle())
                        .accessibilityInfo(
                            label: tab,
                            hint: viewModel.selectedTabIndex == index ? "Selected tab" : "Tap to select this tab"
                        )
                        .id(index) // Add ID for ScrollViewReader
                    }
                }
                .padding(.horizontal, AppLayout.spacingM)
                // Removed vertical padding for sleeker design like FixtureDetailView
            }
            .background(AppColors.tertiaryBackground)
            .edgesIgnoringSafeArea(.horizontal)
            .overlay(
                Rectangle()
                    .fill(AppColors.separator.opacity(0.3))
                    .frame(height: 0.5),
                alignment: .bottom
            )
            .animation(.easeInOut(duration: 0.2), value: viewModel.selectedTabIndex)
            .onChange(of: viewModel.selectedTabIndex) { selectedIndex in
                // Auto-scroll to show the selected tab
                withAnimation(.easeInOut(duration: 0.3)) {
                    proxy.scrollTo(selectedIndex, anchor: .center)
                }
            }
        }
    }

    // MARK: - Tab Content Section
    private var tabContentSection: some View {
        TabView(selection: $viewModel.selectedTabIndex) {
            ForEach(Array(viewModel.tabs.enumerated()), id: \.offset) { index, tab in
                tabContent(for: index, title: tab)
                    .tag(index)
            }
        }
        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        .background(AppColors.background)
        .frame(maxHeight: .infinity)
        .animation(.easeInOut(duration: 0.2), value: viewModel.selectedTabIndex)
        .onChange(of: viewModel.selectedTabIndex) { _ in
            // This ensures the tab navigation section stays in sync when swiping
            // The animation will automatically update the tab indicator
        }
    }

    // MARK: - Tab Content
    private func tabContent(for index: Int, title: String) -> some View {
        Group {
            switch title {
            case "Matches":
                if let selectedSeason = viewModel.selectedSeason {
                    LeagueFixturesRoundView(
                        leagueId: league.id,
                        season: selectedSeason.year
                    )
                    .id("fixtures-\(league.id)-\(selectedSeason.year)") // Force recreation when season changes
                } else {
                    // Show loading or season selection prompt
                    VStack(spacing: AppLayout.spacingM) {
                        Image(systemName: "calendar.badge.exclamationmark")
                            .font(.system(size: AppLayout.iconSizeXL))
                            .foregroundColor(AppColors.Brand.secondary)

                        Text("Select a Season")
                            .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                            .foregroundColor(AppColors.text)

                        Text("Please select a season to view fixtures.")
                            .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                            .foregroundColor(AppColors.secondaryText)
                            .multilineTextAlignment(.center)
                    }
                    .padding(AppLayout.spacingL)
                }
            case "Standings":
                if let selectedSeason = viewModel.selectedSeason {
                    LeagueStandingsView(
                        leagueId: league.id,
                        season: selectedSeason.year
                    )
                    .id("standings-\(league.id)-\(selectedSeason.year)") // Force recreation when season changes
                } else {
                    // Show loading or season selection prompt
                    VStack(spacing: AppLayout.spacingM) {
                        Image(systemName: "calendar.badge.exclamationmark")
                            .font(.system(size: AppLayout.iconSizeXL))
                            .foregroundColor(AppColors.Brand.secondary)

                        Text("Select a Season")
                            .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                            .foregroundColor(AppColors.text)

                        Text("Please select a season to view standings.")
                            .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                            .foregroundColor(AppColors.secondaryText)
                            .multilineTextAlignment(.center)
                    }
                    .padding(AppLayout.spacingL)
                }
            case "Knockout":
                if let selectedSeason = viewModel.selectedSeason {
                    LeagueKnockoutView(
                        leagueId: league.id,
                        season: selectedSeason.year
                    )
                    .id("knockout-\(league.id)-\(selectedSeason.year)") // Force recreation when league or season changes
                } else {
                    // Show loading or season selection prompt
                    VStack(spacing: AppLayout.spacingM) {
                        Image(systemName: "calendar.badge.exclamationmark")
                            .font(.system(size: AppLayout.iconSizeXL))
                            .foregroundColor(AppColors.Brand.secondary)

                        Text("Select a Season")
                            .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                            .foregroundColor(AppColors.text)

                        Text("Please select a season to view knockout bracket.")
                            .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                            .foregroundColor(AppColors.secondaryText)
                            .multilineTextAlignment(.center)
                    }
                    .padding(AppLayout.spacingL)
                }
            case "Transfers":
                LeagueTransfersView(leagueId: league.id)
                    .id("transfers-\(league.id)") // Force recreation when league changes
            default:
                // Placeholder content for other tabs
                ScrollView {
                    VStack(spacing: AppLayout.spacingL) {
                        VStack(spacing: AppLayout.spacingM) {
                            Image(systemName: getTabIcon(for: index))
                                .font(.system(size: AppLayout.iconSizeXL))
                                .foregroundColor(AppColors.Brand.secondary)

                            Text("\(title) Coming Soon!")
                                .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                                .foregroundColor(AppColors.text)

                            Text("Detailed \(title.lowercased()) information will be available in a future update.")
                                .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                                .foregroundColor(AppColors.secondaryText)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, AppLayout.spacingL)
                        }
                        .padding(.vertical, AppLayout.spacingXXL)

                        Spacer(minLength: AppLayout.spacingXXL)
                    }
                }
                .background(AppColors.background)
            }
        }
    }

    // MARK: - Helper Methods
    private func getTabIcon(for index: Int) -> String {
        guard index < viewModel.tabs.count else { return "questionmark.circle.fill" }
        let title = viewModel.tabs[index]

        switch title {
        case "Overview": return "info.circle.fill"
        case "Matches": return "sportscourt.fill"
        case "Standings": return "list.number"
        case "Knockout": return "trophy.fill"
        case "Team Stats": return "person.3.fill"
        case "Player Stats": return "person.fill"
        case "Transfers": return "arrow.left.arrow.right"
        default: return "questionmark.circle.fill"
        }
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        LeagueDetailView(
            league: LeagueData(
                league: LeagueInfo(
                    id: 39,
                    name: "Premier League",
                    type: "League",
                    logo: "https://media.api-sports.io/football/leagues/39.png"
                ),
                country: CountryInfo(
                    name: "England",
                    code: "GB",
                    flag: "https://media.api-sports.io/flags/gb.svg"
                ),
                seasons: [
                    Season(
                        year: 2024, // League season year (2024/25 season)
                        start: "2024-08-16",
                        end: "2025-05-25",
                        current: true,
                        coverage: Coverage(
                            fixtures: FixturesCoverage(
                                events: true,
                                lineups: true,
                                statisticsFixtures: true,
                                statisticsPlayers: true
                            ),
                            standings: true,
                            players: true,
                            topScorers: true,
                            topAssists: true,
                            topCards: true,
                            injuries: true,
                            predictions: true,
                            odds: true
                        )
                    )
                ]
            )
        )
    }
}
