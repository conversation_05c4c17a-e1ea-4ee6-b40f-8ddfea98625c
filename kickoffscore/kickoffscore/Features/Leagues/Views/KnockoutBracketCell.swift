import SwiftUI

/// Individual match cell in the knockout bracket
struct KnockoutBracketCell: View {
    let matchData: KnockoutMatchData
    let heightScalingExponent: Int
    let isTopMatch: Bool
    let isCollapsed: Bool
    let isFirstColumn: Bool
    let isLastColumn: Bool
    
    private var lineColor: Color {
        AppColors.separator
    }
    
    private var height: CGFloat {
        let baseHeight: CGFloat = 100
        let exponent = max(-3, min(3, heightScalingExponent)) // Clamp between -3 and 3
        let calculatedHeight = baseHeight * pow(2, CGFloat(exponent))

        // Ensure we never return NaN or invalid values
        if calculatedHeight.isNaN || calculatedHeight.isInfinite || calculatedHeight <= 0 {
            return baseHeight
        }

        return max(50, min(800, calculatedHeight)) // Clamp between 50 and 800 points
    }
    
    var body: some View {
        HStack(spacing: 0) {
            leftLineArea
            
            VStack(spacing: 0) {
                if !isCollapsed {
                    topLabelArea
                }
                
                matchContentArea
                
                if !isCollapsed {
                    bottomLabelArea
                }
            }
            
            rightLineArea
        }
        .frame(height: height)
        .transition(.opacity.combined(with: .scale(scale: 1, anchor: .top)))
    }
    
    // MARK: - Content Areas
    
    private var leftLineArea: some View {
        Group {
            if !isFirstColumn {
                Rectangle()
                    .foregroundColor(lineColor)
            } else {
                Spacer()
            }
        }
        .frame(width: UIScreen.main.bounds.width * 0.05, height: 2)
    }
    
    private var rightLineArea: some View {
        Group {
            if !isLastColumn {
                rightLine
            } else {
                Spacer()
                    .frame(width: UIScreen.main.bounds.width * 0.05, height: 2)
            }
        }
    }
    
    private var rightLine: some View {
        HStack(spacing: 0) {
            rightHorizontalLine
            
            if isTopMatch {
                topMatchRightVerticalLine
            } else {
                bottomMatchRightVerticalLine
            }
        }
    }
    
    private var rightHorizontalLine: some View {
        Rectangle()
            .frame(width: UIScreen.main.bounds.width * 0.05, height: 2)
            .foregroundColor(lineColor)
    }
    
    private var topMatchRightVerticalLine: some View {
        VStack(spacing: 0) {
            Spacer()
                .frame(height: height / 2)
            Rectangle()
                .frame(width: 2, height: height / 2 + 2)
                .foregroundColor(lineColor)
        }
    }
    
    private var bottomMatchRightVerticalLine: some View {
        VStack(spacing: 0) {
            Rectangle()
                .frame(width: 2, height: height / 2 + 2)
                .foregroundColor(lineColor)
            Spacer()
                .frame(height: height / 2)
        }
    }
    
    private var topLabelArea: some View {
        HStack {
            Spacer()
            Text(formatDate(matchData.date))
                .font(AppTypography.dynamicFont(style: .caption2, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
            Spacer()
        }
        .padding(.top, AppLayout.spacingXS)
    }
    
    private var matchContentArea: some View {
        VStack(spacing: 0) {
            // Home team
            teamRow(
                team: matchData.homeTeam,
                score: matchData.homeScore,
                isWinner: matchData.winner?.id == matchData.homeTeam.id
            )

            // Divider
            Rectangle()
                .fill(AppColors.separator.opacity(0.3))
                .frame(height: 0.5)

            // Away team
            teamRow(
                team: matchData.awayTeam,
                score: matchData.awayScore,
                isWinner: matchData.winner?.id == matchData.awayTeam.id
            )
        }
        .background(
            RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                .fill(AppColors.tertiaryBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                        .stroke(AppColors.separator.opacity(0.2), lineWidth: 0.5)
                )
        )
        .clipShape(RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS))
    }
    
    private func teamRow(team: KnockoutTeam, score: Int?, isWinner: Bool) -> some View {
        HStack(spacing: AppLayout.spacingS) {
            // Team logo - using CachedImageView for consistency
            CachedImageView.teamLogo(
                url: team.logo,
                size: 24,
                teamName: team.name
            )

            // Team name - using full name instead of abbreviation
            Text(team.name)
                .font(AppTypography.dynamicFont(style: .subheadline, weight: isWinner ? .bold : .semibold))
                .foregroundColor(isWinner ? AppColors.text : AppColors.secondaryText)
                .lineLimit(1)

            Spacer()

            // Score with penalty display
            if let score = score {
                HStack(spacing: 2) {
                    Text("\(score)")
                        .font(AppTypography.dynamicFont(style: .title3, weight: .bold))
                        .foregroundColor(isWinner ? AppColors.text : AppColors.secondaryText)

                    // Show penalty score if applicable
                    if matchData.isPenalties {
                        let penaltyScore = team.id == matchData.homeTeam.id ? matchData.homePenaltyScore : matchData.awayPenaltyScore
                        if let penScore = penaltyScore {
                            Text("(\(penScore))")
                                .font(AppTypography.dynamicFont(style: .callout, weight: .medium))
                                .foregroundColor(isWinner ? AppColors.text : AppColors.secondaryText)
                        }
                    }
                }
                .frame(minWidth: 24)
            } else {
                Text("-")
                    .font(AppTypography.dynamicFont(style: .title3, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
                    .frame(minWidth: 24)
            }
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.vertical, AppLayout.spacingS)
    }
    
    private var bottomLabelArea: some View {
        HStack {
            Spacer()
            if ["LIVE", "1H", "HT", "2H", "ET", "P"].contains(matchData.status) {
                Text("LIVE")
                    .font(AppTypography.dynamicFont(style: .caption2, weight: .bold))
                    .foregroundColor(AppColors.error)
            } else if !matchData.isFinished && matchData.status != "NS" {
                Text(matchData.status)
                    .font(AppTypography.dynamicFont(style: .caption2, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            }
            Spacer()
        }
        .padding(.bottom, AppLayout.spacingXS)
    }
    
    // MARK: - Helper Methods



    private func formatDate(_ dateString: String?) -> String {
        guard let dateString = dateString else { return "" }

        let formatter = ISO8601DateFormatter()
        guard let date = formatter.date(from: dateString) else { return "" }

        let displayFormatter = DateFormatter()
        displayFormatter.dateFormat = "d MMM" // More compact format: "28 Jun"
        return displayFormatter.string(from: date)
    }
}

// MARK: - Preview
struct KnockoutBracketCell_Previews: PreviewProvider {
    static var previews: some View {
        let mockMatch = KnockoutMatchData(
            id: 1,
            homeTeam: KnockoutTeam(id: 1, name: "Chelsea", logo: nil),
            awayTeam: KnockoutTeam(id: 2, name: "Arsenal", logo: nil),
            homeScore: 2,
            awayScore: 1,
            status: "FT",
            date: "2024-03-15T20:00:00Z",
            isFinished: true,
            isPenalties: false
        )

        KnockoutBracketCell(
            matchData: mockMatch,
            heightScalingExponent: 0,
            isTopMatch: true,
            isCollapsed: false,
            isFirstColumn: false,
            isLastColumn: false
        )
        .padding()
        .background(AppColors.background)
    }
}
