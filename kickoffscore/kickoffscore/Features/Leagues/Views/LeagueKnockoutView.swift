import SwiftUI

/// Main knockout view for league detail page
struct LeagueKnockoutView: View {
    let leagueId: Int
    let season: Int
    
    @StateObject private var viewModel: KnockoutBracketViewModel
    
    init(leagueId: Int, season: Int) {
        self.leagueId = leagueId
        self.season = season
        self._viewModel = StateObject(wrappedValue: KnockoutBracketViewModel(leagueId: leagueId, season: season))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            if viewModel.isLoading {
                loadingView
            } else if let errorMessage = viewModel.errorMessage {
                errorView(message: errorMessage)
            } else if let tournament = viewModel.tournament, !tournament.brackets.isEmpty {
                KnockoutBracketView(tournament: tournament)
            } else {
                emptyStateView
            }
        }
        .background(AppColors.background)
        .task {
            await viewModel.loadKnockoutBracket()
        }
        .task(id: season) {
            // Update season and reload knockout data when season changes
            await viewModel.updateSeason(season)
        }
        .refreshable {
            await viewModel.refreshKnockoutBracket()
        }
    }
    
    // MARK: - Content Views
    
    private var loadingView: some View {
        VStack(spacing: AppLayout.spacingL) {
            ForEach(0..<6, id: \.self) { _ in
                SkeletonKnockoutMatchView()
            }
        }
        .padding(AppLayout.spacingM)
    }
    
    private func errorView(message: String) -> some View {
        VStack(spacing: AppLayout.spacingL) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.error)
            
            Text("Error Loading Knockout Bracket")
                .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                .foregroundColor(AppColors.text)
                .multilineTextAlignment(.center)
            
            Text(message)
                .font(AppTypography.dynamicFont(style: .body))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
            
            Button("Try Again") {
                Task {
                    await viewModel.refreshKnockoutBracket()
                }
            }
            .buttonStyle(ProminentButtonStyle())
        }
        .padding(AppLayout.spacingXL)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: AppLayout.spacingL) {
            Image(systemName: "trophy.fill")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.Brand.secondary)
            
            Text("No Knockout Stages")
                .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                .foregroundColor(AppColors.text)
            
            Text("This competition doesn't have knockout stages or they haven't started yet.")
                .font(AppTypography.dynamicFont(style: .body))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
        }
        .padding(AppLayout.spacingXL)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Skeleton Views

struct SkeletonKnockoutMatchView: View {
    var body: some View {
        VStack(spacing: AppLayout.spacingS) {
            // Match container
            HStack(spacing: AppLayout.spacingS) {
                // Home team
                HStack(spacing: AppLayout.spacingS) {
                    Circle()
                        .fill(AppColors.skeleton)
                        .frame(width: 20, height: 20)
                    
                    Rectangle()
                        .fill(AppColors.skeleton)
                        .frame(width: 60, height: 12)
                        .cornerRadius(6)
                    
                    Spacer()
                    
                    Rectangle()
                        .fill(AppColors.skeleton)
                        .frame(width: 20, height: 12)
                        .cornerRadius(6)
                }
                
                Text(":")
                    .foregroundColor(AppColors.skeleton)
                
                // Away team
                HStack(spacing: AppLayout.spacingS) {
                    Rectangle()
                        .fill(AppColors.skeleton)
                        .frame(width: 20, height: 12)
                        .cornerRadius(6)
                    
                    Spacer()
                    
                    Rectangle()
                        .fill(AppColors.skeleton)
                        .frame(width: 60, height: 12)
                        .cornerRadius(6)
                    
                    Circle()
                        .fill(AppColors.skeleton)
                        .frame(width: 20, height: 20)
                }
            }
            .padding(AppLayout.spacingS)
            .background(
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                    .fill(AppColors.secondaryBackground)
            )
        }
        .redacted(reason: .placeholder)
    }
}

// MARK: - Preview
struct LeagueKnockoutView_Previews: PreviewProvider {
    static var previews: some View {
        LeagueKnockoutView(leagueId: 2, season: 2024) // Champions League
            .background(AppColors.background)
    }
}
