import SwiftUI

struct LeaguesView: View {
    @StateObject private var viewModel = LeaguesViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject private var navigationState: NavigationStateManager

    var body: some View {
        NavigationStack(path: $navigationState.navigationPath) {
            ZStack {
                AppColors.background.ignoresSafeArea()

                VStack(spacing: 0) {
                    // Search Bar
                    searchBar

                    // Main Content
                    if viewModel.isLoading {
                        SkeletonLeaguesView()
                    } else if let errorMessage = viewModel.errorMessage {
                        errorView(message: errorMessage)
                    } else if viewModel.filteredLeagues.isEmpty && !viewModel.searchText.isEmpty {
                        noSearchResultsView
                    } else if viewModel.filteredLeagues.isEmpty {
                        emptyStateView
                    } else {
                        leaguesListView
                    }
                }
            }
            .navigationTitle("Leagues")
            .navigationBarTitleDisplayMode(.inline)
            .task {
                await viewModel.loadLeagues()
            }
            .refreshable {
                await viewModel.loadLeagues()
            }
            .navigationDestination(for: LeagueData.self) { league in
                LeagueDetailView(league: league)
            }
            .navigationDestination(for: Fixture.self) { fixture in
                FixtureDetailView(fixture: fixture, navigationSource: "Leagues")
            }
            // Add swipe-to-go-back functionality
            .swipeToGoBack()
        }
    }

    // MARK: - Search Bar
    private var searchBar: some View {
        HStack(spacing: AppLayout.spacingM) {
            HStack(spacing: AppLayout.spacingS) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppColors.tertiaryText)

                TextField("Find leagues", text: $viewModel.searchText)
                    .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                    .foregroundColor(AppColors.text)
                    .textFieldStyle(PlainTextFieldStyle())

                if !viewModel.searchText.isEmpty {
                    Button(action: {
                        viewModel.searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 16))
                            .foregroundColor(AppColors.tertiaryText)
                    }
                }
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, AppLayout.spacingS)
            .background(
                RoundedRectangle(cornerRadius: 30)
                    .fill(Color(UIColor { traitCollection in
                        return traitCollection.userInterfaceStyle == .dark ?
                            UIColor.secondarySystemBackground : UIColor.systemBackground
                    }))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 30)
                    .stroke(Color(UIColor.separator).opacity(0.2), lineWidth: 0.5)
            )
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.bottom, AppLayout.spacingM)
    }

    // MARK: - Error View
    private func errorView(message: String) -> some View {
        VStack(spacing: AppLayout.spacingL) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.Brand.secondary)

            Text("Error Loading Leagues")
                .font(AppTypography.dynamicFont(style: .title2, weight: .semibold))
                .foregroundColor(AppColors.text)

            Text(message)
                .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingXL)

            Button(action: {
                Task {
                    await viewModel.loadLeagues()
                }
            }) {
                Text("Try Again")
                    .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, AppLayout.spacingL)
                    .padding(.vertical, AppLayout.spacingM)
                    .background(AppColors.Brand.primary)
                    .clipShape(RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM))
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, AppLayout.spacingL)
    }

    // MARK: - No Search Results View
    private var noSearchResultsView: some View {
        VStack(spacing: AppLayout.spacingL) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.Brand.secondary)

            Text("No Results Found")
                .font(AppTypography.dynamicFont(style: .title2, weight: .semibold))
                .foregroundColor(AppColors.text)

            Text("No leagues match your search for \"\(viewModel.searchText)\"")
                .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingXL)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: AppLayout.spacingL) {
            Image(systemName: "trophy.fill")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.Brand.secondary)

            Text("No Leagues Available")
                .font(AppTypography.dynamicFont(style: .title2, weight: .semibold))
                .foregroundColor(AppColors.text)

            Text("There are no leagues available at the moment. Please try again later.")
                .font(AppTypography.dynamicFont(style: .body, weight: .regular))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingXL)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - Leagues List View
    private var leaguesListView: some View {
        ScrollView(showsIndicators: false) {
            LazyVStack(spacing: AppLayout.spacingS) {
                // Group top competitions and regular competitions
                let topCompetitions = viewModel.filteredLeagues.filter { $0.id.hasPrefix("TopCompetition_") }
                let regularCompetitions = viewModel.filteredLeagues.filter { !$0.id.hasPrefix("TopCompetition_") }

                // Top Competitions Section
                if !topCompetitions.isEmpty {
                    VStack(spacing: AppLayout.spacingS) {
                        topCompetitionsHeader

                        ForEach(topCompetitions) { group in
                            VStack(spacing: 0) {
                                LeagueGroupView(
                                    group: group,
                                    isExpanded: viewModel.isSectionExpanded(group.id),
                                    onToggle: {
                                        viewModel.toggleSection(group.id)
                                    }
                                )
                            }
                            .padding(.top, AppLayout.spacingXS)
                            .padding(.bottom, AppLayout.spacingXS)
                            .background(
                                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                                    .fill(Color(UIColor { traitCollection in
                                        return traitCollection.userInterfaceStyle == .dark ?
                                            UIColor.tertiarySystemBackground : UIColor.systemBackground
                                    }))
                            )
                        }
                    }
                }

                // Regular Competitions Section
                if !regularCompetitions.isEmpty {
                    VStack(spacing: AppLayout.spacingS) {
                        if !topCompetitions.isEmpty {
                            allCompetitionsHeader
                        }

                        ForEach(regularCompetitions) { group in
                            LeagueGroupView(
                                group: group,
                                isExpanded: viewModel.isSectionExpanded(group.id),
                                onToggle: {
                                    viewModel.toggleSection(group.id)
                                }
                            )
                        }
                    }
                }
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.bottom, AppLayout.spacingL)
        }
    }

    // MARK: - Top Competitions Header
    private var topCompetitionsHeader: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
            HStack {
                Text("Top Competitions")
                    .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                    .foregroundColor(AppColors.text)
                Spacer()
            }

            Rectangle()
                .fill(AppColors.Brand.primary.opacity(0.3))
                .frame(height: 2)
                .frame(maxWidth: 120)
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.bottom, AppLayout.spacingS)
        .padding(.top, AppLayout.spacingXS)
    }

    // MARK: - All Competitions Header
    private var allCompetitionsHeader: some View {
        VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
            HStack {
                Text("All Competitions")
                    .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                    .foregroundColor(AppColors.text)
                Spacer()
            }

            Rectangle()
                .fill(AppColors.Brand.secondary.opacity(0.3))
                .frame(height: 2)
                .frame(maxWidth: 140)
        }
        .padding(.horizontal, AppLayout.spacingM)
        .padding(.bottom, AppLayout.spacingS)
        .padding(.top, AppLayout.spacingM)
    }
}

#Preview {
    LeaguesView()
        .environmentObject(AuthViewModel())
        .environmentObject(NavigationStateManager())
}
