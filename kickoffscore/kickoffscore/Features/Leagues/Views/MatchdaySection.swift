import SwiftUI
import <PERSON><PERSON><PERSON>

struct MatchdaySection: View {
    let round: FixtureRound
    @EnvironmentObject private var navigationState: NavigationStateManager

    var body: some View {
        VStack(spacing: AppLayout.spacingS) {
            // Display fixtures grouped by day
            ForEach(round.fixturesByDay, id: \.id) { fixtureDay in
                DaySection(fixtureDay: fixtureDay)
                    .environmentObject(navigationState)
            }
        }
    }

}

// MARK: - Day Section
private struct DaySection: View {
    let fixtureDay: FixtureDay
    @EnvironmentObject private var navigationState: NavigationStateManager

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Day Header
            dayHeader
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.vertical, AppLayout.spacingS)
                .background(AppColors.secondaryBackground)

            // Fixtures List for this day
            VStack(spacing: 0) {
                ForEach(Array(fixtureDay.fixtures.enumerated()), id: \.element.id) { index, fixture in
                    Button(action: {
                        // Add haptic feedback for better UX
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()

                        // Navigate to fixture detail
                        navigationState.navigateToFixtureDetail(fixture: fixture)
                    }) {
                        MatchdayFixtureRow(fixture: fixture)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .accessibilityLabel("View details for \(fixture.teams.home?.name ?? "Home") vs \(fixture.teams.away?.name ?? "Away")")
                    .accessibilityHint("Tap to view match details")
                    // Ensure the button takes priority over any scroll gestures
                    .highPriorityGesture(
                        TapGesture()
                            .onEnded { _ in
                                // Add haptic feedback for better UX
                                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                                impactFeedback.impactOccurred()

                                // Navigate to fixture detail
                                navigationState.navigateToFixtureDetail(fixture: fixture)
                            }
                    )

                    // Add separator between fixtures (except for the last one)
                    if index < fixtureDay.fixtures.count - 1 {
                        Divider()
                            .background(AppColors.separator.opacity(0.3))
                    }
                }
            }
        }
        .background(AppColors.tertiaryBackground)
    }

    // MARK: - Day Header
    private var dayHeader: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(fixtureDay.dayName)
                .font(AppTypography.dynamicFont(style: .headline, weight: .semibold))
                .foregroundColor(AppColors.text)

            Text(fixtureDay.formattedDate)
                .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                .foregroundColor(AppColors.secondaryText)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

// MARK: - Matchday Fixture Row
private struct MatchdayFixtureRow: View {
    let fixture: Fixture

    var body: some View {
        HStack(spacing: AppLayout.spacingM) {
            // Time (Left Column) - Only show time since date is in header
            VStack(alignment: .leading, spacing: 2) {
                Text(formatTime(fixture.date))
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            }
            .frame(width: 45, alignment: .leading)

            // Teams (Middle Column - Stacked)
            VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                // Home Team
                HStack(spacing: AppLayout.spacingXS) {
                    TeamLogo(url: fixture.teams.home?.logo, teamName: fixture.teams.home?.name ?? "Home")

                    Text(fixture.teams.home?.name ?? "Unknown")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(AppColors.text)
                        .lineLimit(1)
                        .minimumScaleFactor(0.8)

                    Spacer()
                }

                // Away Team
                HStack(spacing: AppLayout.spacingXS) {
                    TeamLogo(url: fixture.teams.away?.logo, teamName: fixture.teams.away?.name ?? "Away")

                    Text(fixture.teams.away?.name ?? "Unknown")
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                        .foregroundColor(AppColors.text)
                        .lineLimit(1)
                        .minimumScaleFactor(0.8)

                    Spacer()
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // Scores (Right Column - Stacked)
            VStack(alignment: .trailing, spacing: AppLayout.spacingXS) {
                // Home Score
                Text(isUpcomingFixture(fixture) ? "" : "\(fixture.goals.home ?? 0)")
                    .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                    .foregroundColor(AppColors.text)
                    .frame(minWidth: 20, alignment: .trailing)

                // Away Score
                Text(isUpcomingFixture(fixture) ? "" : "\(fixture.goals.away ?? 0)")
                    .font(AppTypography.dynamicFont(style: .title2, weight: .bold))
                    .foregroundColor(AppColors.text)
                    .frame(minWidth: 20, alignment: .trailing)
            }
            .frame(width: 30, alignment: .trailing)
        }
        .padding(.vertical, AppLayout.spacingM)
        .padding(.horizontal, AppLayout.spacingM)
        .contentShape(Rectangle()) // Makes the entire row tappable
    }
    
    // MARK: - Helper Methods
    private func formatDate(_ dateString: String) -> String {
        let formatter = ISO8601DateFormatter()
        guard let date = formatter.date(from: dateString) else { return "TBD" }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "E MMM d"
        return dateFormatter.string(from: date)
    }
    
    private func formatTime(_ dateString: String) -> String {
        let formatter = ISO8601DateFormatter()
        guard let date = formatter.date(from: dateString) else { return "TBD" }
        
        let timeFormatter = DateFormatter()
        timeFormatter.timeStyle = .short
        return timeFormatter.string(from: date)
    }
    
    private func isUpcomingFixture(_ fixture: Fixture) -> Bool {
        guard let status = fixture.status.short else { return true }
        return ["NS", "TBD", "PST"].contains(status)
    }

    // Helper View for Team Logo with cached image view for persistence
    private func TeamLogo(url: String?, teamName: String) -> some View {
        CachedImageView.teamLogo(
            url: url,
            size: 20,
            teamName: teamName
        )
    }
}

// MARK: - Preview
#Preview {
    ScrollView {
        VStack(spacing: 0) {
            MatchdaySection(round: FixtureRound(name: "Regular Season - 37", fixtures: [Fixture.mock, Fixture.mockUpcoming, Fixture.mockLive]))
                .environmentObject(NavigationStateManager())
        }
        .padding()
    }
    .background(AppColors.background)
}
