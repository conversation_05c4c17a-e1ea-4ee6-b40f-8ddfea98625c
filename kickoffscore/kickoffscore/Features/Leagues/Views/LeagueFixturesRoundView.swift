import SwiftUI

struct LeagueFixturesRoundView: View {
    let leagueId: Int
    let season: Int
    @StateObject private var viewModel: LeagueFixturesRoundViewModel
    @EnvironmentObject private var navigationState: NavigationStateManager
    
    init(leagueId: Int, season: Int) {
        self.leagueId = leagueId
        self.season = season
        self._viewModel = StateObject(wrappedValue: LeagueFixturesRoundViewModel(leagueId: leagueId, season: season))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Matchweek Selection Section
            if viewModel.hasFixtures && !viewModel.isLoading {
                matchweekSelectionSection
            }

            // Content Section
            if viewModel.isLoading {
                loadingView
            } else if let errorMessage = viewModel.errorMessage {
                errorView(message: errorMessage)
            } else if !viewModel.hasFixtures {
                emptyStateView
            } else {
                fixturesListView
            }
        }
        .task {
            await viewModel.loadFixtures()
        }
        .task(id: season) {
            // Update season and reload fixtures when season changes
            await viewModel.updateSeason(season)
        }
        .refreshable {
            await viewModel.refreshFixtures()
        }
    }
    


    // MARK: - Matchweek Selection Section
    private var matchweekSelectionSection: some View {
        VStack(spacing: 0) {
            // Matchweek Dropdown
            MatchweekPullDownButton(
                selectedRoundName: viewModel.selectedRoundDisplayName,
                availableRounds: viewModel.sortedRounds,
                onRoundSelected: { round in
                    viewModel.selectRound(round)
                }
            )
            .padding(.horizontal, AppLayout.spacingM)
        }
        .padding(.top, AppLayout.spacingS)
        .padding(.bottom, AppLayout.spacingXS)
        .background(AppColors.background)
    }

    // MARK: - Content Views
    private var loadingView: some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: AppLayout.spacingL) {
                // Single container with multiple fixture rows
                SkeletonMatchdaySection()
            }
            .padding(.top, AppLayout.spacingS)
            .padding(.bottom, AppLayout.spacingL)
        }
    }
    
    private func errorView(message: String) -> some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.Brand.secondary)
            
            Text("Error Loading Fixtures")
                .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                .foregroundColor(AppColors.text)
            
            Text(message)
                .font(AppTypography.dynamicFont(style: .body))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
            
            Button("Try Again") {
                Task {
                    await viewModel.loadFixtures()
                }
            }
            .buttonStyle(ProminentButtonStyle())
        }
        .padding(AppLayout.spacingL)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: AppLayout.spacingM) {
            Image(systemName: getEmptyStateIcon())
                .font(.system(size: AppLayout.iconSizeXL))
                .foregroundColor(AppColors.Brand.secondary)
            
            Text(getEmptyStateTitle())
                .font(AppTypography.dynamicFont(style: .title3, weight: .semibold))
                .foregroundColor(AppColors.text)
            
            Text(getEmptyStateMessage())
                .font(AppTypography.dynamicFont(style: .body))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
        }
        .padding(AppLayout.spacingL)
    }
    
    private var fixturesListView: some View {
        ScrollView(showsIndicators: false) {
            LazyVStack(spacing: 0) {
                ForEach(viewModel.displayedRounds, id: \.id) { round in
                    MatchdaySection(round: round)
                }
            }
            .padding(.top, AppLayout.spacingXS)
            .padding(.bottom, AppLayout.spacingL)
        }
    }
    
    // MARK: - Helper Methods
    private func getEmptyStateIcon() -> String {
        return "list.bullet"
    }

    private func getEmptyStateTitle() -> String {
        return "No Fixtures Available"
    }

    private func getEmptyStateMessage() -> String {
        return "There are no fixtures available for this league and season."
    }
}



// MARK: - Skeleton Loading View
private struct SkeletonMatchdaySection: View {
    var body: some View {
        VStack(spacing: AppLayout.spacingS) {
            // Multiple fixture rows in a single container
            VStack(spacing: 0) {
                ForEach(0..<8, id: \.self) { index in
                    VStack(spacing: 0) {
                        SkeletonFixtureRowView()
                            .padding(.horizontal, AppLayout.spacingM)
                            .padding(.vertical, AppLayout.spacingS)

                        // Add divider if not the last item
                        if index < 7 {
                            Divider()
                                .background(AppColors.separator)
                                .padding(.horizontal, AppLayout.spacingM)
                        }
                    }
                }
            }
            .background(
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                    .fill(AppColors.tertiaryBackground)
            )
        }
        .shimmering()
    }
}
