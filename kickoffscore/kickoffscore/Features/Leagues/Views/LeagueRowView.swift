import SwiftUI

struct LeagueRowView: View {
    let league: LeagueData
    let showDivider: Bool
    @State private var isFavorited: Bool = false // Will be connected to actual favorite system later
    @State private var isPressed: Bool = false

    var body: some View {
        NavigationLink(value: league) {
            leagueRowContent
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.15), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    private var leagueRowContent: some View {
        HStack(spacing: AppLayout.spacingM) {
                // League Logo - using cached image view for persistence
                CachedImageView.leagueLogo(
                    url: league.league.logo,
                    size: 24,
                    leagueName: league.league.name
                )

                // League Info
                VStack(alignment: .leading, spacing: AppLayout.spacingXS) {
                    Text(league.league.name)
                        .font(AppTypography.dynamicFont(style: .subheadline, weight: .semibold))
                        .foregroundColor(AppColors.secondaryText)
                        .lineLimit(1)

                    HStack(spacing: AppLayout.spacingXS) {
                        // League Type Badge
                        if league.league.type.lowercased() == "cup" {
                            Text("Cup")
                                .font(AppTypography.dynamicFont(style: .caption2, weight: .medium))
                                .foregroundColor(AppColors.Brand.secondary)
                                .padding(.horizontal, AppLayout.spacingXS)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                                        .fill(AppColors.Brand.secondary.opacity(0.1))
                                )
                        }
                    }
                }

                Spacer()

                // Favorite Star Button
                Button(action: {
                    isFavorited.toggle()
                    // TODO: Add actual favorite functionality later
                }) {
                    Image(systemName: isFavorited ? "star.fill" : "star")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(isFavorited ? AppColors.Brand.secondary : AppColors.tertiaryText)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, AppLayout.spacingM)
            .padding(.vertical, AppLayout.spacingS)
            .background(
                // Touch feedback background
                RoundedRectangle(cornerRadius: AppLayout.cornerRadiusS)
                    .fill(isPressed ? Color(UIColor.systemGray4).opacity(0.3) : Color.clear)
                    .animation(.easeInOut(duration: 0.15), value: isPressed)
            )
            .overlay(
                // Conditional bottom divider line
                Group {
                    if showDivider {
                        Rectangle()
                            .fill(Color(UIColor.separator).opacity(0.3))
                            .frame(height: 0.5)
                            .padding(.horizontal, AppLayout.spacingM)
                    }
                },
                alignment: .bottom
            )
    }
}

// Note: LeagueDetailView is now implemented in a separate file

#Preview {
    NavigationStack {
        LeagueRowView(
            league: LeagueData(
            league: LeagueInfo(
                id: 39,
                name: "Premier League",
                type: "League",
                logo: "https://media.api-sports.io/football/leagues/39.png"
            ),
            country: CountryInfo(
                name: "England",
                code: "GB",
                flag: "https://media.api-sports.io/flags/gb.svg"
            ),
            seasons: []
        ),
            showDivider: true
        )
    }
}
