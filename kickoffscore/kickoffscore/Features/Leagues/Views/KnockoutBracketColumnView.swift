import SwiftUI

/// Column view representing a single round in the knockout bracket
struct KnockoutBracketColumnView: View {
    let bracket: KnockoutBracket
    let columnIndex: Int
    let focusedColumnIndex: Int
    let lastColumnIndex: Int
    let didTapCell: (KnockoutMatchData) -> Void
    
    var body: some View {
        LazyVStack(spacing: 0) {
            ForEach(0..<bracket.matches.count, id: \.self) { matchIndex in
                Button(action: {
                    // Add haptic feedback for better UX
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()

                    didTapCell(bracket.matches[matchIndex])
                }) {
                    KnockoutBracketCell(
                        matchData: bracket.matches[matchIndex],
                        heightScalingExponent: columnIndex - focusedColumnIndex,
                        isTopMatch: matchIndex % 2 == 0,
                        isCollapsed: columnIndex < focusedColumnIndex,
                        isFirstColumn: columnIndex == 0,
                        isLastColumn: columnIndex == lastColumnIndex
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle()) // Make the entire cell tappable
                .highPriorityGesture(
                    TapGesture()
                        .onEnded { _ in
                            // Add haptic feedback for better UX
                            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                            impactFeedback.impactOccurred()

                            didTapCell(bracket.matches[matchIndex])
                        }
                )
            }
        }
    }
}

// MARK: - Preview
struct KnockoutBracketColumnView_Previews: PreviewProvider {
    static var previews: some View {
        let mockMatches = [
            KnockoutMatchData(
                id: 1,
                homeTeam: KnockoutTeam(id: 1, name: "Chelsea", logo: nil),
                awayTeam: KnockoutTeam(id: 2, name: "Arsenal", logo: nil),
                homeScore: 3,
                awayScore: 3,
                homePenaltyScore: 4,
                awayPenaltyScore: 2,
                status: "FT",
                date: "2024-03-15T20:00:00Z",
                isFinished: true,
                isPenalties: true,
                isAggregate: true,
                fixtureIds: [1, 2]
            ),
            KnockoutMatchData(
                id: 3,
                homeTeam: KnockoutTeam(id: 3, name: "Liverpool", logo: nil),
                awayTeam: KnockoutTeam(id: 4, name: "Man City", logo: nil),
                homeScore: 4,
                awayScore: 2,
                homePenaltyScore: nil,
                awayPenaltyScore: nil,
                status: "FT",
                date: "2024-03-15T20:00:00Z",
                isFinished: true,
                isPenalties: false,
                isAggregate: true,
                fixtureIds: [3, 4]
            )
        ]

        let mockBracket = KnockoutBracket(
            name: "Semifinals",
            matches: mockMatches,
            roundIndex: 1
        )

        KnockoutBracketColumnView(
            bracket: mockBracket,
            columnIndex: 0,
            focusedColumnIndex: 0,
            lastColumnIndex: 2,
            didTapCell: { _ in }
        )
        .padding()
        .background(AppColors.background)
    }
}
