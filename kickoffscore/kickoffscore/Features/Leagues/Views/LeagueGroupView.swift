import SwiftUI
import <PERSON><PERSON><PERSON>

struct LeagueGroupView: View {
    let group: LeagueGroup
    let isExpanded: Bool
    let onToggle: () -> Void

    var body: some View {
        // Check if this is a top competition (individual league)
        if group.id.hasPrefix("TopCompetition_") {
            // For top competitions, show as a simple row without dropdown and without divider
            if let league = group.leagues.first {
                LeagueRowView(league: league, showDivider: false)
            }
        } else {
            // For all other groups, use CollapsibleSection for consistent styling
            CustomCollapsibleSection(
                isExpanded: isExpanded,
                titleView: { buildGroupTitleView() },
                onToggle: onToggle
            ) {
                VStack(spacing: 0) {
                    ForEach(Array(group.leagues.enumerated()), id: \.element.id) { index, league in
                        LeagueRowView(
                            league: league,
                            showDivider: index < group.leagues.count - 1
                        )
                    }
                }
            }
        }
    }

    // Build the group title view with flag image and count
    private func buildGroupTitleView() -> some View {
        HStack(spacing: AppLayout.spacingS) {
            // Flag image or emoji
            flagView

            // Title and count
            VStack(alignment: .leading, spacing: 2) {
                Text(group.title)
                    .font(AppTypography.dynamicFont(style: .headline, weight: .semibold))
                    .foregroundColor(AppColors.text)
                    .lineLimit(1)

                Text("\(group.leagues.count) league\(group.leagues.count == 1 ? "" : "s")")
                    .font(AppTypography.dynamicFont(style: .caption, weight: .medium))
                    .foregroundColor(AppColors.secondaryText)
            }
        }
    }

    // Flag view that handles both URLs and emojis
    private var flagView: some View {
        Group {
            if group.isCountryGroup {
                // For country groups, try to display flag image from URL
                if let flagURL = group.flagURL, !flagURL.isEmpty, flagURL.hasPrefix("http") {
                    // This is a URL, display as image
                    KFImage(URL(string: flagURL))
                        .placeholder {
                            Text(getCountryFallbackEmoji())
                                .font(.system(size: 16))
                        }
                        .fade(duration: 0.25)
                        .retry(maxCount: 3, interval: .seconds(2))
                        .resizable()
                        .scaledToFill()
                        .frame(width: 24, height: 24)
                        .clipShape(Circle())
                } else {
                    // This is an emoji or no URL, display as text
                    Text(group.flagURL ?? getCountryFallbackEmoji())
                        .font(.system(size: 14))
                        .frame(width: 24, height: 24)
                }
            } else {
                // For continental groups, use emoji
                Text(getContinentEmoji(for: group.title))
                    .font(.system(size: 14))
                    .frame(width: 24, height: 24)
            }
        }
    }

    // Get fallback emoji for countries
    private func getCountryFallbackEmoji() -> String {
        return "🏴" // Fallback flag emoji
    }

    // MARK: - Helper Methods
    private func getContinentEmoji(for title: String) -> String {
        switch title.lowercased() {
        case "world":
            return "🌍"
        case "europe":
            return "🇪🇺"
        case "south america":
            return "🌎"
        case "north & central america":
            return "🌎"
        case "asia":
            return "🌏"
        case "africa":
            return "🌍"
        case "oceania":
            return "🇦🇺"
        default:
            return "🏴"
        }
    }

    private func getContinentIcon(for title: String) -> String {
        switch title.lowercased() {
        case "world":
            return "globe"
        case "europe":
            return "building.columns"
        case "south america", "north & central america":
            return "mountain.2"
        case "asia":
            return "pagoda"
        case "africa":
            return "sun.max"
        case "oceania":
            return "water.waves"
        default:
            return "flag.fill"
        }
    }
}

// MARK: - Custom Collapsible Section for Leagues
struct CustomCollapsibleSection<Content: View, TitleView: View>: View {
    let isExpanded: Bool
    let titleView: TitleView
    let onToggle: () -> Void
    let content: Content

    init(isExpanded: Bool, @ViewBuilder titleView: () -> TitleView, onToggle: @escaping () -> Void, @ViewBuilder content: () -> Content) {
        self.isExpanded = isExpanded
        self.titleView = titleView()
        self.onToggle = onToggle
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    onToggle()
                }
            }) {
                HStack {
                    titleView

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColors.secondaryText)
                }
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.vertical, AppLayout.spacingS)
                .background(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                        .fill(Color(UIColor { traitCollection in
                            return traitCollection.userInterfaceStyle == .dark ?
                                UIColor.secondarySystemBackground : UIColor.systemBackground
                        }))
                        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                        .stroke(isExpanded ? AppColors.Brand.primary.opacity(0.5) : Color(UIColor.separator).opacity(0.2), lineWidth: isExpanded ? 1.5 : 0.5)
                )
            }

            // Content
            if isExpanded {
                VStack(spacing: 0) {
                    content
                }
                .padding(.top, AppLayout.spacingS)
                .padding(.bottom, AppLayout.spacingXS)
                .background(
                    RoundedRectangle(cornerRadius: AppLayout.cornerRadiusM)
                        .fill(Color(UIColor { traitCollection in
                            return traitCollection.userInterfaceStyle == .dark ?
                                UIColor.tertiarySystemBackground : UIColor.systemBackground
                        }))
                )
                .padding(.top, AppLayout.spacingXS)
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isExpanded)
    }
}

#Preview {
    VStack(spacing: AppLayout.spacingM) {
        LeagueGroupView(
            group: LeagueGroup(
                id: "England",
                title: "England",
                leagues: [
                    LeagueData(
                        league: LeagueInfo(
                            id: 39,
                            name: "Premier League",
                            type: "League",
                            logo: "https://media.api-sports.io/football/leagues/39.png"
                        ),
                        country: CountryInfo(
                            name: "England",
                            code: "GB",
                            flag: "https://media.api-sports.io/flags/gb.svg"
                        ),
                        seasons: []
                    ),
                    LeagueData(
                        league: LeagueInfo(
                            id: 45,
                            name: "FA Cup",
                            type: "Cup",
                            logo: "https://media.api-sports.io/football/leagues/45.png"
                        ),
                        country: CountryInfo(
                            name: "England",
                            code: "GB",
                            flag: "https://media.api-sports.io/flags/gb.svg"
                        ),
                        seasons: []
                    )
                ],
                isCountryGroup: true,
                flagURL: "https://media.api-sports.io/flags/gb.svg"
            ),
            isExpanded: true,
            onToggle: {}
        )

        LeagueGroupView(
            group: LeagueGroup(
                id: "World",
                title: "World",
                leagues: [
                    LeagueData(
                        league: LeagueInfo(
                            id: 1,
                            name: "World Cup",
                            type: "Cup",
                            logo: "https://media.api-sports.io/football/leagues/1.png"
                        ),
                        country: CountryInfo(
                            name: "World",
                            code: nil,
                            flag: nil
                        ),
                        seasons: []
                    )
                ],
                isCountryGroup: false,
                flagURL: "🌍"
            ),
            isExpanded: false,
            onToggle: {}
        )
    }
    .padding()
    .background(AppColors.background)
}
