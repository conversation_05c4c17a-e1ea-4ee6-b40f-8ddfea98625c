import SwiftUI

struct LeaguesPlaceholderView: View {
    var body: some View {
        NavigationView {
            ZStack {
                AppColors.background.ignoresSafeArea()

                VStack(spacing: AppLayout.spacingL) {
                    Image(systemName: "hammer.fill")
                        .font(.system(size: AppLayout.iconSizeXL))
                        .foregroundColor(AppColors.Brand.secondary)

                    Text("Leagues Feature Coming Soon!")
                        .font(AppTypography.dynamicFont(style: .title2))
                        .foregroundColor(Color(UIColor.label))

                    Text("This section is currently under development.")
                        .font(AppTypography.dynamicFont(style: .body))
                        .foregroundColor(Color(UIColor.secondaryLabel))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, AppLayout.spacingXL)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .accessibilityElement(children: .combine)
                .accessibilityLabel("Leagues Feature Coming Soon")
                .accessibilityHint("This section is currently under development and will be available in a future update.")
            }
            .navigationTitle("Leagues")
            .navigationBarTitleDisplayMode(.inline) // Consistent with other placeholder views
        }
        .navigationViewStyle(StackNavigationViewStyle()) // Ensures it works correctly in a TabView
    }
}

#Preview {
    LeaguesPlaceholderView()
}
