import Foundation
import Combine

/// Manages the onboarding state and user preferences
class OnboardingManager: ObservableObject {
    // Published properties that the UI can observe
    @Published var hasCompletedOnboarding: Bool
    @Published var currentOnboardingPage: Int = 0
    
    // UserDefaults keys
    private let onboardingCompletedKey = "com.kickoffscore.onboardingCompleted"
    
    // Singleton instance
    static let shared = OnboardingManager()
    
    private init() {
        // Check if user has completed onboarding
        self.hasCompletedOnboarding = UserDefaults.standard.bool(forKey: onboardingCompletedKey)
    }
    
    /// Mark onboarding as completed
    func completeOnboarding() {
        hasCompletedOnboarding = true
        UserDefaults.standard.set(true, forKey: onboardingCompletedKey)
    }
    
    /// Reset onboarding state (for testing)
    func resetOnboarding() {
        hasCompletedOnboarding = false
        currentOnboardingPage = 0
        UserDefaults.standard.set(false, forKey: onboardingCompletedKey)
    }
    
    /// Move to the next onboarding page
    func nextPage() {
        currentOnboardingPage += 1
    }
    
    /// Move to the previous onboarding page
    func previousPage() {
        if currentOnboardingPage > 0 {
            currentOnboardingPage -= 1
        }
    }
}
