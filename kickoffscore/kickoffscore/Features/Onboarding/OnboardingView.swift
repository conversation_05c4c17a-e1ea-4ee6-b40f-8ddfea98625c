import SwiftUI

struct OnboardingView: View {
    @ObservedObject var onboardingManager = OnboardingManager.shared
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var pageIndex = 0

    // Onboarding pages data
    private let pages: [OnboardingPage] = [
        OnboardingPage(
            title: "Welcome to KickoffScore",
            description: "Your ultimate companion for live football scores, stats, and predictions.",
            imageName: "sportscourt.fill",
            backgroundColor: AppColors.Brand.primary
        ),
        OnboardingPage(
            title: "Live Matches",
            description: "Follow live scores, stats, and events from matches around the world.",
            imageName: "stopwatch.fill",
            backgroundColor: AppColors.Brand.secondary
        ),
        OnboardingPage(
            title: "Detailed Statistics",
            description: "Dive deep into match statistics, lineups, and player performances.",
            imageName: "chart.bar.fill",
            backgroundColor: AppColors.Brand.accent
        ),
        OnboardingPage(
            title: "Make Predictions",
            description: "Predict match outcomes and compare with other fans.",
            imageName: "trophy.fill",
            backgroundColor: AppColors.Brand.primary
        )
    ]

    var body: some View {
        ZStack {
            // Background color that changes with the page
            pages[pageIndex].backgroundColor
                .ignoresSafeArea()

            VStack {
                // Skip button
                HStack {
                    Spacer()

                    But<PERSON>(action: {
                        onboardingManager.completeOnboarding()
                    }) {
                        Text("Skip")
                            .font(AppTypography.dynamicFont(style: .subheadline, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, AppLayout.spacingM)
                            .padding(.vertical, AppLayout.spacingS)
                    }
                }
                .padding(.horizontal, AppLayout.spacingM)
                .padding(.top, AppLayout.spacingL)

                // Page content
                TabView(selection: $pageIndex) {
                    ForEach(0..<pages.count, id: \.self) { index in
                        OnboardingPageView(page: pages[index])
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .always))
                .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
                .onChange(of: pageIndex) { newValue in
                    onboardingManager.currentOnboardingPage = newValue
                }

                // Navigation buttons
                HStack {
                    // Back button (hidden on first page)
                    if pageIndex > 0 {
                        Button(action: {
                            withAnimation {
                                pageIndex -= 1
                            }
                        }) {
                            HStack {
                                Image(systemName: "chevron.left")
                                Text("Back")
                            }
                            .font(AppTypography.dynamicFont(style: .body, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, AppLayout.spacingM)
                            .padding(.vertical, AppLayout.spacingS)
                        }
                    }

                    Spacer()

                    // Next/Get Started button
                    Button(action: {
                        if pageIndex < pages.count - 1 {
                            withAnimation {
                                pageIndex += 1
                            }
                        } else {
                            onboardingManager.completeOnboarding()
                        }
                    }) {
                        HStack {
                            Text(pageIndex < pages.count - 1 ? "Next" : "Get Started")
                            Image(systemName: "chevron.right")
                        }
                        .font(AppTypography.dynamicFont(style: .body, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, AppLayout.spacingM)
                        .padding(.vertical, AppLayout.spacingS)
                        .background(
                            Capsule()
                                .fill(Color.white.opacity(0.2))
                        )
                    }
                }
                .padding(.horizontal, AppLayout.spacingL)
                .padding(.bottom, AppLayout.spacingXL)
            }
        }
        .onAppear {
            pageIndex = onboardingManager.currentOnboardingPage
        }
    }
}

// Individual onboarding page view
struct OnboardingPageView: View {
    let page: OnboardingPage

    var body: some View {
        VStack(spacing: AppLayout.spacingXL) {
            // Icon
            Image(systemName: page.imageName)
                .font(.system(size: 100))
                .foregroundColor(.white)
                .padding(.bottom, AppLayout.spacingL)

            // Title
            Text(page.title)
                .font(AppTypography.dynamicFont(style: .title, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingL)

            // Description
            Text(page.description)
                .font(AppTypography.dynamicFont(style: .body))
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.spacingXL)
        }
        .padding()
    }
}

// Onboarding page data model
struct OnboardingPage {
    let title: String
    let description: String
    let imageName: String
    let backgroundColor: Color
}

#Preview {
    OnboardingView()
}
