import SwiftUI

/// Controls the app launch flow between splash screen, onboarding, and main app
struct AppLaunchController: View {
    // State to track the current app state
    @State private var showSplash = true
    @ObservedObject private var onboardingManager = OnboardingManager.shared
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        ZStack {
            // Main app content
            if !showSplash && onboardingManager.hasCompletedOnboarding {
                MainTabView()
            }
            // Onboarding flow
            else if !showSplash && !onboardingManager.hasCompletedOnboarding {
                OnboardingView()
            }
            // Splash screen
            else if showSplash {
                SplashScreen {
                    // When splash completes, hide it
                    withAnimation {
                        showSplash = false
                    }
                }
            }
        }
    }
}

#Preview {
    AppLaunchController()
}
