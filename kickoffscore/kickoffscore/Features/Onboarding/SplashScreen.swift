import SwiftUI

struct SplashScreen: View {
    // Animation states
    @State private var opacity: Double = 1
    @State private var isLoading = true

    // Completion handler
    var onSplashComplete: () -> Void

    var body: some View {
        ZStack {
            // Background gradient with #333 color
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "333333"),
                    Color(hex: "222222")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            VStack(spacing: AppLayout.spacingM) {
                // App icon
                Image("AppIcon")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 120, height: 120)
                    .cornerRadius(24)
                    .overlay(
                        RoundedRectangle(cornerRadius: 24)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
                    .shadow(color: Color.black.opacity(0.2), radius: 15, x: 0, y: 5)

                // App name
                Text("KickoffScore")
                    .font(AppTypography.dynamicFont(style: .largeTitle, weight: .bold))
                    .foregroundColor(.white)

                // Tagline
                Text("Your Ultimate Football Companion")
                    .font(AppTypography.dynamicFont(style: .subheadline))
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding()
        }
        .opacity(opacity)
        .onAppear {
            animateSplash()
        }
    }

    // Animate the splash screen with a fade out at the end
    private func animateSplash() {
        // Simulate loading content in the background
        DispatchQueue.global(qos: .userInitiated).async {
            // Simulate loading tasks
            self.preloadAppContent()

            // After content is loaded, fade out the splash screen
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.easeOut(duration: 0.8)) {
                    self.opacity = 0.0
                }

                // Complete the splash screen after fade out
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                    self.isLoading = false
                    self.onSplashComplete()
                }
            }
        }
    }

    // Simulate preloading app content
    private func preloadAppContent() {
        // This is where you would perform any initialization tasks
        // For example:
        // 1. Load user preferences
        // 2. Initialize network services
        // 3. Preload cached data
        // 4. Check authentication status

        // Minimal loading time for essential initialization only
        Thread.sleep(forTimeInterval: 0.3)
    }
}

#Preview {
    SplashScreen {
        print("Splash completed")
    }
}
