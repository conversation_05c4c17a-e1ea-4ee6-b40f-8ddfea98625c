import Foundation
import Combine

// MARK: - API Error Enum

public enum APIError: Error, LocalizedError {
    case invalidURL
    case requestFailed(Error)
    case invalidResponse
    case statusCode(Int)
    case decodingError(Error)
    case noData
    case custom(String)

    public var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "The endpoint URL is invalid."
        case .requestFailed(let error):
            return "The network request failed: \(error.localizedDescription)"
        case .invalidResponse:
            return "Received an invalid response from the server."
        case .statusCode(let code):
            return "Received HTTP status code \(code)."
        case .decodingError(let error):
            return "Failed to decode the data: \(error.localizedDescription)"
        case .noData:
            return "No data received from the server."
        case .custom(let message):
            return message
        }
    }
}

// MARK: - API Service

public class APIService {

    public static let shared = APIService()

    private let baseURL = URL(string: Constants.apiBaseURL)!
    private let urlSession: URLSession
    private let decoder: JSONDecoder
    private let encoder: JSONEncoder

    // Auth state
    public var isAuthenticated: Bool {
        return SecureConfig.getAuthToken() != nil
    }

    private init() {
        // Configure URLSession with enhanced security
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = Constants.requestTimeoutInterval
        config.timeoutIntervalForResource = Constants.requestTimeoutInterval * 2
        config.waitsForConnectivity = true

        // Load API Key from SecureConfig with environment support
        guard let apiKey = SecureConfig.getAPIKey(), !apiKey.isEmpty else {
            Logger.error("CRITICAL ERROR: API Key not found!", category: .security)
            // In a production app, we would show an error UI or prevent app launch
            fatalError("API Key is required for the app to function")
        }

        // Enhanced headers with security information
        config.httpAdditionalHeaders = [
            "Accept": "application/json",
            "Content-Type": "application/json",
            "X-API-Key": apiKey,
            "User-Agent": "KickoffScore/\(Constants.appVersion) (\(Constants.buildNumber))",
            "X-App-Version": Constants.appVersion,
            "X-Platform": "iOS"
            // x-auth-token will be added per-request if needed
        ]
        config.timeoutIntervalForRequest = 30 // 30 seconds timeout
        config.timeoutIntervalForResource = 60

        self.urlSession = URLSession(configuration: config)

        // Configure JSON Decoder
        self.decoder = JSONDecoder()

        // Add custom date decoding strategy
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)
        decoder.dateDecodingStrategy = .custom { decoder in
            let container = try decoder.singleValueContainer()
            let dateString = try container.decode(String.self)

            if let date = dateFormatter.date(from: dateString) {
                return date
            }

            // Try ISO8601 as fallback
            let isoFormatter = ISO8601DateFormatter()
            isoFormatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
            if let date = isoFormatter.date(from: dateString) {
                return date
            }

            throw DecodingError.dataCorruptedError(in: container, debugDescription: "Cannot decode date string \(dateString)")
        }

        // Configure JSON Encoder
        self.encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        // encoder.outputFormatting = .prettyPrinted // Uncomment if needed for debugging
    }

    // MARK: - Generic Fetch Functions

    /// Create a URLRequest for an API endpoint
    /// - Parameters:
    ///   - endpoint: The API endpoint path
    ///   - parameters: Optional query parameters
    /// - Returns: A configured URLRequest
    public func createRequest(
        endpoint: String,
        parameters: [String: String]? = nil
    ) -> URLRequest? {
        // Construct URL with query parameters
        guard var urlComponents = URLComponents(url: baseURL.appendingPathComponent(endpoint), resolvingAgainstBaseURL: true) else {
            return nil
        }

        if let parameters = parameters {
            urlComponents.queryItems = parameters.map { URLQueryItem(name: $0.key, value: $0.value) }
        }

        guard let finalURL = urlComponents.url else {
            return nil
        }

        // Create request
        var request = URLRequest(url: finalURL)
        request.httpMethod = "GET"

        // Add auth token if available
        if let token = getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
        }

        return request
    }

    /// Fetch raw data from an endpoint
    /// - Parameters:
    ///   - endpoint: The specific endpoint path (e.g., "/fixtures")
    ///   - parameters: Optional query parameters [String: String]
    /// - Returns: A publisher that emits the raw data and response or an error
    public func fetchDataPublisher(endpoint: String, parameters: [String: String]? = nil) -> AnyPublisher<URLSession.DataTaskPublisher.Output, APIError> {
        // Create request
        guard let request = createRequest(endpoint: endpoint, parameters: parameters) else {
            return Fail(error: APIError.invalidURL).eraseToAnyPublisher()
        }

        // Perform request
        return urlSession.dataTaskPublisher(for: request)
            .mapError { error -> APIError in
                return .requestFailed(error)
            }
            .eraseToAnyPublisher()
    }

    /// Generic function to fetch and decode data from an endpoint.
    /// - Parameters:
    ///   - endpoint: The specific endpoint path (e.g., "/fixtures")
    ///   - parameters: Optional query parameters [String: String]
    ///   - completion: Result containing decoded data or an APIError
    public func fetchData<T: Decodable>(endpoint: String, parameters: [String: String]? = nil, completion: @escaping (Result<T, APIError>) -> Void) {

        // 1. Construct URL
        guard var urlComponents = URLComponents(url: baseURL.appendingPathComponent(endpoint), resolvingAgainstBaseURL: true) else {
            completion(.failure(.invalidURL))
            return
        }

        if let parameters = parameters {
            urlComponents.queryItems = parameters.map { URLQueryItem(name: $0.key, value: $0.value) }
        }

        guard let finalURL = urlComponents.url else {
            completion(.failure(.invalidURL))
            return
        }

        // 2. Create Request
        var request = URLRequest(url: finalURL)
        request.httpMethod = "GET"

        // Add auth token if available
        if let token = getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
            print("Adding auth token to GET request: \(token)")
        }

        print("Fetching data from: \(finalURL.absoluteString)")

        // 3. Perform Data Task
        let task = urlSession.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            // 4. Handle Network Errors
            if let error = error {
                DispatchQueue.main.async {
                    completion(.failure(.requestFailed(error)))
                }
                return
            }

            // 5. Validate HTTP Response
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(.failure(.invalidResponse))
                }
                return
            }

            // Print status code for debugging
            print("Received HTTP Status Code: \(httpResponse.statusCode) for \(finalURL.absoluteString)")

            guard (200...299).contains(httpResponse.statusCode) else {
                // Attempt to decode error message from response body if available
                if let data = data, let errorResponse = try? self.decoder.decode(APIErrorResponse.self, from: data) {
                    DispatchQueue.main.async {
                        completion(.failure(.custom("API Error (\(httpResponse.statusCode)): \(errorResponse.message ?? "Unknown error")")))
                    }
                } else {
                    DispatchQueue.main.async {
                        completion(.failure(.statusCode(httpResponse.statusCode)))
                    }
                }
                return
            }

            // 6. Check for Data
            guard let data = data else {
                DispatchQueue.main.async {
                    completion(.failure(.noData))
                }
                return
            }

            // 7. Decode Data
            do {
                // Log the size of the response instead of the full JSON
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("Response received: \(jsonString.count) chars")
                }

                let decodedData = try self.decoder.decode(T.self, from: data)
                print("Successfully decoded data of type \(T.self)")
                DispatchQueue.main.async {
                    completion(.success(decodedData))
                }
            } catch let decodingError {
                print("Decoding Error for URL \(finalURL.absoluteString): \(decodingError)")
                // Log only the size of the JSON data on error
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("Error response size: \(jsonString.count) chars")

                    // Only log a small preview of the JSON for debugging
                    let previewLength = min(200, jsonString.count)
                    let preview = jsonString.prefix(previewLength)
                    print("JSON preview: \(preview)...")
                }

                // Create a more detailed error message
                var detailedError = "Decoding Error: \(decodingError.localizedDescription)"

                // Add more context for DecodingError types
                if let decodingErr = decodingError as? DecodingError {
                    switch decodingErr {
                    case .typeMismatch(let type, let context):
                        detailedError += "\nType mismatch for type \(type) at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                    case .valueNotFound(let type, let context):
                        detailedError += "\nValue not found for type \(type) at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                    case .keyNotFound(let key, let context):
                        detailedError += "\nKey '\(key.stringValue)' not found at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                    case .dataCorrupted(let context):
                        detailedError += "\nData corrupted at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                    @unknown default:
                        detailedError += "\nUnknown decoding error"
                    }
                }

                print("Detailed decoding error: \(detailedError)")

                DispatchQueue.main.async {
                    completion(.failure(.custom(detailedError)))
                }
            }
        }

        task.resume()
    }

    // Helper struct to decode potential error messages from the API
    private struct APIErrorResponse: Decodable {
        let message: String?
        // Add other potential error fields if your API returns them
    }

    // MARK: - Auth Methods

    /// Save authentication token to Keychain
    public func saveAuthToken(_ token: String) {
        _ = SecureConfig.saveAuthToken(token)
    }

    /// Get authentication token from Keychain
    public func getAuthToken() -> String? {
        return SecureConfig.getAuthToken()
    }

    /// Clear authentication token from Keychain
    public func clearAuthToken() {
        _ = SecureConfig.deleteAuthToken()
    }

    // MARK: - POST Method

    /// Generic function to post data to an endpoint.
    /// - Parameters:
    ///   - endpoint: The specific endpoint path (e.g., "/fixtures/votes/123")
    ///   - body: The data to send in the request body
    ///   - completion: Result containing decoded data or an APIError

    // MARK: - Upload Method

    /// Upload multipart form data (e.g., images) to an endpoint.
    /// - Parameters:
    ///   - endpoint: The specific endpoint path (e.g., "/users/profile-image")
    ///   - formData: The MultipartFormData object containing the data to upload
    ///   - completion: Result containing decoded data or an APIError
    public func uploadData<T: Decodable>(endpoint: String, formData: MultipartFormData, completion: @escaping (Result<T, APIError>) -> Void) {

        // 1. Construct URL
        guard let url = URL(string: baseURL.absoluteString + endpoint) else {
            completion(.failure(.invalidURL))
            return
        }

        // 2. Create Request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(formData.contentType, forHTTPHeaderField: "Content-Type")

        // 3. Add auth token if available
        if let token = getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
            print("Adding auth token to upload request: \(token)")
        }

        // 4. Set request body
        request.httpBody = formData.finalize()

        print("Uploading data to: \(url.absoluteString)")

        // 5. Perform Data Task
        let task = urlSession.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            // 6. Handle Network Errors
            if let error = error {
                DispatchQueue.main.async {
                    completion(.failure(.requestFailed(error)))
                }
                return
            }

            // 7. Validate HTTP Response
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(.failure(.invalidResponse))
                }
                return
            }

            // Print status code for debugging
            print("Received HTTP Status Code: \(httpResponse.statusCode) for \(url.absoluteString)")

            guard (200...299).contains(httpResponse.statusCode) else {
                // Attempt to decode error message from response body if available
                if let data = data, let errorResponse = try? self.decoder.decode(APIErrorResponse.self, from: data) {
                    DispatchQueue.main.async {
                        completion(.failure(.custom("API Error (\(httpResponse.statusCode)): \(errorResponse.message ?? "Unknown error")")))
                    }
                } else {
                    DispatchQueue.main.async {
                        completion(.failure(.statusCode(httpResponse.statusCode)))
                    }
                }
                return
            }

            // 8. Check for Data
            guard let data = data else {
                DispatchQueue.main.async {
                    completion(.failure(.noData))
                }
                return
            }

            // 9. Decode Data
            do {
                // Log only the size of the response
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("Response received: \(jsonString.count) chars")
                }

                let decodedData = try self.decoder.decode(T.self, from: data)
                print("Successfully decoded data of type \(T.self)")
                DispatchQueue.main.async {
                    completion(.success(decodedData))
                }
            } catch let decodingError {
                print("Decoding Error for URL \(url.absoluteString): \(decodingError)")
                // Log only the size and a preview of the JSON data on error
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("Error response size: \(jsonString.count) chars")

                    // Only log a small preview of the JSON for debugging
                    let previewLength = min(200, jsonString.count)
                    let preview = jsonString.prefix(previewLength)
                    print("JSON preview: \(preview)...")
                }
                DispatchQueue.main.async {
                    completion(.failure(.decodingError(decodingError)))
                }
            }
        }

        task.resume()
    }
    // MARK: - User Favorites

    /// Update user favorites
    /// - Parameters:
    ///   - parameters: Dictionary containing arrays of favorite IDs (teams, players, leagues)
    ///   - completion: Result indicating success or failure
    public func updateUserFavorites(parameters: [String: Any], completion: @escaping (Result<Bool, APIError>) -> Void) {
        // Ensure user is authenticated
        guard let token = getAuthToken() else {
            completion(.failure(.custom("User not authenticated")))
            return
        }

        // Construct URL
        guard let url = URL(string: baseURL.absoluteString + "/users/favorites") else {
            completion(.failure(.invalidURL))
            return
        }

        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(token, forHTTPHeaderField: "x-auth-token")

        // Encode request body
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            completion(.failure(.custom("Failed to encode request body: \(error.localizedDescription)")))
            return
        }

        print("Updating user favorites at: \(url.absoluteString)")

        // Perform request
        let task = urlSession.dataTask(with: request) { data, response, error in
            // Handle network errors
            if let error = error {
                DispatchQueue.main.async {
                    completion(.failure(.requestFailed(error)))
                }
                return
            }

            // Validate HTTP response
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(.failure(.invalidResponse))
                }
                return
            }

            // Check status code
            guard (200...299).contains(httpResponse.statusCode) else {
                DispatchQueue.main.async {
                    completion(.failure(.statusCode(httpResponse.statusCode)))
                }
                return
            }

            // Success
            DispatchQueue.main.async {
                completion(.success(true))
            }
        }

        task.resume()
    }

    public func postData<T: Encodable, U: Decodable>(endpoint: String, body: T, completion: @escaping (Result<U, APIError>) -> Void) {

        // 1. Construct URL
        guard let url = URL(string: baseURL.absoluteString + endpoint) else {
            completion(.failure(.invalidURL))
            return
        }

        // 2. Create Request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // 3. Add auth token if available
        if let token = getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
            print("Adding auth token to request: \(token)")
        }

        // 4. Encode request body
        do {
            request.httpBody = try encoder.encode(body)
        } catch {
            completion(.failure(.custom("Failed to encode request body: \(error.localizedDescription)")))
            return
        }

        print("Posting data to: \(url.absoluteString)")

        // 5. Perform Data Task
        let task = urlSession.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            // 6. Handle Network Errors
            if let error = error {
                DispatchQueue.main.async {
                    completion(.failure(.requestFailed(error)))
                }
                return
            }

            // 7. Validate HTTP Response
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(.failure(.invalidResponse))
                }
                return
            }

            // Print status code for debugging
            print("Received HTTP Status Code: \(httpResponse.statusCode) for \(url.absoluteString)")

            guard (200...299).contains(httpResponse.statusCode) else {
                // Attempt to decode error message from response body if available
                if let data = data, let errorResponse = try? self.decoder.decode(APIErrorResponse.self, from: data) {
                    DispatchQueue.main.async {
                        completion(.failure(.custom("API Error (\(httpResponse.statusCode)): \(errorResponse.message ?? "Unknown error")")))
                    }
                } else {
                    DispatchQueue.main.async {
                        completion(.failure(.statusCode(httpResponse.statusCode)))
                    }
                }
                return
            }

            // 8. Check for Data
            guard let data = data else {
                DispatchQueue.main.async {
                    completion(.failure(.noData))
                }
                return
            }

            // 9. Decode Data
            do {
                // Log only the size of the response
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("POST response received: \(jsonString.count) chars")
                }

                let decodedData = try self.decoder.decode(U.self, from: data)
                DispatchQueue.main.async {
                    completion(.success(decodedData))
                }
            } catch let decodingError {
                print("Decoding Error for URL \(url.absoluteString): \(decodingError)")
                // Log only the size and a preview of the JSON data on error
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("Error response size: \(jsonString.count) chars")

                    // Only log a small preview of the JSON for debugging
                    let previewLength = min(200, jsonString.count)
                    let preview = jsonString.prefix(previewLength)
                    print("JSON preview: \(preview)...")
                }
                DispatchQueue.main.async {
                    completion(.failure(.decodingError(decodingError)))
                }
            }
        }

        task.resume()
    }

    /// Generic function to put data to an endpoint.
    /// - Parameters:
    ///   - endpoint: The specific endpoint path
    ///   - body: The data to send in the request body
    ///   - completion: Result containing decoded data or an APIError
    public func putData<T: Encodable, U: Decodable>(endpoint: String, body: T, completion: @escaping (Result<U, APIError>) -> Void) {
        // 1. Construct URL
        guard let url = URL(string: baseURL.absoluteString + endpoint) else {
            completion(.failure(.invalidURL))
            return
        }

        // 2. Create Request
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // 3. Add auth token if available
        if let token = getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
            print("Adding auth token to request: \(token)")
        }

        // 4. Encode request body
        do {
            request.httpBody = try encoder.encode(body)
        } catch {
            completion(.failure(.custom("Failed to encode request body: \(error.localizedDescription)")))
            return
        }

        print("Putting data to: \(url.absoluteString)")

        // 5. Perform Data Task
        let task = urlSession.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            // 6. Handle Network Errors
            if let error = error {
                DispatchQueue.main.async {
                    completion(.failure(.requestFailed(error)))
                }
                return
            }

            // 7. Validate HTTP Response
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(.failure(.invalidResponse))
                }
                return
            }

            // Print status code for debugging
            print("Received HTTP Status Code: \(httpResponse.statusCode) for \(url.absoluteString)")

            guard (200...299).contains(httpResponse.statusCode) else {
                // Attempt to decode error message from response body if available
                if let data = data, let errorResponse = try? self.decoder.decode(APIErrorResponse.self, from: data) {
                    DispatchQueue.main.async {
                        completion(.failure(.custom("API Error (\(httpResponse.statusCode)): \(errorResponse.message ?? "Unknown error")")))
                    }
                } else {
                    DispatchQueue.main.async {
                        completion(.failure(.statusCode(httpResponse.statusCode)))
                    }
                }
                return
            }

            // 8. Check for Data
            guard let data = data else {
                DispatchQueue.main.async {
                    completion(.failure(.noData))
                }
                return
            }

            // 9. Decode Data
            do {
                // Log only the size of the response
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("PUT response received: \(jsonString.count) chars")
                }

                let decodedData = try self.decoder.decode(U.self, from: data)
                DispatchQueue.main.async {
                    completion(.success(decodedData))
                }
            } catch let decodingError {
                print("Decoding Error for URL \(url.absoluteString): \(decodingError)")
                // Log only the size and a preview of the JSON data on error
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("Error response size: \(jsonString.count) chars")

                    // Only log a small preview of the JSON for debugging
                    let previewLength = min(200, jsonString.count)
                    let preview = jsonString.prefix(previewLength)
                    print("JSON preview: \(preview)...")
                }
                DispatchQueue.main.async {
                    completion(.failure(.decodingError(decodingError)))
                }
            }
        }

        task.resume()
    }

    /// Generic function to delete data from an endpoint.
    /// - Parameters:
    ///   - endpoint: The specific endpoint path
    ///   - completion: Result containing decoded data or an APIError
    public func deleteData<T: Decodable>(endpoint: String, completion: @escaping (Result<T, APIError>) -> Void) {
        // 1. Construct URL
        guard let url = URL(string: baseURL.absoluteString + endpoint) else {
            completion(.failure(.invalidURL))
            return
        }

        // 2. Create Request
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"

        // 3. Add auth token if available
        if let token = getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
            print("Adding auth token to request: \(token)")
        }

        print("Deleting data from: \(url.absoluteString)")

        // 4. Perform Data Task
        let task = urlSession.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            // 5. Handle Network Errors
            if let error = error {
                DispatchQueue.main.async {
                    completion(.failure(.requestFailed(error)))
                }
                return
            }

            // 6. Validate HTTP Response
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(.failure(.invalidResponse))
                }
                return
            }

            // Print status code for debugging
            print("Received HTTP Status Code: \(httpResponse.statusCode) for \(url.absoluteString)")

            guard (200...299).contains(httpResponse.statusCode) else {
                // Attempt to decode error message from response body if available
                if let data = data, let errorResponse = try? self.decoder.decode(APIErrorResponse.self, from: data) {
                    DispatchQueue.main.async {
                        completion(.failure(.custom("API Error (\(httpResponse.statusCode)): \(errorResponse.message ?? "Unknown error")")))
                    }
                } else {
                    DispatchQueue.main.async {
                        completion(.failure(.statusCode(httpResponse.statusCode)))
                    }
                }
                return
            }

            // 7. Check for Data
            guard let data = data else {
                DispatchQueue.main.async {
                    completion(.failure(.noData))
                }
                return
            }

            // 8. Decode Data
            do {
                // Log only the size of the response
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("DELETE response received: \(jsonString.count) chars")
                }

                let decodedData = try self.decoder.decode(T.self, from: data)
                DispatchQueue.main.async {
                    completion(.success(decodedData))
                }
            } catch let decodingError {
                print("Decoding Error for URL \(url.absoluteString): \(decodingError)")
                // Log only the size and a preview of the JSON data on error
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("Error response size: \(jsonString.count) chars")

                    // Only log a small preview of the JSON for debugging
                    let previewLength = min(200, jsonString.count)
                    let preview = jsonString.prefix(previewLength)
                    print("JSON preview: \(preview)...")
                }
                DispatchQueue.main.async {
                    completion(.failure(.decodingError(decodingError)))
                }
            }
        }

        task.resume()
    }
}
