import Foundation
import Security

struct KeychainHelper {

    // MARK: - Keychain Operations

    static func save(key: String, service: String, account: String) -> OSStatus {
        guard let data = key.data(using: .utf8) else {
            print("Error: Could not convert key to data.")
            return errSecParam // Or a more appropriate error
        }

        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: service,
            kSecAttrAccount: account,
            kSecValueData: data
        ] as [String: Any]

        // Delete any existing item first to allow updates
        SecItemDelete(query as CFDictionary)

        // Add the new item
        let status = SecItemAdd(query as CFDictionary, nil)

        if status != errSecSuccess {
            print("Error saving to Keychain: \(status)")
        }
        return status
    }

    static func load(service: String, account: String) -> String? {
        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: service,
            kSecAttrAccount: account,
            kSecReturnData: kCFBooleanTrue!,
            kSecMatchLimit: kSecMatchLimitOne
        ] as [String: Any]

        var dataTypeRef: AnyObject?
        let status: OSStatus = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)

        if status == errSecSuccess {
            guard let retrievedData = dataTypeRef as? Data,
                  let key = String(data: retrievedData, encoding: .utf8) else {
                print("Error: Could not convert loaded data to string.")
                return nil
            }
            return key
        } else if status == errSecItemNotFound {
            print("Keychain item not found for service: \(service), account: \(account)")
            return nil
        } else {
            print("Error loading from Keychain: \(status)")
            return nil
        }
    }

    static func delete(service: String, account: String) -> OSStatus {
        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: service,
            kSecAttrAccount: account
        ] as [String: Any]

        let status = SecItemDelete(query as CFDictionary)
        if status != errSecSuccess && status != errSecItemNotFound {
            print("Error deleting from Keychain: \(status)")
        }
        return status
    }
}
