import Foundation
import SocketIO
import Combine
import UIKit
import BackgroundTasks

/// A manager for handling socket connections and real-time updates
class SocketManager: ObservableObject {
    // Singleton instance
    static let shared = SocketManager()

    // Published properties for UI updates
    @Published var isConnected = false
    @Published var lastError: String? = nil

    // Socket.IO manager and socket
    private var socketManager: SocketIO.SocketManager?
    private var fixtureSocket: SocketIOClient?

    // Subjects for broadcasting fixture updates
    private let fixtureUpdateSubject = PassthroughSubject<Fixture, Never>()
    private let leagueFixturesUpdateSubject = PassthroughSubject<(leagueId: Int, fixtures: [Fixture]), Never>()
    private let liveFixturesUpdateSubject = PassthroughSubject<[Fixture], Never>()

    // Track recent socket updates to prevent stale data overwrites
    private var recentSocketUpdates: [Int: Date] = [:] // fixtureId -> timestamp
    private let socketUpdateQueue = DispatchQueue(label: "socket.updates", qos: .userInitiated)

    // Set of subscribed fixture IDs to avoid duplicate subscriptions
    private var subscribedFixtureIds = Set<Int>()
    private var subscribedLeagueIds = Set<Int>()
    private var isSubscribedToLive = false

    // Background state tracking
    private var backgroundTask: UIBackgroundTaskIdentifier = .invalid
    private var lastBackgroundDate: Date?
    private var appStateObserver: AnyCancellable?
    private var reconnectionTimer: Timer?
    private var isInBackground = false

    // Background task integration
    @MainActor
    private var backgroundTaskManager: BackgroundTaskManager {
        return BackgroundTaskManager.shared
    }

    // Constants
    private let shortBackgroundTimeThreshold: TimeInterval = 30 // 30 seconds
    private let reconnectionInterval: TimeInterval = 5 // 5 seconds
    private let maxReconnectionAttempts = 5
    private var reconnectionAttempts = 0

    // Private initializer for singleton
    private init() {
        setupSocket()
        setupAppStateObservers()
    }

    // MARK: - Socket Setup

    /// Set up the socket connection
    private func setupSocket() {
        // Create a socket manager configuration
        let config: SocketIOClientConfiguration = [
            .log(true),
            .compress,
            .forceWebsockets(true)
        ]

        // Create the socket manager with the API base URL
        guard let url = URL(string: Constants.apiBaseURL) else {
            Logger.error("Invalid API base URL for socket connection", category: .network)
            return
        }

        // Create the socket manager
        let manager = SocketIO.SocketManager(socketURL: url, config: config)

        // Create a socket for the fixtures namespace
        let fixtureSocket = manager.socket(forNamespace: "/fixtures")

        // Store references
        self.socketManager = manager
        self.fixtureSocket = fixtureSocket

        // Set up event handlers
        setupEventHandlers()
    }

    /// Set up socket event handlers
    private func setupEventHandlers() {
        guard let socket = fixtureSocket else { return }

        // Connection events
        socket.on(clientEvent: .connect) { [weak self] _, _ in
            Logger.info("Socket connected", category: .network)
            DispatchQueue.main.async {
                self?.isConnected = true
                self?.lastError = nil

                // Resubscribe to previously subscribed fixtures and leagues
                self?.resubscribeToAll()
            }
        }

        socket.on(clientEvent: .disconnect) { [weak self] _, _ in
            Logger.info("Socket disconnected", category: .network)
            DispatchQueue.main.async {
                self?.isConnected = false
            }
        }

        socket.on(clientEvent: .error) { [weak self] data, _ in
            let errorMessage = data.description
            Logger.error("Socket error: \(errorMessage)", category: .network)
            DispatchQueue.main.async {
                self?.lastError = "Connection error: \(errorMessage)"
            }
        }

        // Fixture update events
        socket.on("fixture-update") { [weak self] data, _ in
            guard let weakSelf = self,
                  let fixtureData = data.first,
                  let fixtureJSON = try? JSONSerialization.data(withJSONObject: fixtureData),
                  let fixture = try? JSONDecoder().decode(Fixture.self, from: fixtureJSON) else {
                Logger.error("Failed to decode fixture update", category: .network)
                return
            }

            Logger.info("Received fixture update for ID: \(fixture.id)", category: .network)

            // Track this socket update
            weakSelf.trackSocketUpdate(for: fixture.id)

            // Invalidate cache for the updated fixture
            weakSelf.invalidateFixtureCache(fixtureId: fixture.id)

            DispatchQueue.main.async {
                weakSelf.fixtureUpdateSubject.send(fixture)
            }
        }

        // League fixtures update events
        socket.on("league-fixtures-update") { [weak self] data, _ in
            guard let weakSelf = self,
                  let updateData = data.first as? [String: Any],
                  let leagueId = updateData["leagueId"] as? Int,
                  let fixturesData = updateData["fixtures"] as? [[String: Any]] else {
                Logger.error("Failed to decode league fixtures update", category: .network)
                return
            }

            do {
                let fixturesJSON = try JSONSerialization.data(withJSONObject: fixturesData)
                let fixtures = try JSONDecoder().decode([Fixture].self, from: fixturesJSON)

                Logger.info("Received league fixtures update for league ID: \(leagueId) with \(fixtures.count) fixtures", category: .network)

                // Track socket updates for all fixtures
                for fixture in fixtures {
                    weakSelf.trackSocketUpdate(for: fixture.id)
                }

                // Invalidate cache for league fixtures
                weakSelf.invalidateLeagueFixturesCache(leagueId: leagueId)

                DispatchQueue.main.async {
                    weakSelf.leagueFixturesUpdateSubject.send((leagueId: leagueId, fixtures: fixtures))
                }
            } catch {
                Logger.error("Failed to decode league fixtures: \(error.localizedDescription)", category: .network)
            }
        }

        // Live fixtures update events
        socket.on("live-fixtures-update") { [weak self] data, _ in
            guard let weakSelf = self,
                  let fixturesData = data.first,
                  let fixturesJSON = try? JSONSerialization.data(withJSONObject: fixturesData),
                  let fixtures = try? JSONDecoder().decode([Fixture].self, from: fixturesJSON) else {
                Logger.error("Failed to decode live fixtures update", category: .network)
                return
            }

            Logger.info("Received live fixtures update with \(fixtures.count) fixtures", category: .network)

            // Debug: Log fixture details for troubleshooting and track socket updates
            for fixture in fixtures {
                // Track this socket update
                weakSelf.trackSocketUpdate(for: fixture.id)

                if let elapsed = fixture.status.elapsed {
                    Logger.debug("Live fixture update - ID: \(fixture.id), Status: \(fixture.status.short ?? "nil"), Elapsed: \(elapsed), Extra: \(fixture.status.extra ?? 0)", category: .network)
                }
            }

            // Invalidate cache for all live fixtures
            weakSelf.invalidateLiveFixturesCache()

            DispatchQueue.main.async {
                weakSelf.liveFixturesUpdateSubject.send(fixtures)
            }
        }

        // Subscription response events
        socket.on("subscription-success") { data, _ in
            guard let updateData = data.first as? [String: Any],
                  let type = updateData["type"] as? String else {
                return
            }

            var logMessage = "Successfully subscribed to \(type)"
            if let id = updateData["id"] as? Int {
                logMessage += " ID: \(id)"
            }

            Logger.info(logMessage, category: .network)
        }

        socket.on("subscription-error") { data, _ in
            guard let errorData = data.first as? [String: Any],
                  let message = errorData["message"] as? String else {
                return
            }

            Logger.error("Subscription error: \(message)", category: .network)
        }
    }

    // MARK: - Connection Management

    /// Connect to the socket server
    func connect() {
        fixtureSocket?.connect()
    }

    /// Disconnect from the socket server
    func disconnect() {
        fixtureSocket?.disconnect()
    }

    /// Resubscribe to all previously subscribed fixtures and leagues
    private func resubscribeToAll() {
        // Resubscribe to fixtures
        for fixtureId in subscribedFixtureIds {
            subscribeToFixture(fixtureId: fixtureId)
        }

        // Resubscribe to leagues
        for leagueId in subscribedLeagueIds {
            subscribeToLeague(leagueId: leagueId)
        }

        // Resubscribe to live fixtures if previously subscribed
        if isSubscribedToLive {
            subscribeToLiveFixtures()
        }
    }

    // MARK: - Subscription Methods

    /// Subscribe to updates for a specific fixture
    /// - Parameter fixtureId: The fixture ID to subscribe to
    func subscribeToFixture(fixtureId: Int) {
        guard let socket = fixtureSocket, isConnected else {
            // If not connected, store the ID and connect
            subscribedFixtureIds.insert(fixtureId)
            connect()
            return
        }

        // Add to subscribed set
        subscribedFixtureIds.insert(fixtureId)

        // Emit subscription event
        socket.emit("subscribe-fixture", fixtureId)
        Logger.info("Subscribing to fixture ID: \(fixtureId)", category: .network)
    }

    /// Subscribe to updates for all fixtures in a league
    /// - Parameter leagueId: The league ID to subscribe to
    func subscribeToLeague(leagueId: Int) {
        guard let socket = fixtureSocket, isConnected else {
            // If not connected, store the ID and connect
            subscribedLeagueIds.insert(leagueId)
            connect()
            return
        }

        // Add to subscribed set
        subscribedLeagueIds.insert(leagueId)

        // Emit subscription event
        socket.emit("subscribe-league", leagueId)
        Logger.info("Subscribing to league ID: \(leagueId)", category: .network)
    }

    /// Subscribe to updates for all live fixtures
    func subscribeToLiveFixtures() {
        guard let socket = fixtureSocket, isConnected else {
            // If not connected, mark as subscribed and connect
            isSubscribedToLive = true
            connect()
            return
        }

        // Mark as subscribed
        isSubscribedToLive = true

        // Emit subscription event
        socket.emit("subscribe-live")
        Logger.info("Subscribing to all live fixtures", category: .network)
    }

    /// Unsubscribe from updates for a specific fixture
    /// - Parameter fixtureId: The fixture ID to unsubscribe from
    func unsubscribeFromFixture(fixtureId: Int) {
        guard let socket = fixtureSocket, isConnected else {
            // If not connected, just remove from the set
            subscribedFixtureIds.remove(fixtureId)
            return
        }

        // Remove from subscribed set
        subscribedFixtureIds.remove(fixtureId)

        // Emit unsubscription event
        socket.emit("unsubscribe-fixture", fixtureId)
        Logger.info("Unsubscribing from fixture ID: \(fixtureId)", category: .network)
    }

    /// Unsubscribe from updates for all fixtures in a league
    /// - Parameter leagueId: The league ID to unsubscribe from
    func unsubscribeFromLeague(leagueId: Int) {
        guard let socket = fixtureSocket, isConnected else {
            // If not connected, just remove from the set
            subscribedLeagueIds.remove(leagueId)
            return
        }

        // Remove from subscribed set
        subscribedLeagueIds.remove(leagueId)

        // Emit unsubscription event
        socket.emit("unsubscribe-league", leagueId)
        Logger.info("Unsubscribing from league ID: \(leagueId)", category: .network)
    }

    /// Unsubscribe from updates for all live fixtures
    func unsubscribeFromLiveFixtures() {
        guard let socket = fixtureSocket, isConnected else {
            // If not connected, just mark as unsubscribed
            isSubscribedToLive = false
            return
        }

        // Mark as unsubscribed
        isSubscribedToLive = false

        // Emit unsubscription event
        socket.emit("unsubscribe-live")
        Logger.info("Unsubscribing from all live fixtures", category: .network)
    }

    // MARK: - Background State Handling

    /// Set up observers for app state changes
    private func setupAppStateObservers() {
        // Create a publisher that combines multiple app state notifications
        let appStatePublisher = Publishers.MergeMany([
            NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification),
            NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification),
            NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification),
            NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)
        ])

        // Subscribe to the combined publisher
        appStateObserver = appStatePublisher.sink { [weak self] notification in
            guard let self = self else { return }

            switch notification.name {
            case UIApplication.willResignActiveNotification:
                Logger.info("App will resign active", category: .network)
                // App is about to go to background
                self.prepareForBackground()

            case UIApplication.didEnterBackgroundNotification:
                Logger.info("App did enter background", category: .network)
                self.isInBackground = true
                self.lastBackgroundDate = Date()
                // Start background task to keep socket alive longer
                Task { @MainActor in
                    self.beginBackgroundTask()
                }

            case UIApplication.willEnterForegroundNotification:
                Logger.info("App will enter foreground", category: .network)
                self.isInBackground = false
                // End background task as we're coming back to foreground
                Task { @MainActor in
                    self.endBackgroundTask()
                }

            case UIApplication.didBecomeActiveNotification:
                Logger.info("App did become active", category: .network)
                // App is now active again
                self.handleForegroundTransition()

            default:
                break
            }
        }

        // For personal development teams, we'll use a simpler approach
        // No need to register for background tasks with special entitlements
        Logger.info("Using simplified background handling for development", category: .network)
    }

    /// Prepare for app going to background
    private func prepareForBackground() {
        // Keep socket connection alive for short background periods
        // For longer periods, we'll disconnect in beginBackgroundTask if needed
    }

    /// Begin a background task to extend runtime
    @MainActor
    private func beginBackgroundTask() {
        // End any existing background task
        endBackgroundTask()

        // Use the new background task manager for better handling
        backgroundTask = backgroundTaskManager.beginBackgroundTask(name: "Socket Connection Maintenance")

        // If we couldn't get a background task, try the old way
        if backgroundTask == .invalid {
            backgroundTask = UIApplication.shared.beginBackgroundTask { [weak self] in
                // This is the expiration handler - clean up if we're about to be suspended
                Task { @MainActor in
                    self?.endBackgroundTask()
                    self?.disconnect()
                }
            }
        }

        // Schedule a background refresh task
        scheduleBackgroundRefresh()
    }

    /// End the current background task
    @MainActor
    private func endBackgroundTask() {
        if backgroundTask != .invalid {
            backgroundTaskManager.endBackgroundTask(backgroundTask, name: "Socket Connection Maintenance")
            backgroundTask = .invalid
        }

        // Cancel any reconnection timer
        reconnectionTimer?.invalidate()
        reconnectionTimer = nil
        reconnectionAttempts = 0
    }

    /// Handle transition back to foreground
    private func handleForegroundTransition() {
        // Calculate time spent in background
        guard let lastBackgroundDate = lastBackgroundDate else {
            // If we don't have a background date, just reconnect
            ensureConnected()
            return
        }

        let timeInBackground = Date().timeIntervalSince(lastBackgroundDate)

        if timeInBackground < shortBackgroundTimeThreshold {
            // Short background time - socket might still be connected
            Logger.info("Short background time (\(Int(timeInBackground))s), checking connection", category: .network)
            if !isConnected {
                ensureConnected()
            }
        } else {
            // Long background time - reconnect and refresh
            Logger.info("Long background time (\(Int(timeInBackground))s), reconnecting", category: .network)
            reconnect()
        }

        // Reset background date
        self.lastBackgroundDate = nil
    }

    /// Ensure socket is connected
    private func ensureConnected() {
        if !isConnected {
            connect()
        }
    }

    /// Reconnect the socket with retry logic
    private func reconnect() {
        // Disconnect first to ensure clean state
        disconnect()

        // Reset reconnection attempts
        reconnectionAttempts = 0

        // Start reconnection process
        attemptReconnection()
    }

    /// Attempt to reconnect with exponential backoff
    private func attemptReconnection() {
        guard reconnectionAttempts < maxReconnectionAttempts else {
            Logger.error("Max reconnection attempts reached", category: .network)
            return
        }

        reconnectionAttempts += 1

        // Connect
        connect()

        // Schedule next attempt if this one fails
        reconnectionTimer?.invalidate()
        reconnectionTimer = Timer.scheduledTimer(withTimeInterval: reconnectionInterval * Double(reconnectionAttempts), repeats: false) { [weak self] _ in
            guard let self = self, !self.isConnected else { return }

            Logger.info("Reconnection attempt \(self.reconnectionAttempts) failed, trying again", category: .network)
            self.attemptReconnection()
        }
    }

    /// Schedule a background refresh task
    @MainActor
    private func scheduleBackgroundRefresh() {
        // Use the new background task manager for proper scheduling
        if backgroundTaskManager.isBackgroundRefreshAvailable {
            backgroundTaskManager.scheduleSocketRefresh()
            Logger.info("Background socket refresh scheduled via BackgroundTaskManager", category: .network)
        } else {
            // Fallback to simplified approach if background refresh is not available
            Logger.info("Background refresh not available, using simplified approach", category: .network)
        }
    }

    /// Handle a background task
    private func handleBackgroundTask(_ task: BGProcessingTask) {
        // This is a simplified version for personal development teams
        // Just complete the task immediately
        task.setTaskCompleted(success: true)

        // Log that we're using a simplified approach
        Logger.info("Using simplified background task handling for development", category: .network)
    }

    // MARK: - Publishers

    /// Get a publisher for fixture updates
    /// - Returns: A publisher that emits fixture updates
    func fixtureUpdates() -> AnyPublisher<Fixture, Never> {
        return fixtureUpdateSubject.eraseToAnyPublisher()
    }

    /// Get a publisher for league fixtures updates
    /// - Returns: A publisher that emits league fixtures updates
    func leagueFixturesUpdates() -> AnyPublisher<(leagueId: Int, fixtures: [Fixture]), Never> {
        return leagueFixturesUpdateSubject.eraseToAnyPublisher()
    }

    /// Get a publisher for live fixtures updates
    /// - Returns: A publisher that emits live fixtures updates
    func liveFixturesUpdates() -> AnyPublisher<[Fixture], Never> {
        return liveFixturesUpdateSubject.eraseToAnyPublisher()
    }

    // MARK: - Cache Invalidation

    /// Invalidate cache for a specific fixture
    /// - Parameter fixtureId: The ID of the fixture to invalidate
    private func invalidateFixtureCache(fixtureId: Int) {
        // Invalidate specific fixture cache
        let fixtureKey = CacheManager.shared.generateCacheKey(endpoint: "/fixtures", parameters: ["id": String(fixtureId)])
        CacheManager.shared.removeData(forKey: fixtureKey)

        // Invalidate fixture statistics cache
        let statsKey = CacheManager.shared.generateCacheKey(endpoint: "/fixtures/statistics", parameters: ["fixture": String(fixtureId)])
        CacheManager.shared.removeData(forKey: statsKey)

        // Trigger smart invalidation
        CacheManager.shared.triggerInvalidation(event: "fixture_updated")

        Logger.debug("Socket: Invalidated cache for fixture ID: \(fixtureId)", category: .performance)
    }

    /// Invalidate cache for league fixtures
    /// - Parameter leagueId: The ID of the league to invalidate
    private func invalidateLeagueFixturesCache(leagueId: Int) {
        // Invalidate league-specific fixture caches
        CacheManager.shared.invalidateCache(keyPattern: "*leagueId=\(leagueId)*")

        // Trigger smart invalidation
        CacheManager.shared.triggerInvalidation(event: "fixture_updated")

        Logger.debug("Socket: Invalidated cache for league ID: \(leagueId)", category: .performance)
    }

    /// Invalidate cache for all live fixtures
    private func invalidateLiveFixturesCache() {
        // Invalidate all live fixture caches
        CacheManager.shared.invalidateCache(keyPattern: "*status=LIVE*")
        CacheManager.shared.invalidateCache(keyPattern: "*status=1H*")
        CacheManager.shared.invalidateCache(keyPattern: "*status=2H*")
        CacheManager.shared.invalidateCache(keyPattern: "*status=HT*")

        // Trigger smart invalidation
        CacheManager.shared.triggerInvalidation(event: "live_data_updated")

        Logger.debug("Socket: Invalidated all live fixtures cache", category: .performance)
    }

    // MARK: - Background Task Integration

    /// Check if there are active subscriptions
    var hasActiveSubscriptions: Bool {
        return !subscribedFixtureIds.isEmpty || !subscribedLeagueIds.isEmpty || isSubscribedToLive
    }

    /// Refresh all active subscriptions (used by background tasks)
    func refreshAllSubscriptions() {
        guard isConnected else {
            Logger.warning("Cannot refresh subscriptions - socket not connected", category: .network)
            return
        }

        // Resubscribe to all fixture IDs
        for fixtureId in subscribedFixtureIds {
            subscribeToFixture(fixtureId: fixtureId)
        }

        // Resubscribe to all league IDs
        for leagueId in subscribedLeagueIds {
            subscribeToLeague(leagueId: leagueId)
        }

        // Resubscribe to live fixtures if needed
        if isSubscribedToLive {
            subscribeToLiveFixtures()
        }

        Logger.info("Refreshed \(subscribedFixtureIds.count) fixture subscriptions, \(subscribedLeagueIds.count) league subscriptions, live: \(isSubscribedToLive)", category: .network)
    }

    /// Refresh live subscriptions specifically (used by background tasks)
    func refreshLiveSubscriptions() {
        guard isConnected else {
            Logger.warning("Cannot refresh live subscriptions - socket not connected", category: .network)
            return
        }

        if isSubscribedToLive {
            // Emit a refresh request for live fixtures
            fixtureSocket?.emit("refresh-live")
            Logger.info("Requested live fixtures refresh", category: .network)
        }
    }

    /// Get subscription status for debugging
    var subscriptionStatus: String {
        return "Subscriptions - Fixtures: \(subscribedFixtureIds.count), Leagues: \(subscribedLeagueIds.count), Live: \(isSubscribedToLive)"
    }

    /// Check if a fixture was recently updated via socket
    /// - Parameters:
    ///   - fixtureId: The fixture ID to check
    ///   - withinSeconds: Time window to consider "recent" (default: 10 seconds)
    /// - Returns: True if the fixture was recently updated via socket
    func wasRecentlyUpdatedViaSocket(fixtureId: Int, withinSeconds: TimeInterval = 10) -> Bool {
        return socketUpdateQueue.sync {
            guard let lastUpdate = recentSocketUpdates[fixtureId] else { return false }
            return Date().timeIntervalSince(lastUpdate) < withinSeconds
        }
    }

    /// Track a socket update for a fixture
    internal func trackSocketUpdate(for fixtureId: Int) {
        socketUpdateQueue.async { [weak self] in
            self?.recentSocketUpdates[fixtureId] = Date()

            // Clean up old entries (older than 60 seconds)
            let cutoffTime = Date().addingTimeInterval(-60)
            self?.recentSocketUpdates = self?.recentSocketUpdates.filter { $0.value > cutoffTime } ?? [:]
        }
    }
}
