import Foundation
import Combine
import CryptoKit

/// Enhanced API service with improved security features
/// Provides request signing, token refresh, and security validation
@MainActor
class SecureAPIService: ObservableObject {
    static let shared = SecureAPIService()

    // MARK: - Properties

    private let baseURL: URL
    private let urlSession: URLSession
    private let decoder: JSONDecoder
    private let encoder: <PERSON><PERSON><PERSON>ncoder

    @Published var isAuthenticated = false
    @Published var authenticationError: String?

    // Token refresh handling
    private var refreshTask: Task<String, Error>?
    private let refreshQueue = DispatchQueue(label: "com.kickoffscore.token-refresh", qos: .userInitiated)

    // MARK: - Initialization

    private init() {
        // Set base URL from environment configuration
        self.baseURL = URL(string: SecureConfig.Environment.current.apiBaseURL)!

        // Configure URL session with security settings
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = Constants.requestTimeoutInterval
        config.timeoutIntervalForResource = Constants.requestTimeoutInterval * 2
        config.waitsForConnectivity = true
        config.networkServiceType = .responsiveData

        // Security headers
        var headers = [
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "KickoffScore/\(Constants.appVersion) (\(Constants.buildNumber))",
            "X-App-Version": Constants.appVersion,
            "X-Platform": "iOS"
        ]

        // Add API key if available
        if let apiKey = SecureConfig.getAPIKey() {
            headers["X-API-Key"] = apiKey
        }

        config.httpAdditionalHeaders = headers

        self.urlSession = URLSession(configuration: config)

        // Configure JSON handling
        self.decoder = JSONDecoder()
        self.encoder = JSONEncoder()

        // Set up date formatting
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)
        decoder.dateDecodingStrategy = .formatted(dateFormatter)
        encoder.dateEncodingStrategy = .formatted(dateFormatter)

        // Check initial authentication state
        updateAuthenticationState()

        Logger.info("SecureAPIService initialized", category: .security)
    }

    // MARK: - Authentication State Management

    private func updateAuthenticationState() {
        let hasToken = SecureConfig.getAuthToken() != nil
        DispatchQueue.main.async {
            self.isAuthenticated = hasToken
        }
    }

    // MARK: - Request Creation

    private func createSecureRequest(
        endpoint: String,
        method: HTTPMethod = .GET,
        parameters: [String: String]? = nil,
        requiresAuth: Bool = true
    ) async throws -> URLRequest {
        // Validate security environment
        guard SecureConfig.isSecureEnvironment() else {
            Logger.error("Insecure environment detected", category: .security)
            throw APIError.custom("Security validation failed")
        }

        // Build URL
        var urlComponents = URLComponents(url: baseURL.appendingPathComponent(endpoint), resolvingAgainstBaseURL: true)

        if let parameters = parameters {
            urlComponents?.queryItems = parameters.map { URLQueryItem(name: $0.key, value: $0.value) }
        }

        guard let url = urlComponents?.url else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue

        // Add authentication if required
        if requiresAuth {
            let token = try await getValidAuthToken()
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
        }

        // Add request signature for sensitive operations
        if method != .GET {
            try addRequestSignature(to: &request)
        }

        // Add security headers
        request.setValue(UUID().uuidString, forHTTPHeaderField: "X-Request-ID")
        request.setValue(String(Int(Date().timeIntervalSince1970)), forHTTPHeaderField: "X-Timestamp")

        Logger.debug("Created secure request: \(method.rawValue) \(url.absoluteString)", category: .security)

        return request
    }

    // MARK: - Token Management

    private func getValidAuthToken() async throws -> String {
        // Check if we have a valid token
        if let token = SecureConfig.getAuthToken(), !isTokenExpired(token) {
            return token
        }

        // Try to refresh the token
        return try await refreshAuthToken()
    }

    private func refreshAuthToken() async throws -> String {
        // Prevent multiple simultaneous refresh attempts
        if let existingTask = refreshTask {
            return try await existingTask.value
        }

        let task = Task<String, Error> {
            defer { refreshTask = nil }

            guard let refreshToken = SecureConfig.getRefreshToken() else {
                Logger.error("No refresh token available", category: .security)
                DispatchQueue.main.async {
                    self.isAuthenticated = false
                    self.authenticationError = "Authentication expired. Please log in again."
                }
                throw APIError.custom("No refresh token available")
            }

            // Create refresh request
            var request = URLRequest(url: baseURL.appendingPathComponent("/auth/refresh"))
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.setValue(refreshToken, forHTTPHeaderField: "x-refresh-token")

            // Perform refresh request
            let (data, response) = try await urlSession.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }

            guard httpResponse.statusCode == 200 else {
                Logger.error("Token refresh failed: \(httpResponse.statusCode)", category: .security)
                DispatchQueue.main.async {
                    self.isAuthenticated = false
                    self.authenticationError = "Authentication expired. Please log in again."
                }
                throw APIError.statusCode(httpResponse.statusCode)
            }

            // Parse response
            let authResponse = try decoder.decode(AuthResponse.self, from: data)

            // Save new tokens
            _ = SecureConfig.saveAuthToken(authResponse.token)

            Logger.info("Token refreshed successfully", category: .security)
            updateAuthenticationState()

            return authResponse.token
        }

        refreshTask = task
        return try await task.value
    }

    private func isTokenExpired(_ token: String) -> Bool {
        // Simple JWT expiration check
        // In a real implementation, you'd decode the JWT and check the exp claim
        // For now, we'll assume tokens are valid for a reasonable time
        return false
    }

    // MARK: - Request Signing

    private func addRequestSignature(to request: inout URLRequest) throws {
        // Create a signature for the request to prevent tampering
        let timestamp = request.value(forHTTPHeaderField: "X-Timestamp") ?? String(Int(Date().timeIntervalSince1970))
        let method = request.httpMethod ?? "GET"
        let path = request.url?.path ?? ""

        // Create signature payload
        var signaturePayload = "\(method)\n\(path)\n\(timestamp)"

        if let body = request.httpBody {
            let bodyHash = SHA256.hash(data: body)
            signaturePayload += "\n\(bodyHash.compactMap { String(format: "%02x", $0) }.joined())"
        }

        // Sign with app-specific key (in production, use a proper signing key)
        let signatureData = signaturePayload.data(using: .utf8) ?? Data()
        let signature = SHA256.hash(data: signatureData)
        let signatureString = signature.compactMap { String(format: "%02x", $0) }.joined()

        request.setValue(signatureString, forHTTPHeaderField: "X-Signature")

        Logger.debug("Added request signature", category: .security)
    }
}

// MARK: - HTTP Methods

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - Enhanced Error Handling

extension APIError {
    static let securityValidationFailed = APIError.custom("Security validation failed")
    static let tokenRefreshFailed = APIError.custom("Token refresh failed")
    static let requestSigningFailed = APIError.custom("Request signing failed")
}
