import Foundation
import UIKit

/// Modern API service using Swift's async/await pattern
@MainActor
public class AsyncAPIService {
    // Singleton instance
    public static let shared = AsyncAPIService()

    // Base URL for API requests
    private let baseURL: URL

    // URL session for network requests
    private let urlSession: URLSession

    // JSON decoder and encoder
    private let decoder: JSONDecoder
    private let encoder: JSONEncoder

    // MARK: - Initialization

    private init() {
        // Set base URL
        guard let url = URL(string: Constants.apiBaseURL) else {
            fatalError("Invalid API base URL")
        }
        self.baseURL = url

        // Configure URL session
        let config = URLSessionConfiguration.default

        // Load API key from secure storage
        guard let apiKey = SecureConfig.getAPIKey(), !apiKey.isEmpty else {
            fatalError("API key not found in secure storage")
        }

        // Set default headers
        config.httpAdditionalHeaders = [
            "Accept": "application/json",
            "Content-Type": "application/json",
            "X-API-Key": apiKey
        ]

        // Set timeout intervals
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60

        // Create URL session
        self.urlSession = URLSession(configuration: config)

        // Configure JSON decoder
        self.decoder = JSONDecoder()

        // Add custom date decoding strategy
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)
        decoder.dateDecodingStrategy = .custom { decoder in
            let container = try decoder.singleValueContainer()
            let dateString = try container.decode(String.self)

            return DateUtility.parseAPIDate(dateString) ?? Date()
        }

        // Configure JSON encoder
        self.encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
    }

    // MARK: - Authentication

    /// Check if the user is authenticated
    public var isAuthenticated: Bool {
        return SecureConfig.getAuthToken() != nil
    }

    // MARK: - API Methods

    /// Create a URLRequest for an API endpoint
    /// - Parameters:
    ///   - endpoint: The API endpoint path
    ///   - parameters: Optional query parameters
    /// - Returns: A configured URLRequest
    public func createRequest(
        endpoint: String,
        parameters: [String: String]? = nil
    ) throws -> URLRequest {
        // Construct URL with query parameters
        var urlComponents = URLComponents(url: baseURL.appendingPathComponent(endpoint), resolvingAgainstBaseURL: true)

        if let parameters = parameters {
            urlComponents?.queryItems = parameters.map { URLQueryItem(name: $0.key, value: $0.value) }
        }

        guard let url = urlComponents?.url else {
            throw APIError.invalidURL
        }

        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "GET"

        // Add auth token if available
        if let token = SecureConfig.getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
        }

        return request
    }

    /// Fetch raw data from an API endpoint
    /// - Parameters:
    ///   - endpoint: The API endpoint path
    ///   - parameters: Optional query parameters
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: A tuple containing the data and response
    public func fetchRawData(
        endpoint: String,
        parameters: [String: String]? = nil,
        forceRefresh: Bool = false
    ) async throws -> (Data, URLResponse) {
        // Create request
        let request = try createRequest(endpoint: endpoint, parameters: parameters)

        // Check network connectivity
        guard NetworkMonitor.shared.isNetworkAvailable() else {
            throw APIError.custom("No internet connection. Please check your network settings and try again.")
        }

        // Perform request
        return try await urlSession.data(for: request)
    }

    /// Fetch data from an API endpoint with caching
    /// - Parameters:
    ///   - endpoint: The API endpoint path
    ///   - parameters: Optional query parameters
    ///   - forceRefresh: Whether to force a refresh from the network
    ///   - cacheDuration: How long to cache the response (in seconds)
    /// - Returns: Decoded data of the specified type
    public func fetchData<T: Decodable>(
        endpoint: String,
        parameters: [String: String]? = nil,
        forceRefresh: Bool = false,
        cacheDuration: TimeInterval? = nil
    ) async throws -> T {
        // Construct URL with query parameters
        var urlComponents = URLComponents(url: baseURL.appendingPathComponent(endpoint), resolvingAgainstBaseURL: true)

        if let parameters = parameters {
            urlComponents?.queryItems = parameters.map { URLQueryItem(name: $0.key, value: $0.value) }
        }

        guard let url = urlComponents?.url else {
            throw APIError.invalidURL
        }

        // Generate cache key
        let cacheKey = CacheManager.shared.generateCacheKey(endpoint: endpoint, parameters: parameters)

        // Check cache if not forcing refresh
        if !forceRefresh, let cachedData = CacheManager.shared.getData(forKey: cacheKey) {
            do {
                let decodedData = try decoder.decode(T.self, from: cachedData)
                Logger.info("Using cached data for: \(url.absoluteString)", category: .network)
                return decodedData
            } catch {
                Logger.warning("Error decoding cached data: \(error). Fetching fresh data.", category: .network)
                // Continue with network request if cache decoding fails
            }
        }

        // Check network connectivity
        guard NetworkMonitor.shared.isNetworkAvailable() else {
            throw APIError.custom("No internet connection. Please check your network settings and try again.")
        }

        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "GET"

        // Add auth token if available
        if let token = SecureConfig.getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
        }

        Logger.debug("Request: GET \(url.absoluteString)", category: .network)

        // Track request performance
        let requestStartTime = CFAbsoluteTimeGetCurrent()

        do {
            // Perform request
            let (data, response) = try await urlSession.data(for: request)

            // Calculate request duration
            let requestDuration = CFAbsoluteTimeGetCurrent() - requestStartTime

            // Validate response
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }

            let level: Logger.Level = (200...299).contains(httpResponse.statusCode) ? .info : .error
            Logger.log("Response: [\(httpResponse.statusCode)] \(url.absoluteString)", level: level, category: .network)

            // Check status code
            guard (200...299).contains(httpResponse.statusCode) else {
                // Try to decode error message
                if let errorResponse = try? decoder.decode(APIErrorResponse.self, from: data) {
                    throw APIError.custom("API Error (\(httpResponse.statusCode)): \(errorResponse.message ?? "Unknown error")")
                } else {
                    throw APIError.statusCode(httpResponse.statusCode)
                }
            }

            // Decode data
            do {
                // Log raw JSON for debugging (only in DEBUG builds and truncated)
                #if DEBUG
                if let jsonString = String(data: data, encoding: .utf8) {
                    Logger.logJSON(jsonString, category: .network)
                }
                #endif

                let decodedData = try decoder.decode(T.self, from: data)
                Logger.info("Successfully decoded data of type \(T.self)", category: .network)

                // Determine content type for smart caching
                let contentType = determineContentType(from: endpoint, parameters: parameters)

                // Cache the successful response with content type
                CacheManager.shared.saveData(data, forKey: cacheKey, contentType: contentType, duration: cacheDuration)

                // Log performance metrics
                Logger.logPerformance(operation: "API Request: \(endpoint)", duration: requestDuration)

                return decodedData
            } catch {
                // Handle decoding errors with detailed information
                Logger.error("Decoding Error for URL \(url.absoluteString): \(error)", category: .network)

                #if DEBUG
                if let jsonString = String(data: data, encoding: .utf8) {
                    Logger.logJSON(jsonString, category: .network)
                }
                #endif

                var detailedError = "Decoding Error: \(error.localizedDescription)"

                if let decodingErr = error as? DecodingError {
                    switch decodingErr {
                    case .typeMismatch(let type, let context):
                        detailedError += "\nType mismatch for type \(type) at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                    case .valueNotFound(let type, let context):
                        detailedError += "\nValue not found for type \(type) at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                    case .keyNotFound(let key, let context):
                        detailedError += "\nKey '\(key.stringValue)' not found at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                    case .dataCorrupted(let context):
                        detailedError += "\nData corrupted at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                    @unknown default:
                        detailedError += "\nUnknown decoding error"
                    }
                }

                Logger.error("Detailed decoding error: \(detailedError)", category: .network)
                throw APIError.custom(detailedError)
            }
        } catch {
            // Check if we have cached data to fall back to
            if let cachedData = CacheManager.shared.getData(forKey: cacheKey) {
                do {
                    let decodedData = try decoder.decode(T.self, from: cachedData)
                    Logger.info("Network request failed, using cached data for: \(url.absoluteString)", category: .network)
                    return decodedData
                } catch {
                    Logger.warning("Error decoding cached fallback data: \(error)", category: .network)
                    // Continue with throwing the original error
                }
            }

            // No cached data or couldn't decode it, throw the original error
            if let apiError = error as? APIError {
                throw apiError
            } else {
                throw APIError.requestFailed(error)
            }
        }
    }

    /// Post data to an API endpoint
    /// - Parameters:
    ///   - endpoint: The API endpoint path
    ///   - body: The data to send in the request body
    /// - Returns: Decoded response data of the specified type
    public func postData<T: Encodable, U: Decodable>(endpoint: String, body: T) async throws -> U {
        // Construct URL
        guard let url = URL(string: baseURL.absoluteString + endpoint) else {
            throw APIError.invalidURL
        }

        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add auth token if available
        if let token = SecureConfig.getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
        }

        // Encode request body
        do {
            request.httpBody = try encoder.encode(body)
        } catch {
            throw APIError.custom("Failed to encode request body: \(error.localizedDescription)")
        }

        Logger.debug("Request: POST \(url.absoluteString)", category: .network)

        do {
            // Perform request
            let (data, response) = try await urlSession.data(for: request)

            // Validate response
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }

            let level: Logger.Level = (200...299).contains(httpResponse.statusCode) ? .info : .error
            Logger.log("Response: [\(httpResponse.statusCode)] \(url.absoluteString)", level: level, category: .network)

            // Check status code
            guard (200...299).contains(httpResponse.statusCode) else {
                // Try to decode error message
                if let errorResponse = try? decoder.decode(APIErrorResponse.self, from: data) {
                    throw APIError.custom("API Error (\(httpResponse.statusCode)): \(errorResponse.message ?? "Unknown error")")
                } else {
                    throw APIError.statusCode(httpResponse.statusCode)
                }
            }

            // Decode response
            do {
                let decodedData = try decoder.decode(U.self, from: data)
                return decodedData
            } catch {
                throw APIError.decodingError(error)
            }
        } catch {
            if let apiError = error as? APIError {
                throw apiError
            } else {
                throw APIError.requestFailed(error)
            }
        }
    }

    /// Upload multipart form data to an API endpoint
    /// - Parameters:
    ///   - endpoint: The API endpoint path
    ///   - formData: The multipart form data to upload
    /// - Returns: Decoded response data of the specified type
    public func uploadData<T: Decodable>(endpoint: String, formData: MultipartFormData) async throws -> T {
        // Construct URL
        guard let url = URL(string: baseURL.absoluteString + endpoint) else {
            throw APIError.invalidURL
        }

        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(formData.contentType, forHTTPHeaderField: "Content-Type")

        // Add auth token if available
        if let token = SecureConfig.getAuthToken() {
            request.setValue(token, forHTTPHeaderField: "x-auth-token")
        }

        // Set request body
        request.httpBody = formData.finalize()

        Logger.debug("Request: POST (multipart) \(url.absoluteString)", category: .network)

        // Check network connectivity
        guard NetworkMonitor.shared.isNetworkAvailable() else {
            throw APIError.custom("No internet connection. Please check your network settings and try again.")
        }

        do {
            // Perform request
            let (data, response) = try await urlSession.data(for: request)

            // Validate response
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }

            let level: Logger.Level = (200...299).contains(httpResponse.statusCode) ? .info : .error
            Logger.log("Response: [\(httpResponse.statusCode)] \(url.absoluteString)", level: level, category: .network)

            // Check status code
            guard (200...299).contains(httpResponse.statusCode) else {
                // Try to decode error message
                if let errorResponse = try? decoder.decode(APIErrorResponse.self, from: data) {
                    throw APIError.custom("API Error (\(httpResponse.statusCode)): \(errorResponse.message ?? "Unknown error")")
                } else {
                    throw APIError.statusCode(httpResponse.statusCode)
                }
            }

            // Decode response
            do {
                let decodedData = try decoder.decode(T.self, from: data)
                return decodedData
            } catch {
                throw APIError.decodingError(error)
            }
        } catch {
            if let apiError = error as? APIError {
                throw apiError
            } else {
                throw APIError.requestFailed(error)
            }
        }
    }

    // MARK: - Helper Methods

    /// Determine content type based on endpoint and parameters for smart caching
    /// - Parameters:
    ///   - endpoint: The API endpoint
    ///   - parameters: The request parameters
    /// - Returns: The appropriate cache content type
    private func determineContentType(from endpoint: String, parameters: [String: String]?) -> CacheContentType {
        switch endpoint {
        case let ep where ep.contains("/fixtures"):
            if ep.contains("/statistics") {
                return .statistics
            }
            return .fixture
        case let ep where ep.contains("/standings"):
            return .standings
        case let ep where ep.contains("/teams"):
            return .team
        case let ep where ep.contains("/leagues"):
            return .league
        case let ep where ep.contains("/players"):
            return .player
        case let ep where ep.contains("/odds"):
            return .odds
        default:
            // Default to fixture for unknown endpoints
            return .fixture
        }
    }

    // MARK: - Helper Types

    /// Helper struct to decode error messages from the API
    private struct APIErrorResponse: Decodable {
        let message: String?
    }
}
