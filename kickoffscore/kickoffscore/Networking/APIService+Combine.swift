import Foundation
import Combine

// MARK: - Combine Extensions for APIService

extension APIService {
    /// Fetch data from an endpoint and return a publisher
    /// - Parameters:
    ///   - endpoint: The specific endpoint path (e.g., "/fixtures")
    ///   - parameters: Optional query parameters [String: String]
    /// - Returns: A publisher that emits the decoded data or an error
    func fetchDataPublisher<T: Decodable>(endpoint: String, parameters: [String: String]? = nil) -> AnyPublisher<T, APIError> {
        return Future<T, APIError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.custom("APIService instance was deallocated")))
                return
            }

            self.fetchData(endpoint: endpoint, parameters: parameters) { (result: Result<T, APIError>) in
                promise(result)
            }
        }
        .eraseToAnyPublisher()
    }

    /// Post data to an endpoint and return a publisher
    /// - Parameters:
    ///   - endpoint: The specific endpoint path (e.g., "/fixtures/votes/123")
    ///   - body: The data to send in the request body
    /// - Returns: A publisher that emits the decoded data or an error
    func postDataPublisher<T: Encodable, U: Decodable>(endpoint: String, body: T) -> AnyPublisher<U, APIError> {
        return Future<U, APIError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.custom("APIService instance was deallocated")))
                return
            }

            self.postData(endpoint: endpoint, body: body) { (result: Result<U, APIError>) in
                promise(result)
            }
        }
        .eraseToAnyPublisher()
    }

    /// Put data to an endpoint and return a publisher
    /// - Parameters:
    ///   - endpoint: The specific endpoint path
    ///   - body: The data to send in the request body
    /// - Returns: A publisher that emits the decoded data or an error
    func putDataPublisher<T: Encodable, U: Decodable>(endpoint: String, body: T) -> AnyPublisher<U, APIError> {
        return Future<U, APIError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.custom("APIService instance was deallocated")))
                return
            }

            self.putData(endpoint: endpoint, body: body) { (result: Result<U, APIError>) in
                promise(result)
            }
        }
        .eraseToAnyPublisher()
    }

    /// Delete data from an endpoint and return a publisher
    /// - Parameter endpoint: The specific endpoint path
    /// - Returns: A publisher that emits the decoded data or an error
    func deleteDataPublisher<T: Decodable>(endpoint: String) -> AnyPublisher<T, APIError> {
        return Future<T, APIError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.custom("APIService instance was deallocated")))
                return
            }

            self.deleteData(endpoint: endpoint) { (result: Result<T, APIError>) in
                promise(result)
            }
        }
        .eraseToAnyPublisher()
    }

    /// Update user favorites and return a publisher
    /// - Parameter parameters: Dictionary containing arrays of favorite IDs (teams, players, leagues)
    /// - Returns: A publisher that emits a success boolean or an error
    func updateUserFavoritesPublisher(parameters: [String: Any]) -> AnyPublisher<Bool, APIError> {
        return Future<Bool, APIError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.custom("APIService instance was deallocated")))
                return
            }

            self.updateUserFavorites(parameters: parameters) { result in
                promise(result)
            }
        }
        .eraseToAnyPublisher()
    }
}
