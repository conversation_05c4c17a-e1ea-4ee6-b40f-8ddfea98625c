import Foundation

/// Utility for determining competition types (league vs cup/tournament)
struct CompetitionTypeUtils {
    
    /// Determines if a league is a cup competition (tournament) or a regular league
    /// - Parameter leagueId: The ID of the league to check
    /// - Returns: True if it's a cup competition, false if it's a regular league
    static func isCupCompetition(leagueId: Int) -> <PERSON><PERSON> {
        // Check league type from API data first (if available)
        // This would require access to the league data, but for now we'll use the hardcoded list
        
        // Cup competition league IDs (tournaments, cups, continental competitions)
        let cupCompetitionLeagueIds = [
            // FIFA Competitions
            1,    // World Cup
            8,    // World Cup
            15,   // FIFA Club World Cup
            
            // UEFA Competitions
            2,    // UEFA Champions League
            3,    // UEFA Europa League
            848,  // UEFA Europa Conference League
            4,    // European Championship (UEFA Euro)
            5,    // UEFA Nations League
            
            // CONMEBOL Competitions
            9,    // Copa America
            13,   // Copa Libertadores
            11,   // Copa Sudamericana
            
            // CAF Competitions
            6,    // Africa Cup of Nations
            12,   // CAF Champions League
            
            // AFC Competitions
            7,    // AFC Asian Cup
            17,   // AFC Champions League Elite
            1132, // AFC Champions League Two
            
            // CONCACAF Competitions
            22,   // CONCACAF Gold Cup
            856,  // CONCACAF Champions Cup
            
            // Domestic Cup Competitions
            45,   // FA Cup (England)
            48,   // League Cup/Carabao Cup (England)
            66,   // Coupe de France (France)
            81,   // DFB Pokal (Germany)
            143,  // Copa del Rey (Spain)
            137,  // Coppa Italia (Italy)
            73,   // Copa do Brasil (Brazil)
            130,  // Copa Argentina (Argentina)
            220,  // Austrian Cup (Austria)
            246,  // Finland Cup (Suomen Cup)
            514,  // Algeria Cup (Coupe Nationale)
            709,  // Armenian Cup
            420,  // Azerbaidjan Cup
            1049, // Bahrain King's Cup
            657,  // Estonian Cup
            335,  // Ukrainian Cup
            1113, // Copa Venezuela
            
            // World Cup Qualifications
            29,   // WC Qualification Africa (CAF)
            30,   // WC Qualification Asia (AFC)
            31,   // WC Qualification Concacaf
            32,   // WC Qualification Europe (UEFA)
            33,   // WC Qualification Oceania
            34,   // WC Qualification South America (CONMEBOL)
            36,   // Africa Cup of Nations Qualifications
            
            // Other Continental/International Competitions
            480,  // Olympic Games (Men)
            524,  // Olympic Games (Women)
            525,  // UEFA Champions League Women
            38,   // UEFA U21 Championship
            850,  // UEFA U21 Championship - Qualification
            743,  // UEFA Championship - Women
            
            // Play-offs and Special Competitions
            40,   // Championship Play-Offs (included in Championship but treated as cup format)
            179,  // Premiership Play-Offs (Scotland)
        ]
        
        return cupCompetitionLeagueIds.contains(leagueId)
    }
    
    /// Determines if a league is a regular league competition
    /// - Parameter leagueId: The ID of the league to check
    /// - Returns: True if it's a regular league, false if it's a cup competition
    static func isLeagueCompetition(leagueId: Int) -> Bool {
        return !isCupCompetition(leagueId: leagueId)
    }
    
    /// Gets the competition type as a string
    /// - Parameter leagueId: The ID of the league to check
    /// - Returns: "Cup" for cup competitions, "League" for regular leagues
    static func getCompetitionType(leagueId: Int) -> String {
        return isCupCompetition(leagueId: leagueId) ? "Cup" : "League"
    }

    // MARK: - Knockout Stage Detection

    /// Determines if a round name represents a knockout stage
    /// - Parameter roundName: The name of the round to check
    /// - Returns: True if it's a knockout round, false otherwise
    static func isKnockoutRound(roundName: String) -> Bool {
        let lowercaseRound = roundName.lowercased()

        // Specific knockout round patterns we want to include
        // Based on API documentation: 12: "Knockout Round Play-offs", 13: "Round of 16", 14: "Quarter-finals", 15: "Semi-finals", 16: "Final"
        let knockoutPatterns = [
            "final",
            "semi-final", "semifinal", "semi final",
            "quarter-final", "quarterfinal", "quarter final",
            "round of 16", "round of sixteen", "16th final",
            "round of 32", "32nd final",
            "round of 64", "64th final",
            "8th final", "round of 8",
            "4th final", "round of 4",
            "3rd place", "third place",
            "knockout round play-offs" // Only include specific knockout round play-offs, not generic play-offs
        ]

        // Check for exact matches first for specific cases
        if lowercaseRound == "knockout round play-offs" {
            return true
        }

        // Exclude generic play-offs that are not the specific "Knockout Round Play-offs"
        if (lowercaseRound.contains("play-off") || lowercaseRound.contains("playoff")) &&
           !lowercaseRound.contains("knockout round") {
            return false
        }

        return knockoutPatterns.contains { pattern in
            lowercaseRound.contains(pattern)
        }
    }

    /// Determines if a competition has knockout stages based on available rounds
    /// - Parameter rounds: Array of round names for the competition
    /// - Returns: True if the competition has knockout stages
    static func hasKnockoutStages(rounds: [String]) -> Bool {
        return rounds.contains { roundName in
            isKnockoutRound(roundName: roundName)
        }
    }

    /// Gets knockout rounds from a list of rounds, sorted by tournament progression
    /// - Parameter rounds: Array of round names
    /// - Returns: Array of knockout round names sorted from earliest to latest stage
    static func getKnockoutRounds(from rounds: [String]) -> [String] {
        let knockoutRounds = rounds.filter { isKnockoutRound(roundName: $0) }

        // Sort knockout rounds by tournament progression (earliest to latest)
        return knockoutRounds.sorted { round1, round2 in
            let priority1 = getKnockoutRoundPriority(roundName: round1)
            let priority2 = getKnockoutRoundPriority(roundName: round2)
            return priority1 < priority2
        }
    }

    /// Gets the priority order for knockout rounds (lower number = earlier stage)
    /// - Parameter roundName: The name of the knockout round
    /// - Returns: Priority number for sorting (lower = earlier in tournament)
    static func getKnockoutRoundPriority(roundName: String) -> Int {
        let lowercaseRound = roundName.lowercased()

        // Priority order: earlier stages have lower numbers
        // Based on API documentation: 12: "Knockout Round Play-offs", 13: "Round of 16", 14: "Quarter-finals", 15: "Semi-finals", 16: "Final"
        if lowercaseRound.contains("knockout round play-offs") {
            return 1
        } else if lowercaseRound.contains("round of 64") || lowercaseRound.contains("64th final") {
            return 2
        } else if lowercaseRound.contains("round of 32") || lowercaseRound.contains("32nd final") {
            return 3
        } else if lowercaseRound.contains("8th final") || lowercaseRound.contains("round of 16") || lowercaseRound.contains("round of sixteen") || lowercaseRound.contains("16th final") {
            return 4
        } else if lowercaseRound.contains("quarter") {
            return 5
        } else if lowercaseRound.contains("semi") {
            return 6
        } else if lowercaseRound.contains("3rd place") || lowercaseRound.contains("third place") {
            return 7
        } else if lowercaseRound.contains("final") && !lowercaseRound.contains("semi") && !lowercaseRound.contains("quarter") && !lowercaseRound.contains("8th") && !lowercaseRound.contains("knockout") {
            return 8
        }

        // Default priority for unrecognized knockout rounds
        return 10
    }

    /// Formats knockout round names for display
    /// - Parameter roundName: The original round name
    /// - Returns: Formatted round name for UI display
    static func formatKnockoutRoundName(_ roundName: String) -> String {
        let lowercaseRound = roundName.lowercased()

        // Standardize common round names based on API documentation
        // API returns: 12: "Knockout Round Play-offs", 13: "Round of 16", 14: "Quarter-finals", 15: "Semi-finals", 16: "Final"
        // We want: "Playoff round", "Round of 16", "Quarterfinals", "Semifinals", "Final"

        if lowercaseRound.contains("knockout round play-offs") {
            return "Playoff round"
        } else if lowercaseRound.contains("8th final") || lowercaseRound.contains("round of 16") || lowercaseRound.contains("16th final") {
            return "Round of 16"
        } else if lowercaseRound.contains("round of 32") || lowercaseRound.contains("32nd final") {
            return "Round of 32"
        } else if lowercaseRound.contains("quarter") {
            return "Quarterfinals"
        } else if lowercaseRound.contains("semi") {
            return "Semifinals"
        } else if lowercaseRound.contains("final") && !lowercaseRound.contains("semi") && !lowercaseRound.contains("quarter") && !lowercaseRound.contains("8th") && !lowercaseRound.contains("knockout") {
            return "Final"
        } else if lowercaseRound.contains("3rd place") || lowercaseRound.contains("third place") {
            return "3rd Place"
        }

        // Return original name if no standard format found
        return roundName
    }
}

// MARK: - LeagueData Extension
extension LeagueData {
    /// Convenience property to check if this league is a cup competition
    var isCupCompetition: Bool {
        return CompetitionTypeUtils.isCupCompetition(leagueId: self.id)
    }
    
    /// Convenience property to check if this league is a regular league
    var isLeagueCompetition: Bool {
        return CompetitionTypeUtils.isLeagueCompetition(leagueId: self.id)
    }
    
    /// Convenience property to get the competition type
    var competitionType: String {
        return CompetitionTypeUtils.getCompetitionType(leagueId: self.id)
    }
}
