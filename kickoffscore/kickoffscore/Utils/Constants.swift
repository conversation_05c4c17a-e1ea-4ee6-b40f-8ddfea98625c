import Foundation

public struct Constants {
    static let apiBaseURL = "https://api.kickoffpredictions.com/api"

    // SECURITY: API key is now managed through SecureConfig and environment variables
    // No longer hardcoded here for security reasons

    // Cache configuration
    static let defaultCacheDuration: TimeInterval = 300 // 5 minutes
    static let liveCacheDuration: TimeInterval = 15 // 15 seconds for live data

    // Network configuration
    static let requestTimeoutInterval: TimeInterval = 30
    static let maxRetryAttempts = 3

    // App configuration
    static let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    static let buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
}
