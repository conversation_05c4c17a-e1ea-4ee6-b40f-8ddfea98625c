//
//  AppDelegate.swift
//  kickoffscore
//
//  Created by <PERSON><PERSON><PERSON> on 22/04/2025.
//

import UIKit
import BackgroundTasks
import UserNotifications
import SwiftUI
import Kingfisher

class AppDelegate: NSObject, UIApplicationDelegate, UNUserNotificationCenterDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> <PERSON><PERSON> {
        // Configure background task scheduling first
        configureBackgroundTasks()

        // Initialize socket connection
        _ = SocketManager.shared

        // Configure push notifications
        configurePushNotifications()

        // Set up memory pressure handling
        configureMemoryPressureHandling()

        // Clean up expired odds cache
        OddsCache.shared.clearExpiredCache()

        return true
    }

    func applicationWillTerminate(_ application: UIApplication) {
        // Clean up resources when app is terminated
        SocketManager.shared.disconnect()

        // Clean up expired odds cache
        OddsCache.shared.clearExpiredCache()
    }

    // MARK: - Push Notifications

    private func configurePushNotifications() {
        // Request permission for push notifications
        UNUserNotificationCenter.current().delegate = self

        let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
        UNUserNotificationCenter.current().requestAuthorization(options: authOptions) { granted, error in
            if granted {
                print("Push notification permission granted")

                // Register for remote notifications on the main thread
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            } else if let error = error {
                print("Push notification permission denied: \(error.localizedDescription)")
            } else {
                print("Push notification permission denied")
            }
        }
    }

    // Called when the app successfully registers for remote notifications
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        // Convert device token to string
        let tokenParts = deviceToken.map { data in String(format: "%02.2hhx", data) }
        let token = tokenParts.joined()
        print("Device token received: \(token)")

        // Post notification so other parts of the app can use the token
        NotificationCenter.default.post(name: Notification.Name("DeviceTokenReceived"), object: token)

        // Also directly set the token in NotificationService
        NotificationService.shared.setDeviceToken(token)
    }

    // Called when the app fails to register for remote notifications
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("Failed to register for remote notifications: \(error.localizedDescription)")
    }

    // Called when a notification is received while the app is in the foreground
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // Show the notification even when the app is in the foreground
        completionHandler([.banner, .sound, .badge])
    }

    // Called when a user taps on a notification
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo

        // Handle the notification tap
        if let fixtureId = userInfo["fixtureId"] as? Int {
            print("User tapped on notification for fixture ID: \(fixtureId)")
            // TODO: Navigate to the fixture detail view
        }

        completionHandler()
    }

    // MARK: - Background Tasks

    private func configureBackgroundTasks() {
        // Initialize and register background tasks with the new system
        let backgroundTaskManager = BackgroundTaskManager.shared
        backgroundTaskManager.registerBackgroundTasks()

        Logger.info("Background tasks configured with BackgroundTaskManager", category: .background)
    }

    private func scheduleBackgroundSocketRefresh() {
        // Use the new background task manager for scheduling
        let backgroundTaskManager = BackgroundTaskManager.shared
        backgroundTaskManager.scheduleSocketRefresh()

        Logger.info("Background socket refresh scheduled", category: .background)
    }

    func application(_ application: UIApplication, handleEventsForBackgroundURLSession identifier: String, completionHandler: @escaping () -> Void) {
        // Handle background URL session events
        completionHandler()
    }

    // MARK: - App State Transitions

    func applicationDidEnterBackground(_ application: UIApplication) {
        // Clean up expired odds cache when entering background
        OddsCache.shared.clearExpiredCache()
    }

    func applicationWillEnterForeground(_ application: UIApplication) {
        // Clean up expired odds cache when returning to foreground
        OddsCache.shared.clearExpiredCache()
    }

    // MARK: - Memory Pressure Handling

    private func configureMemoryPressureHandling() {
        // Handle memory warnings to clear image caches
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { _ in
            Logger.warning("Memory warning received - clearing image caches", category: .performance)

            // Clear Kingfisher memory cache but keep disk cache
            KingfisherManager.shared.cache.clearMemoryCache()

            // Clear other caches if needed
            CacheManager.shared.clearCache()
            OddsCache.shared.clearExpiredCache()
        }
    }
}
