//
//  kickoffscoreApp.swift
//  kickoffscore
//
//  Created by <PERSON><PERSON><PERSON> on 22/04/2025.
//

import SwiftUI
import BackgroundTasks
import UIKit
import <PERSON><PERSON><PERSON>

@main
struct kickoffscoreApp: App {
    // State object for app-wide theme management
    @StateObject private var themeManager = ThemeManager()

    // App delegate for handling system events
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    init() {
        // Load environment configuration first
        EnvironmentConfig.loadConfiguration()

        // Initialize secure configuration
        SecureConfig.initialize()

        // Validate configuration
        do {
            try EnvironmentConfig.validateConfiguration()
        } catch {
            Logger.error("Configuration validation failed: \(error)", category: .security)
            // In production, you might want to show an error screen
        }

        // Configure Kingfisher for optimal image caching
        configureImageCaching()

        // Configure appearance defaults
        configureAppAppearance()

        // Register background tasks
        registerBackgroundTasks()

        // Print configuration in debug mode
        #if DEBUG
        EnvironmentConfig.printConfiguration()
        #endif

        Logger.info("KickoffScore app initialized successfully", category: .general)
    }

    /// Register background tasks with the system
    private func registerBackgroundTasks() {
        // Initialize the background task manager
        let backgroundTaskManager = BackgroundTaskManager.shared
        backgroundTaskManager.registerBackgroundTasks()

        Logger.info("Background tasks registered via BackgroundTaskManager", category: .background)
    }

    /// Configure Kingfisher for optimal image caching across the app
    private func configureImageCaching() {
        let cache = KingfisherManager.shared.cache

        // Configure disk cache settings
        cache.diskStorage.config.sizeLimit = 100 * 1024 * 1024 // 100 MB disk cache
        cache.diskStorage.config.expiration = .days(7) // Keep images for 7 days

        // Configure memory cache settings
        cache.memoryStorage.config.totalCostLimit = 50 * 1024 * 1024 // 50 MB memory cache
        cache.memoryStorage.config.expiration = .seconds(300) // 5 minutes in memory

        // Configure downloader settings
        let downloader = KingfisherManager.shared.downloader
        downloader.downloadTimeout = 30.0 // 30 second timeout

        Logger.info("Kingfisher image caching configured - Disk: 100MB (7 days), Memory: 50MB (5 min)", category: .performance)
    }

    private func configureAppAppearance() {
        // Configure navigation bar appearance
        let navigationBarAppearance = UINavigationBarAppearance()
        navigationBarAppearance.configureWithTransparentBackground()
        navigationBarAppearance.backgroundColor = UIColor.clear
        navigationBarAppearance.shadowColor = .clear

        // Apply the appearance settings
        UINavigationBar.appearance().standardAppearance = navigationBarAppearance
        UINavigationBar.appearance().compactAppearance = navigationBarAppearance
        UINavigationBar.appearance().scrollEdgeAppearance = navigationBarAppearance
    }

    var body: some Scene {
        WindowGroup {
            AppLaunchController()
                .environmentObject(themeManager) // Provide theme manager to the view hierarchy
        }
    }
}
