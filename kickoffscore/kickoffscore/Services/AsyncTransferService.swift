import Foundation

@MainActor
class AsyncTransferService: ObservableObject {
    static let shared = AsyncTransferService()
    
    private let apiService = AsyncAPIService.shared
    
    private init() {}
    
    /// Fetch transfers for a specific league
    /// - Parameters:
    ///   - leagueId: The ID of the league to fetch transfers for
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of Transfer objects
    func fetchLeagueTransfers(leagueId: Int, forceRefresh: Bool = false) async throws -> [Transfer] {
        let parameters = ["league": String(leagueId)]
        
        let transfers: [Transfer] = try await apiService.fetchData(
            endpoint: "/transfers/leagues",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )
        
        return transfers
    }
    
    /// Fetch transfers for a specific team
    /// - Parameters:
    ///   - teamId: The ID of the team to fetch transfers for
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of Transfer objects
    func fetchTeamTransfers(teamId: Int, forceRefresh: Bool = false) async throws -> [Transfer] {
        let parameters = ["team": String(teamId)]
        
        let transfers: [Transfer] = try await apiService.fetchData(
            endpoint: "/transfers/teams",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )
        
        return transfers
    }
    
    /// Fetch transfers for a specific player
    /// - Parameters:
    ///   - playerId: The ID of the player to fetch transfers for
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of Transfer objects
    func fetchPlayerTransfers(playerId: Int, forceRefresh: Bool = false) async throws -> [Transfer] {
        let parameters = ["player": String(playerId)]
        
        let transfers: [Transfer] = try await apiService.fetchData(
            endpoint: "/transfers/players",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )
        
        return transfers
    }
}

// MARK: - Transfer Service Extensions

extension AsyncTransferService {
    /// Get all recent transfers from a list of transfers, flattened and sorted by date
    /// - Parameter transfers: Array of Transfer objects
    /// - Returns: Array of TransferInfo objects sorted by most recent first
    func getRecentTransfers(from transfers: [Transfer]) -> [TransferInfo] {
        let allTransfers = transfers.flatMap { $0.transfers }

        // Remove duplicates based on ID
        var uniqueTransfers: [TransferInfo] = []
        var seenIds: Set<String> = []

        for transfer in allTransfers {
            if !seenIds.contains(transfer.id) {
                seenIds.insert(transfer.id)
                uniqueTransfers.append(transfer)
            }
        }

        return uniqueTransfers.sorted { first, second in
            guard let firstDate = first.formattedDate,
                  let secondDate = second.formattedDate else {
                return false
            }
            return firstDate > secondDate
        }
    }
    
    /// Filter transfers by type (loan, permanent, etc.)
    /// - Parameters:
    ///   - transfers: Array of Transfer objects
    ///   - type: Transfer type to filter by
    /// - Returns: Filtered array of Transfer objects
    func filterTransfers(_ transfers: [Transfer], byType type: String) -> [Transfer] {
        return transfers.compactMap { transfer in
            let filteredTransferInfos = transfer.transfers.filter { transferInfo in
                transferInfo.type?.lowercased().contains(type.lowercased()) == true
            }
            
            if filteredTransferInfos.isEmpty {
                return nil
            }
            
            return Transfer(
                player: transfer.player,
                update: transfer.update,
                _transfers: filteredTransferInfos
            )
        }
    }
    
    /// Get transfers involving a specific team (either incoming or outgoing)
    /// - Parameters:
    ///   - transfers: Array of Transfer objects
    ///   - teamId: ID of the team to filter by
    /// - Returns: Filtered array of Transfer objects
    func filterTransfers(_ transfers: [Transfer], involvingTeam teamId: Int) -> [Transfer] {
        return transfers.compactMap { transfer in
            let filteredTransferInfos = transfer.transfers.filter { transferInfo in
                transferInfo.teams.in.id == teamId || transferInfo.teams.out.id == teamId
            }
            
            if filteredTransferInfos.isEmpty {
                return nil
            }
            
            return Transfer(
                player: transfer.player,
                update: transfer.update,
                _transfers: filteredTransferInfos
            )
        }
    }
}
