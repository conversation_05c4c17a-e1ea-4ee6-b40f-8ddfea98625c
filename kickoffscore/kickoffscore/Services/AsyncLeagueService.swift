import Foundation
import Combine

@MainActor
class AsyncLeagueService {
    // Singleton instance
    static let shared = AsyncLeagueService()

    // API service for network requests
    private let apiService = AsyncAPIService.shared

    // Private initializer for singleton
    private init() {}

    /// Fetch all leagues with tier information
    /// - Parameter forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of LeagueData objects
    func fetchLeagues(forceRefresh: Bool = false) async throws -> [LeagueData] {
        // Fetch leagues from API
        let leagues: [LeagueData] = try await apiService.fetchData(
            endpoint: "/leagues",
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )

        // Filter leagues to only include those in leagueTiers.ts
        // In a real implementation, we would check against the backend
        // For now, we'll just return all leagues
        return leagues
    }

    /// Fetch leagues with specific ID
    /// - Parameters:
    ///   - leagueId: League ID to filter by
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of LeagueData objects
    func fetchLeagues(leagueId: Int, forceRefresh: Bool = false) async throws -> [LeagueData] {
        // Set parameters for league ID filter
        let parameters = ["id": String(leagueId)]

        // Fetch leagues from API
        let leagues: [LeagueData] = try await apiService.fetchData(
            endpoint: "/leagues",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )

        return leagues
    }

    /// Fetch leagues by country
    /// - Parameters:
    ///   - country: Country name to filter by
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of LeagueData objects for the specified country
    func fetchLeaguesByCountry(country: String, forceRefresh: Bool = false) async throws -> [LeagueData] {
        // Set parameters for country filter
        let parameters = ["country": country]

        // Fetch leagues from API
        let leagues: [LeagueData] = try await apiService.fetchData(
            endpoint: "/leagues",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )

        return leagues
    }

    /// Fetch a specific league by ID
    /// - Parameters:
    ///   - leagueId: The ID of the league to fetch
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: LeagueData object for the specified league ID
    func fetchLeagueById(leagueId: Int, forceRefresh: Bool = false) async throws -> LeagueData? {
        // Set parameters for league ID filter
        let parameters = ["id": String(leagueId)]

        // Fetch league from API
        let leagues: [LeagueData] = try await apiService.fetchData(
            endpoint: "/leagues",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )

        // Return the first (and should be only) league with this ID
        return leagues.first
    }

    /// Search leagues by name
    /// - Parameters:
    ///   - searchTerm: The search term to look for in league names
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of LeagueData objects matching the search term
    func searchLeagues(searchTerm: String, forceRefresh: Bool = false) async throws -> [LeagueData] {
        // Ensure search term is at least 3 characters (API requirement)
        guard searchTerm.count >= 3 else {
            throw APIError.custom("Search term must be at least 3 characters")
        }

        // Set parameters for search
        let parameters = ["search": searchTerm]

        // Fetch leagues from API
        let leagues: [LeagueData] = try await apiService.fetchData(
            endpoint: "/leagues",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )

        return leagues
    }

    /// Fetch top scorers for a specific league and season
    /// - Parameters:
    ///   - leagueId: The ID of the league
    ///   - season: The season year
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of TopScorer objects
    func fetchTopScorers(leagueId: Int, season: Int, forceRefresh: Bool = false) async throws -> [TopScorer] {
        // Set parameters for the request
        let parameters = [
            "league": String(leagueId), // Using "league" instead of "leagueId" to match backend API expectations
            "season": String(season)
        ]

        // Fetch top scorers from API
        let topScorers: [TopScorer] = try await apiService.fetchData(
            endpoint: "/players/topscorers",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )

        return topScorers
    }

    /// Fetch all available league seasons
    /// - Parameter forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of season years
    func fetchLeagueSeasons(forceRefresh: Bool = false) async throws -> [Int] {
        // Fetch league seasons from API
        let seasons: [Int] = try await apiService.fetchData(
            endpoint: "/leagues/seasons",
            forceRefresh: forceRefresh,
            cacheDuration: 21600 // Cache for 6 hours (seasons don't change often)
        )

        // Sort seasons in descending order (most recent first)
        return seasons.sorted(by: >)
    }
}
