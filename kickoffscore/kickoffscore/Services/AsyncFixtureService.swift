import Foundation
import Combine

@MainActor
class AsyncFixtureService {
    // Singleton instance
    static let shared = AsyncFixtureService()

    // API service for network requests
    private let apiService = AsyncAPIService.shared

    // Private initializer for singleton
    private init() {}

    /// Fetch fixtures by league ID
    /// - Parameters:
    ///   - leagueId: The ID of the league
    ///   - season: The season year
    ///   - from: Optional start date (YYYY-MM-DD format)
    ///   - to: Optional end date (YYYY-MM-DD format)
    ///   - status: Optional fixture status filter (e.g., "NS-TBD" for not started and to be determined)
    ///   - round: Optional round filter (e.g., "Regular Season - 1")
    ///   - timezone: Optional timezone identifier for date filtering (e.g., "America/New_York")
    ///   - forceRefresh: Whether to force a refresh from the network
    ///   - cacheDuration: Optional custom cache duration (in seconds)
    /// - Returns: Array of Fixture objects
    func fetchFixturesByLeague(
        leagueId: Int,
        season: Int,
        from: String? = nil,
        to: String? = nil,
        status: String? = nil,
        round: String? = nil,
        timezone: String? = nil,
        forceRefresh: Bool = false,
        cacheDuration: TimeInterval? = nil
    ) async throws -> [Fixture] {
        // Build parameters
        var parameters: [String: String] = [
            "league": String(leagueId),
            "season": String(season)
        ]

        // Add optional parameters if provided
        if let from = from {
            parameters["from"] = from
        }

        if let to = to {
            parameters["to"] = to
        }

        if let status = status {
            parameters["status"] = status
        }

        if let round = round {
            parameters["round"] = round
        }

        if let timezone = timezone {
            parameters["timezone"] = timezone
        }

        // Determine cache duration based on the status or use provided value
        // For live fixtures, use shorter cache
        let effectiveCacheDuration: TimeInterval
        if let customCacheDuration = cacheDuration {
            effectiveCacheDuration = customCacheDuration
        } else {
            effectiveCacheDuration = status?.contains("LIVE") == true ? 15 : 600 // 15 sec for live, 10 min otherwise
        }

        // Fetch fixtures from API
        let fixtures: [Fixture] = try await apiService.fetchData(
            endpoint: "/fixtures",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: effectiveCacheDuration
        )

        return fixtures
    }

    /// Fetch a specific fixture by ID
    /// - Parameters:
    ///   - fixtureId: The ID of the fixture to fetch
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: The fixture with the specified ID, or nil if not found
    func fetchFixtureById(fixtureId: Int, forceRefresh: Bool = false) async throws -> Fixture? {
        // Set parameters for fixture ID filter
        let parameters = ["id": String(fixtureId)]

        // Determine if this might be a live fixture
        let isLiveFixture = await checkIfLiveFixture(fixtureId: fixtureId)

        // Use shorter cache duration for potentially live fixtures
        let cacheDuration: TimeInterval = isLiveFixture ? 15 : 600 // 15 sec for live, 10 min otherwise

        // Fetch fixture from API
        let fixtures: [Fixture] = try await apiService.fetchData(
            endpoint: "/fixtures",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: cacheDuration
        )

        // Return the first (and should be only) fixture with this ID
        return fixtures.first
    }

    /// Check if a fixture might be live based on cached status
    /// - Parameter fixtureId: The ID of the fixture to check
    /// - Returns: True if the fixture might be live, false otherwise
    private func checkIfLiveFixture(fixtureId: Int) async -> Bool {
        do {
            // Try to get cached fixture without forcing refresh
            let cachedFixture = try await fetchFixtureById(fixtureId: fixtureId, forceRefresh: false)

            // If the fixture has a live status, return true
            if let status = cachedFixture?.status.short {
                return status == "1H" || status == "HT" || status == "2H" || status == "ET" || status == "BT" || status == "P" || status == "SUSP" || status == "INT" || status == "LIVE"
            }

            return false
        } catch {
            // If there's an error (e.g., no cached fixture), assume it's not live
            return false
        }
    }

    /// Fetch statistics for a specific fixture with half-time statistics
    /// - Parameters:
    ///   - fixtureId: The ID of the fixture
    ///   - team: Optional team ID to filter statistics for a specific team
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: The fixture with updated statistics including half-time stats
    func fetchFixtureStatistics(fixtureId: Int, team: Int? = nil, forceRefresh: Bool = false) async throws -> Fixture? {
        // Build parameters
        var parameters: [String: String] = [
            "fixture": String(fixtureId),
            "half": "true" // Request half-time statistics
        ]

        // Add team parameter if provided
        if let team = team {
            parameters["team"] = String(team)
        }

        // Determine if this might be a live fixture
        let isLiveFixture = await checkIfLiveFixture(fixtureId: fixtureId)

        // Use shorter cache duration for potentially live fixtures
        let cacheDuration: TimeInterval = isLiveFixture ? 15 : 300 // 15 sec for live, 5 min otherwise

        // Fetch statistics from API
        let statistics: [TeamStatistics] = try await apiService.fetchData(
            endpoint: "/fixtures/statistics",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: cacheDuration
        )

        // Fetch the fixture to update with the statistics
        guard var fixture = try await fetchFixtureById(fixtureId: fixtureId, forceRefresh: forceRefresh) else {
            throw APIError.custom("Fixture not found")
        }

        // Update the fixture with the statistics
        // Make sure we're not trying to assign an empty array
        if !statistics.isEmpty {
            fixture.statistics = statistics
        }

        return fixture
    }

    // MARK: - Cache Management

    /// Invalidate fixture-related cache when live data is updated
    /// - Parameter fixtureId: The ID of the fixture that was updated
    func invalidateFixtureCache(fixtureId: Int) {
        // Invalidate specific fixture cache
        let fixtureKey = CacheManager.shared.generateCacheKey(endpoint: "/fixtures", parameters: ["id": String(fixtureId)])
        CacheManager.shared.removeData(forKey: fixtureKey)

        // Invalidate fixture statistics cache
        let statsKey = CacheManager.shared.generateCacheKey(endpoint: "/fixtures/statistics", parameters: ["fixture": String(fixtureId)])
        CacheManager.shared.removeData(forKey: statsKey)

        // Trigger smart invalidation for fixture content type
        CacheManager.shared.invalidateCache(for: .fixture)

        // Trigger event-based invalidation
        CacheManager.shared.triggerInvalidation(event: "fixture_updated")

        Logger.info("Invalidated cache for fixture ID: \(fixtureId)", category: .performance)
    }

    /// Invalidate all live fixtures cache
    func invalidateLiveFixturesCache() {
        // Use pattern matching to invalidate all live fixture caches
        CacheManager.shared.invalidateCache(keyPattern: "*status=LIVE*")
        CacheManager.shared.invalidateCache(keyPattern: "*status=1H*")
        CacheManager.shared.invalidateCache(keyPattern: "*status=2H*")
        CacheManager.shared.invalidateCache(keyPattern: "*status=HT*")

        // Trigger event-based invalidation
        CacheManager.shared.triggerInvalidation(event: "live_data_updated")

        Logger.info("Invalidated all live fixtures cache", category: .performance)
    }

    /// Fetch fixture rounds for a specific league and season
    /// - Parameters:
    ///   - leagueId: The ID of the league
    ///   - season: The season year
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of round names
    func fetchFixtureRounds(
        leagueId: Int,
        season: Int,
        forceRefresh: Bool = false
    ) async throws -> [String] {
        // Build parameters
        let parameters: [String: String] = [
            "leagueId": String(leagueId),
            "season": String(season)
        ]

        // Fetch rounds from API
        let rounds: [String] = try await apiService.fetchData(
            endpoint: "/fixtures/rounds",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour
        )

        return rounds
    }

    /// Preload popular fixtures for better cache performance
    /// - Parameter leagueIds: Array of popular league IDs to preload
    func preloadPopularFixtures(leagueIds: [Int]) async {
        let today = DateUtility.formatDateForAPI(Date())

        for leagueId in leagueIds {
            do {
                // Preload today's fixtures for popular leagues
                _ = try await fetchFixturesByLeague(
                    leagueId: leagueId,
                    season: Calendar.current.component(.year, from: Date()),
                    from: today,
                    to: today,
                    forceRefresh: false
                )

                Logger.debug("Preloaded fixtures for league ID: \(leagueId)", category: .performance)
            } catch {
                Logger.warning("Failed to preload fixtures for league ID: \(leagueId) - \(error)", category: .performance)
            }
        }
    }
}
