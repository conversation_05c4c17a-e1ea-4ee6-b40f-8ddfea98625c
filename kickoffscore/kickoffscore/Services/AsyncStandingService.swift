import Foundation
import Combine

@MainActor
class AsyncStandingService {
    // Singleton instance
    static let shared = AsyncStandingService()

    // API service for network requests
    private let apiService = AsyncAPIService.shared

    // Private initializer for singleton
    private init() {}
    
    /// Fetch standings for a league and season
    /// - Parameters:
    ///   - leagueId: The ID of the league
    ///   - season: The season year
    ///   - teamId: Optional team ID to filter standings for a specific team
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: Array of standing groups (each group is an array of team standings)
    func fetchStandings(
        leagueId: Int,
        season: Int,
        teamId: Int? = nil,
        forceRefresh: Bool = false
    ) async throws -> [[TeamStanding]] {
        // Build parameters
        var parameters: [String: String] = [
            "leagueId": String(leagueId),
            "season": String(season)
        ]
        
        // Add team ID if provided
        if let teamId = teamId {
            parameters["team"] = String(teamId)
        }
        
        // Fetch standings from API
        let standings: [[TeamStanding]] = try await apiService.fetchData(
            endpoint: "/standings",
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // Cache for 1 hour (standings don't change frequently)
        )
        
        return standings
    }
}
