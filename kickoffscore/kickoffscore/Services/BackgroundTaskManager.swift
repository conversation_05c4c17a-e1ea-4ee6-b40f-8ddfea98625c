//
//  BackgroundTaskManager.swift
//  kickoffscore
//
//  Created by <PERSON><PERSON><PERSON> on 05/07/2025.
//

import Foundation
import BackgroundTasks
import UIKit
import Combine
import King<PERSON>er

/// Manages background tasks for the KickoffScore app
@MainActor
class BackgroundTaskManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = BackgroundTaskManager()
    
    // MARK: - Background Task Identifiers
    struct TaskIdentifiers {
        static let refresh = "com.kickoffscore.refresh"
        static let processing = "com.kickoffscore.processing"
        static let socketRefresh = "com.kickoffscore.socket-refresh"
    }
    
    // MARK: - Published Properties
    @Published var isBackgroundRefreshAvailable = false
    @Published var lastBackgroundRefresh: Date?
    @Published var backgroundTasksScheduled = 0
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private var backgroundTaskIdentifier: UIBackgroundTaskIdentifier = .invalid
    
    // MARK: - Initialization
    private init() {
        checkBackgroundRefreshStatus()
        setupAppStateObservers()
    }
    
    // MARK: - Public Methods
    
    /// Register all background tasks with the system
    func registerBackgroundTasks() {
        // Check if we have the required entitlements
        if hasBackgroundTaskEntitlements() {
            // Register background app refresh task
            BGTaskScheduler.shared.register(
                forTaskWithIdentifier: TaskIdentifiers.refresh,
                using: nil
            ) { [weak self] task in
                Task {
                    await self?.handleBackgroundAppRefresh(task as! BGAppRefreshTask)
                }
            }

            // Register background processing task
            BGTaskScheduler.shared.register(
                forTaskWithIdentifier: TaskIdentifiers.processing,
                using: nil
            ) { [weak self] task in
                Task {
                    await self?.handleBackgroundProcessing(task as! BGProcessingTask)
                }
            }

            // Register socket refresh task
            BGTaskScheduler.shared.register(
                forTaskWithIdentifier: TaskIdentifiers.socketRefresh,
                using: nil
            ) { [weak self] task in
                Task {
                    await self?.handleSocketRefresh(task as! BGAppRefreshTask)
                }
            }

            Logger.info("Background tasks registered successfully with full BGTaskScheduler support", category: .background)
        } else {
            Logger.info("Background task entitlements not available - using enhanced fallback approach", category: .background)
        }
    }
    
    /// Schedule background app refresh
    func scheduleBackgroundAppRefresh() {
        guard hasBackgroundTaskEntitlements() else {
            Logger.info("Background app refresh not available - using fallback approach", category: .background)
            return
        }

        let request = BGAppRefreshTaskRequest(identifier: TaskIdentifiers.refresh)
        request.earliestBeginDate = Date(timeIntervalSinceNow: 15 * 60) // 15 minutes from now

        do {
            try BGTaskScheduler.shared.submit(request)
            backgroundTasksScheduled += 1
            Logger.info("Background app refresh scheduled", category: .background)
        } catch {
            Logger.error("Failed to schedule background app refresh: \(error)", category: .background)
        }
    }
    
    /// Schedule background processing task
    func scheduleBackgroundProcessing() {
        guard hasBackgroundTaskEntitlements() else {
            Logger.info("Background processing not available - using fallback approach", category: .background)
            return
        }

        let request = BGProcessingTaskRequest(identifier: TaskIdentifiers.processing)
        request.earliestBeginDate = Date(timeIntervalSinceNow: 30 * 60) // 30 minutes from now
        request.requiresNetworkConnectivity = true
        request.requiresExternalPower = false

        do {
            try BGTaskScheduler.shared.submit(request)
            backgroundTasksScheduled += 1
            Logger.info("Background processing task scheduled", category: .background)
        } catch {
            Logger.error("Failed to schedule background processing: \(error)", category: .background)
        }
    }
    
    /// Schedule socket refresh task
    func scheduleSocketRefresh() {
        guard hasBackgroundTaskEntitlements() else {
            Logger.info("Socket refresh scheduling not available - using fallback approach", category: .background)
            return
        }

        let request = BGAppRefreshTaskRequest(identifier: TaskIdentifiers.socketRefresh)
        request.earliestBeginDate = Date(timeIntervalSinceNow: 5 * 60) // 5 minutes from now

        do {
            try BGTaskScheduler.shared.submit(request)
            backgroundTasksScheduled += 1
            Logger.info("Socket refresh task scheduled", category: .background)
        } catch {
            Logger.error("Failed to schedule socket refresh: \(error)", category: .background)
        }
    }
    
    /// Begin a standard background task for immediate execution
    func beginBackgroundTask(name: String = "KickoffScore Background Task") -> UIBackgroundTaskIdentifier {
        let taskId = UIApplication.shared.beginBackgroundTask(withName: name) {
            Logger.warning("Background task expired: \(name)", category: .background)
        }
        
        if taskId != .invalid {
            Logger.info("Background task started: \(name)", category: .background)
        } else {
            Logger.error("Failed to start background task: \(name)", category: .background)
        }
        
        return taskId
    }
    
    /// End a background task
    func endBackgroundTask(_ taskId: UIBackgroundTaskIdentifier, name: String = "Unknown") {
        if taskId != .invalid {
            UIApplication.shared.endBackgroundTask(taskId)
            Logger.info("Background task ended: \(name)", category: .background)
        }
    }
    
    // MARK: - Private Methods
    
    /// Check if background app refresh is available
    private func checkBackgroundRefreshStatus() {
        isBackgroundRefreshAvailable = UIApplication.shared.backgroundRefreshStatus == .available
        
        switch UIApplication.shared.backgroundRefreshStatus {
        case .available:
            Logger.info("Background app refresh is available", category: .background)
        case .denied:
            Logger.warning("Background app refresh is denied by user", category: .background)
        case .restricted:
            Logger.warning("Background app refresh is restricted", category: .background)
        @unknown default:
            Logger.warning("Background app refresh status unknown", category: .background)
        }
    }
    
    /// Setup app state observers
    private func setupAppStateObservers() {
        // Observe background refresh status changes
        NotificationCenter.default.publisher(for: UIApplication.backgroundRefreshStatusDidChangeNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.checkBackgroundRefreshStatus()
                }
            }
            .store(in: &cancellables)
        
        // Observe app entering background
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleAppEnteringBackground()
                }
            }
            .store(in: &cancellables)
        
        // Observe app entering foreground
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleAppEnteringForeground()
                }
            }
            .store(in: &cancellables)
    }
    
    /// Handle app entering background
    private func handleAppEnteringBackground() {
        // Schedule background tasks when app goes to background
        if isBackgroundRefreshAvailable {
            scheduleBackgroundAppRefresh()
            scheduleSocketRefresh()
            
            // Schedule processing task less frequently
            if backgroundTasksScheduled < 3 {
                scheduleBackgroundProcessing()
            }
        }
        
        Logger.info("App entering background - background tasks scheduled", category: .background)
    }
    
    /// Handle app entering foreground
    private func handleAppEnteringForeground() {
        // Cancel any pending background tasks since we're now in foreground
        if hasBackgroundTaskEntitlements() {
            BGTaskScheduler.shared.cancelAllTaskRequests()
        }
        backgroundTasksScheduled = 0

        Logger.info("App entering foreground - background tasks cancelled", category: .background)
    }

    /// Check if the app has background task entitlements
    private func hasBackgroundTaskEntitlements() -> Bool {
        // For development teams without paid Apple Developer accounts,
        // background task entitlements are not available
        // We'll use a simple test to see if BGTaskScheduler registration works

        // For development teams without paid Apple Developer accounts,
        // background task entitlements are typically not available
        // We'll assume they're not available for personal development teams
        let hasEntitlements = false

        return hasEntitlements
    }
}

// MARK: - Background Task Handlers
extension BackgroundTaskManager {
    
    /// Handle background app refresh task
    private func handleBackgroundAppRefresh(_ task: BGAppRefreshTask) async {
        Logger.info("Handling background app refresh", category: .background)

        // Schedule the next background refresh
        scheduleBackgroundAppRefresh()

        // Create a task to handle the refresh
        let refreshTask = Task {
            // Refresh live fixtures data
            await refreshLiveData()

            // Update last refresh time
            lastBackgroundRefresh = Date()

            Logger.info("Background app refresh completed successfully", category: .background)
            return true
        }

        // Set up task completion
        task.expirationHandler = {
            refreshTask.cancel()
            Logger.warning("Background app refresh task expired", category: .background)
        }
        
        // Wait for completion and set task result
        let success = await refreshTask.value
        task.setTaskCompleted(success: success)
    }
    
    /// Handle background processing task
    private func handleBackgroundProcessing(_ task: BGProcessingTask) async {
        Logger.info("Handling background processing", category: .background)

        // Schedule the next processing task
        scheduleBackgroundProcessing()

        // Create a task to handle the processing
        let processingTask = Task {
            // Perform data cleanup and optimization
            await performBackgroundProcessing()

            Logger.info("Background processing completed successfully", category: .background)
            return true
        }

        // Set up task completion
        task.expirationHandler = {
            processingTask.cancel()
            Logger.warning("Background processing task expired", category: .background)
        }
        
        // Wait for completion and set task result
        let success = await processingTask.value
        task.setTaskCompleted(success: success)
    }
    
    /// Handle socket refresh task
    private func handleSocketRefresh(_ task: BGAppRefreshTask) async {
        Logger.info("Handling socket refresh", category: .background)

        // Schedule the next socket refresh
        scheduleSocketRefresh()

        // Create a task to handle the socket refresh
        let socketTask = Task {
            // Refresh socket connection and subscriptions
            await refreshSocketConnection()

            Logger.info("Socket refresh completed successfully", category: .background)
            return true
        }

        // Set up task completion
        task.expirationHandler = {
            socketTask.cancel()
            Logger.warning("Socket refresh task expired", category: .background)
        }
        
        // Wait for completion and set task result
        let success = await socketTask.value
        task.setTaskCompleted(success: success)
    }
}

// MARK: - Background Operations
extension BackgroundTaskManager {

    /// Refresh live data in the background
    private func refreshLiveData() async {
        Logger.info("Starting background live data refresh", category: .background)

        // Ensure socket connection is active
        if !SocketManager.shared.isConnected {
            SocketManager.shared.connect()

            // Wait a moment for connection to establish
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
        }

        // Refresh live fixtures if we have active subscriptions
        if SocketManager.shared.hasActiveSubscriptions {
            SocketManager.shared.refreshLiveSubscriptions()
        }

        // Clean up expired cache data
        CacheManager.shared.clearExpiredCache()
        OddsCache.shared.clearExpiredCache()

        Logger.info("Background live data refresh completed", category: .background)
    }

    /// Perform background processing tasks
    private func performBackgroundProcessing() async {
        Logger.info("Starting background processing", category: .background)

        // Clean up old cached data
        CacheManager.shared.performDeepCleanup()
        OddsCache.shared.performDeepCleanup()

        // Clear expired image cache
        await KingfisherManager.shared.cache.cleanExpiredDiskCache()

        // Optimize database if needed (placeholder for future implementation)
        // await DatabaseManager.shared.optimizeDatabase()

        // Update user preferences cache
        UserDefaults.standard.synchronize()

        Logger.info("Background processing completed", category: .background)
    }

    /// Refresh socket connection in the background
    private func refreshSocketConnection() async {
        Logger.info("Starting background socket refresh", category: .background)

        let socketManager = SocketManager.shared

        // Check if socket needs reconnection
        if !socketManager.isConnected {
            // Disconnect first to ensure clean state, then reconnect
            socketManager.disconnect()
            socketManager.connect()

            // Wait for connection to establish
            var attempts = 0
            while !socketManager.isConnected && attempts < 10 {
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                attempts += 1
            }
        }

        // Refresh subscriptions if connected
        if socketManager.isConnected {
            socketManager.refreshAllSubscriptions()
        }

        Logger.info("Background socket refresh completed", category: .background)
    }
}

// MARK: - Debug and Monitoring
extension BackgroundTaskManager {

    /// Get background task status for debugging
    var debugStatus: String {
        var status = "Background Task Manager Status:\n"
        status += "- Background Refresh Available: \(isBackgroundRefreshAvailable)\n"
        status += "- Tasks Scheduled: \(backgroundTasksScheduled)\n"
        status += "- Last Background Refresh: \(lastBackgroundRefresh?.formatted() ?? "Never")\n"
        status += "- Background Refresh Status: \(UIApplication.shared.backgroundRefreshStatus.description)\n"
        return status
    }

    /// Force a background refresh for testing (Debug only)
    #if DEBUG
    func debugForceBackgroundRefresh() {
        Task {
            await refreshLiveData()
            lastBackgroundRefresh = Date()
            Logger.info("Debug: Forced background refresh completed", category: .background)
        }
    }

    func debugForceSocketRefresh() {
        Task {
            await refreshSocketConnection()
            Logger.info("Debug: Forced socket refresh completed", category: .background)
        }
    }
    #endif
}

// MARK: - UIBackgroundRefreshStatus Extension
extension UIBackgroundRefreshStatus {
    var description: String {
        switch self {
        case .available:
            return "Available"
        case .denied:
            return "Denied"
        case .restricted:
            return "Restricted"
        @unknown default:
            return "Unknown"
        }
    }
}


