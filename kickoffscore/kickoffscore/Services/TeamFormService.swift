import Foundation
import Combine

struct DetailedFormResult: Decodable, Identifiable {
    let result: String
    let fixtureId: Int
    let date: String
    let homeTeam: TeamInfo
    let awayTeam: TeamInfo
    let score: Score
    let isHomeTeam: Bool

    var id: Int { fixtureId }

    struct TeamInfo: Decodable {
        let id: Int
        let name: String
        let logo: String
    }

    struct Score: Decodable {
        let home: Int
        let away: Int
    }
}

struct TeamFormResponse: Decodable {
    let teamId: Int
    let season: Int
    let form: String
    let detailedForm: [DetailedFormResult]?
}

class TeamFormService {
    private let apiService = APIService.shared

    // Fetch team form data from the backend
    func fetchTeamForm(teamId: Int, season: Int? = nil, excludeFixtureId: Int? = nil) -> AnyPublisher<TeamFormResponse, APIError> {
        let endpoint = "/teams/form"
        var parameters: [String: String] = ["teamId": String(teamId)]

        // If season is provided, use it; otherwise, let the backend use the current year
        if let season = season {
            parameters["season"] = String(season)
        } else {
            // For testing purposes, use 2023 as we know it has data
            parameters["season"] = "2023"
        }

        // If excludeFixtureId is provided, add it to the parameters
        if let excludeFixtureId = excludeFixtureId {
            parameters["excludeFixtureId"] = String(excludeFixtureId)
        }

        return apiService.fetchDataPublisher(endpoint: endpoint, parameters: parameters)
            .eraseToAnyPublisher()
    }

    // Fetch team form data for both teams in a fixture
    func fetchTeamFormForFixture(fixture: Fixture, excludeCurrentFixture: Bool = true) -> AnyPublisher<(TeamFormResponse?, TeamFormResponse?), APIError> {
        guard let homeTeamId = fixture.teams.home?.id,
              let awayTeamId = fixture.teams.away?.id else {
            return Fail(error: APIError.custom("Missing team IDs"))
                .eraseToAnyPublisher()
        }

        // Get the season from the fixture if available
        let season = fixture.league.season

        // Get the fixture ID to exclude if requested
        let excludeFixtureId = excludeCurrentFixture ? fixture.id : nil

        let homeTeamPublisher = fetchTeamForm(teamId: homeTeamId, season: season, excludeFixtureId: excludeFixtureId)
            .map { Optional($0) }
            .catch { error -> AnyPublisher<TeamFormResponse?, APIError> in
                print("Error fetching home team form: \(error.localizedDescription)")
                return Just(nil).setFailureType(to: APIError.self).eraseToAnyPublisher()
            }

        let awayTeamPublisher = fetchTeamForm(teamId: awayTeamId, season: season, excludeFixtureId: excludeFixtureId)
            .map { Optional($0) }
            .catch { error -> AnyPublisher<TeamFormResponse?, APIError> in
                print("Error fetching away team form: \(error.localizedDescription)")
                return Just(nil).setFailureType(to: APIError.self).eraseToAnyPublisher()
            }

        return Publishers.CombineLatest(homeTeamPublisher, awayTeamPublisher)
            .eraseToAnyPublisher()
    }

    // Generate mock form data based on team ID (fallback if API fails)
    func generateMockForm(teamId: Int) -> String {
        // Use a consistent pattern based on team ID to ensure it's always the same
        let seed = teamId % 5
        switch seed {
        case 0: return "WDWLW" // e.g., W (League), D (Cup), W (League), L (Champions League), W (League)
        case 1: return "DWLDW" // e.g., D (Cup), W (League), L (League), D (Champions League), W (League)
        case 2: return "LWDWL" // e.g., L (League), W (Cup), D (League), W (Champions League), L (League)
        case 3: return "WLWDL" // e.g., W (League), L (Cup), W (League), D (Champions League), L (League)
        case 4: return "DLWWD" // e.g., D (League), L (Cup), W (League), W (Champions League), D (League)
        default: return "WDWLW"
        }
    }
}
