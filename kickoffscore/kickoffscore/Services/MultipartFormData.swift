import Foundation

public class MultipartFormData {
    private let boundary: String
    private var bodyData = Data()

    public init() {
        // Generate a unique boundary string
        boundary = "Boundary-\(UUID().uuidString)"
    }

    public func append(_ data: Data, withName name: String, fileName: String? = nil, mimeType: String? = nil) {
        // Add boundary
        bodyData.append("--\(boundary)\r\n".data(using: .utf8)!)

        // Add content disposition
        var contentDisposition = "Content-Disposition: form-data; name=\"\(name)\""
        if let fileName = fileName {
            contentDisposition += "; filename=\"\(fileName)\""
        }
        bodyData.append("\(contentDisposition)\r\n".data(using: .utf8)!)

        // Add content type if provided
        if let mimeType = mimeType {
            bodyData.append("Content-Type: \(mimeType)\r\n".data(using: .utf8)!)
        }

        // Add empty line before data
        bodyData.append("\r\n".data(using: .utf8)!)

        // Add data
        bodyData.append(data)

        // Add line break after data
        bodyData.append("\r\n".data(using: .utf8)!)
    }

    public func append(_ string: String, withName name: String) {
        if let data = string.data(using: .utf8) {
            append(data, withName: name)
        }
    }

    public func finalize() -> Data {
        var finalData = bodyData
        // Add final boundary
        finalData.append("--\(boundary)--\r\n".data(using: .utf8)!)
        return finalData
    }

    public var contentType: String {
        return "multipart/form-data; boundary=\(boundary)"
    }
}
