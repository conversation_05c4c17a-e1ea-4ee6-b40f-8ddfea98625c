import Foundation
import Combine

class HeadToHeadService {
    private let apiService = APIService.shared

    func fetchHeadToHead(homeTeamId: Int, awayTeamId: Int) -> AnyPublisher<[HeadToHeadFixtureData], Error> {
        let h2hParam = "\(homeTeamId)-\(awayTeamId)"
        let endpoint = "fixtures/h2h"

        // Calculate date 5 years ago from today using DateUtility
        guard let dateRange = DateUtility.dateRangeForPastYears(5) else {
            return Fail(error: APIError.custom("Failed to calculate date range")).eraseToAnyPublisher()
        }

        // Format dates as YYYY-MM-DD using DateUtility
        let fromDate = DateUtility.formatDateForAPI(dateRange.from)
        let toDate = DateUtility.formatDateForAPI(dateRange.to)

        // Set parameters for date range
        let parameters = [
            "h2h": h2hParam,
            "from": fromDate,
            "to": toDate,
            "timezone": TimeZone.current.identifier
        ]

        print("Fetching H2H data for teams: \(h2hParam) from \(fromDate) to \(toDate)")

        return Future<[HeadToHeadFixtureData], Error> { promise in
            self.apiService.fetchData(endpoint: endpoint, parameters: parameters) { (result: Result<[HeadToHeadFixtureData], APIError>) in
                switch result {
                case .success(let fixtures):
                    print("Successfully fetched \(fixtures.count) H2H fixtures")
                    // Filter out future matches (status.short == "NS")
                    let pastMatches = fixtures.filter { fixture in
                        fixture.fixture.status.short != "NS"
                    }
                    print("After filtering future matches: \(pastMatches.count) H2H fixtures")
                    promise(.success(pastMatches))
                case .failure(let error):
                    print("Failed to fetch H2H data: \(error)")
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
}
