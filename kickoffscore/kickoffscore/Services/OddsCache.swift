import Foundation

/// A dedicated cache for storing odds data on disk instead of UserDefaults
class OddsCache {
    // Singleton instance
    static let shared = OddsCache()

    // Cache time-to-live in seconds (10 minutes)
    private let oddsCacheTTL: TimeInterval = 600

    // Directory for storing odds cache files
    private let cacheDirectory: URL

    // Initialize with default settings
    private init() {
        // Get the cache directory
        let fileManager = FileManager.default
        let cacheDir = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        cacheDirectory = cacheDir.appendingPathComponent("oddsCache", isDirectory: true)

        // Create the cache directory if it doesn't exist
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            do {
                try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
            } catch {
                print("Error creating odds cache directory: \(error)")
            }
        }

        // Migrate existing odds data from UserDefaults
        migrateFromUserDefaults()
    }

    /// Save bookmakers data to the cache
    /// - Parameters:
    ///   - bookmakers: The bookmakers data to cache
    ///   - fixtureId: The fixture ID
    func saveBookmakers(_ bookmakers: [BookmakerData], forFixtureId fixtureId: Int) {
        let cacheKey = "odds_\(fixtureId)"
        let timestampKey = "\(cacheKey)_timestamp"
        let timestamp = Date().timeIntervalSince1970

        do {
            // Encode the bookmakers data
            let encoded = try JSONEncoder().encode(bookmakers)

            // Save to file
            let fileURL = cacheDirectory.appendingPathComponent("\(cacheKey).json")
            try encoded.write(to: fileURL)

            // Save timestamp to UserDefaults (small data)
            UserDefaults.standard.set(timestamp, forKey: timestampKey)

            // Remove the old data from UserDefaults if it exists
            UserDefaults.standard.removeObject(forKey: cacheKey)
        } catch {
            print("Error saving odds data to cache: \(error)")
        }
    }

    /// Load bookmakers data from the cache
    /// - Parameter fixtureId: The fixture ID
    /// - Returns: The cached bookmakers data, or nil if not found or expired
    func loadBookmakers(forFixtureId fixtureId: Int) -> [BookmakerData]? {
        let cacheKey = "odds_\(fixtureId)"
        let timestampKey = "\(cacheKey)_timestamp"

        // Check if we have a valid cache timestamp that's within our cache TTL
        guard let cachedTimestamp = UserDefaults.standard.object(forKey: timestampKey) as? TimeInterval else {
            return nil
        }

        let cacheAge = Date().timeIntervalSince1970 - cachedTimestamp

        // If cache is expired, return nil
        if cacheAge >= oddsCacheTTL {
            return nil
        }

        // Get the file URL
        let fileURL = cacheDirectory.appendingPathComponent("\(cacheKey).json")

        do {
            // Check if the file exists
            if FileManager.default.fileExists(atPath: fileURL.path) {
                // Read the data from the file
                let data = try Data(contentsOf: fileURL)

                // Decode the bookmakers data
                return try JSONDecoder().decode([BookmakerData].self, from: data)
            }
        } catch {
            print("Error loading odds data from cache: \(error)")
        }

        return nil
    }

    /// Clear expired cache files
    func clearExpiredCache() {
        let fileManager = FileManager.default

        do {
            // Get all files in the cache directory
            let fileURLs = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)

            for fileURL in fileURLs {
                // Get the file name without extension
                let fileName = fileURL.deletingPathExtension().lastPathComponent

                // Check if it's an odds file
                if fileName.hasPrefix("odds_") {
                    // Extract the fixture ID
                    let fixtureId = fileName.replacingOccurrences(of: "odds_", with: "")

                    // Check if the timestamp exists
                    let timestampKey = "odds_\(fixtureId)_timestamp"
                    if let timestamp = UserDefaults.standard.object(forKey: timestampKey) as? TimeInterval {
                        let cacheAge = Date().timeIntervalSince1970 - timestamp

                        // If cache is expired, delete the file
                        if cacheAge >= oddsCacheTTL {
                            try fileManager.removeItem(at: fileURL)
                            UserDefaults.standard.removeObject(forKey: timestampKey)
                        }
                    } else {
                        // No timestamp, delete the file
                        try fileManager.removeItem(at: fileURL)
                    }
                }
            }
        } catch {
            print("Error clearing expired cache: \(error)")
        }
    }

    /// Clear all cache files
    func clearAllCache() {
        let fileManager = FileManager.default

        do {
            // Get all files in the cache directory
            let fileURLs = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)

            for fileURL in fileURLs {
                // Delete the file
                try fileManager.removeItem(at: fileURL)
            }

            // Remove all odds timestamps from UserDefaults
            let userDefaults = UserDefaults.standard
            let allKeys = userDefaults.dictionaryRepresentation().keys

            for key in allKeys {
                if key.hasPrefix("odds_") && key.hasSuffix("_timestamp") {
                    userDefaults.removeObject(forKey: key)
                }
            }
        } catch {
            print("Error clearing all cache: \(error)")
        }
    }

    /// Migrate existing odds data from UserDefaults to file storage
    private func migrateFromUserDefaults() {
        let userDefaults = UserDefaults.standard
        let allKeys = userDefaults.dictionaryRepresentation().keys

        for key in allKeys {
            // Check if it's an odds key (not a timestamp)
            if key.hasPrefix("odds_") && !key.hasSuffix("_timestamp") {
                // Extract the fixture ID
                if let fixtureId = Int(key.replacingOccurrences(of: "odds_", with: "")) {
                    // Get the data from UserDefaults
                    if let data = userDefaults.data(forKey: key) {
                        do {
                            // Verify data can be decoded as bookmakers (but we don't need the result)
                            _ = try JSONDecoder().decode([BookmakerData].self, from: data)

                            // Save to file
                            let fileURL = cacheDirectory.appendingPathComponent("\(key).json")
                            try data.write(to: fileURL)

                            // Remove from UserDefaults
                            userDefaults.removeObject(forKey: key)

                            print("Migrated odds data for fixture \(fixtureId) from UserDefaults to file storage")
                        } catch {
                            print("Error migrating odds data for fixture \(fixtureId): \(error)")
                        }
                    }
                }
            }
        }
    }

    /// Perform deep cleanup of cache files (used by background tasks)
    func performDeepCleanup() {
        let fileManager = FileManager.default

        do {
            // Get all files in the cache directory
            let fileURLs = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey, .contentModificationDateKey])

            var totalSizeFreed: Int64 = 0
            var filesRemoved = 0

            for fileURL in fileURLs {
                let fileName = fileURL.lastPathComponent.replacingOccurrences(of: ".json", with: "")

                // Check if it's an odds file
                if fileName.hasPrefix("odds_") {
                    // Extract the fixture ID
                    let fixtureId = fileName.replacingOccurrences(of: "odds_", with: "")

                    // Check if the timestamp exists and is expired
                    let timestampKey = "odds_\(fixtureId)_timestamp"
                    var shouldDelete = false

                    if let timestamp = UserDefaults.standard.object(forKey: timestampKey) as? TimeInterval {
                        let cacheAge = Date().timeIntervalSince1970 - timestamp

                        // Delete if expired or very old (more than 1 hour)
                        if cacheAge >= oddsCacheTTL || cacheAge >= 3600 {
                            shouldDelete = true
                        }
                    } else {
                        // No timestamp, delete the file
                        shouldDelete = true
                    }

                    // Also check file modification date as backup
                    if !shouldDelete {
                        if let attributes = try? fileManager.attributesOfItem(atPath: fileURL.path),
                           let modificationDate = attributes[.modificationDate] as? Date {
                            let fileAge = Date().timeIntervalSince(modificationDate)
                            if fileAge >= 3600 { // 1 hour
                                shouldDelete = true
                            }
                        }
                    }

                    if shouldDelete {
                        // Get file size before deletion
                        if let fileSize = try? fileURL.resourceValues(forKeys: [.fileSizeKey]).fileSize {
                            totalSizeFreed += Int64(fileSize)
                        }

                        // Delete the file
                        try fileManager.removeItem(at: fileURL)
                        UserDefaults.standard.removeObject(forKey: timestampKey)
                        filesRemoved += 1
                    }
                }
            }

            Logger.info("Deep cleanup completed: removed \(filesRemoved) files, freed \(totalSizeFreed) bytes", category: .performance)

        } catch {
            Logger.error("Error during deep cleanup: \(error)", category: .performance)
        }
    }
}
