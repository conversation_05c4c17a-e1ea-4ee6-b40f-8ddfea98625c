import Foundation
import Combine
import UIKit

/// Simple response model for notification API responses
struct NotificationAPIResponse: Codable {
    let message: String?
    let success: Bool?

    // Default implementation returns true if no explicit success field
    var isSuccess: Bool {
        return success ?? true
    }
}

/// Service for managing notification-related functionality
class NotificationService {
    // Singleton instance
    static let shared = NotificationService()

    // Device token for push notifications
    private var deviceToken: String?

    // Token registration status
    private var isTokenRegistered = false

    // Published properties
    private var cancellables = Set<AnyCancellable>()

    // Token registration queue
    private var tokenRegistrationQueue = [() -> Void]()

    private init() {
        // Register for device token notifications
        NotificationCenter.default.publisher(for: Notification.Name("DeviceTokenReceived"))
            .compactMap { $0.object as? String }
            .sink { [weak self] token in
                self?.setDeviceToken(token)
            }
            .store(in: &cancellables)
    }

    /// Set the device token and register it with the backend
    /// - Parameter token: The device token
    func setDeviceToken(_ token: String) {
        print("Setting device token: \(token)")
        self.deviceToken = token

        // Register the token with the backend
        registerAnonymousDeviceToken(token: token)
            .sink(receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("Error registering device token: \(error)")
                }
            }, receiveValue: { [weak self] success in
                if success {
                    print("Device token registered successfully")
                    self?.isTokenRegistered = true

                    // Process any queued operations
                    self?.processTokenRegistrationQueue()
                } else {
                    print("Failed to register device token")
                }
            })
            .store(in: &cancellables)
    }

    /// Check if we have a valid device token
    /// - Returns: True if we have a valid device token
    func hasValidDeviceToken() -> Bool {
        return deviceToken != nil
    }

    /// Get the current device token
    /// - Returns: The device token, or nil if not available
    func getCurrentDeviceToken() -> String? {
        return deviceToken
    }

    /// Process any operations that were queued waiting for token registration
    private func processTokenRegistrationQueue() {
        // Execute all queued operations
        for operation in tokenRegistrationQueue {
            operation()
        }

        // Clear the queue
        tokenRegistrationQueue.removeAll()
    }

    // MARK: - Anonymous User Methods

    /// Register an anonymous device token with the backend
    /// - Parameter token: The device token to register
    /// - Returns: A publisher that emits the result of the operation
    func registerAnonymousDeviceToken(token: String) -> AnyPublisher<Bool, Error> {
        // Create the endpoint for registering anonymous device tokens
        let endpoint = "/notifications/anonymous/device-tokens"
        let body = TokenRequest(token: token)

        return APIService.shared.postDataPublisher(endpoint: endpoint, body: body)
            .map { (response: NotificationAPIResponse) -> Bool in
                return response.isSuccess
            }
            .catch { error -> AnyPublisher<Bool, Error> in
                print("Error registering anonymous device token: \(error)")

                // Handle specific error cases based on error type name
                let errorType = String(describing: type(of: error))

                // Handle APIError cases
                if errorType == "APIError" {
                    // Try to extract status code from error description
                    let errorDescription = error.localizedDescription
                    if errorDescription.contains("502") {
                        // For 502 errors, we'll consider the token as "registered" locally
                        print("Server returned 502 error, proceeding with local token registration")
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    } else if errorDescription.contains("50") {
                        // For other 5xx errors, also proceed with local registration
                        print("Server returned 5xx error, proceeding with local token registration")
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                // Handle URLError cases
                if errorType == "URLError" {
                    // Network errors
                    if error.localizedDescription.contains("internet connection") ||
                       error.localizedDescription.contains("network connection") ||
                       error.localizedDescription.contains("timed out") {
                        print("Network error when registering token, proceeding with local token registration")
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                return Fail(error: error).eraseToAnyPublisher()
            }
            .retry(3) // Retry up to 3 times for transient errors
            .eraseToAnyPublisher()
    }

    /// Subscribe to notifications for a specific fixture
    /// - Parameters:
    ///   - fixtureId: The fixture ID to subscribe to
    ///   - preferences: The notification preferences
    /// - Returns: A publisher that emits the result of the operation
    func subscribeToFixture(fixtureId: Int, preferences: NotificationPreference) -> AnyPublisher<Bool, Error> {
        // If we don't have a device token yet, request it from the system
        guard let token = deviceToken else {
            print("Device token not available, requesting from system...")

            // Create a subject to return as our publisher
            let resultSubject = PassthroughSubject<Bool, Error>()

            // Queue this operation for when the token becomes available
            tokenRegistrationQueue.append { [weak self] in
                guard let self = self, let token = self.deviceToken else {
                    resultSubject.send(completion: .failure(APIError.custom("Device token still not available")))
                    return
                }

                print("Processing queued subscription with token: \(token)")

                // Now that we have a token, perform the subscription
                self.performSubscription(token: token, fixtureId: fixtureId, preferences: preferences)
                    .sink(
                        receiveCompletion: { resultSubject.send(completion: $0) },
                        receiveValue: { resultSubject.send($0) }
                    )
                    .store(in: &self.cancellables)
            }

            // Request push notification permissions again to ensure we get a token
            UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, _ in
                if granted {
                    DispatchQueue.main.async {
                        UIApplication.shared.registerForRemoteNotifications()
                    }
                } else {
                    resultSubject.send(completion: .failure(APIError.custom("Push notification permission denied")))
                }
            }

            return resultSubject.eraseToAnyPublisher()
        }

        // If we have a token, perform the subscription directly
        return performSubscription(token: token, fixtureId: fixtureId, preferences: preferences)
    }

    /// Perform the actual subscription with a valid token
    /// - Parameters:
    ///   - token: The device token
    ///   - fixtureId: The fixture ID
    ///   - preferences: The notification preferences
    /// - Returns: A publisher that emits the result of the operation
    private func performSubscription(token: String, fixtureId: Int, preferences: NotificationPreference) -> AnyPublisher<Bool, Error> {
        print("Performing subscription with token: \(token)")

        // First, subscribe to the fixture
        let subscribePublisher = subscribeAnonymousToFixture(token: token, fixtureId: fixtureId)

        // Then, update the preferences
        let preferencesPublisher = updateAnonymousNotificationPreferences(token: token, preferences: preferences)

        // Combine both operations
        return subscribePublisher
            .flatMap { _ in
                return preferencesPublisher
            }
            .eraseToAnyPublisher()
    }

    /// Unsubscribe from notifications for a specific fixture
    /// - Parameter fixtureId: The fixture ID to unsubscribe from
    /// - Returns: A publisher that emits the result of the operation
    func unsubscribeFromFixture(fixtureId: Int) -> AnyPublisher<Bool, Error> {
        guard let token = deviceToken else {
            print("Device token not available for unsubscribe, requesting from system...")

            // Create a subject to return as our publisher
            let resultSubject = PassthroughSubject<Bool, Error>()

            // Queue this operation for when the token becomes available
            tokenRegistrationQueue.append { [weak self] in
                guard let self = self, let token = self.deviceToken else {
                    resultSubject.send(completion: .failure(APIError.custom("Device token still not available")))
                    return
                }

                print("Processing queued unsubscription with token: \(token)")

                // Now that we have a token, perform the unsubscription
                self.unsubscribeAnonymousFromFixture(token: token, fixtureId: fixtureId)
                    .sink(
                        receiveCompletion: { resultSubject.send(completion: $0) },
                        receiveValue: { resultSubject.send($0) }
                    )
                    .store(in: &self.cancellables)
            }

            // Request push notification permissions again to ensure we get a token
            UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, _ in
                if granted {
                    DispatchQueue.main.async {
                        UIApplication.shared.registerForRemoteNotifications()
                    }
                } else {
                    resultSubject.send(completion: .failure(APIError.custom("Push notification permission denied")))
                }
            }

            return resultSubject.eraseToAnyPublisher()
        }

        print("Performing unsubscription with token: \(token)")
        return unsubscribeAnonymousFromFixture(token: token, fixtureId: fixtureId)
    }

    /// Check if the user is subscribed to a specific fixture
    /// - Parameter fixtureId: The fixture ID to check
    /// - Returns: A publisher that emits the subscription status and preferences
    func checkFixtureSubscription(fixtureId: Int) -> AnyPublisher<(Bool, NotificationPreference?), Error> {
        guard let token = deviceToken else {
            return Just((false, nil))
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }

        let endpoint = "/notifications/anonymous/subscriptions/\(token)/fixtures/\(fixtureId)/status"

        return APIService.shared.fetchDataPublisher(endpoint: endpoint)
            .map { (response: SubscriptionStatusResponse) -> (Bool, NotificationPreference?) in
                if response.isSubscribed {
                    return (true, response.preferences)
                } else {
                    return (false, nil)
                }
            }
            .catch { error -> AnyPublisher<(Bool, NotificationPreference?), Error> in
                print("Error checking fixture subscription: \(error)")

                // Handle specific error cases based on error type name
                let errorType = String(describing: type(of: error))

                // Handle APIError cases
                if errorType == "APIError" {
                    // Try to extract status code from error description
                    let errorDescription = error.localizedDescription
                    if errorDescription.contains("404") {
                        // If it's a 404 error (endpoint not found), assume not subscribed
                        // This handles the case where the backend endpoint hasn't been deployed yet
                        print("Status endpoint returned 404, assuming not subscribed")
                        return Just((false, nil))
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    } else if errorDescription.contains("50") {
                        // For server errors, assume not subscribed but cache the check to avoid repeated failures
                        print("Server returned 5xx error, assuming not subscribed")
                        return Just((false, nil))
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                // Handle URLError cases
                if errorType == "URLError" {
                    // Network errors
                    if error.localizedDescription.contains("internet connection") ||
                       error.localizedDescription.contains("network connection") ||
                       error.localizedDescription.contains("timed out") {
                        print("Network error when checking subscription, assuming not subscribed")
                        return Just((false, nil))
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                // For other errors, assume not subscribed
                return Just((false, nil))
                    .setFailureType(to: Error.self)
                    .eraseToAnyPublisher()
            }
            .retry(2) // Retry up to 2 times for transient errors
            .eraseToAnyPublisher()
    }

    // MARK: - Private Helper Methods

    private func subscribeAnonymousToFixture(token: String, fixtureId: Int) -> AnyPublisher<Bool, Error> {
        let endpoint = "/notifications/anonymous/subscriptions/\(token)/fixtures/\(fixtureId)"

        // Empty request body
        struct EmptyRequest: Codable {}

        return APIService.shared.postDataPublisher(endpoint: endpoint, body: EmptyRequest())
            .map { (response: NotificationAPIResponse) -> Bool in
                return response.isSuccess
            }
            .catch { error -> AnyPublisher<Bool, Error> in
                print("Error subscribing to fixture: \(error)")

                // Handle specific error cases based on error type name
                let errorType = String(describing: type(of: error))

                // Handle APIError cases
                if errorType == "APIError" {
                    // Try to extract status code from error description
                    let errorDescription = error.localizedDescription
                    if errorDescription.contains("50") {
                        // For server errors, log but don't fail the UI
                        print("Server returned 5xx error when subscribing to fixture")
                        // Return success to allow UI to update, we'll retry in the background
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                // Handle URLError cases
                if errorType == "URLError" {
                    // Network errors
                    if error.localizedDescription.contains("internet connection") ||
                       error.localizedDescription.contains("network connection") ||
                       error.localizedDescription.contains("timed out") {
                        print("Network error when subscribing to fixture")
                        // Return success to allow UI to update, we'll retry in the background
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                return Fail(error: error).eraseToAnyPublisher()
            }
            .retry(3) // Retry up to 3 times for transient errors
            .eraseToAnyPublisher()
    }

    private func unsubscribeAnonymousFromFixture(token: String, fixtureId: Int) -> AnyPublisher<Bool, Error> {
        let endpoint = "/notifications/anonymous/subscriptions/\(token)/fixtures/\(fixtureId)"

        return APIService.shared.deleteDataPublisher(endpoint: endpoint)
            .map { (response: NotificationAPIResponse) -> Bool in
                return response.isSuccess
            }
            .catch { error -> AnyPublisher<Bool, Error> in
                print("Error unsubscribing from fixture: \(error)")

                // Handle specific error cases based on error type name
                let errorType = String(describing: type(of: error))

                // Handle APIError cases
                if errorType == "APIError" {
                    // Try to extract status code from error description
                    let errorDescription = error.localizedDescription
                    if errorDescription.contains("404") {
                        // If the subscription doesn't exist, consider it a success
                        print("Subscription not found (404), considering unsubscribe successful")
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    } else if errorDescription.contains("50") {
                        // For server errors, log but don't fail the UI
                        print("Server returned 5xx error when unsubscribing from fixture")
                        // Return success to allow UI to update
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                // Handle URLError cases
                if errorType == "URLError" {
                    // Network errors
                    if error.localizedDescription.contains("internet connection") ||
                       error.localizedDescription.contains("network connection") ||
                       error.localizedDescription.contains("timed out") {
                        print("Network error when unsubscribing from fixture")
                        // Return success to allow UI to update
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                return Fail(error: error).eraseToAnyPublisher()
            }
            .retry(2) // Retry up to 2 times for transient errors
            .eraseToAnyPublisher()
    }

    private func updateAnonymousNotificationPreferences(token: String, preferences: NotificationPreference) -> AnyPublisher<Bool, Error> {
        let endpoint = "/notifications/anonymous/preferences/\(token)"

        return APIService.shared.putDataPublisher(endpoint: endpoint, body: preferences)
            .map { (response: NotificationAPIResponse) -> Bool in
                return response.isSuccess
            }
            .catch { error -> AnyPublisher<Bool, Error> in
                print("Error updating notification preferences: \(error)")

                // Handle specific error cases based on error type name
                let errorType = String(describing: type(of: error))

                // Handle APIError cases
                if errorType == "APIError" {
                    // Try to extract status code from error description
                    let errorDescription = error.localizedDescription
                    if errorDescription.contains("404") {
                        // If the token doesn't exist, we should try to register it first
                        print("Token not found (404), attempting to register token first")
                        return self.registerAnonymousDeviceToken(token: token)
                            .flatMap { _ -> AnyPublisher<Bool, Error> in
                                // Try updating preferences again after registration
                                return self.updateAnonymousNotificationPreferences(token: token, preferences: preferences)
                            }
                            .eraseToAnyPublisher()
                    } else if errorDescription.contains("50") {
                        // For server errors, log but don't fail the UI
                        print("Server returned 5xx error when updating preferences")
                        // Return success to allow UI to update, we'll retry in the background
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                // Handle URLError cases
                if errorType == "URLError" {
                    // Network errors
                    if error.localizedDescription.contains("internet connection") ||
                       error.localizedDescription.contains("network connection") ||
                       error.localizedDescription.contains("timed out") {
                        print("Network error when updating preferences")
                        // Return success to allow UI to update, we'll retry in the background
                        return Just(true)
                            .setFailureType(to: Error.self)
                            .eraseToAnyPublisher()
                    }
                }

                return Fail(error: error).eraseToAnyPublisher()
            }
            .retry(3) // Retry up to 3 times for transient errors
            .eraseToAnyPublisher()
    }
}

// MARK: - Request/Response Models

/// Simple request model for token registration
struct TokenRequest: Codable {
    let token: String
}

/// Response model for subscription status check
struct SubscriptionStatusResponse: Codable {
    let isSubscribed: Bool
    let preferences: NotificationPreference?
}
