import Foundation

/// Head-to-head service using async/await pattern
@MainActor
class AsyncHeadToHeadService {
    private let apiService = AsyncAPIService.shared

    /// Fetch head-to-head data for two teams
    /// - Parameters:
    ///   - homeTeamId: The ID of the home team
    ///   - awayTeamId: The ID of the away team
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: An array of head-to-head fixture data
    func fetchHeadToHead(homeTeamId: Int, awayTeamId: Int, forceRefresh: Bool = false) async throws -> [HeadToHeadFixtureData] {
        let h2hParam = "\(homeTeamId)-\(awayTeamId)"
        let endpoint = "fixtures/h2h"

        // Calculate date range for the past 5 years
        guard let dateRange = DateUtility.dateRangeForPastYears(5) else {
            throw APIError.custom("Failed to calculate date range")
        }

        // Format dates for API request
        let fromDate = DateUtility.formatDateForAPI(dateRange.from)
        let toDate = DateUtility.formatDateForAPI(dateRange.to)

        // Set parameters
        let parameters = [
            "h2h": h2hParam,
            "from": fromDate,
            "to": toDate,
            "timezone": TimeZone.current.identifier
        ]

        print("Fetching H2H data for teams: \(h2hParam) from \(fromDate) to \(toDate)")

        // Fetch data from API with caching (cache for 1 hour)
        let fixtures: [HeadToHeadFixtureData] = try await apiService.fetchData(
            endpoint: endpoint,
            parameters: parameters,
            forceRefresh: forceRefresh,
            cacheDuration: 3600 // 1 hour cache
        )

        print("Successfully fetched \(fixtures.count) H2H fixtures")

        // Filter out future matches
        let pastMatches = fixtures.filter { fixture in
            fixture.fixture.status.short != "NS"
        }

        print("After filtering future matches: \(pastMatches.count) H2H fixtures")

        return pastMatches
    }
}
