import Foundation
import Combine

class TeamService {
    private let apiService = APIService.shared

    /// Fetch team details by ID
    /// - Parameter teamId: The ID of the team to fetch
    /// - Returns: A publisher that emits the team data or an error
    func fetchTeamById(teamId: Int) -> AnyPublisher<TeamData, APIError> {
        let endpoint = "/teams"
        let parameters = ["id": String(teamId)]

        // Try to decode as APITeamResponse (direct object from backend)
        return apiService.fetchDataPublisher(endpoint: endpoint, parameters: parameters)
            .tryMap { output -> TeamData in
                let (data, _) = output

                // Try to decode as APITeamResponse first
                do {
                    let decoder = JSONDecoder()
                    let apiTeamResponse = try decoder.decode(APITeamResponse.self, from: data)
                    return apiTeamResponse.toTeamData()
                } catch {
                    // If that fails, try TeamData directly
                    do {
                        let decoder = JSONDecoder()
                        let teamData = try decoder.decode(TeamData.self, from: data)
                        return teamData
                    } catch {
                        // If that fails, try TeamResponse
                        do {
                            let decoder = JSONDecoder()
                            let teamResponse = try decoder.decode(TeamResponse.self, from: data)
                            guard let teamData = teamResponse.response.first else {
                                throw APIError.custom("Team not found")
                            }
                            return teamData
                        } catch {
                            // If all decoding attempts fail, throw a detailed error
                            throw APIError.custom("Failed to decode team data: \(error.localizedDescription)")
                        }
                    }
                }
            }
            .mapError { error -> APIError in
                if let apiError = error as? APIError {
                    return apiError
                }
                return APIError.custom(error.localizedDescription)
            }
            .eraseToAnyPublisher()
    }

    /// Generate mock team data for preview and testing
    /// - Parameter teamId: The ID to use for the mock team
    /// - Returns: A mock TeamData object
    static func mockTeamData(teamId: Int = 33) -> TeamData {
        return TeamData(
            team: Team(
                id: teamId,
                name: "Manchester United",
                code: "MUN",
                country: "England",
                founded: 1878,
                national: false,
                logo: "https://media.api-sports.io/football/teams/33.png"
            ),
            venue: Venue(
                id: 556,
                name: "Old Trafford",
                address: "Sir Matt Busby Way",
                city: "Manchester",
                country: "England",
                capacity: 76212,
                surface: "grass",
                image: "https://media.api-sports.io/football/venues/556.png"
            )
        )
    }

    /// Fetch team squad by team ID
    /// - Parameter teamId: The ID of the team to fetch squad for
    /// - Returns: A publisher that emits the team squad data or an error
    func fetchTeamSquad(teamId: Int) -> AnyPublisher<PlayerSquadResponse, APIError> {
        let endpoint = "/players/squads"
        let parameters = ["team": String(teamId)]

        return apiService.fetchDataPublisher(endpoint: endpoint, parameters: parameters)
            .mapError { error in
                // Just return the error directly, no need for conditional cast
                return APIError.custom(error.localizedDescription)
            }
            .eraseToAnyPublisher()
    }

    /// Generate mock team squad data for preview and testing
    /// - Parameter teamId: The ID to use for the mock team
    /// - Returns: A mock TeamSquad object
    static func mockTeamSquad(teamId: Int = 33) -> TeamSquad {
        return TeamSquad(
            team: TeamSquad.TeamInfo(
                id: teamId,
                name: "Manchester United",
                logo: "https://media.api-sports.io/football/teams/33.png"
            ),
            players: [
                PlayerSquad(
                    player: PlayerSquad.PlayerInfo(
                        id: 882,
                        name: "David de Gea",
                        age: 32,
                        number: 1,
                        position: "Goalkeeper",
                        photo: "https://media.api-sports.io/football/players/882.png"
                    )
                ),
                PlayerSquad(
                    player: PlayerSquad.PlayerInfo(
                        id: 883,
                        name: "Harry Maguire",
                        age: 30,
                        number: 5,
                        position: "Defender",
                        photo: "https://media.api-sports.io/football/players/883.png"
                    )
                ),
                PlayerSquad(
                    player: PlayerSquad.PlayerInfo(
                        id: 884,
                        name: "Bruno Fernandes",
                        age: 28,
                        number: 8,
                        position: "Midfielder",
                        photo: "https://media.api-sports.io/football/players/884.png"
                    )
                ),
                PlayerSquad(
                    player: PlayerSquad.PlayerInfo(
                        id: 885,
                        name: "Marcus Rashford",
                        age: 25,
                        number: 10,
                        position: "Forward",
                        photo: "https://media.api-sports.io/football/players/885.png"
                    )
                )
            ]
        )
    }
}

// MARK: - Async Version
extension TeamService {
    /// Fetch team details by ID using async/await
    /// - Parameters:
    ///   - teamId: The ID of the team to fetch
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: The team data
    func fetchTeamById(teamId: Int, forceRefresh: Bool = false) async throws -> TeamData {
        let endpoint = "/teams"
        let parameters = ["id": String(teamId)]

        // Get the raw data from the API
        let (data, _) = try await AsyncAPIService.shared.fetchRawData(
            endpoint: endpoint,
            parameters: parameters,
            forceRefresh: forceRefresh
        )

        // Try to decode as APITeamResponse first
        do {
            let decoder = JSONDecoder()
            let apiTeamResponse = try decoder.decode(APITeamResponse.self, from: data)
            return apiTeamResponse.toTeamData()
        } catch {
            // If that fails, try TeamData directly
            do {
                let decoder = JSONDecoder()
                let teamData = try decoder.decode(TeamData.self, from: data)
                return teamData
            } catch {
                // If that fails, try TeamResponse
                do {
                    let decoder = JSONDecoder()
                    let teamResponse = try decoder.decode(TeamResponse.self, from: data)
                    guard let teamData = teamResponse.response.first else {
                        throw APIError.custom("Team not found")
                    }
                    return teamData
                } catch {
                    // If all decoding attempts fail, throw a detailed error
                    throw APIError.custom("Failed to decode team data: \(error.localizedDescription)")
                }
            }
        }
    }

    /// Fetch team squad by team ID using async/await
    /// - Parameters:
    ///   - teamId: The ID of the team to fetch squad for
    ///   - forceRefresh: Whether to force a refresh from the network
    /// - Returns: The team squad data
    func fetchTeamSquad(teamId: Int, forceRefresh: Bool = false) async throws -> PlayerSquadResponse {
        let endpoint = "/players/squads"
        let parameters = ["team": String(teamId)]

        do {
            // Try to decode as PlayerSquadResponse
            return try await AsyncAPIService.shared.fetchData(
                endpoint: endpoint,
                parameters: parameters,
                forceRefresh: forceRefresh
            )
        } catch {
            // If decoding fails, check if it's an empty array
            let (data, _) = try await AsyncAPIService.shared.fetchRawData(
                endpoint: endpoint,
                parameters: parameters,
                forceRefresh: forceRefresh
            )

            // Check if the response is an empty array
            if let jsonString = String(data: data, encoding: .utf8), jsonString == "[]" {
                print("Received empty array for team squad, returning empty response")
                // Return an empty response using the custom initializer
                return PlayerSquadResponse(emptyResponse: true)
            } else {
                // If it's not an empty array, rethrow the original error
                throw error
            }
        }
    }
}