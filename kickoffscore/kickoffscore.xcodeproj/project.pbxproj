// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		2012E0862DC5763F00B2B486 /* Kingfisher in Frameworks */ = {isa = PBXBuildFile; productRef = 2012E0852DC5763F00B2B486 /* Kingfisher */; };
		2012E0892DC5764A00B2B486 /* KeychainSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 2012E0882DC5764A00B2B486 /* KeychainSwift */; };
		2012E31B2DC745F300B2B486 /* SocketIO in Frameworks */ = {isa = PBXBuildFile; productRef = 2012E31A2DC745F300B2B486 /* SocketIO */; };
		BCB283F72DCD0E9400F8B38F /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = BCB283F62DCD0E9400F8B38F /* GoogleSignIn */; };
		BCB283F92DCD0E9400F8B38F /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = BCB283F82DCD0E9400F8B38F /* GoogleSignInSwift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		20820D4D2DB7F9FF00EA9D2E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 20820D372DB7F9FD00EA9D2E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 20820D3E2DB7F9FD00EA9D2E;
			remoteInfo = kickoffscore;
		};
		20820D572DB7F9FF00EA9D2E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 20820D372DB7F9FD00EA9D2E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 20820D3E2DB7F9FD00EA9D2E;
			remoteInfo = kickoffscore;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		20820D3F2DB7F9FD00EA9D2E /* kickoffscore.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = kickoffscore.app; sourceTree = BUILT_PRODUCTS_DIR; };
		20820D4C2DB7F9FF00EA9D2E /* kickoffscoreTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = kickoffscoreTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		20820D562DB7F9FF00EA9D2E /* kickoffscoreUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = kickoffscoreUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		20820D412DB7F9FD00EA9D2E /* kickoffscore */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = kickoffscore;
			sourceTree = "<group>";
		};
		20820D4F2DB7F9FF00EA9D2E /* kickoffscoreTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = kickoffscoreTests;
			sourceTree = "<group>";
		};
		20820D592DB7F9FF00EA9D2E /* kickoffscoreUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = kickoffscoreUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		20820D3C2DB7F9FD00EA9D2E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BCB283F92DCD0E9400F8B38F /* GoogleSignInSwift in Frameworks */,
				2012E0892DC5764A00B2B486 /* KeychainSwift in Frameworks */,
				BCB283F72DCD0E9400F8B38F /* GoogleSignIn in Frameworks */,
				2012E0862DC5763F00B2B486 /* Kingfisher in Frameworks */,
				2012E31B2DC745F300B2B486 /* SocketIO in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20820D492DB7F9FF00EA9D2E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20820D532DB7F9FF00EA9D2E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		20820D362DB7F9FD00EA9D2E = {
			isa = PBXGroup;
			children = (
				20820D412DB7F9FD00EA9D2E /* kickoffscore */,
				20820D4F2DB7F9FF00EA9D2E /* kickoffscoreTests */,
				20820D592DB7F9FF00EA9D2E /* kickoffscoreUITests */,
				20820D402DB7F9FD00EA9D2E /* Products */,
			);
			sourceTree = "<group>";
		};
		20820D402DB7F9FD00EA9D2E /* Products */ = {
			isa = PBXGroup;
			children = (
				20820D3F2DB7F9FD00EA9D2E /* kickoffscore.app */,
				20820D4C2DB7F9FF00EA9D2E /* kickoffscoreTests.xctest */,
				20820D562DB7F9FF00EA9D2E /* kickoffscoreUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		20820D3E2DB7F9FD00EA9D2E /* kickoffscore */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 20820D602DB7F9FF00EA9D2E /* Build configuration list for PBXNativeTarget "kickoffscore" */;
			buildPhases = (
				20820D3B2DB7F9FD00EA9D2E /* Sources */,
				20820D3C2DB7F9FD00EA9D2E /* Frameworks */,
				20820D3D2DB7F9FD00EA9D2E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				20820D412DB7F9FD00EA9D2E /* kickoffscore */,
			);
			name = kickoffscore;
			packageProductDependencies = (
				2012E0852DC5763F00B2B486 /* Kingfisher */,
				2012E0882DC5764A00B2B486 /* KeychainSwift */,
				2012E31A2DC745F300B2B486 /* SocketIO */,
				BCB283F62DCD0E9400F8B38F /* GoogleSignIn */,
				BCB283F82DCD0E9400F8B38F /* GoogleSignInSwift */,
			);
			productName = kickoffscore;
			productReference = 20820D3F2DB7F9FD00EA9D2E /* kickoffscore.app */;
			productType = "com.apple.product-type.application";
		};
		20820D4B2DB7F9FF00EA9D2E /* kickoffscoreTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 20820D632DB7F9FF00EA9D2E /* Build configuration list for PBXNativeTarget "kickoffscoreTests" */;
			buildPhases = (
				20820D482DB7F9FF00EA9D2E /* Sources */,
				20820D492DB7F9FF00EA9D2E /* Frameworks */,
				20820D4A2DB7F9FF00EA9D2E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				20820D4E2DB7F9FF00EA9D2E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				20820D4F2DB7F9FF00EA9D2E /* kickoffscoreTests */,
			);
			name = kickoffscoreTests;
			packageProductDependencies = (
			);
			productName = kickoffscoreTests;
			productReference = 20820D4C2DB7F9FF00EA9D2E /* kickoffscoreTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		20820D552DB7F9FF00EA9D2E /* kickoffscoreUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 20820D662DB7F9FF00EA9D2E /* Build configuration list for PBXNativeTarget "kickoffscoreUITests" */;
			buildPhases = (
				20820D522DB7F9FF00EA9D2E /* Sources */,
				20820D532DB7F9FF00EA9D2E /* Frameworks */,
				20820D542DB7F9FF00EA9D2E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				20820D582DB7F9FF00EA9D2E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				20820D592DB7F9FF00EA9D2E /* kickoffscoreUITests */,
			);
			name = kickoffscoreUITests;
			packageProductDependencies = (
			);
			productName = kickoffscoreUITests;
			productReference = 20820D562DB7F9FF00EA9D2E /* kickoffscoreUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		20820D372DB7F9FD00EA9D2E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					20820D3E2DB7F9FD00EA9D2E = {
						CreatedOnToolsVersion = 16.3;
					};
					20820D4B2DB7F9FF00EA9D2E = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 20820D3E2DB7F9FD00EA9D2E;
					};
					20820D552DB7F9FF00EA9D2E = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 20820D3E2DB7F9FD00EA9D2E;
					};
				};
			};
			buildConfigurationList = 20820D3A2DB7F9FD00EA9D2E /* Build configuration list for PBXProject "kickoffscore" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 20820D362DB7F9FD00EA9D2E;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				2012E0842DC5763F00B2B486 /* XCRemoteSwiftPackageReference "Kingfisher" */,
				2012E0872DC5764A00B2B486 /* XCRemoteSwiftPackageReference "keychain-swift" */,
				2012E3192DC745F300B2B486 /* XCRemoteSwiftPackageReference "socket" */,
				BCB283F52DCD0E9400F8B38F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 20820D402DB7F9FD00EA9D2E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				20820D3E2DB7F9FD00EA9D2E /* kickoffscore */,
				20820D4B2DB7F9FF00EA9D2E /* kickoffscoreTests */,
				20820D552DB7F9FF00EA9D2E /* kickoffscoreUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		20820D3D2DB7F9FD00EA9D2E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20820D4A2DB7F9FF00EA9D2E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20820D542DB7F9FF00EA9D2E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		20820D3B2DB7F9FD00EA9D2E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20820D482DB7F9FF00EA9D2E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20820D522DB7F9FF00EA9D2E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		20820D4E2DB7F9FF00EA9D2E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 20820D3E2DB7F9FD00EA9D2E /* kickoffscore */;
			targetProxy = 20820D4D2DB7F9FF00EA9D2E /* PBXContainerItemProxy */;
		};
		20820D582DB7F9FF00EA9D2E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 20820D3E2DB7F9FD00EA9D2E /* kickoffscore */;
			targetProxy = 20820D572DB7F9FF00EA9D2E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		20820D5E2DB7F9FF00EA9D2E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 5NEAGT64RQ;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		20820D5F2DB7F9FF00EA9D2E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 5NEAGT64RQ;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		20820D612DB7F9FF00EA9D2E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = kickoffscore/kickoffscore.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5NEAGT64RQ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIBackgroundModes = "fetch processing";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kickoffscore.kickoffscore;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		20820D622DB7F9FF00EA9D2E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = kickoffscore/kickoffscore.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5NEAGT64RQ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIBackgroundModes = "fetch processing";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kickoffscore.kickoffscore;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		20820D642DB7F9FF00EA9D2E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5NEAGT64RQ;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kickoffscore.kickoffscoreTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/kickoffscore.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/kickoffscore";
			};
			name = Debug;
		};
		20820D652DB7F9FF00EA9D2E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5NEAGT64RQ;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kickoffscore.kickoffscoreTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/kickoffscore.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/kickoffscore";
			};
			name = Release;
		};
		20820D672DB7F9FF00EA9D2E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5NEAGT64RQ;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kickoffscore.kickoffscoreUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = kickoffscore;
			};
			name = Debug;
		};
		20820D682DB7F9FF00EA9D2E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5NEAGT64RQ;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kickoffscore.kickoffscoreUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = kickoffscore;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		20820D3A2DB7F9FD00EA9D2E /* Build configuration list for PBXProject "kickoffscore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				20820D5E2DB7F9FF00EA9D2E /* Debug */,
				20820D5F2DB7F9FF00EA9D2E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		20820D602DB7F9FF00EA9D2E /* Build configuration list for PBXNativeTarget "kickoffscore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				20820D612DB7F9FF00EA9D2E /* Debug */,
				20820D622DB7F9FF00EA9D2E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		20820D632DB7F9FF00EA9D2E /* Build configuration list for PBXNativeTarget "kickoffscoreTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				20820D642DB7F9FF00EA9D2E /* Debug */,
				20820D652DB7F9FF00EA9D2E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		20820D662DB7F9FF00EA9D2E /* Build configuration list for PBXNativeTarget "kickoffscoreUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				20820D672DB7F9FF00EA9D2E /* Debug */,
				20820D682DB7F9FF00EA9D2E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		2012E0842DC5763F00B2B486 /* XCRemoteSwiftPackageReference "Kingfisher" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/onevcat/Kingfisher.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.3.2;
			};
		};
		2012E0872DC5764A00B2B486 /* XCRemoteSwiftPackageReference "keychain-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/evgenyneu/keychain-swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 24.0.0;
			};
		};
		2012E3192DC745F300B2B486 /* XCRemoteSwiftPackageReference "socket" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/socketio/socket.io-client-swift";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		BCB283F52DCD0E9400F8B38F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		2012E0852DC5763F00B2B486 /* Kingfisher */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2012E0842DC5763F00B2B486 /* XCRemoteSwiftPackageReference "Kingfisher" */;
			productName = Kingfisher;
		};
		2012E0882DC5764A00B2B486 /* KeychainSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2012E0872DC5764A00B2B486 /* XCRemoteSwiftPackageReference "keychain-swift" */;
			productName = KeychainSwift;
		};
		2012E31A2DC745F300B2B486 /* SocketIO */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2012E3192DC745F300B2B486 /* XCRemoteSwiftPackageReference "socket" */;
			productName = SocketIO;
		};
		BCB283F62DCD0E9400F8B38F /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = BCB283F52DCD0E9400F8B38F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		BCB283F82DCD0E9400F8B38F /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = BCB283F52DCD0E9400F8B38F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 20820D372DB7F9FD00EA9D2E /* Project object */;
}
