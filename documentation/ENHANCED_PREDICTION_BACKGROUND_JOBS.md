# Enhanced Prediction Background Jobs

This document describes the automated background jobs implemented for the Dixon-Coles enhanced prediction system.

## Overview

The background jobs transform the prediction system from **reactive** (calculate on request) to **proactive** (pre-calculate everything), providing:

- **Faster API responses** (milliseconds instead of seconds)
- **Better reliability** (predictions always available)
- **Automatic updates** (models stay current with new match data)
- **Scalable architecture** (handles high load efficiently)

## Jobs Implemented

### 1. Team Strength Training Job

**Function:** `trainDixonColesModelsForActiveLeagues()`  
**Schedule:** Daily at 1:00 AM  
**Purpose:** Calculate and cache team strengths and league parameters for all active leagues

#### What it does:
- Identifies active leagues with prediction coverage
- Fetches 18 months of historical match data for each league
- Calculates Dixon-Coles team strengths using Maximum Likelihood Estimation
- Stores results in `team_strengths` and `league_parameters` collections
- Processes leagues in batches to avoid system overload

#### Performance:
- Processes ~5 leagues per batch
- 1-second delay between leagues
- 3-second delay between batches
- Skips leagues with recent calculations (< 6 hours old)

### 2. Enhanced Prediction Generation Job

**Function:** `generateEnhancedPredictionsForUpcomingFixtures(days)`  
**Schedule:** Daily at 4:00 AM (after model training)  
**Purpose:** Generate enhanced predictions for upcoming fixtures

#### What it does:
- Finds upcoming fixtures in leagues with prediction coverage
- Generates Dixon-Coles predictions for next 7 days
- Includes correct scores, BTTS, corner predictions, and card predictions
- Stores results in `enhanced_predictions_v2` collection
- Caches predictions in Redis for 6 hours

#### Performance:
- Processes 10 fixtures per batch
- 200ms delay between predictions
- 2-second delay between batches
- Skips fixtures with recent predictions (< 6 hours old)

### 3. Model Refresh Job

**Function:** `refreshDixonColesModels()`  
**Schedule:** Weekly on Sunday at 2:00 AM  
**Purpose:** Retrain models for leagues with recent match results

#### What it does:
- Identifies leagues with ≥3 matches in the last 7 days
- Forces recalculation by clearing existing cache
- Recalculates team strengths with latest match data
- Invalidates related prediction caches
- Ensures models stay current throughout the season

#### Performance:
- 2-second delay between leagues
- 3-second delay after errors
- Processes only leagues with significant recent activity

## Database Collections

### team_strengths
```javascript
{
  _id: "teamId_leagueId",
  teamId: number,
  leagueId: number,
  strength: {
    attack: number,
    defense: number
  },
  statistics: {
    matchesPlayed: number,
    // ... other stats
  },
  lastUpdated: Date
}
```

### league_parameters
```javascript
{
  _id: leagueId,
  leagueId: number,
  parameters: {
    homeAdvantage: number,
    averageGoalsPerGame: number,
    rho: number
  },
  statistics: {
    matchesAnalyzed: number,
    // ... other stats
  },
  lastUpdated: Date
}
```

### enhanced_predictions_v2
```javascript
{
  _id: fixtureId,
  predictions: {
    correctScore: { /* Dixon-Coles scores */ },
    bothTeamsToScore: { /* BTTS predictions */ },
    matchOutcome: { /* Home/Draw/Away */ },
    corners: { /* Corner predictions */ },
    cards: { /* Card predictions */ }
  },
  metadata: {
    algorithm: "dixon-coles",
    confidence: number,
    processingTime: number
  },
  lastUpdated: Date
}
```

## Caching Strategy

### Redis Caching
- **Enhanced predictions:** 6 hours TTL
- **Failure cache:** 10 minutes TTL (prevents repeated expensive calculations)
- **Cache keys:** `enhanced_prediction:fixtureId`

### Database Caching
- **Team strengths:** 6 hours before recalculation
- **League parameters:** 6 hours before recalculation
- **Predictions:** 6 hours before regeneration

## Error Handling

### Individual Item Failures
- Continue processing other items
- Log detailed error information
- Add longer delays after errors
- Track failure counts for monitoring

### Batch Operation Failures
- Wrap bulk operations in try-catch
- Log batch-level errors
- Continue with next batch
- Maintain operation counts

### Job-Level Failures
- Catch at top level
- Log comprehensive error details
- Don't crash the scheduler
- Allow next scheduled run

## Monitoring and Logging

### Job Completion Logs
```
trainDixonColesModelsForActiveLeagues job completed.
Total leagues processed: 45
Total leagues updated: 38
Total leagues skipped: 7
```

### Prediction Generation Logs
```
generateEnhancedPredictionsForUpcomingFixtures job completed.
Total fixtures processed: 234
Total predictions generated: 198
Total fixtures skipped: 28
Total failures: 8
```

### Model Refresh Logs
```
refreshDixonColesModels job completed.
Total leagues processed: 12
Total leagues refreshed: 10
```

## Performance Impact

### Before Background Jobs
- **API Response Time:** 5-15 seconds per prediction
- **Database Load:** Heavy aggregations on every request
- **User Experience:** Slow, unreliable
- **Scalability:** Limited by calculation time

### After Background Jobs
- **API Response Time:** 50-200 milliseconds per prediction
- **Database Load:** Simple document lookups
- **User Experience:** Fast, reliable
- **Scalability:** Handles thousands of concurrent requests

## Configuration

### Timing Schedule
```javascript
// Model Training: Daily at 1:00 AM
cron.schedule('0 1 * * *', trainDixonColesModelsForActiveLeagues);

// Prediction Generation: Daily at 4:00 AM
cron.schedule('0 4 * * *', generateEnhancedPredictionsForUpcomingFixtures);

// Model Refresh: Weekly on Sunday at 2:00 AM
cron.schedule('0 2 * * 0', refreshDixonColesModels);
```

### Batch Sizes
- **League processing:** 5 leagues per batch
- **Fixture processing:** 10 fixtures per batch
- **Delays:** 200ms-3000ms between operations

## Testing

Run the test suite to verify job functionality:

```bash
npm run test:enhanced-prediction-jobs
```

The test script verifies:
- Database connectivity
- Prediction generation capability
- Model refresh functionality
- Collection accessibility

## Deployment Notes

1. **Initial Setup:** Run model training job manually for immediate results
2. **Monitoring:** Watch logs for job completion and error rates
3. **Performance:** Monitor database and Redis performance during job runs
4. **Scaling:** Adjust batch sizes and delays based on system performance

## Future Enhancements

1. **League-specific optimization:** Implement xi parameter optimization per league
2. **Ensemble methods:** Add multiple prediction algorithms
3. **Real-time updates:** Trigger model updates when new matches finish
4. **Performance monitoring:** Add detailed metrics and alerting
5. **Adaptive scheduling:** Adjust job frequency based on league activity
