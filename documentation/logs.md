[15:23:44.813] Running build in Washington, D.C., USA (East) – iad1
[15:23:44.814] Build machine configuration: 2 cores, 8 GB
[15:23:44.826] Retrieving list of deployment files...
[15:23:44.829] Skipping build cache, deployment was triggered without cache.
[15:23:45.154] Downloading 63 deployment files...
[15:23:46.088] Running "vercel build"
[15:23:47.195] Vercel CLI 44.6.4
[15:23:47.999] Running "install" command: `npm install`...
[15:24:02.529] 
[15:24:02.530] added 391 packages, and audited 392 packages in 14s
[15:24:02.531] 
[15:24:02.531] 146 packages are looking for funding
[15:24:02.531]   run `npm fund` for details
[15:24:02.531] 
[15:24:02.532] found 0 vulnerabilities
[15:24:02.586] Detected Next.js version: 15.4.4
[15:24:02.587] Running "npm run build"
[15:24:02.699] 
[15:24:02.702] > frontend@0.1.0 build
[15:24:02.702] > next build
[15:24:02.702] 
[15:24:03.508] Attention: Next.js now collects completely anonymous telemetry regarding usage.
[15:24:03.509] This information is used to shape Next.js' roadmap and prioritize features.
[15:24:03.509] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[15:24:03.510] https://nextjs.org/telemetry
[15:24:03.510] 
[15:24:03.613]    ▲ Next.js 15.4.4
[15:24:03.613]    - Experiments (use with caution):
[15:24:03.614]      · optimizePackageImports
[15:24:03.614] 
[15:24:03.648]    Creating an optimized production build ...
[15:24:26.089]  ⚠ Compiled with warnings in 9.0s
[15:24:26.089] 
[15:24:26.090] ./node_modules/framer-motion/dist/cjs/feature-bundle-DrbfNaqu.js
[15:24:26.090] Module not found: Can't resolve '@emotion/is-prop-valid' in '/vercel/path0/node_modules/framer-motion/dist/cjs'
[15:24:26.090] 
[15:24:26.090] Import trace for requested module:
[15:24:26.090] ./node_modules/framer-motion/dist/cjs/feature-bundle-DrbfNaqu.js
[15:24:26.090] ./node_modules/framer-motion/dist/cjs/index.js
[15:24:26.090] ./src/components/LazyComponents.tsx
[15:24:26.091] ./src/app/page.tsx
[15:24:26.091] 
[15:24:26.131]  ✓ Compiled successfully in 19.0s
[15:24:26.134]    Linting and checking validity of types ...
[15:24:36.638]    Collecting page data ...
[15:24:38.827]    Generating static pages (0/5) ...
[15:24:39.848]    Generating static pages (1/5) 
[15:24:39.849]    Generating static pages (2/5) 
[15:24:39.850]    Generating static pages (3/5) 
[15:24:39.850]  ✓ Generating static pages (5/5)
[15:24:40.402]    Finalizing page optimization ...
[15:24:40.404]    Collecting build traces ...
[15:24:47.072] 
[15:24:47.083] Route (app)                                 Size  First Load JS
[15:24:47.083] ┌ ○ /                                    49.8 kB         187 kB
[15:24:47.084] ├ ○ /_not-found                            995 B         101 kB
[15:24:47.084] └ ƒ /match/[...slug]                     13.6 kB         150 kB
[15:24:47.084] + First Load JS shared by all             100 kB
[15:24:47.084]   ├ chunks/4bd1b696-9911af18dede28aa.js  54.1 kB
[15:24:47.084]   ├ chunks/964-004b34719833a9b4.js       43.7 kB
[15:24:47.085]   └ other shared chunks (total)          2.15 kB
[15:24:47.085] 
[15:24:47.085] 
[15:24:47.085] ○  (Static)   prerendered as static content
[15:24:47.085] ƒ  (Dynamic)  server-rendered on demand
[15:24:47.085] 
[15:24:47.195] Traced Next.js server files in: 59.203ms
[15:24:47.456] Created all serverless functions in: 261.24ms
[15:24:47.471] Collected static files (public/, static/, .next/static): 11.053ms
[15:24:47.545] Build Completed in /vercel/output [60s]
[15:24:47.628] Deploying outputs...
[15:24:54.277] 
[15:24:54.403] Deployment completed
[15:25:10.602] Uploading build cache [184.43 MB]...