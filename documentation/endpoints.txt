# API-Football v3 Documentation Summary (Direct API-Sports Subscription - Based on v3.9.3)

## Changelog 3.9.3

*   **Endpoint `fixtures`**:
    *   Add field `extra` that returns the additional time played in a half.
*   **Endpoint `fixtures/rounds`**:
    *   Add the `dates` parameter that allows to retrieve the dates of each round in the response.
*   **Endpoint `fixtures/statistics`**:
    *   Add the `half` parameter that allows to retrieve the halftime statistics in the response.
*   **Endpoint `injuries`**:
    *   Add the `ids` parameter that allows to retrieve data from several fixtures in one call.
*   **Endpoint `teams/statistics`**:
    *   More statistics added: Goals Over, Goals Under.
*   **New Endpoint `players/profiles`**:
    *   Returns the list of all available players.
*   **New Endpoint `players/teams`**:
    *   Returns the list of teams and seasons in which the player played during his career.

## General Backend Integration Tips (API-Sports Subscription)

*   **Authentication**: Use your API key obtained from the [API-Football Dashboard](https://dashboard.api-football.com/) in the request header.
    *   Header Name: `x-apisports-key`
    *   **Important:** You also *still need* the `x-rapidapi-host` header with the value `v3.football.api-sports.io`. Although the name seems related to RapidAPI, it's required for routing even with direct subscriptions.
*   **Request Method**: Use **GET** requests *only*. Non-GET requests will result in errors.
*   **Headers**: Only send the required `x-apisports-key` and `x-rapidapi-host` headers. Some frameworks add extra headers automatically; ensure these are removed.
*   **API URL**: Use the base URL: `https://v3.football.api-sports.io/`
*   **Rate Limits**: Be mindful of your subscription's rate limits (daily and per minute). Monitor the following response headers:
    *   `x-ratelimit-requests-limit`: Daily request limit allocated by your subscription.
    *   `x-ratelimit-requests-remaining`: Remaining requests in the daily limit.
    *   `X-RateLimit-Limit`: Maximum calls allowed per minute.
    *   `X-RateLimit-Remaining`: Remaining calls for the current minute.
*   **Status Check**: Use the `/status` endpoint (`https://v3.football.api-sports.io/status`) to check your account status, subscription details, and current usage without consuming your daily quota. This is highly recommended for monitoring.
*   **Caching**: Cache responses on your server, especially for endpoints with low update frequency (like `timezone`, `countries`, `leagues/seasons`), to reduce API calls and stay within limits. Use the "Recommended Calls" frequency as a guideline for cache duration.
*   **Pagination**: Some endpoints (`players/profiles`, `players`, `odds`, `odds/mapping`) return paginated results. Use the `page` query parameter to navigate through results. Check the `paging` object in the response for total pages.
*   **Media Assets**: Use the provided media URLs for flags, logos, and photos to offload delivery: `https://media.api-sports.io/...`
*   **Timezones**: Use the `/timezone` endpoint to get valid timezone strings for filtering fixture times. Apply the desired timezone using the `timezone` query parameter in relevant fixture endpoints.
*   **Error Handling**: Implement proper error handling for potential API errors (e.g., 4xx client errors like invalid parameters or exceeding quota, 5xx server errors). Check the `errors` array in the API response.

---

Fixtures

For all requests to fixtures you can add the query parameter timezone to your request in order to retrieve the list of matches in the time zone of your choice like “Europe/London“

To know the list of available time zones you have to use the endpoint timezone.

Available fixtures status
SHORT 	LONG 	TYPE 	DESCRIPTION
TBD 	Time To Be Defined 	Scheduled 	Scheduled but date and time are not known
NS 	Not Started 	Scheduled 	
1H 	First Half, Kick Off 	In Play 	First half in play
HT 	Halftime 	In Play 	Finished in the regular time
2H 	Second Half, 2nd Half Started 	In Play 	Second half in play
ET 	Extra Time 	In Play 	Extra time in play
BT 	Break Time 	In Play 	Break during extra time
P 	Penalty In Progress 	In Play 	Penaly played after extra time
SUSP 	Match Suspended 	In Play 	Suspended by referee's decision, may be rescheduled another day
INT 	Match Interrupted 	In Play 	Interrupted by referee's decision, should resume in a few minutes
FT 	Match Finished 	Finished 	Finished in the regular time
AET 	Match Finished 	Finished 	Finished after extra time without going to the penalty shootout
PEN 	Match Finished 	Finished 	Finished after the penalty shootout
PST 	Match Postponed 	Postponed 	Postponed to another day, once the new date and time is known the status will change to Not Started
CANC 	Match Cancelled 	Cancelled 	Cancelled, match will not be played
ABD 	Match Abandoned 	Abandoned 	Abandoned for various reasons (Bad Weather, Safety, Floodlights, Playing Staff Or Referees), Can be rescheduled or not, it depends on the competition
AWD 	Technical Loss 	Not Played 	
WO 	WalkOver 	Not Played 	Victory by forfeit or absence of competitor
LIVE 	In Progress 	In Play 	Used in very rare cases. It indicates a fixture in progress but the data indicating the half-time or elapsed time are not available

Fixtures with the status TBD may indicate an incorrect fixture date or time because the fixture date or time is not yet known or final. Fixtures with this status are checked and updated daily. The same applies to fixtures with the status PST, CANC.

The fixtures ids are unique and specific to each fixture. In no case an ID will change.

Not all competitions have livescore available and only have final result. In this case, the status remains in NS and will be updated in the minutes/hours following the match (this can take up to 48 hours, depending on the competition).

    Although the data is updated every 15 seconds, depending on the competition there may be a delay between reality and the availability of data in the API.

Update Frequency : This endpoint is updated every 15 seconds.

Recommended Calls : 1 call per minute for the leagues, teams, fixtures who have at least one fixture in progress otherwise 1 call per day.

## API Endpoints

**Note:** All endpoints require the `x-apisports-key` and `x-rapidapi-host: v3.football.api-sports.io` headers for authentication.

### Timezone

*   **Endpoint:** `/timezone`
*   **Description:** Get the list of available timezone strings to use in fixture-related endpoints.
*   **Update Frequency:** Not updated (contains standard timezone identifiers).
*   **Recommended Calls:** 1 call when needed.
*   **Query Parameters:** None.
*   **Use Cases:** Fetching the list of valid timezones to present to a user or use in subsequent fixture calls.

### Countries

*   **Endpoint:** `/countries`
*   **Description:** Get the list of available countries. Country codes can be used to retrieve flags (`https://media.api-sports.io/flags/{country_code}.svg`).
*   **Update Frequency:** Updated when new countries/leagues are added.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `name` (string): Filter by country name.
    *   `code` (string [2..6 chars]): Filter by country alpha code (e.g., `FR`, `GB`).
    *   `search` (string >= 3 chars): Search by country name.
*   **Use Cases:** Getting all available countries; searching for a specific country by name (e.g., `England`) or code (`GB`). Displaying country flags.

### Leagues

*   **Endpoint:** `/leagues`
*   **Description:** Get the list of available leagues and cups, including coverage details (what data points like events, lineups, stats, odds are available for that league/season). League logos: `https://media.api-sports.io/football/leagues/{league_id}.png`. Coverage indicates current availability and may change as a competition progresses.
*   **Update Frequency:** Several times a day.
*   **Recommended Calls:** 1 call per hour.
*   **Query Parameters:**
    *   `id` (integer): Filter by league ID.
    *   `name` (string): Filter by league name.
    *   `country` (string): Filter by country name.
    *   `code` (string [2..6 chars]): Filter by country alpha code.
    *   `season` (integer, YYYY): Filter by season year.
    *   `team` (integer): Filter leagues a specific team participates in.
    *   `type` (string, enum: "league", "cup"): Filter by league type.
    *   `current` (string, enum: "true", "false"): Filter for currently active leagues/seasons.
    *   `search` (string >= 3 chars): Search by league name or country.
    *   `last` (integer <= 99): Get the X most recently added leagues/cups.
*   **Use Cases:** Fetching all leagues; finding a specific league by ID (`39`), name (`Premier League`), or country (`England`); getting all leagues for a specific season (`2023`); finding leagues a specific team plays in (`team=33`); getting only currently active leagues (`current=true`); listing cup competitions (`type=cup`). Checking data coverage for a league.

*   **Endpoint:** `/leagues/seasons`
*   **Description:** Get the list of all available seasons (years) across all leagues in the API.
*   **Update Frequency:** Updated when new leagues/seasons are added.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:** None.
*   **Use Cases:** Getting a list of all historical and current seasons covered by the API to populate season selection dropdowns.

### Teams
*   **Endpoint:** `/teams`
*   **Description:** Get team information. Requires at least one parameter. Team logos: `https://media.api-sports.io/football/teams/{team_id}.png`.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `id` (integer): Filter by team ID.
    *   `name` (string): Filter by team name.
    *   `league` (integer): Filter teams by league ID.
    *   `season` (integer, YYYY): Filter teams by season (requires `league` or `id`).
    *   `country` (string): Filter by team's country name.
    *   `code` (string, 3 chars): Filter by team's 3-letter code.
    *   `venue` (integer): Filter by venue ID.
    *   `search` (string >= 3 chars): Search by team name or country name.
*   **Use Cases:** Finding a specific team by ID (`33`), name (`Manchester United`), or code (`MUN`); getting all teams in a specific league and season (`league=39&season=2023`); searching for teams by name (`search=liverpool`); getting team info based on their venue (`venue=556`).

*   **Endpoint:** `/teams/statistics`
*   **Description:** Get detailed statistics for a team within a specific league and season (form, goals scored/conceded, cards, lineups used, biggest wins/losses, etc.).
*   **Update Frequency:** Twice a day.
*   **Recommended Calls:** 1 call per day for teams with a match today, otherwise 1 call per week.
*   **Query Parameters:**
    *   `league` (integer): **Required**. League ID.
    *   `season` (integer, YYYY): **Required**. Season year.
    *   `team` (integer): **Required**. Team ID.
    *   `date` (string, YYYY-MM-DD): Calculate stats up to this date within the season.
*   **Use Cases:** Retrieving the season performance statistics for a specific team in a league (`league=39&season=2023&team=33`); getting stats up to a certain point in the season (`date=2023-12-31`). Analyzing team form, goal averages, clean sheets.

*   **Endpoint:** `/teams/seasons`
*   **Description:** Get the list of seasons (years) a specific team has participated in competitions covered by the API. Requires `team` parameter.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `team` (integer): **Required**. Team ID.
*   **Use Cases:** Finding out which historical seasons are available for a specific team (`team=33`).

*   **Endpoint:** `/teams/countries`
*   **Description:** Get the list of countries for which teams are available in the API.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:** None.
*   **Use Cases:** Populating a filter or understanding the geographical coverage of teams within the API.

### Venues

*   **Endpoint:** `/venues`
*   **Description:** Get venue (stadium) information. Requires at least one parameter. Venue images: `https://media.api-sports.io/football/venues/{venue_id}.png`.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `id` (integer): Filter by venue ID.
    *   `name` (string): Filter by venue name.
    *   `city` (string): Filter by city name.
    *   `country` (string): Filter by country name.
    *   `search` (string >= 3 chars): Search by venue name, city, or country.
*   **Use Cases:** Finding a specific venue by ID (`556`), name (`Old Trafford`), city (`Manchester`), or country (`England`); searching for venues (`search=wembley`).

### Standings

*   **Endpoint:** `/standings`
*   **Description:** Get league standings (tables). Can return multiple standings for leagues with different phases (e.g., groups, regular season). Requires `season`.
*   **Update Frequency:** Every hour.
*   **Recommended Calls:** 1 call per hour for leagues/teams with active fixtures, otherwise 1 call per day.
*   **Query Parameters:**
    *   `league` (integer): League ID.
    *   `season` (integer, YYYY): **Required**. Season year.
    *   `team` (integer): Filter standings to show only the specified team's standing.
*   **Use Cases:** Getting the full league table for a specific league and season (`league=39&season=2023`); retrieving the standing details specifically for one team (`league=39&season=2023&team=33`).

### Fixtures

*   **Endpoint:** `/fixtures/rounds`
*   **Description:** Get the list of rounds for a specific league and season.
*   **Update Frequency:** Every day.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `league` (integer): **Required**. League ID.
    *   `season` (integer, YYYY): **Required**. Season year.
    *   `current` (boolean, enum: "true", "false"): Get only the current round.
    *   `dates` (boolean, enum: "true", "false", default: false): Include start/end dates for each round in the response.
    *   `timezone` (string): Adjust round dates/times to the specified timezone.
*   **Use Cases:** Getting all rounds for a league season (`league=61&season=2023`); finding the name of the current round (`current=true`); getting rounds with their date ranges (`dates=true`). Using round names to filter the `/fixtures` endpoint.

*   **Endpoint:** `/fixtures`
*   **Description:** Get fixtures (matches) based on various criteria. Supports multiple fixture IDs and multiple statuses. Can retrieve live scores.
    *   **Fixture Statuses:** (List as provided in the previous response)
    *   **Available Information (especially when using `id` or `ids` parameters):** (List as provided in the previous response)
    *   **Important Note on Data Retrieval:** (As provided in the previous response - emphasizing data inclusion with `id`/`ids`)
*   **Update Frequency:** Every 15 seconds (for live data).
*   **Recommended Calls:** 1 call per minute for active leagues/teams/fixtures, otherwise 1 call per day for non-live data.
*   **Query Parameters:**
    *   `id` (integer): Get a specific fixture by ID (includes detailed data if available).
    *   `ids` (string): Get multiple fixtures by IDs (e.g., `id1-id2-id3`). **Maximum of 20 fixture IDs.** (Includes detailed data for each fixture if available).
    *   `live` (string, enum: "all", "L1-L2"): Get live fixtures (all or from specific league IDs).
    *   `date` (string, YYYY-MM-DD): Get fixtures for a specific date.
    *   `league` (integer): Get fixtures for a league.
    *   `season` (integer, YYYY): Get fixtures for a season (usually combined with `league` or `team`).
    *   `team` (integer): Get fixtures for a team.
    *   `last` (integer <= 99): Get the last X fixtures (often used with `team` or `league`).
    *   `next` (integer <= 99): Get the next X fixtures (often used with `team` or `league`).
    *   `from` (string, YYYY-MM-DD): Date range start.
    *   `to` (string, YYYY-MM-DD): Date range end.
    *   `round` (string): Filter by specific round name (from `/fixtures/rounds`).
    *   `status` (string): Filter by one or more fixture status short codes (e.g., `NS`, `FT`, `HT`, `NS-PST-FT`).
    *   `venue` (integer): Filter by venue ID.
    *   `timezone` (string): Return fixture dates/times in the specified timezone.
*   **Use Cases:** Getting all live games (`live=all`); getting today's fixtures (`date=YYYY-MM-DD`); getting all fixtures for a league/season (`league=39&season=2023`); getting the next 5 fixtures for a team (`team=33&next=5`); getting finished games (`status=FT`); getting details for one specific game including events/stats/lineups (`id=718243`); getting details for several specific games (`ids=718243-718244`).

*   **Endpoint:** `/fixtures/headtohead`
*   **Description:** Get head-to-head (H2H) fixtures between two teams.
*   **Update Frequency:** Every 15 seconds (reflects latest fixture results).
*   **Recommended Calls:** 1 call per minute for active fixtures/teams, otherwise 1 call per day.
*   **Query Parameters:**
    *   `h2h` (string, `team1ID-team2ID`): **Required**. The two team IDs.
    *   `date`, `league`, `season`, `last`, `next`, `from`, `to`, `status`, `venue`, `timezone`: Same as `/fixtures` endpoint for filtering H2H results.
*   **Use Cases:** Getting the H2H record between two teams (`h2h=33-34`); getting the last 10 H2H matches (`h2h=33-34&last=10`); filtering H2H within a specific league (`h2h=33-34&league=39`).

*   **Endpoint:** `/fixtures/statistics`
*   **Description:** Get detailed match statistics (shots, possession, fouls, cards, etc.) for a specific fixture. *(Note: This data might already be included in the `/fixtures?id={id}` or `/fixtures?ids={ids}` response).*
*   **Update Frequency:** Every minute (during the match).
*   **Recommended Calls:** 1 call per minute for active fixtures if not using `/fixtures?id=...`, otherwise 1 call per day.
*   **Query Parameters:**
    *   `fixture` (integer): **Required**. Fixture ID.
    *   `team` (integer): Filter statistics for a specific team.
    *   `type` (string): Filter by a specific statistic type (e.g., "Total Shots").
    *   `half` (boolean, enum: "true", "false", default: false): Include halftime statistics (available from 2024 season).
*   **Use Cases:** Getting full stats for a specific game (`fixture=215662`); getting stats only for the home/away team (`fixture=215662&team=463`); retrieving only a specific stat type like 'Ball Possession'. Retrieving halftime stats (`half=true`).

*   **Endpoint:** `/fixtures/events`
*   **Description:** Get events (goals, cards, substitutions, VAR decisions) for a specific fixture. *(Note: This data might already be included in the `/fixtures?id={id}` or `/fixtures?ids={ids}` response).*
*   **Update Frequency:** Every 15 seconds (during the match).
*   **Recommended Calls:** 1 call per minute for active fixtures if not using `/fixtures?id=...`, otherwise 1 call per day.
*   **Query Parameters:**
    *   `fixture` (integer): **Required**. Fixture ID.
    *   `team` (integer): Filter events for a specific team.
    *   `player` (integer): Filter events involving a specific player.
    *   `type` (string, enum: "Goal", "Card", "Subst", "Var"): Filter by event type.
*   **Use Cases:** Getting all events for a game (`fixture=215662`); finding all goals (`type=Goal`); finding events related to a specific player (`player=184`); getting events for one team only (`team=463`).

*   **Endpoint:** `/fixtures/lineups`
*   **Description:** Get lineups (coach, formation, starting XI, substitutes, player positions on grid) for a specific fixture. Usually available 20-40 mins before kickoff if covered. *(Note: This data might already be included in the `/fixtures?id={id}` or `/fixtures?ids={ids}` response).*
*   **Update Frequency:** Every 15 minutes (before and during the match).
*   **Recommended Calls:** 1 call every 15 minutes for upcoming/active fixtures if not using `/fixtures?id=...`, otherwise 1 call per day.
*   **Query Parameters:**
    *   `fixture` (integer): **Required**. Fixture ID.
    *   `team` (integer): Filter lineup for a specific team.
    *   `player` (integer): Check if a specific player is in the lineup (response format might differ).
    *   `type` (string): Possibly used for filtering specific lineup info (e.g., 'formation', documentation lacks detail here).
*   **Use Cases:** Getting the lineups for both teams (`fixture=592872`); getting the lineup for just one team (`fixture=592872&team=33`).

*   **Endpoint:** `/fixtures/players`
*   **Description:** Get player statistics *within* a specific fixture (rating, minutes played, shots, goals, passes, tackles, etc.). *(Note: This data might already be included in the `/fixtures?id={id}` or `/fixtures?ids={ids}` response).*
*   **Update Frequency:** Every minute (during the match).
*   **Recommended Calls:** 1 call per minute for active fixtures if not using `/fixtures?id=...`, otherwise 1 call per day.
*   **Query Parameters:**
    *   `fixture` (integer): **Required**. Fixture ID.
    *   `team` (integer): Filter statistics for players of a specific team.
*   **Use Cases:** Getting detailed performance stats for all players in a match (`fixture=169080`); getting stats for players of only one team (`fixture=169080&team=489`).

### Injuries

*   **Endpoint:** `/injuries`
*   **Description:** Get player injuries and suspensions for fixtures, leagues, teams, or players. Requires at least one parameter. Data available from April 2021.
*   **Update Frequency:** Every 4 hours.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `league` (integer): Filter by league ID (requires `season`).
    *   `season` (integer, YYYY): Season year (required with `league`, `team`, `player`).
    *   `fixture` (integer): Filter by fixture ID.
    *   `team` (integer): Filter by team ID (requires `season`).
    *   `player` (integer): Filter by player ID (requires `season`).
    *   `date` (string, YYYY-MM-DD): Filter injuries active on a specific date.
    *   `ids` (string): Get injuries for multiple fixtures by IDs (e.g., `id1-id2-id3`). **Maximum of 20 fixture IDs.**
    *   `timezone` (string): Adjust dates/times based on timezone.
*   **Use Cases:** Getting injuries/suspensions for an upcoming fixture (`fixture=686314`); getting all injuries for a team in a season (`team=33&season=2023`); checking injuries for a specific player (`player=184&season=2023`); finding injuries active on a specific date (`date=YYYY-MM-DD`). Getting injury lists for multiple fixtures at once (`ids=fixture1-fixture2`).

### Predictions

*   **Endpoint:** `/predictions`
*   **Description:** Get algorithmic predictions (match winner, under/over, goals, advice) and comparative stats for a specific fixture. Does not use bookmaker odds.
*   **Update Frequency:** Every hour.
*   **Recommended Calls:** 1 call per hour for upcoming/active fixtures, otherwise 1 call per day.
*   **Query Parameters:**
    *   `fixture` (integer): **Required**. Fixture ID.
*   **Use Cases:** Getting the predicted outcome and comparative stats for an upcoming match (`fixture=198772`).

### Coaches

*   **Endpoint:** `/coachs`
*   **Description:** Get coach information and career details. Coach photos: `https://media.api-sports.io/football/coachs/{coach_id}.png`.
*   **Update Frequency:** Every day.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `id` (integer): Filter by coach ID.
    *   `team` (integer): Get the coach(es) for a specific team.
    *   `search` (string >= 3 chars): Search by coach name.
*   **Use Cases:** Finding the current coach of a team (`team=85`); getting details for a specific coach by ID (`id=1`); searching for a coach by name (`search=guardiola`).

### Players

*   **Endpoint:** `/players/seasons`
*   **Description:** Get all available seasons (years) for which player statistics exist.
*   **Update Frequency:** Every day.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `player` (integer): Get seasons specifically for this player ID (optional).
*   **Use Cases:** Getting a list of all seasons where player stats are available; finding the specific seasons for which a given player has stats (`player=276`).

*   **Endpoint:** `/players/profiles`
*   **Description:** Get the list of all available player profiles (basic info like ID, name, age, nationality, photo). Supports pagination. Player photos: `https://media.api-sports.io/football/players/{player_id}.png`.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per week (or less frequently, depending on need to refresh full player list).
*   **Query Parameters:**
    *   `player` (integer): Get profile for a specific player ID.
    *   `search` (string >= 3 chars): Search by player lastname.
    *   `page` (integer, default: 1): Page number for pagination (250 results per page).
*   **Use Cases:** Retrieving the profile for a known player ID (`player=276`); searching for players by last name (`search=messi`); fetching the entire player database page by page (`page=1`, `page=2`, ...).

*   **Endpoint:** `/players` (or `/players/statistics` implicitly)
*   **Description:** Get detailed player statistics for a specific season (games played, goals, assists, cards, rating, etc.). Can filter by player, team, or league. Supports pagination.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `id` (integer): Get stats for a specific player ID (requires `season`).
    *   `team` (integer): Get stats for all players in a team (requires `season`).
    *   `league` (integer): Get stats for all players in a league (requires `season`).
    *   `season` (integer, YYYY): **Required** with `id`, `league`, or `team`. Season year.
    *   `search` (string >= 4 chars): Search player names within a `league` or `team` context (requires `league` or `team`, and `season`).
    *   `page` (integer, default: 1): Page number for pagination (20 results per page).
*   **Use Cases:** Getting the season stats for a specific player (`id=276&season=2019`); getting stats for all players in a team for a season (`team=33&season=2019`); getting stats for all players in a league for a season (`league=39&season=2019`); searching for a player's stats within a team (`team=33&season=2019&search=rashford`). Paginate through large lists (`page=2`).

*   **Endpoint:** `/players/squads`
*   **Description:** Get the current squad (list of players) for a team OR get the list of teams a specific player has been associated with. Requires `team` OR `player`.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per week.
*   **Query Parameters:**
    *   `team` (integer): Get squad for this team ID.
    *   `player` (integer): Get teams associated with this player ID.
*   **Use Cases:** Getting the list of players currently in a team's squad (`team=33`); getting the history of teams a player has played for (`player=276`).

*   **Endpoint:** `/players/teams`
*   **Description:** Get the list of teams and seasons a specific player played for during their career. Requires `player`.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per week.
*   **Query Parameters:**
    *   `player` (integer): **Required**. Player ID.
*   **Use Cases:** Retrieving a player's career history, showing which teams they played for in which seasons (`player=276`).

*   **Endpoint:** `/players/topscorers`
*   **Description:** Get the top 20 goal scorers for a specific league and season.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `league` (integer): **Required**. League ID.
    *   `season` (integer, YYYY): **Required**. Season year.
*   **Use Cases:** Displaying the top scorers chart for a league/season (`league=61&season=2018`).

*   **Endpoint:** `/players/topassists`
*   **Description:** Get the top 20 players with the most assists for a specific league and season.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `league` (integer): **Required**. League ID.
    *   `season` (integer, YYYY): **Required**. Season year.
*   **Use Cases:** Displaying the top assists chart for a league/season (`league=61&season=2020`).

*   **Endpoint:** `/players/topyellowcards`
*   **Description:** Get the top 20 players with the most yellow cards for a specific league and season.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `league` (integer): **Required**. League ID.
    *   `season` (integer, YYYY): **Required**. Season year.
*   **Use Cases:** Displaying the most yellow-carded players list for a league/season (`league=61&season=2020`).

*   **Endpoint:** `/players/topredcards`
*   **Description:** Get the top 20 players with the most red cards for a specific league and season.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `league` (integer): **Required**. League ID.
    *   `season` (integer, YYYY): **Required**. Season year.
*   **Use Cases:** Displaying the most red-carded players list for a league/season (`league=61&season=2020`).

### Transfers

*   **Endpoint:** `/transfers/players`
*   **Description:** Get player transfer history for a specific player.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `player` (integer): **Required**. Filter transfers for a specific player ID.
*   **Use Cases:** Getting the transfer history for a specific player (`player=35845`).

*   **Endpoint:** `/transfers/teams`
*   **Description:** Get player transfer history involving a specific team.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `team` (integer): **Required**. Filter transfers involving a specific team ID (both incoming and outgoing).
*   **Use Cases:** Listing all transfers (in/out) for a specific team (`team=33`).

*   **Endpoint:** `/transfers` (Legacy)
*   **Description:** Legacy endpoint for getting player transfer history. Can filter by player or team. Use the specific endpoints above instead.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `player` (integer): Filter transfers for a specific player ID.
    *   `team` (integer): Filter transfers involving a specific team ID (both incoming and outgoing).
*   **Use Cases:** Getting the transfer history for a specific player (`player=35845`); listing all transfers (in/out) for a specific team (`team=33`).

### Sidelined

*   **Endpoint:** `/sidelined`
*   **Description:** Get historical sidelined (injury/suspension) records for a player or coach. Requires `player` OR `coach`. (Different from `/injuries` which is fixture-focused).
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `player` (integer): Get sidelined history for this player ID.
    *   `coach` (integer): Get sidelined history for this coach ID.
*   **Use Cases:** Viewing the past injury/suspension history for a player (`player=276`) or coach (`coach=1`).

*   **Endpoint:** `/sidelined/teams`
*   **Description:** Get sidelined players for a specific team.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `team` (integer): **Required**. Team ID.
    *   `season` (integer, YYYY): Optional. Season year.
*   **Use Cases:** Viewing all currently injured/suspended players for a team (`team=33`).

*   **Endpoint:** `/sidelined/leagues`
*   **Description:** Get sidelined players for all teams in a specific league and season.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `league` (integer): **Required**. League ID.
    *   `season` (integer, YYYY): **Required**. Season year.
*   **Use Cases:** Viewing all currently injured/suspended players across a league (`league=39&season=2023`).

### Odds (In-Play / Live)

*   **Endpoint:** `/odds/live`
*   **Description:** Get live, in-play odds for fixtures currently in progress. Fixtures appear 5-15 mins before start and disappear 5-20 mins after end. No history kept.
*   **Update Frequency:** Every 5-60 seconds.
*   **Recommended Calls:** Depends heavily on real-time needs; potentially multiple calls per minute per active fixture.
*   **Query Parameters:**
    *   `fixture` (integer): Filter live odds for a specific fixture ID.
    *   `league` (integer): Filter live odds for fixtures in a specific league.
    *   `bet` (integer): Filter live odds for a specific bet ID (use IDs from `/odds/live/bets`).
*   **Use Cases:** Getting live odds for a specific ongoing match (`fixture=721238`); getting all currently available live odds (`/odds/live`); filtering live odds for a specific bet type (`bet=1`).

*   **Endpoint:** `/odds/live/bets`
*   **Description:** Get the list of available bet types and their IDs *specifically for in-play odds*. These IDs are used in the `/odds/live` endpoint.
*   **Update Frequency:** Every 60 seconds.
*   **Recommended Calls:** 1 call per day (or less, as live bet types change infrequently).
*   **Query Parameters:**
    *   `id` (string): Filter by specific bet ID.
    *   `search` (string >= 3 chars): Search by bet name.
*   **Use Cases:** Retrieving the list of all possible bet types available for live odds; searching for a specific type like 'Next Goal' (`search=goal`).

### Odds (Pre-Match)

*   **Endpoint:** `/odds`
*   **Description:** Get pre-match odds for fixtures. Odds available 1-14 days before match, 7-day history kept. Supports pagination.
*   **Update Frequency:** Every 3 hours.
*   **Recommended Calls:** 1 call every 3 hours for relevant upcoming fixtures.
*   **Query Parameters:**
    *   `fixture` (integer): Get odds for a specific fixture ID.
    *   `league` (integer): Get odds for fixtures in a league.
    *   `season` (integer, YYYY): Filter by season (usually with `league`).
    *   `date` (string, YYYY-MM-DD): Get odds for fixtures on a specific date.
    *   `timezone` (string): Adjust fixture dates if using `date` parameter.
    *   `page` (integer, default: 1): Page number for pagination (10 results per page).
    *   `bookmaker` (integer): Filter odds from a specific bookmaker ID (use IDs from `/odds/bookmakers`).
    *   `bet` (integer): Filter for a specific bet ID (use IDs from `/odds/bets`).
*   **Use Cases:** Getting odds for a specific fixture (`fixture=157140`); getting odds for all fixtures on a certain date (`date=YYYY-MM-DD`); filtering odds by bookmaker (`bookmaker=6`) and bet type (`bet=1`); getting odds for a whole league/season (`league=39&season=2019`).

*   **Endpoint:** `/odds/mapping`
*   **Description:** Get the list of fixture IDs for which pre-match odds are available. Useful for discovering which fixtures have odds data. Supports pagination.
*   **Update Frequency:** Every day.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `page` (integer, default: 1): Page number for pagination (100 results per page).
*   **Use Cases:** Obtaining a list of all fixture IDs that have associated pre-match odds, often used to then query the main `/odds` endpoint for those specific fixtures.

*   **Endpoint:** `/odds/bookmakers`
*   **Description:** Get the list of available bookmakers and their IDs for pre-match odds.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `id` (integer): Filter by bookmaker ID.
    *   `search` (string >= 3 chars): Search by bookmaker name.
*   **Use Cases:** Getting the list of all supported bookmakers; finding a specific bookmaker by ID (`id=6`) or name (`search=bet365`).

*   **Endpoint:** `/odds/bets`
*   **Description:** Get the list of available bet types and their IDs *specifically for pre-match odds*. These IDs are used in the `/odds` endpoint.
*   **Update Frequency:** Several times a week.
*   **Recommended Calls:** 1 call per day.
*   **Query Parameters:**
    *   `id` (string): Filter by bet ID.
    *   `search` (string >= 3 chars): Search by bet name.
*   **Use Cases:** Getting the list of all pre-match bet types available (e.g., Match Winner, Over/Under); searching for specific bet types by name (`search=under`).