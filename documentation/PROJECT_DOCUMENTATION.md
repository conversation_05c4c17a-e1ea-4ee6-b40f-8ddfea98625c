# Project Documentation: kickoffscorev2

This document provides an overview of the key components of the kickoffscorev2 backend application, including API endpoints, scheduled jobs, user authentication, database models, external integrations, and configuration.

## 1. API Endpoints

The API is structured with base paths defined in `src/server.ts` and specific routes within files in `src/routes/`.

Countries -- DOne
GET /api/countries - Get all countries
GET /api/countries?name=X - Get country by name
GET /api/countries?code=X - Get country by code
GET /api/countries?search=X - Search countries

Leagues -- Done
GET /api/leagues/seasons - Get all available seasons
GET /api/leagues - Get all leagues
GET /api/leagues?id=X - Get league by ID
GET /api/leagues?name=X - Get league by name
GET /api/leagues?country=X - Get leagues by country
GET /api/leagues?code=X - Get leagues by country code
GET /api/leagues?season=X - Get leagues by season
GET /api/leagues?team=X - Get leagues by team
GET /api/leagues?type=X - Get leagues by type (league/cup)
GET /api/leagues?current=X - Get current leagues
GET /api/leagues?search=X - Search leagues

Teams - Done
GET /api/teams - Get teams (requires at least one parameter)
GET /api/teams?id=X - Get team by ID
GET /api/teams?name=X - Get team by name
GET /api/teams?league=X&season=Y - Get teams by league and season
GET /api/teams?country=X - Get teams by country
GET /api/teams?code=X - Get teams by country code
GET /api/teams?venue=X - Get teams by venue
GET /api/teams?search=X - Search teams
GET /api/teams/statistics?league=X&season=Y&team=Z - Get team statistics

Venues -- Done
GET /api/venues?id=X - Get venue by ID
GET /api/venues?name=X - Get venue by name
GET /api/venues?city=X - Get venues by city
GET /api/venues?country=X - Get venues by country
GET /api/venues?search=X - Search venues

Standings -- Done
GET /api/standings?leagueId=X&season=Y - Get standings by league and season
GET /api/standings?leagueId=X&season=Y&team=Z - Get standings for a specific team

Fixtures - Done
GET /api/fixtures - Get fixtures (requires at least one parameter)
GET /api/fixtures?id=X - Get fixture by ID
GET /api/fixtures?ids=X-Y-Z - Get fixtures by IDS (up to 20)
GET /api/fixtures?live=all - Get all live fixtures
GET /api/fixtures?date=YYYY-MM-DD - Get fixtures by date
GET /api/fixtures?league=X&season=Y - Get fixtures by league and season
GET /api/fixtures?team=X - Get fixtures by team
GET /api/fixtures?league=X&season=Y&team=Z - Get fixtures by league, season, and team
GET /api/fixtures?from=YYYY-MM-DD&to=YYYY-MM-DD - Get fixtures between dates
GET /api/fixtures/h2h?h2h=X-Y - Get head-to-head fixtures
GET /api/fixtures/:fixtureId/statistics - Get fixture statistics
GET /api/fixtures/:fixtureId/events - Get fixture events
GET /api/fixtures/:fixtureId/lineups - Get fixture lineups
GET /api/fixtures/:fixtureId/players - Get fixture player statistics
GET /api/fixtures/rounds?league=X&season=Y - Get fixture rounds

Predictions - Done
GET /api/predictions?fixture=X - Get predictions for a fixture

Injuries - Done
GET /api/injuries?league=X&season=Y - Get injuries by league and season
GET /api/injuries?fixture=X - Get injuries by fixture
GET /api/injuries?team=X&season=Y - Get injuries by team and season
GET /api/injuries?player=X&season=Y - Get injuries by player and season
GET /api/injuries?date=YYYY-MM-DD - Get injuries by date
GET /api/injuries?ids=X-Y-Z - Get injuries by fixture IDs

Players - Done
GET /api/players - Get players (requires parameters)
GET /api/players/squads?team=X - Get player squads by team
GET /api/players/seasons?player=X - Get player seasons
GET /api/players/top?league=X&season=Y - Get top players

Coaches - Done
GET /api/coaches?id=X - Get coach by ID
GET /api/coaches?team=X - Get coach by team
GET /api/coaches?search=X - Search coaches

Transfers - Done
GET /api/transfers/teams?team=X - Get team transfers
GET /api/transfers/players?player=X - Get player transfers

Sidelined - Done
GET /api/sidelined/teams?team=X&season=Y - Get team sidelined players
GET /api/sidelined/leagues?league=X&season=Y - Get league sidelined players

Odds
GET /api/odds/bookmakers - Get all bookmakers
GET /api/odds/bets - Get all bet types
GET /api/odds?fixture=X - Get odds by fixture
GET /api/odds?league=X&season=Y - Get odds by league and season
GET /api/odds?date=YYYY-MM-DD - Get odds by date
GET /api/odds/live?fixture=X - Get live odds by fixture

**Base Routes:**

*   `GET /`: Simple welcome message.
*   `GET /health`: Health check endpoint, returns 'OK'.

**Mounted Routers:**

*   `/api/countries` (`src/routes/countryRoutes.ts`)
    *   `GET /`: Fetches all countries, sorted by name. Uses Redis caching. (Public)
*   `/api/leagues/seasons` (`src/routes/leagueSeasonRoutes.ts`)
    *   `GET /`: Fetches all available league season years. Uses Redis caching. (Public)
*   `/api/leagues` (`src/routes/leagueRoutes.ts`)
    *   `GET /`: Fetches leagues. Supports filtering via query parameters (`id`, `name`, `country`, `code`, `season`, `type`, `current`, `search`). Uses Redis caching for the unfiltered request. (Public)
*   `/api/teams/statistics` (`src/routes/teamStatisticRoutes.ts`)
    *   `GET /`: Fetches team statistics. Requires query parameters: `leagueId`, `season`, `teamId`. Uses Redis caching. (Public)
*   `/api/teams` (`src/routes/teamRoutes.ts`)
    *   `GET /`: Fetches teams. Supports filtering via query parameters (`id`, `name`, `country`, `code`, `venue`, `search`). Returns a single team object if filtered by `id`, otherwise an array. Uses Redis caching for unfiltered and by-ID requests. (Public)
    *   `GET /:teamId/seasons`: Fetches the seasons a specific team has participated in by calling the external `apiFootball` service. Uses Redis caching. (Public)
*   `/api/venues` (`src/routes/venueRoutes.ts`)
    *   `GET /`: Fetches venues. Supports filtering via query parameters (`id`, `name`, `city`, `country`, `search`). Returns a single venue object if filtered by `id`, otherwise an array. Uses Redis caching for unfiltered and by-ID requests. (Public)
*   `/api/standings` (`src/routes/standingRoutes.ts`)
    *   `GET /`: Fetches league standings. Requires query parameters: `leagueId`, `season`. Optionally accepts `teamId` to filter for a specific team's standing. Uses Redis caching. (Public)
*   `/api/fixtures/rounds` (`src/routes/fixtureRoundRoutes.ts`)
    *   `GET /`: Fetches the list of fixture round names for a specific league and season. Requires query parameters: `leagueId`, `season`. Uses Redis caching. (Public)
*   `/api/fixtures` (`src/routes/fixtureRoutes.ts`)
    *   `GET /`: Fetches fixtures with various filters (`id`, `ids`, `live`, `date`, `league`, `season`, `team`, `last`, `next`, `from`, `to`, `round`, `status`, `venue`). Uses Redis caching for specific common queries. (Public)
    *   `GET /h2h`: Fetches head-to-head fixtures. Requires `h2h` query parameter (`teamId1-teamId2`). Calls external API. Uses Redis caching. (Public)
    *   `GET /:fixtureId/statistics`: Fetches statistics for a specific fixture. Optional query parameters: `team`, `type`, `half`. Calls external API. Uses Redis caching. (Public)
    *   `GET /:fixtureId/events`: Fetches events for a specific fixture. Optional query parameters: `team`, `player`, `type`. Calls external API. Uses Redis caching. (Public)
    *   `GET /:fixtureId/lineups`: Fetches lineups for a specific fixture. Optional query parameters: `team`, `player`, `type`. Calls external API. Uses Redis caching. (Public)
    *   `GET /:fixtureId/players`: Fetches player statistics for a specific fixture. Optional query parameter: `team`. Calls external API. Uses Redis caching. (Public)
    *   `GET /rounds`: Fetches fixture rounds for a league/season. Requires `league`, `season`. Optional query parameters: `current`, `dates`, `timezone`. Calls external API. Uses Redis caching. (Public)
    *   `GET /:fixtureId/messages`: Fetches chat messages for a specific fixture. Supports pagination with `limit` and `before` parameters. Returns message history and chat status (open/closed). Uses Redis caching. (Private)
*   `/api/fixtures/votes` (`src/routes/fixtureVoteRoutes.ts`)
    *   `POST /:fixtureId`: Vote for a fixture outcome (home win, draw, away win). Requires authentication. (Private)
    *   `GET /:fixtureId/stats`: Get vote statistics for a fixture. Returns counts for home, draw, away votes and total votes. Uses Redis caching. (Public)
    *   `GET /batch/stats`: Get vote statistics for multiple fixtures. Requires `ids` query parameter (comma-separated fixture IDs, max 20). Uses Redis caching. (Public)
    *   `GET /:fixtureId/user`: Get the current user's vote for a fixture. Requires authentication. (Private)
*   `/api/predictions` (`src/routes/predictionRoutes.ts`)
    *   `GET /`: Fetches predictions for a specific fixture. Requires `fixtureId` query parameter. Calls external API. Uses Redis caching. (Public)
*   `/api/injuries` (`src/routes/injuryRoutes.ts`)
    *   `GET /`: Fetches injuries. Requires at least one filter (`league`, `fixture`, `team`, `player`, `date`, `ids`). Requires `season` if `league`, `team`, or `player` is used. Uses Redis caching for specific queries. (Public)
*   `/api/players` (`src/routes/playerRoutes.ts`)
    *   `GET /profiles`: Fetches player profiles. Filters: `player` (ID), `search`. Supports `page`. Uses Redis caching. (Public)
    *   `GET /all-profiles`: Fetches a list of all player profiles (basic info). Supports `search`. Uses Redis caching. (Public)
    *   `GET /statistics`: Fetches player statistics. Requires `season`. Requires `id`, `team`, or `league`. Supports `search`, `page`. Uses Redis caching. (Public)
    *   `GET /squads`: Fetches squad information. Requires `team` (ID) or `player` (ID). Uses Redis caching. (Public)
    *   `GET /topscorers`: Fetches top scorers. Requires `league` (ID), `season`. Uses Redis caching. (Public)
    *   `GET /topassists`: Fetches top assists. Requires `league` (ID), `season`. Uses Redis caching. (Public)
    *   `GET /topyellowcards`: Fetches top yellow cards. Requires `league` (ID), `season`. Uses Redis caching. (Public)
    *   `GET /topredcards`: Fetches top red cards. Requires `league` (ID), `season`. Uses Redis caching. (Public)
    *   `GET /teams`: Fetches player team history. Requires `player` (ID). Uses Redis caching. (Public)
*   `/api/coaches` (`src/routes/coachRoutes.ts`)
    *   `GET /`: Fetches coaches. Requires `id`, `team`, or `search`. Uses Redis caching. (Public)
*   `/api/transfers` (`src/routes/transferRoutes.ts`)
    *   `GET /`: Fetches transfers. Requires `player` (ID) or `team` (ID). Uses Redis caching. (Public)
*   `/api/sidelined` (`src/routes/sidelinedRoutes.ts`)
    *   `GET /`: Fetches sidelined players. Requires `player`, `coach`, `team`, `league`, or `ids`. Requires `season` if `league` used. Uses Redis caching. (Public)
*   `/api/odds` (`src/routes/oddsRoutes.ts`)
    *   `GET /`: Fetches pre-match odds. Requires `fixture`, `league`, or `date`. Requires `season` if `league` used. Optional filters: `bookmaker`, `bet`. Uses Redis caching. (Public)
    *   `GET /live`: Fetches live odds. Optional filters: `fixture`, `league`, `bet`. Uses Redis caching (short TTL). (Public)
    *   `GET /bookmakers`: Fetches bookmaker info. Optional filters: `id`, `search`. Uses Redis caching. (Public)
    *   `GET /bets`: Fetches bet type info. Optional filters: `id`, `search`. Uses Redis caching. (Public)
    *   `GET /live/bets`: Fetches live bet type info. Optional filters: `id`, `search`. Uses Redis caching. (Public)
*   `/api/users` (`src/routes/userRoutes.ts`)
    *   `POST /register`: Registers a new user. (Public)
    *   `POST /login`: Authenticates a user, returns JWT. (Public)
    *   `GET /me`: Gets current authenticated user's profile. (Private)
    *   `POST /me/device-tokens`: Adds a device token for push notifications. (Private)
    *   `DELETE /me/device-tokens/:token`: Removes a device token. (Private)
    *   `PUT /me/preferences/notifications`: Updates notification preferences. (Private)
    *   `GET /favorites`: Gets favorite IDs (teams, players, leagues). (Private)
    *   `POST /favorites/teams/:teamId`: Adds a favorite team. (Private)
    *   `DELETE /favorites/teams/:teamId`: Removes a favorite team. (Private)
    *   `POST /favorites/players/:playerId`: Adds a favorite player. (Private)
    *   `DELETE /favorites/players/:playerId`: Removes a favorite player. (Private)
    *   `POST /favorites/leagues/:leagueId`: Adds a favorite league. (Private)
    *   `DELETE /favorites/leagues/:leagueId`: Removes a favorite league. (Private)
    *   `GET /favorites/teams/details`: Gets details of favorite teams. (Private)
    *   `GET /favorites/players/details`: Gets details of favorite players. (Private)
    *   `GET /favorites/leagues/details`: Gets details of favorite leagues. (Private)
*   `/api/admin` (`src/routes/adminRoutes.ts`)
    *   `POST /jobs/upcomingFixtures`: Manually triggers the job to fetch upcoming fixtures. Optional query parameter: `days` (number of days to fetch, default: 7). (Public, but should be restricted in production)

## 2. Cron Jobs

Scheduled tasks are defined in `src/jobs/` and initialized in `src/jobs/scheduler.ts`.

*   **`fetchAndUpdateCountries` (`countryJobs.ts`)**: Fetches all countries and updates DB. Schedule: Daily @ 3:00 AM.
*   **`fetchAndUpdateLeagueSeasons` (`leagueSeasonJobs.ts`)**: Fetches available season years and updates DB. Schedule: Daily @ 3:05 AM.
*   **`fetchAndUpdateLeagues` (`leagueJobs.ts`)**: Fetches leagues, filters by targeted list, updates DB. Schedule: Hourly @ minute 10.
*   **`fetchAndUpdateTeamsForActiveLeagues` (`teamJobs.ts`)**: Finds active targeted leagues, fetches teams for each, updates DB. Schedule: Daily @ 3:15 AM.
*   **`fetchAndUpdateTeamStatistics` (`teamStatisticJobs.ts`)**: Finds active leagues, fetches stats for teams in those leagues, updates DB. Schedule: Twice Daily @ 4:00 AM & 4:00 PM.
*   **`fetchAndUpdateVenuesForActiveTeams` (`venueJobs.ts`)**: Finds unique venues for teams in targeted leagues, fetches details, updates DB. Schedule: Daily @ 3:20 AM.
*   **`fetchAndUpdateStandings` (`standingJobs.ts`)**: Finds active targeted leagues with standings coverage, fetches standings, updates DB. Schedule: Hourly @ minute 15.
*   **`fetchAndUpdateFixtureRounds` (`fixtureRoundJobs.ts`)**: Finds active leagues, fetches fixture round names, updates DB. Schedule: Daily @ 3:25 AM.
*   **`fetchAndUpdateFixturesByDate` (`fixtureJobs.ts`)**: Fetches fixtures for current date, filters by targeted leagues, updates DB, invalidates cache. Schedule: Every 5 minutes.
*   **`fetchAndUpdateLiveFixtures` (`fixtureJobs.ts`)**: Fetches live fixtures, filters by targeted leagues, updates DB, fetches details via `fetchAndUpdateFixturesById`, invalidates cache. Schedule: Every 15 seconds (`setInterval`).
*   **`fetchAndUpdateUpcomingFixtures` (`fixtureJobs.ts`)**: Fetches fixtures for next N days, filters by targeted leagues, updates DB, invalidates cache. Schedule: Daily @ 2:00 AM. This job is essential for the fixture voting feature, as users can only vote on upcoming fixtures.
*   **`fetchAndUpdateInjuriesForUpcomingFixtures` (`injuryJobs.ts`)**: Finds upcoming fixtures (today/tomorrow) in targeted leagues, fetches injuries in batches, updates DB. Schedule: Every 4 hours.
*   **`fetchAndUpdatePlayerProfiles` (`playerJobs.ts`)**: Fetches player profiles (paginated), updates DB. Schedule: Weekly (Sun) @ 1:00 AM.
*   **`fetchAndUpdatePlayerStatistics` (`playerJobs.ts`)**: Finds active targeted leagues, fetches player stats (paginated), updates DB. Schedule: Daily @ 3:30 AM.
*   **`fetchAndUpdatePlayerSquads` (`playerJobs.ts`)**: Finds teams in targeted leagues, fetches squads, updates DB. Schedule: Weekly (Sun) @ 2:00 AM.
*   **`fetchAndUpdateTopPlayers` (`playerJobs.ts`)**: Finds active targeted leagues, fetches top scorers/assists/cards, updates DB. Schedule: Daily @ 4:00 AM.
*   **`fetchAndUpdateCoaches` (`coachJobs.ts`)**: Finds teams in targeted leagues, fetches coaches, updates DB. Schedule: Weekly (Sun) @ 3:00 AM.
*   **`fetchAndUpdateTeamTransfers` (`transferJobs.ts`)**: Finds teams in targeted leagues, fetches transfers, updates DB. Schedule: Weekly (Sun) @ 4:00 AM.
*   **`fetchAndUpdatePlayerTransfers` (`transferJobs.ts`)**: Finds top players in targeted leagues, fetches transfers, updates DB. Schedule: Weekly (Sun) @ 4:30 AM.
*   **`fetchAndUpdateTeamSidelined` (`sidelinedJobs.ts`)**: Finds teams in targeted leagues, fetches sidelined players, updates DB. Schedule: Daily @ 5:00 AM.
*   **`fetchAndUpdateLeagueSidelined` (`sidelinedJobs.ts`)**: Finds players in targeted leagues, fetches sidelined status, updates DB. Schedule: Daily @ 5:30 AM.
*   **`fetchAndUpdateBookmakers` (`oddsJobs.ts`)**: Fetches bookmakers, updates DB. Schedule: Daily @ 6:00 AM.
*   **`fetchAndUpdateBets` (`oddsJobs.ts`)**: Fetches pre-match and live bet types, updates DB. Schedule: Daily @ 6:30 AM.
*   **`fetchAndUpdateOdds` (`oddsJobs.ts`)**: Finds upcoming fixtures in targeted leagues, fetches pre-match odds, updates DB. Schedule: Every 3 hours.
*   **`fetchAndUpdateLiveOdds` (`oddsJobs.ts`)**: Fetches live odds, filters by targeted leagues, updates DB. Schedule: Every 5 minutes.
*   **`upcomingFixtureNotificationJob` (`notificationJobs.ts`)**: Checks for favorite teams' matches tomorrow, identifies users with notifications enabled, logs placeholder notification sends. Schedule: Daily @ 7:00 AM.
*   **`checkFinishedFixturesJob` (`chatJobs.ts`)**: Checks for fixtures that have just finished, notifies users that chat will close in 20 minutes, and closes chats after the 20-minute period. Schedule: Every minute.

## 3. User Authentication

*   **Mechanism:** JSON Web Tokens (JWT).
*   **Registration (`POST /api/users/register`)**: Creates user, hashes password (bcrypt), generates JWT.
*   **Login (`POST /api/users/login`)**: Validates credentials (bcrypt compare), generates JWT.
*   **Middleware (`src/middleware/auth.ts`)**:
    *   Expects token in `x-auth-token` header.
    *   Verifies token signature and expiration using `JWT_SECRET` (from `.env`).
    *   Checks if user ID from token exists in the database.
    *   Attaches user ID to `req.user` for protected routes.
*   **Password Handling:** Passwords are hashed using `bcrypt` before storing (`src/models/User.ts`).

## 4. Database Models (`src/models/`)

*   `Coach.ts`: Coach profile and career data.
*   `Country.ts`: Country details (name, code, flag).
*   `Fixture.ts`: Match details (teams, date, score, status, events, lineups, stats). Includes chat-related fields to track chat status.
*   `FixtureRound.ts`: List of rounds for a league/season.
*   `FixtureVote.ts`: User votes on fixture outcomes (home win, draw, away win).
    *   Includes functions for creating/updating votes, retrieving user votes, and getting vote statistics.
    *   Has indexes on `{ userId: 1, fixtureId: 1 }` (unique) and `{ fixtureId: 1 }` for efficient queries.
*   `Message.ts`: Chat messages for fixtures.
    *   Includes functions for creating messages, retrieving message history, and checking/controlling chat status.
    *   Has indexes on `{ fixtureId: 1, createdAt: -1 }` and `{ userId: 1 }` for efficient queries.
*   `Injury.ts`: Player injury details.
*   `League.ts`: League details (name, type, country, seasons).
*   `LeagueSeason.ts`: Available season years.
*   `Odds.ts`: Betting odds (pre-match, live), bookmakers, bet types.
*   `Player.ts`: Player details (profile, stats, squads, top lists).
*   `Prediction.ts`: Match predictions.
*   `Sidelined.ts`: Sidelined player status.
*   `Standing.ts`: League standings.
*   `Team.ts`: Team details (name, country, venue).
*   `TeamStatistic.ts`: Team stats for a league/season.
*   `Transfer.ts`: Player transfer history.
*   `User.ts`: Application user data (auth, favorites, preferences, device tokens).
*   `Venue.ts`: Stadium/venue details.

## 5. External API Integrations

*   **API-Football (`src/services/apiFootball.ts`)**:
    *   Primary external data source.
    *   Service acts as a wrapper using `axios`.
    *   Handles API key authentication, base URL, rate limit logging, and basic error handling.
    *   Provides specific functions (e.g., `fetchFixtures`, `fetchLeagues`) mapping to API endpoints.
*   **Other Services (`src/services/`)**:
    *   `coachService.ts`, `oddsService.ts`, `playerService.ts`, `sidelinedService.ts`, `transferService.ts`: Likely provide more specific interfaces or logic built upon `apiFootball.ts` for fetching their respective data types.

## 6. Configuration (`src/config/index.ts`)

*   Loads settings from `.env` file.
*   Validates presence of essential keys (`API_FOOTBALL_KEY`, `MONGO_URI`, `REDIS_URI`).
*   Exports a structured `config` object containing:
    *   Server port (`PORT`).
    *   API-Football credentials (`API_FOOTBALL_KEY`).
    *   Database connection string (`MONGO_URI`).
    *   Cache connection string (`REDIS_URI`).

## 7. User Features

*   **Authentication**:
    *   Registration and login with email/password
    *   JWT-based authentication
*   **Favorites**:
    *   Add/remove favorite teams
    *   Add/remove favorite players
    *   Add/remove favorite leagues
    *   View details of favorites
*   **Notifications**:
    *   Register device tokens for push notifications
    *   Toggle preferences for upcoming fixture notifications
*   **Fixture Voting**:
    *   Vote on match outcomes (home win, draw, away win)
    *   View personal votes
    *   View community vote statistics
*   **Fixture Chat**:
    *   Send and receive real-time messages about specific fixtures
    *   View message history for fixtures
    *   Automatic chat closing 20 minutes after match ends

## 8. API Security Implementation

The API has been secured with multiple layers of protection:

*   **API Key Authentication**: All non-public endpoints require a valid API key
*   **Rate Limiting**: Limits are applied to prevent abuse (standard: 100 requests per 15 minutes)
*   **Stricter Limits for Sensitive Routes**: Auth endpoints have tighter rate limits (10 attempts per hour)
*   **HTTPS/TLS**: All API communication is encrypted
*   **Security Headers**: Added via Helmet middleware
*   **CORS Restrictions**: Only specific origins are allowed*
* X-API-Key: 1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756

### Next Steps for Your iPhone App

When developing your iPhone app, make sure to implement the following:

#### API Key Storage
* Store the API key securely in your app configuration
* Include it with every request to the backend

#### JWT Token Management
* After login, store the JWT token securely in the iOS Keychain
* Include the JWT token in the `x-auth-token` header for authenticated requests

#### Certificate Pinning (optional but recommended)
* Add SSL pinning to your network layer to prevent MITM attacks

#### Error Handling
* Add proper handling for 401 (Unauthorized) and 429 (Too Many Requests) responses