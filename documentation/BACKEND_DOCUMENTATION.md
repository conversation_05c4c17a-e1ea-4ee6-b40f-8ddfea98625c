# KickoffScore Backend API Documentation

## Overview
KickoffScore is a comprehensive football (soccer) live score and prediction platform built with Node.js, Express, MongoDB, Redis, and Socket.IO. The backend provides real-time match data, predictions, user management, and live chat functionality.

## Architecture

### Technology Stack
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: MongoDB (primary data storage)
- **Cache**: Redis (caching and session management)
- **Real-time**: Socket.IO (WebSocket connections)
- **External API**: API-Football (v3.football.api-sports.io)
- **Process Management**: PM2 (production deployment)

### Server Configuration
- **Port**: 3000 (configurable via PORT env variable)
- **MongoDB URI**: `mongodb://localhost:27017/kickoffscore`
- **Redis URI**: `redis://localhost:6379`
- **API Key**: `1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756`

## Authentication & Security

### API Key Authentication
All API endpoints (except `/health` and `/`) require the `X-API-Key` header:
```
X-API-Key: 1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756
```

### JWT Authentication
User-specific endpoints require JWT token in `x-auth-token` header:
```
x-auth-token: <JWT_TOKEN>
```

### CORS Configuration
Allowed origins:
- `https://kickoffpredictions.com`
- `https://www.kickoffpredictions.com`
- `http://localhost:3000` (development)

## API Endpoints

### Core Data Endpoints

#### Countries
- **GET** `/api/countries` - Get all countries with flags
- Response: Array of countries with name, code, and flag URL

#### Leagues
- **GET** `/api/leagues` - Get all leagues
- **GET** `/api/leagues/seasons` - Get league seasons
- Query params: `league`, `season`, `current`, etc.

#### Teams
- **GET** `/api/teams` - Get teams
- **GET** `/api/teams/statistics` - Get team statistics
- **GET** `/api/teams/form` - Get team form data
- Query params: `league`, `season`, `team`, etc.

#### Fixtures (Matches)
- **GET** `/api/fixtures` - Get fixtures with various filters

**Query Parameters:**
- `live=all` - Get all live matches
- `date=YYYY-MM-DD` - Get matches for specific date
- `league=ID` - Get matches for specific league
- `team=ID` - Get matches for specific team
- `id=ID` - Get specific match with detailed data (includes events, lineups, stats)
- `ids=ID1-ID2-ID3` - Get multiple matches (max 20)
- `last=N` - Get last N matches (with team/league)
- `next=N` - Get next N matches (with team/league)
- `from=YYYY-MM-DD` - Date range start
- `to=YYYY-MM-DD` - Date range end
- `status=NS,1H,FT` - Filter by status codes
- `timezone=Europe/London` - Return times in specific timezone

**Response Examples:**
```json
// Live matches (?live=all)
[
  {
    "_id": 12345,
    "fixture": {
      "id": 12345,
      "date": "2024-01-15T15:00:00Z",
      "status": { "short": "1H", "long": "First Half", "elapsed": 23 }
    },
    "league": {
      "id": 39,
      "name": "Premier League",
      "season": 2024
    },
    "teams": {
      "home": { "id": 33, "name": "Manchester United", "logo": "..." },
      "away": { "id": 34, "name": "Newcastle", "logo": "..." }
    },
    "goals": { "home": 1, "away": 0 },
    "events": [
      {
        "time": { "elapsed": 15, "extra": null },
        "team": { "id": 33, "name": "Manchester United" },
        "player": { "id": 123, "name": "Marcus Rashford" },
        "type": "Goal",
        "detail": "Normal Goal"
      }
    ]
  }
]
```

#### Standings
- **GET** `/api/standings` - Get league tables

**Query Parameters:**
- `league=ID` - League ID (required with season)
- `season=YYYY` - Season year (required)
- `team=ID` - Filter to show specific team's position

**Response Example:**
```json
[
  {
    "league": {
      "id": 39,
      "name": "Premier League",
      "season": 2024
    },
    "standings": [
      [
        {
          "rank": 1,
          "team": {
            "id": 50,
            "name": "Manchester City",
            "logo": "..."
          },
          "points": 45,
          "goalsDiff": 25,
          "group": "Premier League",
          "form": "WWWWW",
          "status": "same",
          "description": "Promotion - Champions League",
          "all": {
            "played": 18,
            "win": 14,
            "draw": 3,
            "lose": 1,
            "goals": { "for": 42, "against": 17 }
          }
        }
      ]
    ]
  }
]
```

#### Players
- **GET** `/api/players` - Get player data and statistics

**Query Parameters:**
- `league=ID` - League ID (required with season)
- `season=YYYY` - Season year (required)
- `team=ID` - Filter by team
- `search=name` - Search by player name (min 4 chars)
- `page=N` - Pagination (20 results per page)

#### Leagues
- **GET** `/api/leagues` - Get leagues and competitions

**Query Parameters:**
- `id=ID` - Specific league ID
- `country=name` - Filter by country
- `season=YYYY` - Filter by season
- `current=true` - Only current/active leagues
- `type=league|cup` - Filter by competition type

### Prediction Endpoints

#### Basic Predictions
- **GET** `/api/predictions` - Get basic match predictions
- Query params: `fixture`, `league`, `date`

#### Enhanced Predictions
- **GET** `/api/enhanced-predictions` - Get advanced predictions
- Includes Dixon-Coles model, corner predictions, card predictions
- Query params: `fixture`, `league`, `date`

### User Management

#### Authentication
- **POST** `/api/users/register` - Register new user
- **POST** `/api/users/login` - User login
- **GET** `/api/users/profile` - Get user profile (requires auth)

#### User Preferences
- **POST** `/api/users/favorites/teams` - Add favorite team
- **DELETE** `/api/users/favorites/teams/:teamId` - Remove favorite team
- **POST** `/api/users/favorites/leagues` - Add favorite league
- **POST** `/api/users/favorites/players` - Add favorite player

#### Notifications
- **POST** `/api/users/device-tokens` - Register device for push notifications
- **PUT** `/api/users/notifications/preferences` - Update notification settings

### Live Features

#### Fixture Votes
- **POST** `/api/fixtures/:fixtureId/votes` - Vote on match outcome
- **GET** `/api/fixtures/:fixtureId/votes` - Get voting results

#### Messages/Chat
- **GET** `/api/fixtures/:fixtureId/messages` - Get chat messages
- **POST** `/api/fixtures/:fixtureId/messages` - Send message (requires auth)

## WebSocket Implementation

### Namespaces

#### Chat Namespace (`/chat`)
Real-time chat functionality for match discussions.

**Authentication**: JWT token required in handshake
```javascript
const socket = io('/chat', {
  auth: { token: 'JWT_TOKEN' }
});
```

**Events**:
- `join-fixture` - Join a match chat room
- `send-message` - Send a message
- `message` - Receive new messages
- `error` - Error notifications

#### Fixture Updates Namespace (`/fixtures`)
Real-time match updates and live scores.

**Events**:
- `subscribe-fixture` - Subscribe to specific match updates
- `subscribe-league` - Subscribe to all matches in a league
- `subscribe-live` - Subscribe to all live matches
- `unsubscribe-fixture` - Unsubscribe from match updates
- `fixture-update` - Receive match updates
- `goal-scored` - Goal notifications
- `red-card` - Red card notifications
- `status-change` - Match status changes

### Event Detection System
The backend automatically detects and broadcasts:
- Goals (including VAR decisions)
- Red cards
- Match status changes (kickoff, halftime, fulltime, extra time, penalties)
- Score updates

## Background Jobs & Scheduler

### Job Categories

#### Data Synchronization (Cron Jobs)
- **Countries**: Daily at 3:00 AM
- **Leagues**: Hourly at minute 10
- **Teams**: Daily at 4:00 AM
- **Fixtures**: Every 15 seconds for live matches
- **Standings**: Hourly for active leagues
- **Player Data**: Daily at 5:00 AM

#### Prediction Jobs
- **Basic Predictions**: Every 2 hours for upcoming matches
- **Enhanced Predictions**: Every 4 hours using Dixon-Coles model
- **ELO Ratings**: Daily at 2:00 AM UTC

#### Notification Jobs
- **Match Reminders**: 30 minutes before kickoff
- **Live Events**: Real-time during matches
- **Chat Management**: Open/close chat based on match status

### Job Monitoring
Jobs are monitored and logged with error handling and retry mechanisms.

## Data Models & Response Formats

### Key Collections

#### Fixtures (Complete Structure)
```typescript
{
  _id: number,           // API fixture ID
  fixture: {
    id: number,
    referee: string | null,
    timezone: string,
    date: string,        // ISO 8601 format
    timestamp: number,
    periods: {
      first: number | null,
      second: number | null
    },
    venue: {
      id: number | null,
      name: string | null,
      city: string | null
    },
    status: {
      long: string,      // "Match Finished", "Not Started", etc.
      short: string,     // "FT", "NS", "1H", "2H", "HT", etc.
      elapsed: number | null
    }
  },
  league: {
    id: number,
    name: string,
    country: string,
    logo: string | null,
    flag: string | null,
    season: number,
    round: string
  },
  teams: {
    home: {
      id: number,
      name: string,
      logo: string | null,
      winner: boolean | null
    },
    away: {
      id: number,
      name: string,
      logo: string | null,
      winner: boolean | null
    }
  },
  goals: {
    home: number | null,
    away: number | null
  },
  score: {
    halftime: { home: number | null, away: number | null },
    fulltime: { home: number | null, away: number | null },
    extratime: { home: number | null, away: number | null },
    penalty: { home: number | null, away: number | null }
  },
  events: [
    {
      time: { elapsed: number, extra: number | null },
      team: { id: number, name: string, logo: string | null },
      player: { id: number | null, name: string | null },
      assist: { id: number | null, name: string | null },
      type: string,      // "Goal", "Card", "Subst", "Var"
      detail: string,    // "Normal Goal", "Yellow Card", etc.
      comments: string | null
    }
  ],
  lineups: [
    {
      team: { id: number, name: string, logo: string | null },
      coach: { id: number | null, name: string | null, photo: string | null },
      formation: string | null,
      startXI: [
        {
          player: {
            id: number,
            name: string,
            number: number | null,
            pos: string | null,
            grid: string | null
          }
        }
      ],
      substitutes: [/* Same structure as startXI */]
    }
  ],
  statistics: [
    {
      team: { id: number, name: string, logo: string | null },
      statistics: [
        {
          type: string,    // "Shots on Goal", "Ball Possession", etc.
          value: number | string | null
        }
      ]
    }
  ]
}
```

#### Users
```typescript
{
  _id: ObjectId,
  email: string,
  password: string,      // Hashed with bcrypt
  name: string,
  profileImage: string | null,
  favoriteTeams: number[],
  favoriteLeagues: number[],
  favoritePlayers: number[],
  deviceTokens: string[],
  notificationPreferences: {
    goals: boolean,
    redCards: boolean,
    matchStart: boolean,
    matchEnd: boolean,
    favoriteTeams: boolean
  },
  createdAt: Date,
  updatedAt: Date
}
```

#### Enhanced Predictions
```typescript
{
  _id: ObjectId,
  fixtureId: number,
  homeTeamId: number,
  awayTeamId: number,
  leagueId: number,
  season: number,
  predictions: {
    homeWin: number,     // Probability 0-1
    draw: number,
    awayWin: number,
    over25: number,      // Over 2.5 goals
    under25: number,
    btts: number,        // Both teams to score
    correctScore: {
      "0-0": number,
      "1-0": number,
      "0-1": number,
      "1-1": number,
      "2-0": number,
      "0-2": number,
      "2-1": number,
      "1-2": number,
      "2-2": number,
      "3-0": number,
      "0-3": number,
      "3-1": number,
      "1-3": number,
      "3-2": number,
      "2-3": number,
      "other": number
    }
  },
  cornerPredictions: {
    totalCornersOver85: number,
    totalCornersOver95: number,
    totalCornersOver105: number,
    totalCornersOver115: number,
    totalCornersUnder85: number,
    totalCornersUnder95: number,
    totalCornersUnder105: number,
    totalCornersUnder115: number
  },
  cardPredictions: {
    totalCardsOver15: number,
    totalCardsOver25: number,
    totalCardsOver35: number,
    totalCardsOver45: number,
    totalCardsUnder15: number,
    totalCardsUnder25: number,
    totalCardsUnder35: number,
    totalCardsUnder45: number
  },
  confidence: number,    // 0-1 confidence score
  createdAt: Date,
  updatedAt: Date
}
```

## Caching Strategy

### Redis Usage
- **Fixture Data**: Cached for 15 seconds during live matches
- **League Data**: Cached for 1 hour
- **Team Statistics**: Cached for 6 hours
- **User Sessions**: JWT token validation
- **API Rate Limiting**: Track API usage

### Cache Keys Pattern
- `fixtures:id:{fixtureId}`
- `leagues:{leagueId}`
- `teams:{teamId}:stats`
- `standings:{leagueId}:{season}`

## Match Status Codes

Understanding match status is crucial for live score functionality:

### Status Categories

#### Scheduled Matches
- **TBD** - Time To Be Defined (date/time not confirmed)
- **NS** - Not Started (scheduled, waiting for kickoff)

#### Live Matches
- **1H** - First Half (match in progress, first half)
- **HT** - Halftime (15-minute break between halves)
- **2H** - Second Half (match in progress, second half)
- **ET** - Extra Time (30 minutes additional time)
- **BT** - Break Time (break during extra time)
- **P** - Penalty Shootout (penalties in progress)
- **LIVE** - In Progress (rare, when specific time data unavailable)

#### Interrupted Matches
- **SUSP** - Match Suspended (may be rescheduled)
- **INT** - Match Interrupted (should resume shortly)

#### Finished Matches
- **FT** - Full Time (finished in regular 90 minutes)
- **AET** - After Extra Time (finished after 120 minutes)
- **PEN** - Finished on Penalties (decided by penalty shootout)

#### Cancelled/Postponed
- **PST** - Match Postponed (rescheduled to another date)
- **CANC** - Match Cancelled (will not be played)
- **ABD** - Match Abandoned (stopped due to external factors)
- **AWD** - Technical Loss (awarded result)
- **WO** - Walk Over (victory by forfeit)

### Frontend Status Handling
```javascript
function getMatchStatusDisplay(status) {
  const statusMap = {
    'NS': 'Not Started',
    '1H': 'First Half',
    'HT': 'Half Time',
    '2H': 'Second Half',
    'ET': 'Extra Time',
    'BT': 'Break',
    'P': 'Penalties',
    'FT': 'Full Time',
    'AET': 'After Extra Time',
    'PEN': 'Penalties',
    'PST': 'Postponed',
    'CANC': 'Cancelled',
    'SUSP': 'Suspended',
    'ABD': 'Abandoned'
  };
  return statusMap[status] || status;
}

function isLiveMatch(status) {
  return ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE'].includes(status);
}

function isFinishedMatch(status) {
  return ['FT', 'AET', 'PEN'].includes(status);
}
```

## Push Notifications

### APNs Integration
- **Certificate**: `AuthKey_F8D572C22G.p8`
- **Bundle ID**: `com.kickoffscore.kickoffscore`
- **Environment**: Development/Production configurable

### Notification Types
- **Goal Alerts**: Real-time goal notifications
- **Match Status**: Kickoff, halftime, fulltime alerts
- **Red Cards**: Disciplinary action alerts
- **Match Reminders**: Pre-match notifications

## Error Handling & Logging

### Error Response Format
```json
{
  "message": "Error description",
  "errors": [/* Validation errors */]
}
```

### Logging Levels
- **Info**: General application flow
- **Warn**: Potential issues
- **Error**: Application errors
- **Debug**: Detailed debugging information

## Development & Deployment

### Local Development
```bash
npm run dev          # Start development server
npm run build        # Build TypeScript
npm start           # Start production server
```

### Environment Variables
```env
API_FOOTBALL_KEY=your_api_key
MONGO_URI=mongodb://localhost:27017/kickoffscore
REDIS_URI=redis://localhost:6379
JWT_SECRET=your_jwt_secret
API_KEY=your_api_key
PORT=3000
```

### Production Deployment
- Uses PM2 for process management
- Deployed to `/var/www/api.kickoffpredictions.com`
- Nginx reverse proxy configuration
- SSL/TLS termination at proxy level

## Rate Limiting & Performance

### API Rate Limits
- External API: Managed per subscription limits
- Internal API: 100 requests per minute per IP
- WebSocket: Connection limits per user

### Performance Optimizations
- Database indexing on frequently queried fields
- Redis caching for expensive operations
- Connection pooling for database connections
- Gzip compression for API responses

## Monitoring & Health Checks

### Health Endpoints
- **GET** `/health` - Basic health check
- **GET** `/api/status` - Detailed system status

### Metrics Tracked
- API response times
- Database query performance
- Cache hit/miss ratios
- WebSocket connection counts
- Background job success rates

## Frontend Integration Examples

### WebSocket Connection Examples

#### Connecting to Live Fixtures
```javascript
// Connect to fixture updates namespace
const fixtureSocket = io('http://localhost:3000/fixtures');

// Subscribe to specific match
fixtureSocket.emit('subscribe-fixture', 12345);

// Listen for match updates
fixtureSocket.on('fixture-update', (data) => {
  console.log('Match updated:', data);
  // Update UI with new match data
});

// Listen for goals
fixtureSocket.on('goal-scored', (data) => {
  console.log('GOAL!', data);
  // Show goal notification
});

// Listen for red cards
fixtureSocket.on('red-card', (data) => {
  console.log('Red card!', data);
  // Show red card notification
});
```

#### Chat Integration
```javascript
// Connect to chat namespace with authentication
const chatSocket = io('http://localhost:3000/chat', {
  auth: { token: userJwtToken }
});

// Join a match chat
chatSocket.emit('join-fixture', 12345);

// Send message
chatSocket.emit('send-message', {
  content: 'Great goal!'
});

// Listen for new messages
chatSocket.on('message', (message) => {
  console.log('New message:', message);
  // Add message to chat UI
});
```

### API Request Examples

#### Get Live Matches
```javascript
const response = await fetch('http://localhost:3000/api/fixtures?live=all', {
  headers: {
    'X-API-Key': '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
  }
});
const liveMatches = await response.json();
```

#### Get Match Details with Events
```javascript
const response = await fetch('http://localhost:3000/api/fixtures?id=12345', {
  headers: {
    'X-API-Key': '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
  }
});
const matchDetails = await response.json();
// Includes events, lineups, statistics if available
```

#### User Authentication
```javascript
// Register user
const registerResponse = await fetch('http://localhost:3000/api/users/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123',
    name: 'John Doe'
  })
});
const { token, user } = await registerResponse.json();

// Use token for authenticated requests
const profileResponse = await fetch('http://localhost:3000/api/users/profile', {
  headers: {
    'X-API-Key': '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756',
    'x-auth-token': token
  }
});
```

## Data Flow Architecture

### Real-time Updates Flow
1. **External API Polling**: Background jobs fetch data from API-Football every 15 seconds
2. **Event Detection**: System compares new data with cached state to detect changes
3. **WebSocket Broadcast**: Changes are broadcast to subscribed clients
4. **Frontend Updates**: Connected clients receive real-time updates

### Prediction System Flow
1. **Data Collection**: Historical match data, team statistics, player data
2. **Model Training**: Dixon-Coles model trained on recent match data
3. **Prediction Generation**: Enhanced predictions calculated for upcoming matches
4. **API Serving**: Predictions served via REST API with caching

### Chat System Flow
1. **Authentication**: JWT token validation on WebSocket connection
2. **Room Management**: Users join match-specific chat rooms
3. **Message Broadcasting**: Messages broadcast to all users in the room
4. **Moderation**: Automatic chat opening/closing based on match status

## Database Schema Details

### Indexes for Performance
```javascript
// Fixtures collection indexes
db.fixtures.createIndex({ "_id": 1 })
db.fixtures.createIndex({ "league.id": 1, "fixture.date": -1 })
db.fixtures.createIndex({ "teams.home.id": 1, "fixture.date": -1 })
db.fixtures.createIndex({ "teams.away.id": 1, "fixture.date": -1 })
db.fixtures.createIndex({ "fixture.status.short": 1 })

// Users collection indexes
db.users.createIndex({ "email": 1 }, { unique: true })
db.users.createIndex({ "favoriteTeams": 1 })
db.users.createIndex({ "favoriteLeagues": 1 })
```

### Data Relationships
- **Fixtures** reference Teams, Leagues, and Venues by ID
- **Users** store arrays of favorite team/league/player IDs
- **Messages** reference fixture ID and user ID
- **Predictions** reference fixture ID with calculated probabilities

## Security Considerations

### API Security
- Rate limiting per IP address
- API key validation on all endpoints
- JWT token expiration and refresh
- Input validation and sanitization
- CORS restrictions to allowed domains

### WebSocket Security
- JWT authentication required for chat
- Room-based message isolation
- Automatic disconnection on invalid tokens
- Message content filtering and validation

## Troubleshooting Guide

### Common Issues

#### WebSocket Connection Fails
- Check CORS configuration
- Verify JWT token format and expiration
- Ensure proper namespace connection (`/chat` or `/fixtures`)

#### API Returns 401 Unauthorized
- Verify X-API-Key header is included
- Check API key value matches server configuration
- For user endpoints, ensure x-auth-token header is present

#### Live Updates Not Working
- Check WebSocket subscription events are properly emitted
- Verify background jobs are running (check server logs)
- Ensure Redis connection is active for caching

#### Predictions Not Available
- Check if enhanced prediction jobs are running
- Verify sufficient historical data exists for teams
- Ensure Dixon-Coles model training completed successfully

### Server Logs
Monitor these log patterns:
- `Server listening on port 3000` - Server startup
- `MongoDB connected successfully` - Database connection
- `Redis connected successfully` - Cache connection
- `Processing fixture update for ID` - Live data processing
- `Chat jobs started on main worker` - Background jobs active

This comprehensive documentation provides everything needed to build a robust frontend web application that integrates seamlessly with the KickoffScore backend API and real-time features.
