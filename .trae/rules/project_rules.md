# Backend Architecture & Configuration
- KickoffScore has Node.js/Express backend with MongoDB and Redis at api.kickoffpredictions.com (**************) and an iOS SwiftUI app.
- Backend config: MongoDB at mongodb://localhost:27017/kickoffscore, Redis at redis://localhost:6379, port 3000, X-API-Key 1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756.
- Backend provides football data via API-Football integration with caching and should use PM2 for process management.
- Backend should be deployed to /var/www/api.kickoffpredictions.com on the remote server. Always make changes locally first, then deploy.
- Disable caching during development and testing phases to enable real-time testing of system adjustments and improvements.

# Prediction Enhancements
- User wants to enhance KickoffScore predictions by implementing correct score, BTTS (Both Teams To Score), corner predictions, and Expected Cards market predictions using mathematical models from Dixon-Coles, Poisson distribution, and research papers (https://arxiv.org/pdf/2112.13001 and https://www.diva-portal.org/smash/get/diva2:1902863/FULLTEXT01.pdf).
- For corner predictions in KickoffScore, user wants to implement only Total Corners Over/Under markets, not team-specific or handicap corner markets.
- User wants to implement corner predictions in KickoffScore using research papers (arxiv.org/pdf/2112.13001 and lup.lub.lu.se paper) and integrate it seamlessly with existing Dixon-Coles prediction system.
- User wants to implement Expected Cards market predictions using leagues with statistics_fixtures: true, following similar approach to corner predictions implementation.
- For KickoffScore predictions: use ELO only for cross-competitions (Champions League, Europa League, etc.) not domestic leagues, reduce ELO influence to ~60%, and ensure home advantage is around 1.3 not 1.0.
- KickoffScore needs to handle newly promoted teams (like Sunderland to Premier League) that lack sufficient recent league data for the 10-match minimum requirement in enhanced predictions.
- KickoffScore has 200+ leagues and user wants xi parameter optimization per league as a future enhancement, preferring a systematic plan approach for large-scale optimization.
- User is interested in learning about ensemble methods for prediction systems.

# League Configuration
- League configuration data is stored at /Users/<USER>/Downloads/kickoffscore/src/config/leagueTiers.ts.

# Frontend Development
- User wants to create a website frontend for KickoffScore similar to betmines.com that provides predictions for upcoming fixtures, live scores, and fixture information using the existing backend endpoints.
- KickoffScore backend is already running on live server, and there's a separate frontend directory that should be used for frontend development instead of running npm run dev from root.
- For KickoffScore frontend: table headers should appear only once at top (not under each league), add logos to sidebar leagues/countries, and use API-Football's country flag URLs instead of external flag services.
- For KickoffScore frontend: move fixture Status (like FT) under Fav icon, remove league logos from league headers (keep only country logos), add league logos to sidebar top leagues (remove country logos), and reduce spacing above league headers.