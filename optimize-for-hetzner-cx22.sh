#!/bin/bash

# Hetzner CX22 Optimization Script
# Optimizes KickoffScore for 2 vCPU, 4GB RAM configuration

set -e

REMOTE_SERVER="**************"
REMOTE_USER="root"

echo "🚀 Hetzner CX22 Optimization for KickoffScore"
echo "=============================================="
echo "Server: $REMOTE_SERVER (2 vCPU, 4GB RAM)"
echo "Time: $(date)"
echo ""

# Check current system resources
ssh $REMOTE_USER@$REMOTE_SERVER << 'EOF'
echo "📊 CURRENT SYSTEM RESOURCES"
echo "============================"
echo "CPU Info:"
nproc
cat /proc/cpuinfo | grep "model name" | head -1
echo ""

echo "Memory Info:"
free -h
echo ""

echo "Current PM2 Status:"
pm2 status
echo ""

echo "Current Memory Usage by Process:"
ps aux --sort=-%mem | head -10
EOF

echo ""
echo "🔧 OPTIMIZATION RECOMMENDATIONS FOR HETZNER CX22"
echo "================================================="
echo ""

echo "1. 📈 CURRENT CONFIGURATION ANALYSIS:"
echo "   • Single instance (not utilizing 2 vCPUs optimally)"
echo "   • 2GB memory limit (50% of available 4GB)"
echo "   • Fork mode (not cluster mode)"
echo ""

echo "2. 🎯 RECOMMENDED OPTIMIZATIONS:"
echo "   • Use cluster mode with 2 instances (1 per vCPU)"
echo "   • Increase memory limit to 1.5GB per instance"
echo "   • Enable load balancing across instances"
echo "   • Optimize MongoDB and Redis memory usage"
echo ""

echo "3. 💾 MEMORY ALLOCATION STRATEGY:"
echo "   • Node.js instances: 3GB (2 × 1.5GB)"
echo "   • MongoDB: ~512MB"
echo "   • Redis: ~128MB"
echo "   • System: ~384MB"
echo "   • Total: ~4GB (optimal utilization)"
echo ""

read -p "🤔 Do you want to apply these optimizations? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Optimization cancelled."
    exit 0
fi

echo ""
echo "🔄 APPLYING HETZNER CX22 OPTIMIZATIONS..."
echo "========================================="

# Create optimized ecosystem config
cat > ecosystem.config.js << 'ECOSYSTEM_EOF'
module.exports = {
  apps: [{
    name: "kickoffscore-api",
    script: "dist/server.js",
    cwd: "/var/www/api.kickoffpredictions.com",
    instances: 2, // Utilize both vCPUs
    exec_mode: "cluster", // Enable cluster mode
    autorestart: true,
    watch: false,
    max_memory_restart: "1536M", // 1.5GB per instance
    min_uptime: "10s",
    max_restarts: 5,
    env: {
      NODE_ENV: "production",
      NODE_OPTIONS: "--max-old-space-size=1536" // Optimize V8 heap
    },
    log_date_format: "YYYY-MM-DD HH:mm:ss",
    error_file: "/var/www/api.kickoffpredictions.com/logs/error.log",
    out_file: "/var/www/api.kickoffpredictions.com/logs/out.log",
    merge_logs: true,
    // Performance optimizations
    node_args: [
      "--max-old-space-size=1536",
      "--optimize-for-size"
    ]
  }]
};
ECOSYSTEM_EOF

echo "✅ Created optimized ecosystem.config.js for Hetzner CX22"

# Build and deploy
echo "📦 Building optimized version..."
npm run build

echo "📦 Creating deployment package..."
tar -czf hetzner-cx22-optimized.tar.gz \
    dist/ \
    package.json \
    package-lock.json \
    ecosystem.config.js

echo "🌐 Uploading to server..."
scp hetzner-cx22-optimized.tar.gz $REMOTE_USER@$REMOTE_SERVER:/tmp/

# Deploy optimizations
ssh $REMOTE_USER@$REMOTE_SERVER << 'EOF'
set -e

echo "🛑 Stopping current services..."
pm2 stop all || echo "No processes to stop"
pm2 delete all || echo "No processes to delete"

echo "🗂️ Updating deployment..."
cd /var/www/api.kickoffpredictions.com

# Backup .env
if [ -f ".env" ]; then
    cp .env /tmp/.env.backup
fi

# Extract optimized version
tar -xzf /tmp/hetzner-cx22-optimized.tar.gz -C /var/www/api.kickoffpredictions.com/

# Restore .env
if [ -f "/tmp/.env.backup" ]; then
    cp /tmp/.env.backup .env
    rm /tmp/.env.backup
fi

echo "🧹 Optimizing system for Hetzner CX22..."

# Optimize MongoDB for limited memory
echo "Configuring MongoDB for 512MB memory limit..."
sudo tee /etc/mongod.conf.optimized << 'MONGO_EOF'
# MongoDB configuration optimized for Hetzner CX22
storage:
  dbPath: /var/lib/mongodb
  journal:
    enabled: true
  wiredTiger:
    engineConfig:
      cacheSizeGB: 0.5  # Limit to 512MB
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

net:
  port: 27017
  bindIp: 127.0.0.1

processManagement:
  fork: true
  pidFilePath: /var/run/mongodb/mongod.pid

setParameter:
  failIndexKeyTooLong: false
MONGO_EOF

# Backup original and apply optimized config
sudo cp /etc/mongod.conf /etc/mongod.conf.backup
sudo cp /etc/mongod.conf.optimized /etc/mongod.conf

echo "🔄 Restarting MongoDB with optimized settings..."
sudo systemctl restart mongod

echo "⚡ Configuring Redis for optimal memory usage..."
# Optimize Redis configuration
sudo tee -a /etc/redis/redis.conf << 'REDIS_EOF'

# Hetzner CX22 optimizations
maxmemory 128mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
REDIS_EOF

echo "🔄 Restarting Redis with optimized settings..."
sudo systemctl restart redis

echo "🚀 Starting optimized cluster..."
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

echo "⏱️ Waiting for cluster to stabilize..."
sleep 15

echo "📊 OPTIMIZATION RESULTS:"
echo "======================="
pm2 status

echo ""
echo "🧠 Memory Usage After Optimization:"
pm2 show kickoffscore-api

echo ""
echo "💾 System Memory Usage:"
free -h

echo ""
echo "🧪 Testing optimized endpoints..."
CURRENT_DATE=$(date +%Y-%m-%d)

# Test with timeout to ensure responsiveness
if timeout 5 curl -f -H "X-API-Key: 1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756" \
   "http://localhost:3000/api/fixtures?date=$CURRENT_DATE" > /dev/null 2>&1; then
    echo "✅ Fixtures API responding quickly"
else
    echo "⚠️ Fixtures API test failed or slow"
fi

if timeout 5 curl -f -H "X-API-Key: 1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756" \
   "http://localhost:3000/api/enhanced-predictions/1411542" > /dev/null 2>&1; then
    echo "✅ Enhanced predictions API responding quickly"
else
    echo "⚠️ Enhanced predictions API test failed or slow"
fi

echo ""
echo "🧹 Cleaning up..."
rm -f /tmp/hetzner-cx22-optimized.tar.gz

echo ""
echo "✅ HETZNER CX22 OPTIMIZATION COMPLETE!"
echo "======================================"
echo ""
echo "🎯 Applied Optimizations:"
echo "   • Cluster mode with 2 instances (utilizing both vCPUs)"
echo "   • 1.5GB memory limit per instance"
echo "   • MongoDB limited to 512MB cache"
echo "   • Redis limited to 128MB"
echo "   • V8 heap optimization"
echo ""
echo "📈 Expected Benefits:"
echo "   • Better CPU utilization (2 instances)"
echo "   • Improved fault tolerance"
echo "   • Load distribution"
echo "   • Optimal memory allocation"
echo ""
echo "📝 Monitor with:"
echo "   pm2 monit"
echo "   pm2 logs"

EOF

echo ""
echo "✅ Hetzner CX22 optimization deployment completed!"
echo ""
echo "🎯 Your server is now optimized for:"
echo "   • 2 vCPU utilization with cluster mode"
echo "   • 4GB RAM optimal allocation"
echo "   • Better performance and reliability"
