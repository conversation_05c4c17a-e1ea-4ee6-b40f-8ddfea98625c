import express, { Router, RequestHandler } from 'express';
import { getStandingsCollection, Standing, createStandingId } from '../models/Standing';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();
const STANDINGS_BASE_CACHE_KEY = 'standings:';
const CACHE_TTL_SECONDS = 60 * 5; // Cache for 5 minutes (standings update frequently)

// Define the handler function with <PERSON>questHand<PERSON> type
const getStandingsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { leagueId, season, teamId } = req.query; // Allow optional teamId filter

    // Validate required parameters
    // Either (leagueId AND season) OR (teamId AND season) must be provided
    if ((!leagueId && !teamId) || !season) {
        res.status(400).json({ message: 'Missing required query parameters: either (leagueId AND season) OR (teamId AND season) must be provided' });
        return;
    }

    const seasonNum = parseInt(season as string);
    const teamNum = teamId ? parseInt(teamId as string) : undefined;
    const leagueNum = leagueId ? parseInt(leagueId as string) : undefined;

    if ((leagueId && isNaN(leagueNum as number)) || isNaN(seasonNum) || (teamId && isNaN(teamNum as number))) {
        res.status(400).json({ message: 'Invalid numeric value for leagueId, season, or teamId' });
        return;
    }

    // Handle different cache keys based on the provided parameters
    let cacheKey: string;

    if (leagueNum && teamNum) {
        // Case 1: Both league and team provided
        const standingId = createStandingId(leagueNum, seasonNum);
        cacheKey = `${STANDINGS_BASE_CACHE_KEY}${standingId}:team:${teamNum}`;
    } else if (leagueNum) {
        // Case 2: Only league provided
        const standingId = createStandingId(leagueNum, seasonNum);
        cacheKey = `${STANDINGS_BASE_CACHE_KEY}${standingId}`;
    } else {
        // Case 3: Only team provided (no league)
        cacheKey = `${STANDINGS_BASE_CACHE_KEY}team:${teamNum}:season:${seasonNum}`;
    }

    try {
        // 1. Check cache
        const cachedStandings = await redisClient.get(cacheKey);
        if (cachedStandings) {
            console.log(`Serving standings from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedStandings));
            return;
        }

        // 2. Fetch from DB
        const collection = getStandingsCollection();
        const projection = { lastUpdated: 0 }; // Exclude lastUpdated field
        let resultData: any[][] = [];

        if (leagueNum) {
            // Case 1 or 2: League is provided
            const standingId = createStandingId(leagueNum, seasonNum);
            console.log(`Fetching standings from DB (ID: ${standingId})`);

            const standingDoc = await collection.findOne({ _id: standingId }, { projection });

            if (!standingDoc) {
                res.status(404).json({ message: 'Standings not found for the specified league and season.' });
                return;
            }

            resultData = standingDoc.standings; // Default to all standings groups

            // Filter by teamId if provided (Case 1)
            if (teamNum !== undefined) {
                console.log(`Filtering standings for team ID: ${teamNum}`);
                const filteredStandings: any[][] = [];
                standingDoc.standings.forEach(group => {
                    const teamEntry = group.find(team => team.team.id === teamNum);
                    if (teamEntry) {
                        // If team found, return only that team's entry within its group structure
                        filteredStandings.push([teamEntry]); // Return as array within array to maintain structure
                    }
                });
                resultData = filteredStandings; // Update result data to filtered version
            }
        } else {
            // Case 3: Only team is provided (no league)
            console.log(`Fetching standings for team ID: ${teamNum} across all leagues for season: ${seasonNum}`);

            // Find all standings documents for the specified season
            const allStandings = await collection.find({
                'league.season': seasonNum
            }, { projection }).toArray();

            if (!allStandings || allStandings.length === 0) {
                res.status(404).json({ message: 'No standings found for the specified season.' });
                return;
            }

            // Filter all standings to find the team
            const teamStandings: any[][] = [];

            for (const standing of allStandings) {
                standing.standings.forEach(group => {
                    const teamEntry = group.find(team => team.team.id === teamNum);
                    if (teamEntry) {
                        // Add league info to the team entry
                        const enrichedTeamEntry = {
                            ...teamEntry,
                            leagueId: standing.leagueId,
                            leagueName: standing.league?.apiId?.toString() || 'Unknown League'
                        };
                        teamStandings.push([enrichedTeamEntry]);
                    }
                });
            }

            resultData = teamStandings;
        }

        // 4. Store in cache (cache the filtered or full result based on cacheKey)
        // Only cache if data was found (or if caching empty results is desired)
        if (resultData.length > 0 || (teamNum !== undefined && resultData.length === 0)) { // Cache empty result if filtered by team
             await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(resultData));
             console.log(`Standings stored in cache (Key: ${cacheKey})`);
        }


        res.status(200).json(resultData);

    } catch (error) {
        console.error(`Error fetching standings:`, error);
        res.status(500).json({ message: 'Failed to fetch standings' });
    }
};

// GET /api/standings?leagueId=X&season=Y[&teamId=Z] - Get standings by league and season, optionally filtered by team
// GET /api/standings?team=X&season=Y - Get standings for a team across all leagues
router.get('/', getStandingsHandler);

export default router;
