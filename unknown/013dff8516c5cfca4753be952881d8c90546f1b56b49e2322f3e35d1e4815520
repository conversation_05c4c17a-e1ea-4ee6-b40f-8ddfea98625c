import { fetchTeamStatistics } from '../services/apiFootball';
import { getTeamStatisticsCollection, TeamStatistic, createTeamStatisticId } from '../models/TeamStatistic';
import { getLeaguesCollection } from '../models/League';
import { getTeamsCollection } from '../models/Team';
import { getFixturesCollection } from '../models/Fixture';
import { targetedLeagues } from '../config/targetedLeagues';
import { AnyBulkWriteOperation } from 'mongodb';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Optimized function to fetch and update team statistics based on the recommended usage pattern:
 * - 1 call per day for teams with a match today
 * - 1 call per week for teams without a match today
 *
 * This function is designed to be called daily and will automatically determine which teams
 * should have their statistics updated based on the above criteria.
 */
export async function fetchAndUpdateTeamStatistics() {
    console.log('Starting optimized fetchAndUpdateTeamStatistics job...');
    try {
        const leaguesCollection = getLeaguesCollection();
        const teamsCollection = getTeamsCollection();
        const fixturesCollection = getFixturesCollection();
        const statsCollection = getTeamStatisticsCollection();
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const isWeeklyUpdate = now.getDay() === 1; // Monday = weekly update day

        // 1. Find all targeted leagues with a currently active season
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: targetedLeagues },
                "seasons.current": true
            },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        if (!activeLeagues || activeLeagues.length === 0) {
            console.log('No active leagues found to fetch team statistics for.');
            return;
        }

        console.log(`Found ${activeLeagues.length} targeted leagues with active seasons for stats update.`);
        console.log(`Targeted leagues: ${activeLeagues.map(l => l._id).join(', ')}`);

        // 2. Get teams with matches today
        // Find all fixtures for today
        const todayStart = new Date(today);
        const todayEnd = new Date(today);
        todayEnd.setDate(todayEnd.getDate() + 1);

        const todayFixtures = await fixturesCollection.find({
            date: { $gte: todayStart, $lt: todayEnd }
        }).toArray();

        // Extract unique team IDs from today's fixtures
        const teamsWithMatchesToday = new Set<number>();
        todayFixtures.forEach(fixture => {
            if (fixture.teams?.home?.id) teamsWithMatchesToday.add(fixture.teams.home.id);
            if (fixture.teams?.away?.id) teamsWithMatchesToday.add(fixture.teams.away.id);
        });

        console.log(`Found ${teamsWithMatchesToday.size} teams with matches today.`);

        // 3. Process each active league
        let totalUpserted = 0;
        let totalModified = 0;
        let teamsProcessed = 0;
        let teamsSkipped = 0;

        for (const league of activeLeagues) {
            const currentSeason = league.seasons.find(s => s.current);
            if (!currentSeason) continue;

            const leagueId = league._id;
            const seasonYear = currentSeason.year;

            // Find teams that are associated with this league/season
            // Only get teams that are in this specific league and season
            const teamsInLeague = await teamsCollection.find({
                "leagues": {
                    $elemMatch: {
                        apiId: leagueId,
                        season: seasonYear
                    }
                }
            }).toArray();

            if (!teamsInLeague || teamsInLeague.length === 0) {
                console.log(`No teams found for League ID: ${leagueId}, Season: ${seasonYear}. Skipping.`);
                continue;
            }

            console.log(`Processing League ID: ${leagueId}, Season: ${seasonYear}. Found ${teamsInLeague.length} teams.`);
            console.log(`Teams in League ${leagueId}: ${teamsInLeague.map(t => t._id).join(', ')}`);

            // Skip leagues with too many teams (likely incorrect data)
            if (teamsInLeague.length > 50) {
                console.log(`WARNING: League ${leagueId} has ${teamsInLeague.length} teams, which is unusually high. Skipping to prevent excessive API calls.`);
                continue;
            }

            for (const team of teamsInLeague) {
                const teamId = team._id;

                // Check if this team should be updated today based on our criteria:
                // 1. Teams with matches today should always be updated
                // 2. All teams should be updated on the weekly update day (Monday)
                // 3. Teams without matches today should only be updated on the weekly update day
                const hasMatchToday = teamsWithMatchesToday.has(teamId);

                if (!hasMatchToday && !isWeeklyUpdate) {
                    // Skip teams without matches today on non-weekly update days
                    teamsSkipped++;
                    continue;
                }

                // Check when this team's stats were last updated
                const statId = createTeamStatisticId(teamId, leagueId, seasonYear);
                const existingStat = await statsCollection.findOne({ _id: statId }, { projection: { lastUpdated: 1 } });

                // If we have stats and they were updated today, skip unless it's a team with a match today
                if (existingStat && existingStat.lastUpdated) {
                    const lastUpdated = new Date(existingStat.lastUpdated);
                    const isUpdatedToday = lastUpdated.getDate() === today.getDate() &&
                                          lastUpdated.getMonth() === today.getMonth() &&
                                          lastUpdated.getFullYear() === today.getFullYear();

                    if (isUpdatedToday && !hasMatchToday) {
                        // Already updated today and no match today, skip
                        teamsSkipped++;
                        continue;
                    }
                }

                console.log(`  Fetching stats for Team ID: ${teamId}, League: ${leagueId}, Season: ${seasonYear}${hasMatchToday ? ' (has match today)' : ''}`);
                teamsProcessed++;

                try {
                    const statsFromApi = await fetchTeamStatistics({ league: leagueId, season: seasonYear, team: teamId });

                    // API returns null or empty object if no stats found for the combo
                    if (!statsFromApi || !statsFromApi.league || !statsFromApi.team) {
                        console.log(`  No stats found for Team ${teamId}, League ${leagueId}, Season ${seasonYear}.`);
                        continue; // Skip this team
                    }

                    // Log the full team stats structure for debugging
                    if (process.env.DEBUG_TEAM_STATS === 'true') {
                        console.log(`Full team stats for Team ID: ${teamId}:`, JSON.stringify(statsFromApi, null, 2));
                    }

                    const updateDoc: TeamStatistic = {
                        ...statsFromApi, // Spread the fields from the API response
                        _id: statId,
                        lastUpdated: now,
                    };

                    // Use updateOne with upsert for each stat record
                    const result = await statsCollection.updateOne(
                        { _id: statId },
                        { $set: updateDoc },
                        { upsert: true }
                    );

                    if (result.upsertedCount > 0) totalUpserted++;
                    if (result.modifiedCount > 0) totalModified++;

                    // Delay between each team's stat fetch to respect API rate limits
                    await delay(1000); // Increased delay to 1 second to better respect API rate limits

                } catch (statError: any) {
                    // Handle cases where API returns error (e.g., team not in league for that season)
                    if (statError.message?.includes('Unexpected data structure')) {
                        console.log(`  Skipping Team ${teamId}, League ${leagueId}, Season ${seasonYear} due to unexpected data (likely not applicable).`);
                    } else {
                        console.error(`  Error fetching stats for Team ${teamId}, League ${leagueId}, Season ${seasonYear}:`, statError);
                    }
                    // Continue to the next team even if one fails
                    await delay(100); // Shorter delay on error
                }
            }
            // Longer delay between leagues to respect API rate limits
            await delay(3000); // Increased to 3 seconds between leagues
        }

        console.log(`Optimized fetchAndUpdateTeamStatistics job finished.`);
        console.log(`Teams processed: ${teamsProcessed}, Teams skipped: ${teamsSkipped}`);
        console.log(`Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateTeamStatistics job:', error);
    }
}
