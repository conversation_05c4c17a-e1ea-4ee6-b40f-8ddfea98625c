import { spawn } from 'child_process';
import path from 'path';

// Start the server
console.log('Starting server...');
const server = spawn('ts-node', [path.join(__dirname, '../server.ts')], {
  stdio: 'pipe',
  env: process.env
});

// Listen for server output
server.stdout.on('data', (data) => {
  console.log(`Server: ${data.toString().trim()}`);
  
  // Once the server is running, run the tests
  if (data.toString().includes('Server listening on port')) {
    console.log('Server is running. Starting tests...');
    
    // Run the tests
    const tests = spawn('ts-node', [path.join(__dirname, './endpointTests.ts')], {
      stdio: 'inherit',
      env: process.env
    });
    
    // When tests are done, kill the server
    tests.on('close', (code) => {
      console.log(`Tests exited with code ${code}`);
      server.kill();
      process.exit(code || 0);
    });
  }
});

// Handle server errors
server.stderr.on('data', (data) => {
  console.error(`Server Error: ${data.toString().trim()}`);
});

// Handle server exit
server.on('close', (code) => {
  console.log(`Server exited with code ${code}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Process interrupted. Shutting down...');
  server.kill();
  process.exit(0);
});
