import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { fetchAndUpdateBookmakers, fetchAndUpdateBets, fetchAndUpdateOdds, fetchAndUpdateLiveOdds } from '../jobs/oddsJobs';

// Load environment variables
dotenv.config();

// Main function
async function runOddsJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        connectRedis();
        
        console.log('Running fetchAndUpdateBookmakers job...');
        await fetchAndUpdateBookmakers();
        
        console.log('Running fetchAndUpdateBets job...');
        await fetchAndUpdateBets();
        
        console.log('Running fetchAndUpdateOdds job...');
        await fetchAndUpdateOdds();
        
        console.log('Running fetchAndUpdateLiveOdds job...');
        await fetchAndUpdateLiveOdds();
        
        console.log('Odds jobs completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running odds jobs:', error);
        process.exit(1);
    }
}

// Run the job
runOddsJob();
