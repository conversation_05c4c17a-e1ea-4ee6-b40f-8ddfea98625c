import { fetchSidelined } from '../services/sidelinedService';
import { getSidelinedCollection, Sidelined, createSidelinedId } from '../models/Sidelined';
import { getTeamsCollection } from '../models/Team';
import { getLeaguesCollection } from '../models/League';
import { AnyBulkWriteOperation } from 'mongodb';
import { targetedLeagues } from '../config/targetedLeagues';

// Helper functions for execution control
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Retry a function with exponential backoff
 * @param fn Function to retry
 * @param retries Maximum number of retries
 * @param initialDelay Initial delay in ms
 * @param maxDelay Maximum delay in ms
 * @returns Result of the function
 */
async function retryWithBackoff<T>(
    fn: () => Promise<T>,
    retries = 3,
    initialDelay = 1000,
    maxDelay = 10000
): Promise<T> {
    let numRetries = 0;
    let delay = initialDelay;
    
    while (true) {
        try {
            return await fn();
        } catch (error) {
            if (numRetries >= retries) {
                throw error;
            }
            
            console.log(`Operation failed, retrying (${numRetries + 1}/${retries}) after ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            
            // Exponential backoff with jitter
            delay = Math.min(delay * 2, maxDelay) * (0.8 + Math.random() * 0.4);
            numRetries++;
        }
    }
}

// Fetch sidelined players for active teams
export async function fetchAndUpdateTeamSidelined() {
    console.log('Starting fetchAndUpdateTeamSidelined job...');
    try {
        const teamsCollection = getTeamsCollection();
        const sidelinedCollection = getSidelinedCollection();
        const now = new Date();

        // Get teams from targeted leagues
        // First, get teams that belong to targeted leagues
        const leaguesCollection = getLeaguesCollection();

        // Find teams that are associated with targeted leagues using the leagues array structure
        // This query finds teams where at least one of the leagues in the leagues array
        // has an apiId that matches one of our targeted leagues
        const teams = await teamsCollection.find({
            'leagues.apiId': { $in: targetedLeagues },
            '_id': { $type: 16 } // Keep the numeric ID filter, but use it directly
        }).toArray();
        
        console.log(`Found ${teams.length} teams with targeted leagues.`);

        if (!teams || teams.length === 0) {
            console.log('No teams found to fetch sidelined players for.');
            return;
        }

        console.log(`Found ${teams.length} teams.`);

        let totalUpserted = 0;
        let totalModified = 0;

        for (const team of teams) {
            const teamId = team._id;

            console.log(`Fetching sidelined players for Team ID: ${teamId}`);

            try {
                // The API doesn't support team parameter for sidelined endpoint
                // Instead, we'll fetch players for the team and then fetch sidelined data for each player
                const { getPlayersCollection } = await import('../models/Player');
                const playersCollection = getPlayersCollection();

                // Get players for this team
                // Based on database analysis, we have identified specific patterns
                // that link players to teams in the database
                
                // Create a targeted query that uses the exact patterns we've identified
                const players = await playersCollection.find({
                    $and: [
                        { apiId: { $type: 16 } }, // Only get players with numeric apiId
                        { $or: [
                            // The following patterns have been confirmed to exist in the database
                            { 'statistics.1:2022.0.team.id': teamId },
                            { 'statistics.4:2024.0.team.id': teamId },
                            { 'statistics.4:2020.0.team.id': teamId },
                            { 'statistics.2:2022.0.team.id': teamId },
                            { 'statistics.3:2023.0.team.id': teamId },
                            { 'statistics.5:2024.0.team.id': teamId },
                            { 'statistics.1:2018.0.team.id': teamId },
                            { 'statistics.1:2019.0.team.id': teamId },
                            { 'statistics.1:2021.0.team.id': teamId },
                            // Also include traditional patterns as fallbacks
                            { 'statistics.*.team.id': teamId }
                        ]}
                    ]
                }).limit(30).toArray(); // Limit to 30 players per team
                
                // Log detailed debugging info
                if (teamId === 7) {
                    console.log(`Debug - Using targeted query for Team ${teamId}: Count manually tested = 26`);
                    const countCheck = await playersCollection.countDocuments({ 'statistics.1:2022.0.team.id': teamId });
                    console.log(`Debug - Actual count for statistics.1:2022.0.team.id = ${countCheck}`);
                }

                if (!players || players.length === 0) {
                    console.log(`No players found for Team ${teamId}.`);
                    continue;
                }

                console.log(`Found ${players.length} players for Team ${teamId}.`);

                // Create team object for sidelined records
                const teamObj = {
                    id: teamId,
                    name: team.team?.name || `Team ${teamId}`,
                    logo: team.team?.logo || null
                };

                let teamSidelinedCount = 0;

                // Extract player IDs for batch request (max 20 per batch)
                const BATCH_SIZE = 20;
                const playerIds = players.map(player => player.apiId).filter(id => id !== undefined);

                // Process players in batches
                for (let i = 0; i < playerIds.length; i += BATCH_SIZE) {
                    const batchIds = playerIds.slice(i, i + BATCH_SIZE);
                    const idsString = batchIds.join('-');

                    try {
                        console.log(`Fetching sidelined data for batch ${Math.floor(i/BATCH_SIZE) + 1}: ${idsString}`);

                        // Use the 'players' parameter for batch request
                        const sidelinedPlayers = await fetchSidelined({ players: idsString });

                        if (!sidelinedPlayers || sidelinedPlayers.length === 0) {
                            console.log(`No sidelined data found for players in batch ${Math.floor(i/BATCH_SIZE) + 1}.`);
                            continue;
                        }

                        console.log(`Received ${sidelinedPlayers.length} sidelined records for batch ${Math.floor(i/BATCH_SIZE) + 1}.`);
                        teamSidelinedCount += sidelinedPlayers.length;

                        // The response format for 'players' parameter is different
                        // Each item in the response has an 'id' and 'sidelined' array
                        const sidelinedByPlayer: Record<number, any[]> = {};

                        for (const playerSidelined of sidelinedPlayers) {
                            const playerId = playerSidelined.id;
                            sidelinedByPlayer[playerId] = playerSidelined.sidelined;
                        }

                        const bulkOps: AnyBulkWriteOperation<Sidelined>[] = [];

                        // Process each player's sidelined records
                        for (const player of players) {
                            const playerId = player.apiId;
                            if (!playerId || !sidelinedByPlayer[playerId]) continue;

                            const playerSidelined = sidelinedByPlayer[playerId];

                            for (const sidelined of playerSidelined) {
                                const type = sidelined.type || 'Unknown';
                                const start = sidelined.start || new Date().toISOString();
                                const sidelinedId = createSidelinedId(playerId, type, start);

                                // Create sidelined document
                                const sidelinedDoc: Sidelined = {
                                    _id: sidelinedId,
                                    player: {
                                        id: playerId,
                                        name: player.profile?.name || `Player ${playerId}`,
                                        photo: player.profile?.photo || null
                                    },
                                    team: teamObj,
                                    type: type,
                                    reason: null, // The batch API doesn't return reason
                                    start: start,
                                    end: sidelined.end || null,
                                    lastUpdated: now
                                };

                                bulkOps.push({
                                    updateOne: {
                                        filter: { _id: sidelinedId },
                                        update: { $set: sidelinedDoc },
                                        upsert: true
                                    }
                                });
                            }
                        }

                        if (bulkOps.length > 0) {
                            const result = await sidelinedCollection.bulkWrite(bulkOps);
                            totalUpserted += result.upsertedCount;
                            totalModified += result.modifiedCount;
                        }

                        // Add a delay between batches to respect API rate limits
                        await delay(1000);

                    } catch (batchError) {
                        console.error(`Error fetching sidelined status for batch ${Math.floor(i/BATCH_SIZE) + 1}:`, batchError);
                        await delay(2000); // Longer delay after an error
                    }
                }

                console.log(`Team ${teamId} sidelined players updated. Found ${teamSidelinedCount} sidelined records.`);

                // Add a delay between teams to respect API rate limits
                await delay(1000);

            } catch (teamError) {
                console.error(`Error processing Team ${teamId}:`, teamError);
                // Continue to the next team even if one fails
                await delay(2000); // Longer delay after an error
            }
        }

        console.log(`fetchAndUpdateTeamSidelined job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateTeamSidelined job:', error);
    }
}

// Fetch sidelined players by player ID for a specific league
export async function fetchAndUpdateLeagueSidelined() {
    console.log('Starting fetchAndUpdateLeagueSidelined job...');
    try {
        // Import the Player model
        const { getPlayersCollection } = await import('../models/Player');
        const playersCollection = getPlayersCollection();
        const sidelinedCollection = getSidelinedCollection();
        const teamsCollection = getTeamsCollection();
        const now = new Date();

        // Get players from targeted leagues
        // First, get teams that belong to targeted leagues
        const leaguesCollection = getLeaguesCollection();

        // Process each targeted league
        for (const leagueId of targetedLeagues) {
            console.log(`Processing league ID: ${leagueId}`);

            try {
                // Get the league details
                const league = await leaguesCollection.findOne({ _id: leagueId });
                if (!league) {
                    console.log(`League ${leagueId} not found.`);
                    continue;
                }

                const leagueName = league.league?.name || `League ${leagueId}`;
                const seasonYear = league.seasons?.[0]?.year || new Date().getFullYear();

                console.log(`Processing ${leagueName} (Season: ${seasonYear})`);

                // Get teams in this league using the leagues array structure
                const teams = await teamsCollection.find({
                    'leagues.apiId': leagueId,
                    '_id': { $type: 16 } // Only get teams with numeric IDs
                }).toArray();

                if (!teams || teams.length === 0) {
                    console.log(`No teams found for League ${leagueId} (Season: ${seasonYear}).`);
                    continue;
                }

                console.log(`Found ${teams.length} teams in League ${leagueId} (Season: ${seasonYear}).`);

                let leagueTotalUpserted = 0;
                let leagueTotalModified = 0;

                // Process each team in the league
                for (const team of teams) {
                    const teamId = team._id;
                    const teamName = team.team?.name || `Team ${teamId}`;

                    console.log(`Processing team: ${teamName} (ID: ${teamId})`);

                    // Create team object for sidelined records
                    const teamObj = {
                        id: teamId,
                        name: teamName,
                        logo: team.team?.logo || null
                    };

                    // Get players for this team
                    // Based on database analysis, we have identified specific patterns
                    // that link players to teams in the database
                    
                    // Create a targeted query that uses the exact patterns we've identified
                    const players = await playersCollection.find({
                        $and: [
                            { apiId: { $type: 16 } }, // Only get players with numeric apiId
                            { $or: [
                                // The following patterns have been confirmed to exist in the database
                                { 'statistics.1:2022.0.team.id': teamId },
                                { 'statistics.4:2024.0.team.id': teamId },
                                { 'statistics.4:2020.0.team.id': teamId },
                                { 'statistics.2:2022.0.team.id': teamId },
                                { 'statistics.3:2023.0.team.id': teamId },
                                { 'statistics.5:2024.0.team.id': teamId },
                                { 'statistics.1:2018.0.team.id': teamId },
                                { 'statistics.1:2019.0.team.id': teamId },
                                { 'statistics.1:2021.0.team.id': teamId },
                                // Also include traditional patterns as fallbacks
                                { 'statistics.*.team.id': teamId }
                            ]}
                        ]
                    }).limit(20).toArray(); // Limit to 20 players per team

                    if (!players || players.length === 0) {
                        console.log(`No players found for Team ${teamName}.`);
                        continue;
                    }

                    console.log(`Found ${players.length} players for Team ${teamName}.`);

                    // Extract player IDs for batch request (max 20 per batch)
                    const BATCH_SIZE = 20;
                    const playerIds = players.map(player => player.apiId).filter(id => id !== undefined);

                    // Process players in batches
                    for (let i = 0; i < playerIds.length; i += BATCH_SIZE) {
                        const batchIds = playerIds.slice(i, i + BATCH_SIZE);
                        const idsString = batchIds.join('-');

                        try {
                            console.log(`Fetching sidelined data for batch ${Math.floor(i/BATCH_SIZE) + 1}: ${idsString}`);

                            // Use the 'players' parameter for batch request
                            const sidelinedPlayers = await fetchSidelined({ players: idsString });

                            if (!sidelinedPlayers || sidelinedPlayers.length === 0) {
                                console.log(`No sidelined data found for players in batch ${Math.floor(i/BATCH_SIZE) + 1}.`);
                                continue;
                            }

                            console.log(`Received ${sidelinedPlayers.length} sidelined records for batch ${Math.floor(i/BATCH_SIZE) + 1}.`);

                            // The response format for 'players' parameter is different
                            // Each item in the response has an 'id' and 'sidelined' array
                            const sidelinedByPlayer: Record<number, any[]> = {};

                            for (const playerSidelined of sidelinedPlayers) {
                                const playerId = playerSidelined.id;
                                sidelinedByPlayer[playerId] = playerSidelined.sidelined;
                            }

                            const bulkOps: AnyBulkWriteOperation<Sidelined>[] = [];

                            // Process each player's sidelined records
                            for (const player of players) {
                                const playerId = player.apiId;
                                if (!playerId || !sidelinedByPlayer[playerId]) continue;

                                const playerName = player.profile?.name || `Player ${playerId}`;
                                const playerSidelined = sidelinedByPlayer[playerId];

                                console.log(`Found ${playerSidelined.length} sidelined record(s) for Player ${playerName}.`);

                                for (const sidelined of playerSidelined) {
                                    const type = sidelined.type || 'Unknown';
                                    const start = sidelined.start || new Date().toISOString();
                                    const sidelinedId = createSidelinedId(playerId, type, start);

                                    // Create sidelined document
                                    const sidelinedDoc: Sidelined = {
                                        _id: sidelinedId,
                                        player: {
                                            id: playerId,
                                            name: playerName,
                                            photo: player.profile?.photo || null
                                        },
                                        team: teamObj,
                                        type: type,
                                        reason: null, // The batch API doesn't return reason
                                        start: start,
                                        end: sidelined.end || null,
                                        lastUpdated: now
                                    };

                                    bulkOps.push({
                                        updateOne: {
                                            filter: { _id: sidelinedId },
                                            update: { $set: sidelinedDoc },
                                            upsert: true
                                        }
                                    });
                                }
                            }

                            if (bulkOps.length > 0) {
                                const result = await sidelinedCollection.bulkWrite(bulkOps);
                                leagueTotalUpserted += result.upsertedCount;
                                leagueTotalModified += result.modifiedCount;
                            }

                            // Add a delay between batches to respect API rate limits
                            await delay(1000);

                        } catch (batchError) {
                            console.error(`Error fetching sidelined status for batch ${Math.floor(i/BATCH_SIZE) + 1}:`, batchError);
                            await delay(2000); // Longer delay after an error
                        }
                    }

                    // Add a delay between teams to respect API rate limits
                    await delay(1000);
                }

                console.log(`League ${leagueName} sidelined players updated. Upserted: ${leagueTotalUpserted}, Modified: ${leagueTotalModified}`);

            } catch (leagueError) {
                console.error(`Error processing League ${leagueId}:`, leagueError);
                await delay(2000); // Longer delay after an error
            }
        }

        console.log(`fetchAndUpdateLeagueSidelined job finished.`);

    } catch (error) {
        console.error('Error in fetchAndUpdateLeagueSidelined job:', error);
    }
}
