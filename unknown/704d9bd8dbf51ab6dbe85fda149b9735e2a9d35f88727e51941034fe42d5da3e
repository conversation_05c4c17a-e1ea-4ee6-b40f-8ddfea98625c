import express, { Router, RequestHandler } from 'express';
import { getVenuesCollection, Venue } from '../models/Venue';
import { getRedisClient } from '../config/redis';
import { Filter } from 'mongodb';

const router: Router = express.Router();
const VENUES_BASE_CACHE_KEY = 'venues:';
const CACHE_TTL_SECONDS = 60 * 60; // Cache for 1 hour

// Define the handler function with <PERSON>questHandler type
const getVenuesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const queryParams = req.query;

    // Build MongoDB filter dynamically based on query params
    const filter: Filter<Venue> = {};
    if (queryParams.id) filter._id = parseInt(queryParams.id as string); // Use _id for venue ID
    if (queryParams.name) filter.name = new RegExp(queryParams.name as string, 'i');
    if (queryParams.city) filter.city = new RegExp(queryParams.city as string, 'i');
    if (queryParams.country) filter.country = new RegExp(queryParams.country as string, 'i');
    if (queryParams.search) {
        const searchRegex = new RegExp(queryParams.search as string, 'i');
        filter.$or = [
            { name: searchRegex },
            { city: searchRegex },
            { country: searchRegex }
        ];
    }

    // Generate cache key based on query params (simple version: only cache unfiltered/by ID)
    let cacheKey: string | null = null;
    const isFiltered = Object.keys(queryParams).length > 0;
    if (!isFiltered) {
        cacheKey = `${VENUES_BASE_CACHE_KEY}all`;
    } else if (queryParams.id && Object.keys(queryParams).length === 1) {
        cacheKey = `${VENUES_BASE_CACHE_KEY}id:${queryParams.id}`;
    } // Add more specific cache keys if needed

    try {
        // 1. Check cache
        if (cacheKey) {
            const cachedVenues = await redisClient.get(cacheKey);
            if (cachedVenues) {
                console.log(`Serving venues from cache (Key: ${cacheKey})`);
                res.status(200).json(JSON.parse(cachedVenues));
                return;
            }
        }

        // 2. Fetch from DB using the constructed filter
        console.log('Fetching venues from DB with filter:', JSON.stringify(filter));
        const collection = getVenuesCollection();
        const projection = { lastUpdated: 0 }; // Exclude lastUpdated field
        // if (!filter._id) projection._id = 0; // Decide if you want _id

        const venues = await collection.find(filter, { projection }).sort({ name: 1 }).toArray();

        // 3. Store in cache (only for cached keys)
        if (cacheKey && venues.length > 0) {
            const dataToCache = (queryParams.id && venues.length === 1) ? venues[0] : venues;
            await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(dataToCache));
            console.log(`Venues stored in cache (Key: ${cacheKey})`);
        }

        // Handle single ID fetch result
        if (queryParams.id && venues.length === 0) {
            res.status(404).json({ message: 'Venue not found' });
            return;
        }
        if (queryParams.id && venues.length === 1) {
            res.status(200).json(venues[0]);
            return;
        }

        // Return array for general queries
        res.status(200).json(venues);

    } catch (error) {
        console.error('Error fetching venues:', error);
        res.status(500).json({ message: 'Failed to fetch venues' });
    }
};

// GET /api/venues - Fetches venues with optional filtering
router.get('/', getVenuesHandler);

export default router;
