import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interface based on API response structure for /venues
export interface Venue {
    _id: number; // Use venue ID from API as the MongoDB _id
    name: string | null;
    address: string | null;
    city: string | null;
    country: string | null;
    capacity: number | null;
    surface: string | null;
    image: string | null;
    lastUpdated: Date;
}

// Function to get the venues collection
export function getVenuesCollection(): Collection<Venue> {
    const db = getDb();
    return db.collection<Venue>('venues');
}

// Optional: Add helper functions for common operations
// e.g., findVenueById, findVenuesByCity, etc.
