import { fetchCoaches } from '../services/coachService';
import { getCoachesCollection, Coach } from '../models/Coach';
import { getTeamsCollection } from '../models/Team';
import { getLeaguesCollection } from '../models/League';
import { AnyBulkWriteOperation } from 'mongodb';
import { targetedLeagues } from '../config/targetedLeagues';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Fetch coaches for active teams
export async function fetchAndUpdateCoaches() {
    console.log('Starting fetchAndUpdateCoaches job...');
    try {
        const teamsCollection = getTeamsCollection();
        const coachesCollection = getCoachesCollection();
        const now = new Date();

        // Get teams from targeted leagues
        // First, get teams that belong to targeted leagues
        const leaguesCollection = getLeaguesCollection();

        // Find teams that are associated with targeted leagues
        const teamsInTargetedLeagues = await leaguesCollection.aggregate([
            { $match: { _id: { $in: targetedLeagues } } },
            { $lookup: {
                from: 'teams',
                localField: '_id',
                foreignField: 'league.id',
                as: 'teams'
            }},
            { $unwind: '$teams' },
            { $project: { 'teamId': '$teams._id' } }
        ]).toArray();

        const teamIds = teamsInTargetedLeagues.map(t => t.teamId);

        // Get all teams from targeted leagues with numeric IDs
        const teams = await teamsCollection.find({
            _id: { $in: teamIds, $type: 16 } // 16 is the BSON type code for Int32
        }).toArray();

        if (!teams || teams.length === 0) {
            console.log('No teams found to fetch coaches for.');
            return;
        }

        console.log(`Found ${teams.length} teams.`);

        let totalUpserted = 0;
        let totalModified = 0;

        for (const team of teams) {
            const teamId = team._id;

            console.log(`Fetching coach for Team ID: ${teamId}`);

            try {
                const coaches = await fetchCoaches({ team: teamId });

                if (!coaches || coaches.length === 0) {
                    console.log(`No coach information received for Team ${teamId}.`);
                    continue;
                }

                console.log(`Received ${coaches.length} coach(es) for Team ${teamId}.`);

                const bulkOps: AnyBulkWriteOperation<Coach>[] = [];

                for (const coach of coaches) {
                    const coachId = coach.id;

                    // Create coach document
                    const coachDoc: Coach = {
                        _id: coachId,
                        profile: {
                            id: coach.id,
                            name: coach.name,
                            firstname: coach.firstname,
                            lastname: coach.lastname,
                            age: coach.age,
                            birth: coach.birth,
                            nationality: coach.nationality,
                            height: coach.height,
                            weight: coach.weight,
                            photo: coach.photo
                        },
                        career: coach.career || [],
                        lastUpdated: now
                    };

                    bulkOps.push({
                        updateOne: {
                            filter: { _id: coachId },
                            update: { $set: coachDoc },
                            upsert: true
                        }
                    });
                }

                if (bulkOps.length > 0) {
                    const result = await coachesCollection.bulkWrite(bulkOps);
                    console.log(`Team ${teamId} coaches updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                    totalUpserted += result.upsertedCount;
                    totalModified += result.modifiedCount;
                }

                // Add a delay between teams to respect API rate limits
                await delay(1000);

            } catch (teamError) {
                console.error(`Error fetching coach for Team ${teamId}:`, teamError);
                // Continue to the next team even if one fails
                await delay(2000); // Longer delay after an error
            }
        }

        console.log(`fetchAndUpdateCoaches job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateCoaches job:', error);
    }
}
