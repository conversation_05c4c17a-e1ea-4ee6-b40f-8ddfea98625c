import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Basic player profile information
export interface PlayerProfile {
    id: number;
    name: string;
    firstname: string | null;
    lastname: string | null;
    age: number | null;
    birth: {
        date: string | null;
        place: string | null;
        country: string | null;
    };
    nationality: string | null;
    height: string | null;
    weight: string | null;
    injured: boolean | null;
    photo: string | null;
}

// Player statistics for a specific season
export interface PlayerStatistics {
    team: {
        id: number;
        name: string;
        logo: string | null;
    };
    league: {
        id: number;
        name: string;
        country: string | null;
        logo: string | null;
        flag: string | null;
        season: number;
    };
    games: {
        appearences: number | null;
        lineups: number | null;
        minutes: number | null;
        number: number | null;
        position: string | null;
        rating: string | null;
        captain: boolean;
    };
    substitutes: {
        in: number | null;
        out: number | null;
        bench: number | null;
    };
    shots: {
        total: number | null;
        on: number | null;
    };
    goals: {
        total: number | null;
        conceded: number | null;
        assists: number | null;
        saves: number | null;
    };
    passes: {
        total: number | null;
        key: number | null;
        accuracy: number | null;
    };
    tackles: {
        total: number | null;
        blocks: number | null;
        interceptions: number | null;
    };
    duels: {
        total: number | null;
        won: number | null;
    };
    dribbles: {
        attempts: number | null;
        success: number | null;
        past: number | null;
    };
    fouls: {
        drawn: number | null;
        committed: number | null;
    };
    cards: {
        yellow: number | null;
        yellowred: number | null;
        red: number | null;
    };
    penalty: {
        won: number | null;
        committed: number | null;
        scored: number | null;
        missed: number | null;
        saved: number | null;
    };
}

// Player with statistics
export interface PlayerWithStats {
    player: PlayerProfile;
    statistics: PlayerStatistics[];
}

// Player squad information
export interface PlayerSquad {
    player: {
        id: number;
        name: string;
        age: number | null;
        number: number | null;
        position: string | null;
        photo: string | null;
    };
}

// Team squad
export interface TeamSquad {
    team: {
        id: number;
        name: string;
        logo: string | null;
    };
    players: PlayerSquad[];
}

// Player career team information
export interface PlayerTeam {
    team: {
        id: number;
        name: string;
        logo: string | null;
    };
    statistics: {
        season: number;
        league: {
            id: number;
            name: string;
            country: string | null;
            logo: string | null;
            flag: string | null;
        };
    }[];
}

// Main Player document for MongoDB
export interface Player {
    _id: number; // Use player ID from API as MongoDB _id
    apiId: number; // Duplicate of _id to match existing database index
    profile: PlayerProfile;
    statistics?: { [key: string]: PlayerStatistics[] }; // Key is 'leagueId:seasonYear'
    teams?: PlayerTeam[]; // Career teams
    lastUpdated: Date;
}

// Function to get the players collection
export function getPlayersCollection(): Collection<Player> {
    const db = getDb();
    return db.collection<Player>('players');
}

// Helper function to create a unique key for player statistics
export function createPlayerStatKey(leagueId: number, seasonYear: number): string {
    return `${leagueId}:${seasonYear}`;
}

// Function to get the player squads collection
export function getPlayerSquadsCollection(): Collection<TeamSquad> {
    const db = getDb();
    return db.collection<TeamSquad>('playerSquads');
}

// Top player stats (scorers, assists, cards)
export interface TopPlayer {
    player: {
        id: number;
        name: string;
        firstname: string | null;
        lastname: string | null;
        age: number | null;
        birth: {
            date: string | null;
            place: string | null;
            country: string | null;
        };
        nationality: string | null;
        height: string | null;
        weight: string | null;
        injured: boolean | null;
        photo: string | null;
    };
    statistics: {
        team: {
            id: number;
            name: string;
            logo: string | null;
        };
        league: {
            id: number;
            name: string;
            country: string | null;
            logo: string | null;
            flag: string | null;
            season: number;
        };
        games: {
            appearences: number | null;
            lineups: number | null;
            minutes: number | null;
            position: string | null;
            rating: string | null;
        };
        goals: {
            total: number | null;
            assists: number | null;
        };
        cards: {
            yellow: number | null;
            yellowred: number | null;
            red: number | null;
        };
    }[];
}

// Function to get the top players collection
export function getTopPlayersCollection(): Collection<{ _id: string, players: TopPlayer[] }> {
    const db = getDb();
    return db.collection<{ _id: string, players: TopPlayer[] }>('topPlayers');
}

// Helper function to create a unique key for top players
export function createTopPlayersKey(leagueId: number, seasonYear: number, type: 'scorers' | 'assists' | 'yellowcards' | 'redcards'): string {
    return `${leagueId}:${seasonYear}:${type}`;
}
