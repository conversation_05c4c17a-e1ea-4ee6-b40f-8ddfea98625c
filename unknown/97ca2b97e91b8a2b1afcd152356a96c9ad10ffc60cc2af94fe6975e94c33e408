import express, { Router, RequestHandler } from 'express';
import { getLeagueSeasons } from '../models/LeagueSeason';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();
const SEASONS_CACHE_KEY = 'leagues:seasons:all';
const CACHE_TTL_SECONDS = 60 * 60 * 6; // Cache for 6 hours (since it updates daily)

// Define the handler function with RequestHandler type
const getLeagueSeasonsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();

    try {
        // 1. Check cache first
        const cachedSeasons = await redisClient.get(SEASONS_CACHE_KEY);
        if (cachedSeasons) {
            console.log('Serving league seasons from cache');
            res.status(200).json(JSON.parse(cachedSeasons));
            return;
        }

        // 2. If not in cache, fetch from DB
        console.log('Fetching league seasons from DB');
        const seasons = await getLeagueSeasons(); // Use helper from model

        // 3. Store in cache
        if (seasons.length > 0) {
            await redisClient.setex(SEASONS_CACHE_KEY, CACHE_TTL_SECONDS, JSON.stringify(seasons));
            console.log('League seasons stored in cache');
        }

        res.status(200).json(seasons);

    } catch (error) {
        console.error('Error fetching league seasons:', error);
        res.status(500).json({ message: 'Failed to fetch league seasons' });
    }
};

// GET /api/leagues/seasons - Fetches all available league season years
router.get('/', getLeagueSeasonsHandler);

export default router;
