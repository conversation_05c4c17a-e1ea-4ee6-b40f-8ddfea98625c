import { Response, Request } from 'express';

declare global {
  namespace Express {
    interface Response {
      json: (body?: any) => Response;
      status: (code: number) => Response;
    }

    // Add Multer namespace and File interface
    namespace Multer {
      interface File {
        fieldname: string;
        originalname: string;
        encoding: string;
        mimetype: string;
        size: number;
        destination: string;
        filename: string;
        path: string;
        buffer: Buffer;
      }
    }

    // Extend Request interface to include user property
    interface Request {
      user?: {
        id: string;
      };
    }
  }
}

export {};
