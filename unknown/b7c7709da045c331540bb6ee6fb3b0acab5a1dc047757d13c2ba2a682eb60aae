import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateTeamTransfers, fetchAndUpdatePlayerTransfers } from '../jobs/transferJobs';

// Load environment variables
dotenv.config();

// Main function
async function runTransferJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        // Choose which job to run
        const jobType = process.argv[2] || 'team'; // Default to team transfers
        
        if (jobType === 'team') {
            console.log('Running fetchAndUpdateTeamTransfers job...');
            await fetchAndUpdateTeamTransfers();
            console.log('Team transfers job completed successfully');
        } else if (jobType === 'player') {
            console.log('Running fetchAndUpdatePlayerTransfers job...');
            await fetchAndUpdatePlayerTransfers();
            console.log('Player transfers job completed successfully');
        } else {
            console.error('Invalid job type. Use "team" or "player"');
            process.exit(1);
        }
        
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runTransferJob();
