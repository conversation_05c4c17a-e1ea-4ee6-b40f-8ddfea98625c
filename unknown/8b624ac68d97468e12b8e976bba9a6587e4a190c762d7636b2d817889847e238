import { Collection, MongoClient } from 'mongodb';
import { getDb } from '../config/database';

// Define the LeagueCoverage interface
export interface LeagueCoverage {
  _id: number;  // League ID
  name: string;
  country: string;
  season: number;
  coverage: {
    standings: boolean;
    players: boolean;
    top_scorers: boolean;
    top_assists: boolean;
    top_cards: boolean;
    injuries: boolean;
    predictions: boolean;
    odds: boolean;
    fixtures?: {
      events?: boolean;
      lineups?: boolean;
      statistics?: boolean;
      players_statistics?: boolean;
    };
  };
  lastUpdated: Date;
}

// Get the league coverage collection
export function getLeagueCoverageCollection(): Collection<LeagueCoverage> {
  const db = getDb();
  return db.collection<LeagueCoverage>('leagueCoverage');
}

// Create indexes for the collection
export async function createLeagueCoverageIndexes(): Promise<void> {
  const collection = getLeagueCoverageCollection();
  
  // Create indexes
  await collection.createIndex({ lastUpdated: 1 });
  
  console.log('League coverage indexes created');
}

// Helper function to check if a league has coverage for a specific feature
export async function hasFeatureCoverage(leagueId: number, feature: string): Promise<boolean> {
  const collection = getLeagueCoverageCollection();
  const league = await collection.findOne({ _id: leagueId });
  
  if (!league || !league.coverage) return false;
  
  // Handle nested features like 'fixtures.events'
  if (feature.includes('.')) {
    const [parent, child] = feature.split('.');
    return league.coverage[parent] && league.coverage[parent][child];
  }
  
  return !!league.coverage[feature];
}

// Helper function to get all leagues with coverage for a specific feature
export async function getLeaguesWithCoverage(feature: string): Promise<number[]> {
  const collection = getLeagueCoverageCollection();
  
  // Handle nested features like 'fixtures.events'
  let query: any;
  if (feature.includes('.')) {
    const [parent, child] = feature.split('.');
    query = { [`coverage.${parent}.${child}`]: true };
  } else {
    query = { [`coverage.${feature}`]: true };
  }
  
  const leagues = await collection.find(query, { projection: { _id: 1 } }).toArray();
  return leagues.map(league => league._id);
}
